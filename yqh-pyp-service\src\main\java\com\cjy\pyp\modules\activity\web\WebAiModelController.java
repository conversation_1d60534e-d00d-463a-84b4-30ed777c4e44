package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;
import com.cjy.pyp.modules.activity.service.AiModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI模型Web接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("web/aimodel")
public class WebAiModelController {
    
    @Autowired
    private AiModelService aiModelService;
    
    /**
     * 获取可用的AI模型列表
     */
    @GetMapping("/available")
    public R available() {
        List<AiModelConfigEntity> models = aiModelService.getAvailableModels();
        
        // 只返回必要的字段，不暴露敏感信息
        List<Object> result = models.stream().map(model -> {
            return new Object() {
                public final String modelCode = model.getModelCode();
                public final String modelName = model.getModelName();
                public final String provider = model.getProvider();
                public final Integer maxTokens = model.getMaxTokens();
                public final Boolean isDefault = model.getIsDefault() == 1;
            };
        }).collect(Collectors.toList());
        
        return R.ok().put("models", result);
    }
    
    /**
     * 获取默认AI模型
     */
    @GetMapping("/default")
    public R getDefault() {
        AiModelConfigEntity defaultModel = aiModelService.getDefaultModel();
        if (defaultModel == null) {
            return R.error("未配置默认AI模型");
        }
        
        // 只返回必要的字段
        Object result = new Object() {
            public final String modelCode = defaultModel.getModelCode();
            public final String modelName = defaultModel.getModelName();
            public final String provider = defaultModel.getProvider();
            public final Integer maxTokens = defaultModel.getMaxTokens();
        };
        
        return R.ok().put("model", result);
    }
}
