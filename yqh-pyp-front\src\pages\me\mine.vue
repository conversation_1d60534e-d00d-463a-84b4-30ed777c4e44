<template>
  <div :class="isMobilePhone ? 'page' : 'page pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-card style="background: white" :thumb="userInfo.avatar ? userInfo.avatar : 'photo-o'">
      <div slot="title" style="font-size: 18px">{{ userInfo.contact }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        {{ userInfo.mobile }}
      </div>
      <!-- <div slot="price" style="font-size: 14px">{{item.address}}</div> -->
      <div slot="tag">
        <van-tag type="primary" mark v-if="userInfo.status == 1">已报名</van-tag>
        <van-tag type="danger" mark v-else>未报名</van-tag>
      </div>
      <div slot="price">
        <van-tag plain size="medium" type="danger">{{
          userInfo.activityName
        }}</van-tag>
      </div>
    </van-card>
    <div class="footer-box">
      <div @click="$router.push({ name: 'account', query: { activityId: activityId } })" class="item">我的账户</div>
      <div @click="$router.push({ name: 'meApplyInfo', query: { id: activityId } })" v-if="userInfo.status == 1"
        class="item">报名信息</div>
      <div @click="$router.push({ name: 'applyIndex', query: { id: activityId } })" v-else class="item">参会报名</div>
      <div @click="$router.push({ name: 'applyQrCode', query: { id: activityId } })" v-if="userInfo.status == 1"
        class="item">报名二维码</div>
      <div @click="$router.push({ name: 'meApplyList', query: { id: activityId } })" class="item">参会记录</div>
      <div @click="$router.push({ name: 'meHotelList', query: { id: activityId } })" class="item">酒店订单</div>
      <div @click="showScan" class="item" v-if="userInfo && userInfo.roleId == 1">扫码签到</div>
    </div>
  </div>
</template>

<script>
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import { checkAndRedirectToBindMobile, handleMobileError } from '@/js/mobileChecker.js';
export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      activityId: undefined,
      userInfo: {},
    };
  },
  mounted() {
    document.title = "个人中心";
    this.$wxShare(
      this.$cookie.get("accountName"),
      this.$cookie.get("logo"),
      this.$cookie.get("slog")
    );
    this.activityId = this.$route.query.id;

    // 检查用户是否绑定手机号
    this.checkMobileBinding();
  },
  methods: {
    // 检查手机号绑定状态
    checkMobileBinding() {
      console.log("开始检查用户手机号绑定状态");

      // 使用工具函数检查手机号，如果未绑定会自动跳转
      checkAndRedirectToBindMobile(window.location.href).then((hasMobile) => {
        if (hasMobile) {
          // 用户已绑定手机号，继续加载用户信息
          console.log("用户已绑定手机号，继续加载页面数据");
          this.getUserActivityInfo();
        }
        // 如果没有手机号，工具函数会自动处理跳转，这里不需要额外操作
      }).catch((error) => {
        console.error("检查手机号绑定状态失败:", error);
        // 即使检查失败，也尝试加载用户信息（避免阻塞用户）
        this.getUserActivityInfo();
      });
    },

    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activityuser/meMine`, {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.result;
          } else {
            // 使用工具函数处理手机号相关错误
            if (!handleMobileError(res, window.location.href)) {
              vant.Toast(res.msg);
            }
          }
        });
    },
    showScan() {
      var that = this;
      wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: function (res) {
          var result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
          console.log(result);
          that.$fly
            .get(
              `/pyp/web/activity/activityuserapplyorder/getByActivityUserIdNotCancel`,
              {
                activityUserId: result,
              }
            )
            .then((res2) => {
              if (res2.code == 200) {
                if (res2.signType != 0) {
                  vant.Dialog.confirm({
                    title: "提示",
                    message: "“" + res2.username + "”已签到",
                    confirmButtonText: "确定",
                    showCancelButton: false,
                  })
                    .then(() => {
                      // on close
                    })
                    .catch(() => {
                      // on close
                    });
                } else if (res2.result.status == 1) {
                  vant.Dialog.confirm({
                    title: "提示",
                    message: "确认为 “" + res2.username + "”签到？",
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                  })
                    .then(() => {
                      // on close
                      that.$fly
                        .get(
                          `/pyp/web/activityusersign/sign`,
                          {
                            activityUserId: result,
                            type: 1,
                          }
                        )
                        .then((res1) => {
                          if (res1.code == 200) {
                            vant.Dialog.confirm({
                              title: "提示",
                              message: "“" + res2.username + "”签到成功",
                              confirmButtonText: "确定",
                              showCancelButton: false,
                            })
                              .then(() => {
                                // on close
                              })
                              .catch(() => {
                                // on close
                              });
                          } else {
                            vant.Dialog.confirm({
                              title: "失败",
                              message: res1.msg,
                              confirmButtonText: "确定",
                              showCancelButton: false,
                            })
                              .then(() => {
                                // on close
                              })
                              .catch(() => {
                                // on close
                              });
                          }
                        });
                    })
                    .catch(() => {
                      // on close
                    });
                } else {
                  vant.Dialog.confirm({
                    title: "提示",
                    message: "“" + res2.username + "“还未报名",
                    confirmButtonText: "确定",
                    showCancelButton: false,
                  })
                    .then(() => {
                      // on close
                    })
                    .catch(() => {
                      // on close
                    });
                }
              } else {
                vant.Dialog.confirm({
                  title: "失败",
                  message: res2.msg,
                  confirmButtonText: "确定",
                  showCancelButton: false,
                })
                  .then(() => {
                    // on close
                  })
                  .catch(() => {
                    // on close
                  });
              }
            });
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.template5 {
  margin-top: 20px;
  background: #f6f6f6;
}

.template5 /deep/ .van-grid-item__content {

  background: linear-gradient(to bottom, #a2d6fb, #d9e9f3);
  padding: 4px 6px;
}

.template5 /deep/ .van-grid-item__icon {
  width: 100%;
  height: auto;
}

.template5 /deep/ .van-icon__image {
  width: 100%;
  height: auto;
  object-fit: fill;
}

.footer-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 10px; /* 元素之间的间隔 */

  .item {
    width: 30%;
    height: 110px;
    background: linear-gradient(to bottom, #a2d6fb, #d9e9f3);
    border-radius: 8px;
    text-align: center;
    line-height: 110px;
    margin-top: 10px;
    color: #0128a1;
    font-size: 22px;
    font-weight: 600;
  }
}
</style>