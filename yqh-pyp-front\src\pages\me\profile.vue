<template>
  <div class="profile-page">
    <pcheader v-if="!isMobilePhone" />

    <!-- 头部背景区域 -->
    <div class="header-section">
      <div class="header-bg">
        <!-- 用户信息卡片 -->
        <div class="user-card">
          <div class="user-avatar">
            <img v-if="userInfo.headimgurl" :src="userInfo.headimgurl" alt="头像" />
            <van-icon v-else name="user-circle-o" size="60" />
          </div>
          <div class="user-info">
            <h2 class="user-name">{{ userInfo.nickname || '用户' }}</h2>
            <div class="user-contact">
              <div v-if="userInfo.mobile" class="mobile-info">
                <van-icon name="phone-o" size="14" />
                <span>{{ userInfo.mobile }}</span>
              </div>
              <div v-else class="mobile-warning" @click="goToBindMobile">
                <van-icon name="warning-o" size="14" />
                <span>未绑定手机号，点击绑定</span>
                <van-icon name="arrow" size="12" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">

      <!-- 业务员信息卡片 -->
      <div v-if="isSalesman" class="salesman-section">
        <!-- 业务员信息 -->
        <div class="salesman-card">
          <div class="card-header">
            <div class="header-icon">
              <van-icon name="manager-o" size="20" />
            </div>
            <div class="header-text">
              <h3>业务员中心</h3>
              <p>专属业务员信息</p>
            </div>
          </div>
          <div class="salesman-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">姓名</span>
                <span class="value">{{ salesmanInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">编号</span>
                <span class="value">{{ salesmanInfo.code }}</span>
              </div>
              <div class="info-item" v-if="salesmanInfo.department">
                <span class="label">部门</span>
                <span class="value">{{ salesmanInfo.department }}</span>
              </div>
              <div class="info-item" v-if="salesmanInfo.position">
                <span class="label">职位</span>
                <span class="value">{{ salesmanInfo.position }}</span>
              </div>
              <div class="info-item">
                <span class="label">层级</span>
                <span class="value">第{{ salesmanInfo.level }}级</span>
              </div>
              <div class="info-item" @click="goToCommissionRules">
                <span class="label">抽成规则</span>
                <span class="value">查看</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 业务统计 -->
        <div class="stats-card" @click="goToOrderList">
          <div class="card-header">
            <div class="header-icon stats-icon">
              <van-icon name="chart-trending-o" size="20" />
            </div>
            <div class="header-text">
              <h3>业务统计</h3>
              <p>数据概览</p>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ salesmanStats.totalOrders || 0 }}</div>
              <div class="stat-label">总订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">¥{{ (salesmanStats.totalAmount || 0).toFixed(2) }}</div>
              <div class="stat-label">销售额</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">¥{{ (salesmanStats.totalCommission || 0).toFixed(2) }}</div>
              <div class="stat-label">佣金收入</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ childrenCount || 0 }}</div>
              <div class="stat-label">下级业务员</div>
            </div>
          </div>
        </div>

        <!-- 业务员功能 -->
        <div class="actions-card">
          <div class="actions-grid">
            <!-- <div class="action-item" @click="goToChildrenManage">
              <div class="action-icon">
                <van-icon name="friends-o" size="24" />
              </div>
              <span class="action-text">子业务员</span>
            </div>
            <div class="action-item" @click="goToInviteManage">
              <div class="action-icon invite-icon">
                <van-icon name="add-square" size="24" />
              </div>
              <span class="action-text">邀请业务员</span>
            </div> -->
            <div class="action-item" @click="goToInviteCustomer">
              <div class="action-icon customer-icon">
                <van-icon name="user-o" size="24" />
              </div>
              <span class="action-text">邀请客户</span>
            </div>
            <div class="action-item" @click="goToMyCustomers">
              <div class="action-icon customers-icon">
                <van-icon name="contact" size="24" />
              </div>
              <span class="action-text">我的客户</span>
            </div>
            <div class="action-item" @click="goToCommissionRules">
              <div class="action-icon commission-icon">
                <van-icon name="gold-coin-o" size="24" />
              </div>
              <span class="action-text">抽成规则</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 设置区域 -->
      <div class="settings-section">
        <div class="settings-card">
          <div class="card-header">
            <div class="header-icon settings-icon">
              <van-icon name="setting-o" size="20" />
            </div>
            <div class="header-text">
              <h3>设置</h3>
              <p>账户设置与帮助</p>
            </div>
          </div>
          <div class="settings-list">
            <div class="setting-item" @click="goToUserAgreement">
              <div class="setting-content">
                <div class="setting-icon">
                  <van-icon name="description" size="18" />
                </div>
                <span class="setting-text">用户协议</span>
              </div>
              <van-icon name="arrow" size="16" />
            </div>
            <div class="setting-item" @click="goToPrivacyPolicy">
              <div class="setting-content">
                <div class="setting-icon">
                  <van-icon name="shield-o" size="18" />
                </div>
                <span class="setting-text">隐私政策</span>
              </div>
              <van-icon name="arrow" size="16" />
            </div>
            <div class="setting-item logout-item" @click="handleLogout">
              <div class="setting-content">
                <div class="setting-icon logout-icon">
                  <van-icon name="stop-circle-o" size="18" />
                </div>
                <span class="setting-text">退出登录</span>
              </div>
              <van-icon name="arrow" size="16" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  isMobilePhone
} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import { checkAndRedirectToBindMobile, handleMobileError } from '@/js/mobileChecker.js';

export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      userInfo: {},
      activityId: null,
      // 业务员相关数据
      isSalesman: false,
      salesmanInfo: {},
      salesmanStats: {},
      childrenCount: 0,
      showSalesmanDetails: false
    }
  },
  computed: {
    // 从 store 中获取活动相关状态
    userActivities() {
      return this.$store.state.activity.userActivities;
    },
    selectedActivityId() {
      return this.$store.state.activity.selectedActivityId;
    },
    currentActivity() {
      return this.$store.state.activity.currentActivity;
    },
    activityOptions() {
      return this.$store.state.activity.activityOptions;
    },
    currentActivityName() {
      return this.$store.getters['activity/currentActivityName'];
    },
    hasActivities() {
      return this.$store.getters['activity/hasActivities'];
    }
  },
  watch: {
    // 监听当前活动变化
    currentActivity: {
      handler(newActivity, oldActivity) {
        if (newActivity && oldActivity && newActivity.id !== oldActivity.id) {
          console.log('Profile页面检测到活动变化:', newActivity);
          // 更新本地的 activityId
          this.activityId = newActivity.id;
        }
      },
      deep: true
    },
    // 监听选中的活动ID变化
    selectedActivityId(newId, oldId) {
      if (newId !== oldId && newId) {
        console.log('Profile页面检测到选中活动ID变化:', newId);
        // 更新本地的 activityId
        this.activityId = newId;
      }
    }
  },
  mounted() {
    document.title = "我的"
    this.$wxShare(
      this.$cookie.get("accountName"),
      this.$cookie.get("logo"),
      this.$cookie.get("slog")
    );

    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
      }
    }

    // 检查用户是否绑定手机号
    this.checkMobileBinding();
  },
  activated() {
    // 如果页面被缓存，在激活时检查状态同步
    console.log('Profile页面被激活，检查活动状态同步');
    this.checkActivitySync();
  },
  methods: {
    // 检查手机号绑定状态
    checkMobileBinding() {
      console.log("开始检查用户手机号绑定状态");

      // 使用工具函数检查手机号，如果未绑定会自动跳转
      checkAndRedirectToBindMobile(window.location.href).then((hasMobile) => {
        if (hasMobile) {
          // 用户已绑定手机号，继续加载用户信息
          console.log("用户已绑定手机号，继续加载页面数据");
          this.getUserInfo();
        }
        // 如果没有手机号，工具函数会自动处理跳转，这里不需要额外操作
      }).catch((error) => {
        console.error("检查手机号绑定状态失败:", error);
        // 即使检查失败，也尝试加载用户信息（避免阻塞用户）
        this.getUserInfo();
      });
    },

    // 跳转到订单列表页面
    goToOrderList() {
      this.$router.push({ name: 'salesmanOrders' });
    },

    // 获取用户信息
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;

          // 继续初始化其他数据
          this.initActivity();

          // 检查业务员身份
          this.checkSalesmanStatus();
        } else {
          // 使用工具函数处理手机号相关错误
          if (!handleMobileError(res, window.location.href)) {
            vant.Toast(res.msg);
          }
        }
      }).catch((error) => {
        console.error("获取用户信息失败:", error);
        vant.Toast("获取用户信息失败，请重试");
      });
    },

    // 初始化活动数据
    async initActivity() {
      console.log('开始初始化活动数据...', 'preferredActivityId:', this.activityId);

      try {
        // 只有当 this.activityId 是有效值且来自URL参数时才传递 preferredActivityId
        const initParams = {
          api: this.$fly,
          toast: this.$toast
        };

        // 只有当URL中明确传递了activityId时，才作为首选活动ID传递
        // 如果activityId来自Store（即从其他页面跳转过来），则不传递preferredActivityId，让系统使用缓存
        const urlActivityId = this.$route.query.activityId;
        if (urlActivityId && urlActivityId !== 'undefined' && urlActivityId !== null) {
          initParams.preferredActivityId = parseInt(urlActivityId);
          console.log('使用URL参数作为首选活动ID:', initParams.preferredActivityId);
        } else {
          console.log('不传递preferredActivityId，让系统使用缓存的活动ID');
        }

        const result = await this.$store.dispatch('activity/initializeActivity', initParams);

        if (result.success) {
          console.log('活动数据初始化成功:', result.data);
          // 更新本地的 activityId 为实际选中的活动ID
          this.activityId = this.selectedActivityId;
        } else {
          console.error('活动数据初始化失败:', result.error);
        }
      } catch (error) {
        console.error('初始化活动数据时发生错误:', error);
      }
    },

    // 检查活动状态同步
    checkActivitySync() {
      // 如果全局状态中没有活动数据，重新初始化
      if (!this.hasActivities) {
        console.log('Profile页面检测到没有活动数据，重新初始化');
        this.initActivity();
      } else if (this.currentActivity && this.activityId !== this.currentActivity.id) {
        // 如果当前页面的activityId与全局状态不一致，同步状态
        console.log('Profile页面检测到活动状态不一致，同步状态');
        this.activityId = this.currentActivity.id;
      }
    },

    // 活动切换
    async onActivityChange(value) {
      console.log('Activity changed to:', value);

      try {
        const result = await this.$store.dispatch('activity/switchActivity', value);
        if (result.success) {
          console.log('活动切换成功:', result.activity);
          this.activityId = value;
        } else {
          console.error('活动切换失败:', result.error);
          vant.Toast('切换活动失败');
        }
      } catch (error) {
        console.error('切换活动时发生错误:', error);
        vant.Toast('切换活动失败');
      }
    },

    // 跳转到绑定手机号页面
    goToBindMobile() {
      this.$router.push({
        name: 'bindMobile',
        query: {
          returnUrl: encodeURIComponent(window.location.href)
        }
      });
    },

    // 跳转到用户协议
    goToUserAgreement() {
      this.$router.push({ name: 'userAgreement' });
    },

    // 跳转到隐私政策
    goToPrivacyPolicy() {
      this.$router.push({ name: 'privacyPolicy' });
    },

    // 退出登录
    handleLogout() {
      vant.Dialog.confirm({
        title: '确认退出',
        message: '确定要退出登录吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        // 用户确认退出
        this.performLogout();
      }).catch(() => {
        // 用户取消退出
        console.log('用户取消退出登录');
      });
    },

    // 执行退出登录
    async performLogout() {
      try {
        // 调用后端退出登录接口
        const res = await this.$fly.post('/pyp/sys/logout');

        if (res.code === 200) {
          // 清除本地数据
          this.clearLocalData();

          // 显示退出成功提示
          vant.Toast('退出登录成功');

          // 延迟跳转到登录页面
          setTimeout(() => {
            this.navigateToLogin();
          }, 1000);
        } else {
          // 即使后端接口失败，也清除本地数据
          console.warn('后端退出登录接口失败，但仍清除本地数据:', res.msg);
          this.clearLocalData();
          vant.Toast('退出登录成功');
          setTimeout(() => {
            this.navigateToLogin();
          }, 1000);
        }
      } catch (error) {
        // 网络错误或其他异常，仍然清除本地数据
        console.error('退出登录请求失败:', error);
        this.clearLocalData();
        vant.Toast('退出登录成功');
        setTimeout(() => {
          this.navigateToLogin();
        }, 1000);
      }
    },

    // 清除本地数据
    clearLocalData() {
      // 清除cookie中的token
      this.$cookie.delete('token');

      // 清除localStorage中的数据
      localStorage.removeItem('userInfo');

      // 清除sessionStorage中的数据
      sessionStorage.removeItem('userInfo');

      // 清除store中的用户信息
      this.$store.commit('user/logout');

      // 清除活动相关的store数据
      this.$store.commit('activity/SET_USER_ACTIVITIES', []);
      this.$store.commit('activity/SET_SELECTED_ACTIVITY', { activityId: null, activity: null });
    },

    // 跳转到登录页面
    navigateToLogin() {
      try {
        console.log('=== profile 跳转登录开始 ===');
        console.log('当前URL:', window.location.href);

        const returnUrl = encodeURIComponent(window.location.href);
        console.log('编码后的returnUrl:', returnUrl);

        if (this.isMobilePhone) {
          // 手机端跳转到手机登录页面
          this.$router.replace({
            name: 'mobileLogin',
            query: { returnUrl: returnUrl }
          });
        } else {
          // PC端显示登录弹窗
          this.$store.commit('user/changePcLogin', true);
        }
        console.log('=== profile 跳转登录结束 ===');
      } catch (error) {
        console.error('跳转登录页面失败:', error);
        // 备用方案：直接刷新页面
        window.location.reload();
      }
    },

    // 检查业务员身份
    async checkSalesmanStatus() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/checkSalesmanStatus');

        if (res.code === 200 && res.isSalesman) {
          this.isSalesman = true;
          this.salesmanInfo = res.salesman;
          this.salesmanStats = res.stats || {};
          this.childrenCount = res.childrenCount || 0;
          console.log('业务员身份验证成功:', res);
        } else {
          this.isSalesman = false;
          console.log('用户不是业务员:', res.message);
        }
      } catch (error) {
        console.error('检查业务员身份失败:', error);
        this.isSalesman = false;
      }
    },

    // 跳转到子业务员管理
    goToChildrenManage() {
      this.$router.push({
        name: 'salesmanChildren',
        query: {
        }
      });
    },

    // 跳转到邀请管理
    goToInviteManage() {
      this.$router.push({
        name: 'salesmanInvite',
        query: {
        }
      });
    },

    // 跳转到邀请客户
    goToInviteCustomer() {
      this.$router.push({
        name: 'salesmanInviteCustomer',
        query: {
        }
      });
    },

    // 跳转到我的客户
    goToMyCustomers() {
      this.$router.push({
        name: 'salesmanMyCustomers',
        query: {
        }
      });
    },

    // 跳转到抽成规则
    goToCommissionRules() {
      this.$router.push({
        name: 'salesmanCommissionRules'
      });
    },

  }
}
</script>

<style lang="less" scoped>
.profile-page {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部区域 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0 40px 0;
  position: relative;
}

.header-bg {
  position: relative;
  z-index: 1;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: white;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar .van-icon {
  color: rgba(255, 255, 255, 0.8);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.user-contact {
  opacity: 0.9;
}

.mobile-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.mobile-warning {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 12px;
  background: rgba(255, 151, 106, 0.2);
  border-radius: 20px;
  border: 1px solid rgba(255, 151, 106, 0.3);
  transition: all 0.3s ease;
}

.mobile-warning:hover {
  background: rgba(255, 151, 106, 0.3);
}

/* 内容区域 */
.content-section {
  background: #f8f9fa;
  border-radius: 20px 20px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 2;
  min-height: calc(100vh - 160px);
  padding: 20px 12px 50px 12px;
}

/* 业务员区域 */
.salesman-section {
  margin-bottom: 20px;
}

/* 卡片通用样式 */
.salesman-card,
.stats-card,
.actions-card,
.settings-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  margin-right: 10px;
  color: white;
}

.header-icon.stats-icon {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
}

.header-icon.settings-icon {
  background: linear-gradient(135deg, #666 0%, #999 100%);
}

.header-text h3 {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.header-text p {
  margin: 0;
  font-size: 11px;
  color: #999;
}

/* 业务员信息 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: 10px 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  text-align: center;
}

.info-item .label {
  font-size: 11px;
  color: #999;
  margin-bottom: 3px;
}

.info-item .value {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

/* 统计数据 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 12px 6px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

/* 操作按钮 */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  border-radius: 50%;
  margin-bottom: 6px;
  color: white;
}

.action-icon.invite-icon {
  background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
}

.action-icon.customer-icon {
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
}

.action-icon.customers-icon {
  background: linear-gradient(135deg, #ff976a 0%, #ff6b35 100%);
}

.action-icon.commission-icon {
  background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
}

.action-text {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.logout-item .setting-text {
  color: #ee0a24;
}

.mobile-info {
  display: flex;
  align-items: center;
}

.mobile-warning {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
  transition: opacity 0.3s ease;
}

.mobile-warning:hover {
  opacity: 0.8;
}

/* 设置区域样式补充 */
.settings-list {
  padding: 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.setting-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  margin: 0 -20px;
  padding: 16px 20px;
  border-radius: 8px;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-content {
  display: flex;
  align-items: center;
}

.setting-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  border-radius: 8px;
  margin-right: 12px;
  color: white;
}

.setting-icon.logout-icon {
  background: linear-gradient(135deg, #ee0a24 0%, #cc0000 100%);
}

.setting-text {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.setting-item .van-icon[name="arrow"] {
  color: #c8c9cc;
}

.logout-item {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
  padding-top: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .user-avatar {
    width: 70px;
    height: 70px;
  }

  .user-name {
    font-size: 20px;
  }

  .info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .content-section {
    padding: 16px 10px 50px 10px;
  }

  .salesman-card,
  .stats-card,
  .actions-card,
  .settings-card {
    padding: 12px;
    margin-bottom: 10px;
  }
}

/* 业务员相关样式 */
.salesman-section {
  margin-top: 10px;
}

.salesman-info {
  padding: 10px 0;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.8);
  min-width: 60px;
}

.info-row .value {
  color: white;
  font-weight: 500;
}

.stats-item {
  text-align: center;
  padding: 15px 10px;
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.salesman-actions {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-item:hover {
  background-color: #f8f9fa;
  border-radius: 8px;
}

.action-item span {
  margin-top: 8px;
  font-size: 14px;
  color: #333;
}
</style>
