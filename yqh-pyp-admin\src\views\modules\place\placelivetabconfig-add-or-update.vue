<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <el-form-item label="标签名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="标签名称"></el-input>
      </el-form-item>
      <el-form-item label="所属会场" prop="placeId">
        <el-select v-model="dataForm.placeId" placeholder="所属会场" filterable>
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in placeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="orderBy">
        <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="类型" filterable>
          <el-option v-for="item in liveConfig" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.type == 0" label="内容" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
      </el-form-item>
      <el-form-item v-if="dataForm.type == 1" label="外部连接" prop="content">
        <el-input v-model="dataForm.content" placeholder="外部连接"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { liveConfig } from "@/data/place.js";
export default {
  data() {
    return {
      visible: false,
      liveConfig,
      placeList: [],
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        orderBy: 0,
        content: "",
        placeId: "",
        type: 0,
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "标签名称不能为空", trigger: "blur" },
        ],
        orderBy: [
          {
            required: true,
            message: "排序，数值越小越靠前不能为空",
            trigger: "blur",
          },
        ],
        type: [{ required: true, message: "类型不能为空", trigger: "blur" }],
      },
    };
  },
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor")
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId || 0;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placelivetabconfig/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.placeLiveTabConfig.activityId;
              this.dataForm.name = data.placeLiveTabConfig.name;
              this.dataForm.orderBy = data.placeLiveTabConfig.orderBy;
              this.dataForm.content = data.placeLiveTabConfig.content;
              this.dataForm.placeId = data.placeLiveTabConfig.placeId;
              this.dataForm.type = data.placeLiveTabConfig.type;
            }
          });
        }
      });
      this.getPlace();
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placelivetabconfig/${!this.dataForm.id ? "save" : "update"
              }`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              activityId: this.dataForm.activityId,
              name: this.dataForm.name,
              orderBy: this.dataForm.orderBy,
              content: this.dataForm.content,
              placeId: this.dataForm.placeId,
              type: this.dataForm.type,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
  },
};
</script>
