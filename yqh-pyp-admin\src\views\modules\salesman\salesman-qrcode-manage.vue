<template>
  <div>
  <el-dialog
    title="二维码管理"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="80%">
    <div class="qrcode-manage">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-select v-model="dataForm.activityId" placeholder="选择活动（可选）" clearable>
            <el-option
              v-for="activity in activityList"
              :key="activity.id"
              :label="activity.name"
              :value="activity.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="generateQrcode()">生成二维码</el-button>
          <el-button @click="getQrcodeList()">刷新</el-button>
        </el-form-item>
      </el-form>
      
      <el-table
        :data="qrcodeList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
        <el-table-column
          prop="qrcodeContent"
          header-align="center"
          align="center"
          width="120"
          label="二维码">
          <template slot-scope="scope">
            <div style="cursor: pointer;" @click="previewQrcode(scope.row.qrcodeContent)">
              <qrcode-generator :value="scope.row.qrcodeContent" :size="80"></qrcode-generator>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="activityName"
          header-align="center"
          align="center"
          label="关联活动">
          <template slot-scope="scope">
            {{ scope.row.activityName || '通用二维码' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="scanCount"
          header-align="center"
          align="center"
          label="扫码次数">
        </el-table-column>
        <el-table-column
          prop="orderCount"
          header-align="center"
          align="center"
          label="订单数量">
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          header-align="center"
          align="center"
          label="总金额">
          <template slot-scope="scope">
            ¥{{ scope.row.totalAmount }}
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
            <el-tag v-else size="small" type="success">启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="copyQrcodeUrl(scope.row.qrcodeContent)">复制链接</el-button>
            <el-button type="text" size="small" @click="updateStatus(scope.row.id, scope.row.status === 1 ? 0 : 1)">
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
  
    <!-- 二维码预览弹窗 -->
    <el-dialog
      title="二维码预览"
      :visible.sync="previewVisible"
      width="400px">
      <div style="text-align: center;">
        <qrcode-generator ref="previewQrcode" :value="previewContent" :size="300"></qrcode-generator>
        <div style="margin-top: 10px;">
          <el-button type="primary" @click="downloadQrcode">下载二维码</el-button>
          <el-button @click="copyQrcodeUrl(previewContent)">复制链接</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import QrcodeGenerator from '@/components/qrcode-generator'

  export default {
    components: {
      QrcodeGenerator
    },
    data () {
      return {
        visible: false,
        salesmanId: 0,
        dataForm: {
          activityId: ''
        },
        qrcodeList: [],
        activityList: [],
        dataListLoading: false,
        previewVisible: false,
        previewContent: ''
      }
    },
    methods: {
      init (salesmanId) {
        this.salesmanId = salesmanId
        this.visible = true
        this.$nextTick(() => {
          this.getActivityList()
          this.getQrcodeList()
        })
      },
      // 获取活动列表
      getActivityList () {
        this.$http({
          url: this.$http.adornUrl('/activity/activity/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': 1,
            'limit': 1000
          })
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.activityList = data.page.list
          }
        })
      },
      // 获取二维码列表
      getQrcodeList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl(`/salesman/qrcode/findBySalesmanId/${this.salesmanId}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.qrcodeList = data.result
          } else {
            this.qrcodeList = []
          }
          this.dataListLoading = false
        })
      },
      // 生成二维码
      generateQrcode () {
        this.$http({
          url: this.$http.adornUrl('/salesman/qrcode/generate'),
          method: 'post',
          params: this.$http.adornParams({
            'salesmanId': this.salesmanId,
            'activityId': this.dataForm.activityId || undefined
          })
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.$message({
              message: '生成二维码成功',
              type: 'success',
              duration: 1500
            })
            this.getQrcodeList()
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 预览二维码
      previewQrcode (content) {
        this.previewContent = content
        this.previewVisible = true
      },
      // 下载二维码
      downloadQrcode () {
        if (this.$refs.previewQrcode) {
          this.$refs.previewQrcode.download('salesman-qrcode.png')
        }
      },
      // 复制二维码链接
      copyQrcodeUrl (content) {
        const input = document.createElement('input')
        input.value = content
        document.body.appendChild(input)
        input.select()
        document.execCommand('copy')
        document.body.removeChild(input)
        this.$message({
          message: '链接已复制到剪贴板',
          type: 'success',
          duration: 1500
        })
      },
      // 更新状态
      updateStatus (id, status) {
        this.$http({
          url: this.$http.adornUrl('/salesman/qrcode/updateStatus'),
          method: 'post',
          params: this.$http.adornParams({
            'id': id,
            'status': status
          })
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500
            })
            this.getQrcodeList()
          } else {
            this.$message.error(data.msg)
          }
        })
      }
    }
  }
</script>

<style scoped>
.qrcode-manage {
  min-height: 400px;
}
</style>
