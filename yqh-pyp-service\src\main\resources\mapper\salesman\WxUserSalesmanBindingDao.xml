<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.WxUserSalesmanBindingDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity" id="wxUserSalesmanBindingMap">
        <result property="id" column="id"/>
        <result property="wxUserId" column="wx_user_id"/>
        <result property="salesmanId" column="salesman_id"/>
        <result property="bindingType" column="binding_type"/>
        <result property="bindingSource" column="binding_source"/>
        <result property="bindingTime" column="binding_time"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="expiryTime" column="expiry_time"/>
        <result property="status" column="status"/>
        <result property="priority" column="priority"/>
        <result property="remarks" column="remarks"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <result property="salesmanName" column="salesman_name"/>
        <result property="salesmanCode" column="salesman_code"/>
        <result property="salesmanMobile" column="salesman_mobile"/>
        <result property="wxUserName" column="wx_user_name"/>
        <result property="wxUserMobile" column="wx_user_mobile"/>
        <result property="wxUserOpenid" column="wx_user_openid"/>
    </resultMap>

    <select id="queryPage" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT
            wsb.*,
            s.name as salesman_name,
            s.code as salesman_code,
            s.mobile as salesman_mobile,
            wu.nickname as wx_user_name,
            wu.mobile as wx_user_mobile,
            wu.openid as wx_user_openid,
            COALESCE(order_stats.order_count, 0) as order_count,
            COALESCE(order_stats.order_amount, 0) as order_amount
        FROM wx_user_salesman_binding wsb
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        LEFT JOIN wx_user wu ON wsb.wx_user_id = wu.id
        LEFT JOIN (
            SELECT
                arr.user_id,
                arr.salesman_id,
                COUNT(*) as order_count,
                SUM(COALESCE(arr.pay_amount, 0)) as order_amount
            FROM activity_recharge_record arr
            WHERE arr.status = 1  -- 只统计已支付的订单
            AND arr.salesman_id IS NOT NULL
            <if test="appid != null and appid != ''">
                AND arr.appid = #{appid}
            </if>
            GROUP BY arr.user_id, arr.salesman_id
        ) order_stats ON wsb.wx_user_id = order_stats.user_id AND wsb.salesman_id = order_stats.salesman_id
        WHERE 1=1
        <if test="wxUserId != null and wxUserId != ''">
            AND wsb.wx_user_id = #{wxUserId}
        </if>
        <if test="salesmanId != null and salesmanId != ''">
            AND wsb.salesman_id = #{salesmanId}
        </if>
        <if test="status != null and status != ''">
            AND wsb.status = #{status}
        </if>
        <if test="bindingType != null and bindingType != ''">
            AND wsb.binding_type = #{bindingType}
        </if>
        <if test="wxUserName != null and wxUserName != ''">
            AND wu.nickname LIKE CONCAT('%', #{wxUserName}, '%')
        </if>
        <if test="salesmanName != null and salesmanName != ''">
            AND s.name LIKE CONCAT('%', #{salesmanName}, '%')
        </if>
        <if test="appid != null and appid != ''">
            AND wsb.appid = #{appid}
        </if>
        ORDER BY wsb.create_on DESC
    </select>

    <select id="getActiveBindingByWxUser" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT 
            wsb.*,
            s.name as salesman_name,
            s.code as salesman_code,
            s.mobile as salesman_mobile
        FROM wx_user_salesman_binding wsb
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        WHERE wsb.wx_user_id = #{wxUserId}
          AND wsb.status = 1
          AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
          AND wsb.appid = #{appid}
          AND s.status = 1
        ORDER BY wsb.priority DESC, wsb.binding_time DESC
        LIMIT 1
    </select>

    <select id="getBindingsBySlesman" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT 
            wsb.*,
            wu.nickname as wx_user_name,
            wu.mobile as wx_user_mobile,
            wu.openid as wx_user_openid
        FROM wx_user_salesman_binding wsb
        LEFT JOIN wx_user wu ON wsb.wx_user_id = wu.id
        WHERE wsb.salesman_id = #{salesmanId}
          AND wsb.status = 1
          AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
          AND wsb.appid = #{appid}
        ORDER BY wsb.binding_time DESC
    </select>

    <select id="getBindingHistoryByWxUser" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT 
            wsb.*,
            s.name as salesman_name,
            s.code as salesman_code,
            s.mobile as salesman_mobile
        FROM wx_user_salesman_binding wsb
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        WHERE wsb.wx_user_id = #{wxUserId}
          AND wsb.appid = #{appid}
        ORDER BY wsb.create_on DESC
    </select>

    <select id="existsBinding" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM wx_user_salesman_binding
        WHERE wx_user_id = #{wxUserId}
          AND salesman_id = #{salesmanId}
          AND status = 1
          AND appid = #{appid}
    </select>

    <select id="getBatchActiveBindingsByWxUsers" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT 
            wsb.*,
            s.name as salesman_name,
            s.code as salesman_code,
            s.mobile as salesman_mobile
        FROM wx_user_salesman_binding wsb
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        WHERE wsb.wx_user_id IN
        <foreach collection="wxUserIds" item="wxUserId" open="(" separator="," close=")">
            #{wxUserId}
        </foreach>
          AND wsb.status = 1
          AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
          AND wsb.appid = #{appid}
          AND s.status = 1
        ORDER BY wsb.priority DESC, wsb.binding_time DESC
    </select>

    <select id="countBindingsBySlesman" resultType="integer">
        SELECT COUNT(1)
        FROM wx_user_salesman_binding
        WHERE salesman_id = #{salesmanId}
          AND status = 1
          AND (expiry_time IS NULL OR expiry_time > NOW())
          AND appid = #{appid}
    </select>

    <select id="getExpiringBindings" resultType="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity">
        SELECT 
            wsb.*,
            s.name as salesman_name,
            s.code as salesman_code,
            wu.nickname as wx_user_name,
            wu.mobile as wx_user_mobile
        FROM wx_user_salesman_binding wsb
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        LEFT JOIN wx_user wu ON wsb.wx_user_id = wu.id
        WHERE wsb.status = 1
          AND wsb.expiry_time IS NOT NULL
          AND wsb.expiry_time > NOW()
          AND wsb.expiry_time &lt;= DATE_ADD(NOW(), INTERVAL #{days} DAY)
          AND wsb.appid = #{appid}
        ORDER BY wsb.expiry_time ASC
    </select>

    <update id="expireOverdueBindings">
        UPDATE wx_user_salesman_binding
        SET status = 0,
            update_on = NOW(),
            update_by = 0
        WHERE status = 1
          AND expiry_time IS NOT NULL
          AND expiry_time &lt;= NOW()
          AND appid = #{appid}
    </update>

</mapper>
