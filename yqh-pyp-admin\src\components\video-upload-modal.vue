<template>
  <el-dialog 
    title="选择视频" 
    :visible.sync="dialogVisible" 
    width="80%" 
    :close-on-click-modal="false"
    @close="handleClose"
    append-to-body
    :modal-append-to-body="false"
    class="video-upload-dialog"
  >
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 本地上传 -->
      <el-tab-pane label="本地上传" name="upload">
        <div class="upload-content">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="uploadFileList"
            :limit="1"
            :on-exceed="handleExceed"
            list-type="picture-card"
            accept="video/*"
          >
            <i class="el-icon-video-camera"></i>
          </el-upload>
          <div class="upload-tips">
            <p>支持格式：MP4、AVI、MOV、WMV、FLV、MKV、WEBM</p>
            <p>单个视频大小不超过 {{ maxSize }}GB</p>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 已选择的视频预览 -->
    <div v-if="selectedVideo" class="selected-preview">
      <h4>已选择视频：</h4>
      <div class="selected-video">
        <div class="video-item">
          <video :src="selectedVideo.url" width="200" height="150" controls preload="metadata"></video>
          <div class="video-info">
            <p class="video-name">{{ selectedVideo.name || '未命名视频' }}</p>
            <p class="video-url">{{ selectedVideo.url }}</p>
            <p class="video-size" v-if="selectedVideo.fileSize">文件大小: {{ formatFileSize(selectedVideo.fileSize) }}</p>
            <p class="video-duration" v-if="selectedVideo.duration">时长: {{ formatDuration(selectedVideo.duration) }}</p>
          </div>
          <div class="remove-btn" @click="removeSelected">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="!selectedVideo"
      >
        确定选择
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'VideoUploadModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    maxSize: {
      type: Number,
      default: 1 // GB
    },
    defaultVideo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'upload',
      selectedVideo: null,
      uploadFileList: [],
      uploadUrl: '',
      uploadHeaders: {}
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.init()
      }
    },
    defaultVideo: {
      handler(val) {
        this.selectedVideo = val
      },
      immediate: true
    }
  },
  mounted() {
    this.initUploadConfig()
  },
  methods: {
    init() {
      // 清空所有旧数据
      this.selectedVideo = this.defaultVideo
      this.uploadFileList = []
      this.activeTab = 'upload'

      // 清空上传组件的文件列表
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    },
    
    initUploadConfig() {
      // 使用 OSS 上传接口，与图片上传保持一致
      this.uploadUrl = this.$http.adornUrl(`/sys/oss/upload?token=${this.$cookie.get("token")}`)
      this.uploadHeaders = {
        'token': this.$cookie.get("token")
      }
    },
    
    handleTabClick() {
      // 可以在这里添加tab切换逻辑
    },
    
    // 上传前检查
    beforeUpload(file) {
      const isVideo = file.type.startsWith('video/')
      const isLtMaxSize = file.size / 1024 / 1024 / 1024 < this.maxSize
      
      if (!isVideo) {
        this.$message.error('只能上传视频文件!')
        return false
      }
      if (!isLtMaxSize) {
        this.$message.error(`视频大小不能超过 ${this.maxSize}GB!`)
        return false
      }
      
      this.$message.info('视频上传中，请稍候...')
      return true
    },
    
    // 上传成功
    handleUploadSuccess(response, file) {
      if (response && response.code === 200) {
        // OSS 上传成功后，需要获取视频元信息
        this.getVideoMetadata(file, response.url)
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 获取视频元信息
    getVideoMetadata(file, videoUrl) {
      const video = document.createElement('video')
      video.preload = 'metadata'

      video.onloadedmetadata = () => {
        const newVideo = {
          id: Date.now(), // 临时ID
          url: videoUrl,
          name: file.name,
          fileSize: file.size, // 保存文件大小（字节）
          duration: Math.round(video.duration) || 0, // 视频时长（秒）
          width: video.videoWidth || 0,
          height: video.videoHeight || 0,
          frameRate: 25, // 默认帧率
          createDate: new Date().toISOString()
        }

        this.selectedVideo = newVideo
        this.$message.success('视频上传成功')
      }

      video.onerror = () => {
        // 如果无法获取视频元信息，使用默认值
        const newVideo = {
          id: Date.now(),
          url: videoUrl,
          name: file.name,
          fileSize: file.size,
          duration: 0,
          width: 0,
          height: 0,
          frameRate: 25,
          createDate: new Date().toISOString()
        }

        this.selectedVideo = newVideo
        this.$message.success('视频上传成功（无法获取视频信息）')
      }

      video.src = videoUrl
    },
    
    // 上传失败
    handleUploadError(err) {
      this.$message.error('上传失败: ' + err.message)
    },

    // 超出文件数量限制
    handleExceed() {
      this.$message.warning('最多只能上传 1 个视频')
    },
    
    // 移除已选择的视频
    removeSelected() {
      this.selectedVideo = null
    },
    
    // 确认选择
    handleConfirm() {
      this.$emit('confirm', this.selectedVideo)
      this.handleClose()
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')

      // 关闭时清空数据，避免下次打开时显示旧数据
      this.$nextTick(() => {
        this.selectedVideo = null
        this.uploadFileList = []
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    },
    
    // 格式化文件大小 - 固定显示为MB，保留2位小数
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },
    
    // 格式化视频时长
    formatDuration(seconds) {
      if (!seconds || seconds === 0) return '0秒'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else if (minutes > 0) {
        return `${minutes}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${secs}秒`
      }
    }
  }
}
</script>

<style scoped>
.upload-content {
  min-height: 300px;
  text-align: center;
}

.upload-tips {
  margin-top: 20px;
  color: #999;
}

.upload-tips p {
  margin: 5px 0;
}

.selected-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.selected-video {
  margin-top: 10px;
}

.video-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
}

.video-info {
  flex: 1;
}

.video-name {
  font-weight: 500;
  color: #303133;
  margin: 0 0 5px 0;
}

.video-url {
  color: #909399;
  font-size: 12px;
  margin: 0 0 5px 0;
  word-break: break-all;
}

.video-size,
.video-duration {
  color: #67C23A;
  font-size: 12px;
  margin: 0;
}

.remove-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.remove-btn:hover {
  background: #f78989;
}

/* 确保弹窗显示在最上层 */
.video-upload-dialog {
  z-index: 3000 !important;
}

.video-upload-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.video-upload-dialog .el-overlay {
  z-index: 2999 !important;
}
</style>

<style>
/* 全局样式，确保视频上传弹窗显示在最上层 */
.video-upload-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.video-upload-dialog .el-overlay {
  z-index: 2999 !important;
}

.video-upload-dialog .el-dialog {
  z-index: 3001 !important;
}
</style>
