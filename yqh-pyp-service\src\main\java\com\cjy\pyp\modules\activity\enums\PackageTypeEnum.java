package com.cjy.pyp.modules.activity.enums;

/**
 * 套餐类型枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
public enum PackageTypeEnum {
    
    /**
     * 充值次数套餐
     */
    RECHARGE_COUNT(1, "充值次数套餐"),
    
    /**
     * 创建活动套餐
     */
    CREATE_ACTIVITY(2, "创建活动套餐"),

    /**
     * 活动续费套餐
     */
    RENEWAL_ACTIVITY(3, "活动续费套餐");
    
    private final Integer code;
    private final String desc;
    
    PackageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PackageTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PackageTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为充值次数套餐
     */
    public static boolean isRechargeCountPackage(Integer code) {
        return RECHARGE_COUNT.getCode().equals(code);
    }
    
    /**
     * 判断是否为创建活动套餐
     */
    public static boolean isCreateActivityPackage(Integer code) {
        return CREATE_ACTIVITY.getCode().equals(code);
    }

    /**
     * 判断是否为活动续费套餐
     */
    public static boolean isRenewalActivityPackage(Integer code) {
        return RENEWAL_ACTIVITY.getCode().equals(code);
    }
}
