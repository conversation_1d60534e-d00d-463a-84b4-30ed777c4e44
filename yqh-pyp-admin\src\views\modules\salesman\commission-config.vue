<template>
  <div class="mod-commission-config">
    <!-- 页面标题 -->
    <div v-if="currentSalesmanName" class="page-header" style="margin-bottom: 20px;">
      <h3 style="margin: 0; color: #303133;">
        <i class="el-icon-user" style="margin-right: 8px;"></i>
        {{ currentSalesmanName }} - 佣金配置管理
      </h3>
    </div>

    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item v-if="!currentSalesmanId">
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.commissionType" placeholder="佣金类型" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option v-for="type in commissionTypes" :key="type.code" :label="type.desc" :value="type.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" :value="1"></el-option>
          <el-option label="禁用" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="业务员">
      </el-table-column>
      <el-table-column prop="salesmanCode" header-align="center" align="center" label="业务员编号">
      </el-table-column>
      <el-table-column prop="commissionTypeDesc" header-align="center" align="center" label="佣金类型">
      </el-table-column>
      <el-table-column prop="calculationTypeDesc" header-align="center" align="center" label="计算方式">
      </el-table-column>
      <el-table-column prop="commissionValue" header-align="center" align="center" label="佣金值">
        <template slot-scope="scope">
          <span v-if="scope.row.calculationType === 1">¥{{ scope.row.commissionValue }}</span>
          <span v-else>{{ (scope.row.commissionValue * 100).toFixed(2) }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="minAmount" header-align="center" align="center" label="最小金额">
        <template slot-scope="scope">
          <span v-if="scope.row.minAmount">¥{{ scope.row.minAmount }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="maxAmount" header-align="center" align="center" label="最大金额">
        <template slot-scope="scope">
          <span v-if="scope.row.maxAmount">¥{{ scope.row.maxAmount }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="effectiveDate" header-align="center" align="center" width="150" label="生效时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './commission-config-add-or-update'

export default {
  data() {
    return {
      dataForm: {
        salesmanId: '',
        salesmanName: '',
        commissionType: '',
        status: ''
      },
      currentSalesmanId: null, // 当前选中的业务员ID
      currentSalesmanName: '', // 当前选中的业务员姓名
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      commissionTypes: []
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.initPageParams()
    this.getDataList()
    this.getCommissionTypes()
  },
  methods: {
    // 初始化页面参数
    initPageParams() {
      // 从URL参数中获取业务员信息
      this.currentSalesmanId = this.$route.query.salesmanId
      this.currentSalesmanName = this.$route.query.salesmanName
        this.dataForm.salesmanId = this.currentSalesmanId

    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/commission/config/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'salesmanId': this.dataForm.salesmanId,
          'salesmanName': this.dataForm.salesmanName,
          'commissionType': this.dataForm.commissionType,
          'status': this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取佣金类型选项
    getCommissionTypes() {
      this.$http({
        url: this.$http.adornUrl('/salesman/commission/config/getCommissionTypes'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.commissionTypes = data.commissionTypes
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.currentSalesmanId)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/commission/config/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
