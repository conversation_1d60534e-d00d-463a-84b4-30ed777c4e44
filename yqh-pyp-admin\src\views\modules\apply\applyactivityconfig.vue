<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" >
      <!-- <el-form-item>
          <el-button v-if="isAuth('apply:applyactivityconfig:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button v-if="isAuth('apply:applyactivityconfig:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        </el-form-item> -->
      <el-form-item>
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :default-expand-all="isExpand" :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="expand">
      <template slot-scope="props" v-if="props.row.children">
        <el-table :data="props.row.children" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column
        prop="finalName"
        header-align="center"
        align="center"
        label="字段名称">
      </el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-'+(scope.row.type)">{{scope.row.type | applyTypeFilter}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        prop="selectData"
        header-align="center"
        align="center"
        label="字段选项">
      </el-table-column>
      <!-- <el-table-column
        prop="defaultValue"
        header-align="center"
        align="center"
        label="默认值">
      </el-table-column> -->
      <el-table-column
        prop="required"
        header-align="center"
        align="center"
        label="是否必填">
        <div slot-scope="scope">
          <el-tag type="success" v-if="scope.row.required == 1">是</el-tag>
          <el-tag type="danger" v-else>否</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"><template slot-scope="scopeExt">
    <el-button type="text" size="small" @click="configUpdateHandle(
      scopeExt.row,
      scopeExt.$index)">
      修改</el-button>
    <el-button type="text" style="color: red" size="small" @click="configdeleteHandle(
      scopeExt.row.id,
      scopeExt.$index)">
      删除</el-button>
      </template>
      </el-table-column>
    </el-table>
      </template>
    </el-table-column>
      <el-table-column
        prop="isSelect"
        header-align="center"
        align="center"
        label="是否已配置">
        <div slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.isSelect == 0">否</el-tag>
          <el-tag type="success" v-else>是</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        prop="required"
        header-align="center"
        align="center"
        label="是否必填">
        <div slot-scope="scope">
          <el-tag type="success" v-if="scope.row.required == 1">是</el-tag>
          <el-tag type="danger" v-else>否</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        prop="applyConfigName"
        header-align="center"
        align="center"
        label="配置字段名称">
      </el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-'+(scope.row.type)">{{scope.row.type | applyTypeFilter}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        prop="finalName"
        header-align="center"
        align="center"
        label="字段最终名称">
      </el-table-column>
      <el-table-column
        prop="selectData"
        header-align="center"
        align="center"
        label="字段选项">
      </el-table-column>
      <!-- <el-table-column
        prop="defaultValue"
        header-align="center"
        align="center"
        label="默认值">
      </el-table-column> -->
      <el-table-column
        prop="createOn"
        header-align="center"
        align="center"
        label="创建时间">
      </el-table-column>
      <el-table-column
        prop="updateOn"
        header-align="center"
        align="center"
        label="更新时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"><template slot-scope="scope">
          <!-- 扩展字段配置 -->
  <div v-if="scope.row.applyConfigFieldName == 'extra'">
    <el-button type="text" size="small" @click="configAddOrUpdateHandle(scope.row.activityId,scope.row.applyActivityChannelConfigId,scope.row.applyConfigId,scope.row.applyConfigName,scope.row.id)">
      添加</el-button>
    <el-button style="color: red" type="text" size="small" @click="deleteHandle(scope.row.id)">
      取消配置</el-button>
  </div>
  <!-- 普通字段 -->
  <div v-else>
    <el-button type="text" size="small" v-if="scope.row.isSelect == 0" @click="addOrUpdateHandle(scope.row.activityId,scope.row.applyActivityChannelConfigId,scope.row.applyConfigId,scope.row.applyConfigName)">
      配置</el-button>
    <el-button style="color: red" type="text" size="small" v-if="scope.row.isSelect == 1" @click="deleteHandle(scope.row.id)">
      取消配置</el-button>
    <el-button style="color: red" type="text" size="small" v-if="scope.row.isSelect == 1" @click="addOrUpdateHandle(scope.row.activityId,scope.row.applyActivityChannelConfigId,scope.row.applyConfigId,scope.row.applyConfigName,scope.row.id)">
      修改配置</el-button>
  </div></template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <extra-add v-if="extraAddVisible" ref="extraAdd" @refreshDataList="getDataList">></extra-add>
    <extra-update v-if="extraUpdateVisible" ref="extraUpdate" @refreshDataList="getDataList">></extra-update>
  </div>
</template>

<script>
  import AddOrUpdate from "./applyactivityconfig-add-or-update";
  import ExtraAdd from "./applyactivityconfig-extra-add";
  import ExtraUpdate from "./applyactivityconfig-extra-update";
  export default {
    data() {
      return {
        isExpand: true,
        dataForm: {
          key: "",
          channelId: undefined,
        },
        // 0-填空，1-单选，2-多选，3-扩展
        applyTypeList: [{
            id: 0,
            name: "填空"
          },
          {
            id: 1,
            name: "单选"
          },
          {
            id: 2,
            name: "多选"
          },
          {
            id: 3,
            name: "扩展"
          },
          {id: 4, name: '特殊'},
          {id: 5, name: '日期'},
        ],
        dataList: [],
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        extraAddVisible: false,
        extraUpdateVisible: false,
      };
    },
    components: {
      AddOrUpdate,
      ExtraAdd,
      ExtraUpdate,
    },
    activated() {
      this.dataForm.channelId = this.$route.query.channelId;
      this.getDataList();
    },
    filters: {
      applyTypeFilter(v) {
        var applyTypes = [{
            id: 0,
            name: "填空"
          },
          {
            id: 1,
            name: "单选"
          },
          {
            id: 2,
            name: "多选"
          },{
            id: 3,
            name: "扩展"
          },
          {id: 4, name: '特殊'},
          {id: 5, name: '日期'},
        ];
        let value = applyTypes.filter((item) => item.id === v);
        if (value.length >= 1) {
          return value[0].name;
        }
      },
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl("/apply/applyactivityconfig/list"),
          method: "get",
          params: this.$http.adornParams({
            channelId: this.dataForm.channelId,
          }),
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.result;
          } else {
            this.dataList = [];
          }
          this.dataListLoading = false;
        });
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val;
      },
      // 新增 / 修改
      addOrUpdateHandle(
        activityId,
        applyActivityChannelConfigId,
        applyConfigId,
        finalName,
        id
      ) {
        this.addOrUpdateVisible = true;
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(
            activityId,
            applyActivityChannelConfigId,
            applyConfigId,
            finalName,
            id
          );
        });
      },
      // 扩展字段配置
      configAddOrUpdateHandle(
        activityId,
        applyActivityChannelConfigId,
        applyConfigId,
        finalName,
        id
      ) {
        this.extraAddVisible = true;
        this.$nextTick(() => {
          this.$refs.extraAdd.init(
            activityId,
            applyActivityChannelConfigId,
            applyConfigId,
            finalName,
            id
          );
        });
      },
      configUpdateHandle(row,index) {
        this.extraUpdateVisible = true;
        this.$nextTick(() => {
          this.$refs.extraUpdate.init(
            row,
            index
          );
        });

      },
      // 扩展配置删除
      configdeleteHandle(v,index) {
        this.$confirm(
          `确认删除改扩展配置？`,
          "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          this.$http({
            
              url: this.$http.adornUrl(`/apply/applyactivityconfig/deleteExtra`),
              method: 'post',
              data: this.$http.adornData({
                'id': v,
                'row': index,
              })
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        });

      },
      // 删除
      deleteHandle(id) {
        var ids = id ?
          [id] :
          this.dataListSelections.map((item) => {
            return item.id;
          });
        this.$confirm(
          `确定对[id=${ids.join(",")}]进行[${
            id ? "取消配置" : "批量取消配置"
          }]操作?`,
          "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          this.$http({
            url: this.$http.adornUrl("/apply/applyactivityconfig/delete"),
            method: "post",
            data: this.$http.adornData(ids, false),
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        });
      },
    },
  };
</script>
