package com.cjy.pyp.modules.activity.service.impl;

import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.net.SocketTimeoutException;
import java.util.*;

/**
 * Kimi模型服务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class KimiModelService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 调用Kimi API生成文本，带重试机制
     *
     * @param prompt 提示词
     * @param config 模型配置
     * @return 生成的文本内容
     * @throws Exception 调用失败时抛出异常
     */
    public String generateText(String prompt, AiModelConfigEntity config) throws Exception {
        if (config == null) {
            throw new Exception("Kimi模型配置不能为空");
        }
        
        int maxRetries = config.getMaxRetries() != null ? config.getMaxRetries() : 3;
        int retryDelay = config.getRetryDelay() != null ? config.getRetryDelay() : 2000;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return doCallKimiApi(prompt, config);
            } catch (ResourceAccessException e) {
                if (e.getCause() instanceof SocketTimeoutException || 
                    e.getMessage().contains("Read timed out")) {
                    
                    if (attempt == maxRetries) {
                        throw new Exception("Kimi API调用超时，已重试" + maxRetries + "次，请稍后再试");
                    }
                    
                    // 等待后重试，使用递增延迟
                    try {
                        Thread.sleep(retryDelay * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("API调用被中断");
                    }
                } else {
                    throw new Exception("Kimi API网络连接失败: " + e.getMessage());
                }
            } catch (Exception e) {
                if (attempt == maxRetries) {
                    throw new Exception("Kimi API调用失败: " + e.getMessage());
                }
                
                // 对于其他异常也进行重试
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("API调用被中断");
                }
            }
        }
        
        throw new Exception("Kimi API调用失败，已达到最大重试次数");
    }
    
    /**
     * 实际调用Kimi API的方法
     */
    private String doCallKimiApi(String prompt, AiModelConfigEntity config) throws Exception {
        // 验证输入参数
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new Exception("提示词不能为空");
        }
        
        if (config.getApiKey() == null || config.getApiKey().trim().isEmpty()) {
            throw new Exception("Kimi API密钥未配置");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + config.getApiKey());

        ObjectMapper mapper = new ObjectMapper();

        // 构建请求消息
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);

        List<Map<String, Object>> messages = new ArrayList<>();
        messages.add(message);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", config.getModelCode());
        requestBody.put("messages", messages);
        requestBody.put("stream", false);
        requestBody.put("max_tokens", config.getMaxTokens() != null ? config.getMaxTokens() : 8000);
        requestBody.put("temperature", config.getTemperature() != null ? config.getTemperature().doubleValue() : 0.7);

        HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(requestBody), headers);
        
        // 发送请求
        String response = restTemplate.postForObject(config.getApiUrl() + "/v1/chat/completions", request, String.class);

        if (response == null || response.trim().isEmpty()) {
            throw new Exception("Kimi API返回空响应");
        }

        // 解析响应
        return parseApiResponse(response, mapper);
    }
    
    /**
     * 解析API响应
     */
    private String parseApiResponse(String response, ObjectMapper mapper) throws Exception {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = mapper.readValue(response, Map.class);
            
            // 检查是否有错误
            if (responseMap.containsKey("error")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> error = (Map<String, Object>) responseMap.get("error");
                String errorMessage = (String) error.get("message");
                String errorType = (String) error.get("type");
                throw new Exception("Kimi API错误 [" + errorType + "]: " + errorMessage);
            }
            
            // 获取生成的内容
            List<?> choices = (List<?>) responseMap.get("choices");
            if (choices == null || choices.isEmpty()) {
                throw new Exception("Kimi API返回的choices为空");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> choice = (Map<String, Object>) choices.get(0);
            @SuppressWarnings("unchecked")
            Map<String, Object> message = (Map<String, Object>) choice.get("message");
            
            String content = (String) message.get("content");
            if (content == null || content.trim().isEmpty()) {
                throw new Exception("Kimi API返回的内容为空");
            }
            
            return content;
            
        } catch (Exception e) {
            if (e.getMessage().contains("Kimi API")) {
                throw e; // 重新抛出我们自定义的异常
            }
            throw new Exception("解析Kimi API响应失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试连接
     */
    public boolean testConnection(AiModelConfigEntity config) {
        try {
            String testPrompt = "Hello";
            String result = generateText(testPrompt, config);
            return result != null && !result.trim().isEmpty();
        } catch (Exception e) {
            log.error("Kimi模型连接测试失败: {}", config.getModelCode(), e);
            return false;
        }
    }
}
