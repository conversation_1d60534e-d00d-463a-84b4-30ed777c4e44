<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="摘要" prop="name">
        <el-input v-model="dataForm.name" placeholder="摘要"></el-input>
      </el-form-item>
      <el-form-item label="科目" prop="priceConfigId">
        <el-select v-model="dataForm.priceConfigId" placeholder="科目" filterable>
          <el-option v-for="item in priceConfig" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="到款金额" prop="price">
        <el-input v-model="dataForm.price" placeholder="到款金额"></el-input>
      </el-form-item>
      <el-form-item label="付款类型" prop="type">
        <el-input v-model="dataForm.type" placeholder="付款类型"></el-input>
      </el-form-item>
      <el-form-item label="付款时间" prop="payTime">
        <el-date-picker v-model="dataForm.payTime" type="datetime" placeholder="请选择付款时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="银行账号" prop="priceBankId">
        <el-input v-model="dataForm.priceBankId" placeholder="银行账号"></el-input>
      </el-form-item>
      <el-form-item label="余额" prop="balance">
        <el-input v-model="dataForm.balance" placeholder="余额"></el-input>
      </el-form-item>
      <el-form-item label="0-未比对，1-已比对" prop="status">
        <el-input v-model="dataForm.status" placeholder="0-未比对，1-已比对"></el-input>
      </el-form-item>
      <el-form-item label="交易流水号" prop="tranFlow">
        <el-input v-model="dataForm.tranFlow" placeholder="交易流水号"></el-input>
      </el-form-item>
      <el-form-item label="活存账户明细号" prop="detNo">
        <el-input v-model="dataForm.detNo" placeholder="活存账户明细号"></el-input>
      </el-form-item>
      <el-form-item label="对方账号" prop="accNumber">
        <el-input v-model="dataForm.accNumber" placeholder="对方账号"></el-input>
      </el-form-item>
      <el-form-item label="对方户名" prop="accName">
        <el-input v-model="dataForm.accName" placeholder="对方户名"></el-input>
      </el-form-item>
      <el-form-item label="对方账户开户行名称" prop="accBankName">
        <el-input v-model="dataForm.accBankName" placeholder="对方账户开户行名称"></el-input>
      </el-form-item>
      <el-form-item label="到款金额" prop="matchPrice">
        <el-input v-model="dataForm.matchPrice" placeholder="到款金额"></el-input>
      </el-form-item>
      <el-form-item label="会议ID" prop="activityId">
        <el-input v-model="dataForm.activityId" placeholder="会议ID"></el-input>
      </el-form-item>
      <el-form-item label="所属会议结算单" prop="activitySettleId">
        <el-input v-model="dataForm.activitySettleId" placeholder="所属会议结算单"></el-input>
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      priceConfig: [],
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,

        name: '',

        price: '',

        type: '',

        payTime: '',

        priceConfigId: '',

        priceBankId: '',

        balance: '',

        status: '',

        tranFlow: '',

        detNo: '',

        accNumber: '',

        accName: '',

        accBankName: '',

        matchPrice: '',

        activityId: '',

        activitySettleId: ''
      },
      dataRule: {
        name: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '付款类型不能为空', trigger: 'blur' }
        ],
        payTime: [
          { required: true, message: '付款时间不能为空', trigger: 'blur' }
        ],
        priceConfigId: [
          { required: true, message: '科目不能为空', trigger: 'blur' }
        ],
        priceBankId: [
          { required: true, message: '银行账号不能为空', trigger: 'blur' }
        ],
        balance: [
          { required: true, message: '余额不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '0-未比对，1-已比对不能为空', trigger: 'blur' }
        ],
        tranFlow: [
          { required: true, message: '交易流水号不能为空', trigger: 'blur' }
        ],
        detNo: [
          { required: true, message: '活存账户明细号不能为空', trigger: 'blur' }
        ],
        accNumber: [
          { required: true, message: '对方账号不能为空', trigger: 'blur' }
        ],
        accName: [
          { required: true, message: '对方户名不能为空', trigger: 'blur' }
        ],
        accBankName: [
          { required: true, message: '对方账户开户行名称不能为空', trigger: 'blur' }
        ],
        matchPrice: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '会议ID不能为空', trigger: 'blur' }
        ],
        activitySettleId: [
          { required: true, message: '所属会议结算单不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.getPriceConfig();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricewater/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.priceWater.name
              this.dataForm.price = data.priceWater.price
              this.dataForm.type = data.priceWater.type
              this.dataForm.payTime = data.priceWater.payTime
              this.dataForm.priceConfigId = data.priceWater.priceConfigId
              this.dataForm.priceBankId = data.priceWater.priceBankId
              this.dataForm.balance = data.priceWater.balance
              this.dataForm.status = data.priceWater.status
              this.dataForm.tranFlow = data.priceWater.tranFlow
              this.dataForm.detNo = data.priceWater.detNo
              this.dataForm.accNumber = data.priceWater.accNumber
              this.dataForm.accName = data.priceWater.accName
              this.dataForm.accBankName = data.priceWater.accBankName
              this.dataForm.matchPrice = data.priceWater.matchPrice
              this.dataForm.activityId = data.priceWater.activityId
              this.dataForm.activitySettleId = data.priceWater.activitySettleId
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    getPriceConfig() {
      this.$http({
        url: this.$http.adornUrl("/price/priceconfig/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceConfig = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricewater/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'price': this.dataForm.price,
              'type': this.dataForm.type,
              'payTime': this.dataForm.payTime,
              'priceConfigId': this.dataForm.priceConfigId,
              'priceBankId': this.dataForm.priceBankId,
              'balance': this.dataForm.balance,
              'status': this.dataForm.status,
              'tranFlow': this.dataForm.tranFlow,
              'detNo': this.dataForm.detNo,
              'accNumber': this.dataForm.accNumber,
              'accName': this.dataForm.accName,
              'accBankName': this.dataForm.accBankName,
              'matchPrice': this.dataForm.matchPrice,
              'appid': this.$cookie.get('appid'),

              'activityId': this.dataForm.activityId,
              'activitySettleId': this.dataForm.activitySettleId,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
