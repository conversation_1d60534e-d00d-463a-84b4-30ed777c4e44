<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.sendTrip" placeholder="发送行程填写" filterable>
          <el-option label="全部(发送行程填写)" value=""></el-option>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.isBuy" placeholder="是否出票" filterable>
          <el-option label="全部(是否出票)" value=""></el-option>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.planeOrderStatus" placeholder="飞机订单状态" filterable @change="changePlaneOrderStatus">
          <el-option label="全部(飞机订单状态)" value=""></el-option>
          <el-option v-for="item in tripPlaneStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.trainOrderStatus" placeholder="火车订单状态" filterable @change="changeTrainOrderStatus">
          <el-option label="全部(火车订单状态)" value=""></el-option>
          <el-option v-for="item in tripTrainStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <!-- <el-button v-if="isAuth('activity:activityguest:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button> -->
        <!-- <el-button v-if="isAuth('activity:activityguest:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
        <!-- <el-button @click="exportHandle()" type="success">导出</el-button> -->
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" :span-method="arraySpanMethod"
      style="width: 100%;">
      <!-- <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column> -->
      <el-table-column prop="name" header-align="center" align="center" label="专家信息">
        <div slot-scope="scope" @click="updateUser(scope.row)">
          <div>{{ scope.row.activityGuestName }}</div>
          <div>{{ scope.row.activityGuestMobile }}</div>
        </div>
      </el-table-column>
      <el-table-column prop="sendTrip" header-align="center" align="center" label="行程短信发送">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendTrip == 1 ? 'success' : 'danger'">{{ scope.row.sendTrip == 1 ? '已发送' : '未发送'
            }}</el-tag>
            <el-button style="margin-left: 5px;" v-if="!scope.row.sendTrip" type="text" size="mini" @click="sendTaskHandle(scope.row.activityGuestId)">发送短信</el-button>
        </div>
      </el-table-column>
      <el-table-column prop="sendTripTime" header-align="center" align="center" label="发送时间">
      </el-table-column>
      <el-table-column prop="isLink" header-align="center" align="center" label="接送确认" width="200px">
        <template slot-scope="scope">
          <div>
            <!-- <el-tag :type="scope.row.isLink === 1 ? 'success' : 'info'">
              {{ scope.row.isLink === 1 ? '已确认' : '未确认' }}
            </el-tag> -->
            <div v-if="scope.row.isLink === 1">
              <div v-if="scope.row.isLinkStart == 1">接站点: {{ scope.row.linkStart || '暂无' }}
                <!-- <el-tag size="mini" :type="scope.row.isLinkStart === 1 ? 'success' : 'info'">
                  {{ scope.row.isLinkStart === 1 ? '已确认' : '未确认' }}
                </el-tag> -->
              </div>
              <div v-if="scope.row.isLinkEnd === 1">送站点: {{ scope.row.linkEnd || '暂无' }}
                <!-- <el-tag size="mini" :type="scope.row.isLinkEnd === 1 ? 'success' : 'info'">
                  {{ scope.row.isLinkEnd === 1 ? '已确认' : '未确认' }}
                </el-tag> -->
              </div>
              <div>确认时间:{{ scope.row.isLinkTime | formatDate }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="交通工具(班次)">
        <div style="display: flex;align-items: center;justify-content: center;flex-direction: column;" slot-scope="scope">
          <el-tag v-if="scope.row.inType !== null"  type="primary" :class="('tag-color tag-color-' + scope.row.inType)">{{ 
              guestGoType[scope.row.inType].value }}</el-tag>
          <div v-if="scope.row.inNumber">({{ scope.row.inNumber }})</div>
        </div>
      </el-table-column>
      <el-table-column prop="inDate" header-align="center" align="center" label="日期">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="出发地点-到达地点">
        <div slot-scope="scope">
          <span>{{ scope.row.inStartPlace }}-{{ scope.row.inStartTerminal }}</span>-<span>{{ scope.row.inEndPlace }}-{{ scope.row.inEndTerminal }}</span>
        </div>
      </el-table-column>
      <el-table-column prop="unit" width="300px" header-align="center" align="center" label="出发时间-到达时间">
        <div slot-scope="scope">
          <span v-if="scope.row.inStartDate && scope.row.inEndDate">{{ scope.row.inStartDate }}-{{ scope.row.inEndDate | dateFilter }}</span>
          <span v-else>-</span>
        </div>
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="类型">
        <div slot-scope="scope">
          <el-tag type="primary" @click="activityguesttripupdatetype(scope.row.id)"  :class="('tag-color tag-color-' + scope.row.type)">{{ scope.row.type == null ? '空' :
              tripType[scope.row.type].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="价格">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="是否出票">
        <div slot-scope="scope">
          <div v-if="scope.row.id">
          <el-tag @click="activityguesttripupdatestatusHandle(scope.row.id)" type="primary" :class="('tag-color tag-color-' + scope.row.isBuy)">{{ scope.row.isBuy == null ? '空' :
              isBuy[scope.row.isBuy].value }}</el-tag>
          </div>
          <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="订单状态">
        <div slot-scope="scope">
          <div v-if="scope.row.id">
          <el-tag v-if="scope.row.inType == 0" @click="activityguesttripupdatestatusHandle(scope.row.id)" type="primary" :class="('tag-color tag-color-1')">{{ 
            scope.row.orderStatus | tripPlaneStatusFilter }}</el-tag>
          <el-tag v-else-if="scope.row.inType == 1" @click="activityguesttripupdatestatusHandle(scope.row.id)" type="primary" :class="('tag-color tag-color-2')">{{ 
            scope.row.orderStatus | tripTrainStatusFilter }}</el-tag>
          </div>
          <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="remarks" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="300" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.id && !scope.row.isCha" type="text" size="small" @click="activityguesttripaddorupdateHandle(scope.row.id,scope.row.activityId,scope.row.activityGuestId,1)" style="color: red;">改签</el-button>
          <el-button v-if="scope.row.id && (scope.row.orderStatus == 2 || scope.row.orderStatus == 4)" type="text" size="small" @click="planeTuiPiao(scope.row.id)" style="color: red;">退票</el-button>
          <el-button v-if="scope.row.id && scope.row.orderStatus == 0" type="text" size="small" @click="planeChuPiao(scope.row.id)" style="color: red;">出票&扣款</el-button>
          <!-- <el-button v-if="scope.row.id && scope.row.orderStatus == 1" type="text" size="small" @click="planePay(scope.row.id)" style="color: red;"></el-button> -->
          <el-button v-if="scope.row.id" type="text" size="small" @click="activityguesttripaddorupdateHandle(scope.row.id,scope.row.activityId,scope.row.activityGuestId,0)">修改</el-button>
          <el-button v-if="!scope.row.id" type="text" size="small" @click="activityguesttripaddorupdateHandle('',scope.row.activityId,scope.row.activityGuestId,0)" style="color: red;">新增专家行程</el-button>
          <el-button v-if="scope.row.id" type="text" size="small" @click="activityguesttripaddorupdateHandle('',scope.row.activityId,scope.row.activityGuestId,0)" style="color: red;">继续新增专家行程</el-button>
          <el-button v-if="scope.row.id" type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <showtask v-if="showtaskVisible" ref="showtask" @refreshDataList="getDataList"></showtask>
    <activityguesttripupdatestatus v-if="activityguesttripupdatestatusVisible" ref="activityguesttripupdatestatus" @refreshDataList="getDataList"></activityguesttripupdatestatus>
    <activityguesttripaddorupdate v-if="activityguesttripaddorupdateVisible" ref="activityguesttripaddorupdate" @refreshDataList="getDataList"></activityguesttripaddorupdate>
    <activityguesttripupdatetype v-if="activityguesttripupdatetypeVisible" ref="activityguesttripupdatetype" @refreshDataList="getDataList"></activityguesttripupdatetype>
    <activityguestaddorupdate v-if="activityguestaddorupdateVisible" ref="activityguestaddorupdate" @refreshDataList="getDataList"></activityguestaddorupdate>
  </div>
</template>

<script>
import AddOrUpdate from './activityguestplane-save-or-update'
import showtask from './activityguest-showtask'
import activityguesttripupdatestatus from './activityguesttrip-updatestatus'
import activityguesttripaddorupdate from './activityguesttrip-add-or-update'
import activityguesttripupdatetype from './activityguesttrip-updatetype'
import activityguestaddorupdate from './activityguest-add-or-update'
import { guestGoType,tripType,isBuy,tripPlaneStatus,tripTrainStatus } from '@/data/activity'
import {yesOrNo} from '@/data/common'
export default {
  data() {
    return {
      isBuy,
      tripPlaneStatus,
      tripTrainStatus,
      guestGoType,
      tripType,
      yesOrNo,
      dataForm: {
        inType: '',
        isBuy: '',
        planeOrderStatus: '',
        trainOrderStatus: '',
        name: '',
        mobile: '',
        sendTrip: '',
        isFirstChar: 0,
        activityId: undefined
      },
      activityInfo: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 200,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dataMerge: [],
      showtaskVisible: false,
      activityguesttripupdatestatusVisible: false,
      activityguesttripaddorupdateVisible: false,
      activityguesttripupdatetypeVisible: false,
      activityguestaddorupdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    showtask,
    activityguesttripupdatestatus,
    activityguesttripaddorupdate,
    activityguesttripupdatetype,
    activityguestaddorupdate,
  },
  filters: {
    dateFilter(val) {
      return val.substring(11 );
    },
    tripPlaneStatusFilter(val) {
      let data = tripPlaneStatus.filter(item => item.key === val)
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    tripTrainStatusFilter(val) {
      let data = tripTrainStatus.filter(item => item.key === val)
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    formatDate(time) {
      if (time == null || time === '') {
        return '暂无';
      }
      let date = new Date(time);
      return date.getFullYear() + '-' +
             ((date.getMonth() + 1).toString().padStart(2, '0')) + '-' +
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    }
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getActivity();
  },
  methods: {
    changePlaneOrderStatus(val) {
      this.dataForm.orderStatus = val
      this.dataForm.inType = 0
      this.dataForm.trainOrderStatus = ''
      this.getDataList()
    },
    changeTrainOrderStatus(val) {
      this.dataForm.orderStatus = val
      this.dataForm.inType = 1
      this.dataForm.planeOrderStatus = ''
      this.getDataList()
    },
    updateUser(row) {
      this.activityguestaddorupdateVisible = true
      this.$nextTick(() => {
        this.$refs.activityguestaddorupdate.init(row.activityId, row.activityGuestId)
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/tripList'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'sendTrip': this.dataForm.sendTrip,
          'activityId': this.dataForm.activityId,
          'orderStatus': this.dataForm.orderStatus,
          'isBuy': this.dataForm.isBuy,
          'isFirstChar': this.activityInfo.isFirstChar,
          'inType': this.dataForm.inType,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
          this.dataMerge = data.page.extra
        } else {
          this.dataList = []
          this.dataMerge = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguesttrip/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    planeChuPiao(id) {
      this.$confirm(`确定出票?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/panhe/plane/createOrder'),
          method: 'get',
          params: this.$http.adornParams({
            tripId: id
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    planeTuiPiao(id) {
      this.$confirm(`确定退票?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/panhe/plane/applyRefundTicket'),
          method: 'get',
          params: this.$http.adornParams({
            tripId: id
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    planePay(id) {
      this.$confirm(`确定支付?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/panhe/plane/pay'),
          method: 'get',
          params: this.$http.adornParams({
            tripId: id
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendTaskHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送行程填写短信' : '批量发送行程填写短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendTrip'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
          this.getDataList()
        }
      });
    },
    updateIsFirstChar(v) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/updateIsFirstChar`),
        method: "post",
        data: this.$http.adornData({
          id: this.dataForm.activityId,
          isFirstChar: v,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message.success("操作成功");
          this.getActivity();
          this.pageIndex = 1;
          this.getDataList()
        } else {
          this.$message.error(data.msg)

        }
      });
    },
    showTaskHandle(v) {
      this.showtaskVisible = true
      this.$nextTick(() => {
        this.$refs.showtask.init(v)
      })
    },
    activityguesttripupdatestatusHandle(v) {
      this.activityguesttripupdatestatusVisible = true
      this.$nextTick(() => {
        this.$refs.activityguesttripupdatestatus.init(v)
      })
    },
    activityguesttripaddorupdateHandle(id,activityId,activityGuestId,type) {
      this.activityguesttripaddorupdateVisible = true
      this.$nextTick(() => {
        this.$refs.activityguesttripaddorupdate.init(id,activityId,activityGuestId,type)
      })
    },
    activityguesttripupdatetype(id) {
      this.activityguesttripupdatetypeVisible = true
      this.$nextTick(() => {
        this.$refs.activityguesttripupdatetype.init(id)
      })
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activityguest/exportplane?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "activityId=" + this.dataForm.activityId,
        "isFirstChar=" + this.activityInfo.isFirstChar
      ].join('&'));
      window.open(url);
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (
        columnIndex === 0
         || columnIndex === 1
         || columnIndex === 2
      ) {
        // 判断当前在哪个节点数据
        let number = 0;
        let index = 0;
        let flag = 0;
        for (var i = 0; i < this.dataMerge.length; i++) {
          if (rowIndex == number) {
            index = i;
            flag = 1;
            number += this.dataMerge[i];
            break;
          } else if (rowIndex < number) {
            index = i;
            flag = 0;
            break;
          }
          number += this.dataMerge[i];
        }
        console.log(rowIndex)
        if (flag == 1) {
          return [this.dataMerge[index], 1];
        } else {
          return [0, 0];
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>

.tablebig {
  height: 300px;
  /* 设置内容滚动区域的高度 */
  overflow-y: auto;
  /* 允许垂直滚动 */
  overflow-x: hidden;
  /* 禁止水平滚动 */

}

table {
  width: 100%;
  /* 表格宽度占据全部容器宽度 */
  border-collapse: collapse;
  /* 边框合并，使得相邻单元格之间没有空隙 */
  font-family: Arial, sans-serif;
  /* 设置字体样式 */
}

th {
  background-color: #f2f2f2;
  /* 表头背景颜色 */
  color: #333;
  position: -webkit-sticky;
  /* 对于 Safari */
  position: sticky;
  top: 0;
  background: #f1f1f1;
  z-index: 2;
  /* 保证表头在内容之上 */
  border: 1px solid #ddd;
  /* 设置单元格边框样式和颜色 */
  text-align: left;
  /* 文本对齐方式 */
  padding: 8px;
  /* 单元格内边距 */
}

td {
  border: 1px solid #ddd;
  /* 设置单元格边框样式和颜色 */
  text-align: right;
  /* 文本对齐方式 */
  padding: 8px;
  /* 单元格内边距 */
}

tr:nth-child(even) {
  background-color: #f9f9f9;
  /* 偶数行背景颜色 */
}

tr:hover {
  background-color: #e8e8e8;
  /* 鼠标悬停行的背景颜色 */
}</style>