<template>
    <div class="content">
        <div style="margin-top: 8px" class="nav-title">
            <div class="color"></div>
            <div class="text">绑定手机号</div>
        </div>

        <!-- 提示信息 -->
        <div class="tip-container">
            <van-notice-bar
                left-icon="info-o"
                text="为了更好地为您提供服务，请绑定您的手机号码"
                color="#1989fa"
                background="#ecf5ff"
            />
        </div>

        <van-cell-group inset>
            <van-field v-model="dataForm.mobile" name="手机号" label="手机号" required placeholder="手机号"
                :rules="[{ required: true, message: '请填写手机号' }]" />
            <!-- @input="verifyApplyByMobile"  -->
            <van-field v-model="dataForm.code" center clearable maxlength="6" label="短信验证码" required
                placeholder="请输入短信验证码">
                <template #button>
                    <van-button size="small" type="primary" :disabled="waiting" @click="doSendSmsCode()">
                        <span v-if="waiting">{{ waitingTime }}秒后重发</span>
                        <span v-else style="font-size: 13px">获取验证码</span>
                    </van-button></template>
            </van-field>
        </van-cell-group>

        <div style="margin: 16px">
            <van-button round block type="info" @click="onSubmit" :loading="loading"
                loading-text="提交中">提交</van-button>
        </div>
    </div>
</template>

<script>
import { isMobile } from "@/js/validate";
export default {
    data() {
        return {
            returnUrl: '',
            waitingTime: 60,
            waiting: false,
            isSend: false,
            loading: false,
            timer: null,
            userInfo: {},
            dataForm: {
                mobile: '',
                code: '',
            }
        }
    },
    mounted() {
        document.title = "报名信息填写";
        this.returnUrl = decodeURIComponent(this.$route.query.returnUrl);
        this.getUserInfo();
    },
    methods: {
        getUserInfo() {
            this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
                if (res.code == 200) {
                    this.userInfo = res.data;
                    this.dataForm.mobile = this.userInfo.mobile;
                } else {
                    vant.Toast(res.msg);
                }
            });
        },

        doSendSmsCode() {
            if (!this.dataForm.mobile) {
                vant.Toast("请输入手机号");
                return false;
            }
            if (!isMobile(this.dataForm.mobile)) {
                vant.Toast("手机号格式错误");
                return false;
            }
            this.$fly
                .post("/pyp/sms/sms/send", {
                    mobile: this.dataForm.mobile,
                })
                .then((res) => {
                    if (res && res.code === 200) {
                        this.isSend = true;
                        this.countdown();
                        vant.Toast("发送验证码成功");
                    } else {
                        vant.Toast(res.msg);
                    }
                });
        },
        countdown() {
            this.waiting = true
            this.timer = setInterval(() => {
                this.waitingTime--
                if (this.waitingTime < 0) {
                    clearInterval(this.timer)
                    this.waitingTime = 60
                    this.waiting = false
                }
            }, 1000)
        },
        onSubmit() {
            if (!this.dataForm.mobile) {
                vant.Toast("请输入手机号");
                return false;
            }
            if (!isMobile(this.dataForm.mobile)) {
                vant.Toast("手机号格式错误");
                return false;
            }
            if (!this.dataForm.code) {
                vant.Toast("请输入验证码");
                return false;
            }
            if (!/^\d{6}$/.test(this.dataForm.code)) {
                vant.Toast("验证码格式错误");
                return false;
            }
            this.loading = true;
            // 保存
            this.$fly
                .post(
                    "/pyp/sms/sms/verificationCodeAndBind",
                    this.dataForm
                )
                .then((res) => {
                    if (res && res.code === 200) {
                        vant.Toast.success("手机号绑定成功！");
                        // 延迟跳转，让用户看到成功提示
                        setTimeout(() => {
                            location.href = this.returnUrl;
                        }, 1000);
                    } else {
                        vant.Toast(res.msg);
                    }
                    this.loading = false;
                });
        }
    },
};
</script>



<style lang="less" scoped>
::v-deep .van-form {
    padding: 0 20rpx;
}


.bottom-botton {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-bottom: 50rpx;
    padding: 0 24rpx;
}

.nav-title {
    background-color: transparent;
}

.tip-container {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
}
</style>