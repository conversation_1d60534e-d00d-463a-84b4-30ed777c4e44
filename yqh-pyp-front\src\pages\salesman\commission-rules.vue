<template>
  <div class="commission-rules-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="我的抽成规则"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      <template #icon>
        <van-icon name="spinner" class="loading-icon" />
      </template>
      加载中...
    </van-loading>

    <!-- 内容区域 -->
    <div v-else class="content">
      <!-- 业务员信息卡片 -->
      <div class="salesman-info-card" v-if="salesmanInfo">
        <div class="info-header">
          <van-icon name="user-o" size="20" />
          <span class="title">业务员信息</span>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ salesmanInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">编号：</span>
            <span class="value">{{ salesmanInfo.code }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机：</span>
            <span class="value">{{ salesmanInfo.mobile }}</span>
          </div>
        </div>
      </div>

      <!-- 佣金规则列表 -->
      <div class="rules-section">
        <div class="section-header">
          <van-icon name="gold-coin-o" size="20" />
          <span class="title">抽成规则</span>
        </div>

        <div v-if="commissionRules.length === 0" class="empty-state">
          <van-empty description="暂无抽成规则" />
        </div>

        <div v-else class="rules-list">
          <div 
            v-for="rule in commissionRules" 
            :key="rule.id" 
            class="rule-card"
          >
            <div class="rule-header">
              <div class="rule-type">
                <van-tag :type="rule.status === 1 ? 'success' : 'danger'">
                  {{ rule.commissionTypeDesc }}
                </van-tag>
              </div>
              <div class="rule-status">
                <span :class="['status-text', rule.status === 1 ? 'active' : 'inactive']">
                  {{ rule.status === 1 ? '启用' : '禁用' }}
                </span>
              </div>
            </div>

            <div class="rule-content">
              <div class="rule-item">
                <span class="item-label">计算方式：</span>
                <span class="item-value">{{ rule.calculationTypeDesc }}</span>
              </div>
              
              <div class="rule-item" v-if="rule.calculationType === 1">
                <span class="item-label">抽成比例：</span>
                <span class="item-value highlight">{{ rule.commissionValue * 100 }}%</span>
              </div>
              
              <div class="rule-item" v-if="rule.calculationType === 2">
                <span class="item-label">固定金额：</span>
                <span class="item-value highlight">¥{{ rule.commissionValue }}</span>
              </div>

              <div class="rule-item" v-if="rule.minAmount">
                <span class="item-label">最低金额：</span>
                <span class="item-value">¥{{ rule.minAmount }}</span>
              </div>

              <div class="rule-item" v-if="rule.maxAmount">
                <span class="item-label">最高金额：</span>
                <span class="item-value">¥{{ rule.maxAmount }}</span>
              </div>

              <div class="rule-item" v-if="rule.remark">
                <span class="item-label">备注：</span>
                <span class="item-value">{{ rule.remark }}</span>
              </div>
            </div>

            <div class="rule-footer">
              <span class="create-time">创建时间：{{ rule.createTime }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 说明信息 -->
      <div class="notice-section">
        <van-notice-bar
          left-icon="info-o"
          text="抽成规则由管理员配置，如有疑问请联系管理员"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommissionRules',
  data() {
    return {
      loading: false,
      salesmanInfo: null,
      commissionRules: []
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadSalesmanInfo(),
          this.loadCommissionRules()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$toast('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载业务员信息
    async loadSalesmanInfo() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/checkSalesmanStatus')
        
        if (res.code === 200 && res.isSalesman) {
          this.salesmanInfo = res.salesman
        } else {
          this.$toast('获取业务员信息失败')
        }
      } catch (error) {
        console.error('获取业务员信息失败:', error)
      }
    },

    // 加载佣金规则
    async loadCommissionRules() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/getCommissionRules')
        
        if (res.code === 200) {
          this.commissionRules = res.result || []
        } else {
          console.log('获取佣金规则失败:', res.msg)
        }
      } catch (error) {
        console.error('获取佣金规则失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.commission-rules-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.loading-container {
  padding: 100px 0;
  text-align: center;
}

.loading-icon {
  font-size: 30px;
  animation: van-rotate 1s linear infinite;
}

.content {
  padding: 16px;
}

/* 业务员信息卡片 */
.salesman-info-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebedf0;
}

.info-header .title {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #969799;
  font-size: 14px;
  width: 60px;
}

.info-item .value {
  color: #323233;
  font-size: 14px;
  font-weight: 500;
}

/* 规则部分 */
.rules-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-header .title {
  margin-left: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 40px 16px;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 规则卡片 */
.rule-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.rule-type {
  flex: 1;
}

.rule-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-text.active {
  color: #07c160;
}

.status-text.inactive {
  color: #ee0a24;
}

.rule-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.rule-item {
  display: flex;
  align-items: center;
}

.item-label {
  color: #969799;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.item-value {
  color: #323233;
  font-size: 14px;
  flex: 1;
}

.item-value.highlight {
  color: #ff6b35;
  font-weight: 600;
  font-size: 16px;
}

.rule-footer {
  padding-top: 8px;
  border-top: 1px solid #ebedf0;
}

.create-time {
  color: #c8c9cc;
  font-size: 12px;
}

/* 说明信息 */
.notice-section {
  margin-bottom: 16px;
}
</style>
