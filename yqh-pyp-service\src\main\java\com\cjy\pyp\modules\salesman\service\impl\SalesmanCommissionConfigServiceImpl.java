package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.dao.SalesmanCommissionConfigDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionConfigService;
import com.github.pagehelper.PageHelper;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金配置服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Service("salesmanCommissionConfigService")
public class SalesmanCommissionConfigServiceImpl extends ServiceImpl<SalesmanCommissionConfigDao, SalesmanCommissionConfigEntity> 
        implements SalesmanCommissionConfigService {

    @Override
    public List<SalesmanCommissionConfigEntity> queryPage(Map<String, Object> params) {
        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page,limit);
        List<SalesmanCommissionConfigEntity> list = baseMapper.queryPage(params);
        
        
        return list;
    }

    @Override
    public SalesmanCommissionConfigEntity getByTypeAndSalesman(Long salesmanId, Integer commissionType, String appid) {
        return baseMapper.getByTypeAndSalesman(salesmanId, commissionType, appid);
    }

    @Override
    public List<SalesmanCommissionConfigEntity> getEffectiveConfigsBySalesman(Long salesmanId, String appid) {
        return baseMapper.getEffectiveConfigsBySalesman(salesmanId, appid);
    }

    @Override
    public List<SalesmanCommissionConfigEntity> getBatchConfigsBySalesmen(List<Long> salesmanIds, String appid) {
        return baseMapper.getBatchConfigsBySalesmen(salesmanIds, appid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateConfig(SalesmanCommissionConfigEntity config) {
        if (config.getId() != null) {
            // 编辑模式：检查是否与其他配置冲突
            if (existsConfig(config.getSalesmanId(), config.getCommissionType(),
                           config.getAppid(), config.getId())) {
                throw new RuntimeException("该业务员的此类型佣金配置已存在");
            }
            return this.updateById(config);
        } else {
            // 新增模式：检查是否已存在相同类型的配置
            if (existsConfig(config.getSalesmanId(), config.getCommissionType(),
                           config.getAppid(), null)) {
                throw new RuntimeException("该业务员的此类型佣金配置已存在，请直接编辑现有配置");
            }
            return this.save(config);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveConfigs(List<SalesmanCommissionConfigEntity> configs) {
        for (SalesmanCommissionConfigEntity config : configs) {
            if (!saveOrUpdateConfig(config)) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfigsBySalesman(Long salesmanId, String appid) {
        QueryWrapper<SalesmanCommissionConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("salesman_id", salesmanId)
               .eq("appid", appid);
        return this.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyConfigs(Long fromSalesmanId, Long toSalesmanId, String appid) {
        // 获取源业务员的配置
        List<SalesmanCommissionConfigEntity> sourceConfigs = getEffectiveConfigsBySalesman(fromSalesmanId, appid);
        
        if (sourceConfigs.isEmpty()) {
            return true;
        }
        
        // 删除目标业务员的现有配置
        deleteConfigsBySalesman(toSalesmanId, appid);
        
        // 复制配置
        for (SalesmanCommissionConfigEntity sourceConfig : sourceConfigs) {
            SalesmanCommissionConfigEntity newConfig = new SalesmanCommissionConfigEntity();
            newConfig.setSalesmanId(toSalesmanId);
            newConfig.setCommissionType(sourceConfig.getCommissionType());
            newConfig.setCalculationType(sourceConfig.getCalculationType());
            newConfig.setCommissionValue(sourceConfig.getCommissionValue());
            newConfig.setMinAmount(sourceConfig.getMinAmount());
            newConfig.setMaxAmount(sourceConfig.getMaxAmount());
            newConfig.setStatus(sourceConfig.getStatus());
            newConfig.setEffectiveDate(sourceConfig.getEffectiveDate());
            newConfig.setExpiryDate(sourceConfig.getExpiryDate());
            newConfig.setRemarks("从业务员" + fromSalesmanId + "复制");
            newConfig.setAppid(appid);
            
            if (!this.save(newConfig)) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    public boolean existsConfig(Long salesmanId, Integer commissionType, String appid, Long excludeId) {
        QueryWrapper<SalesmanCommissionConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("salesman_id", salesmanId)
               .eq("commission_type", commissionType)
               .eq("appid", appid);

        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }

        return this.count(wrapper) > 0;
    }
}
