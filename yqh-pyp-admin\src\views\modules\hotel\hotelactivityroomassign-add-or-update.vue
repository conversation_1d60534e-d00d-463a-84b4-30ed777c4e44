<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议酒店房型id" prop="hotelActivityRoomId">
      <el-input v-model="dataForm.hotelActivityRoomId" placeholder="会议酒店房型id"></el-input>
    </el-form-item>
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="酒店id" prop="hotelId">
      <el-input v-model="dataForm.hotelId" placeholder="酒店id"></el-input>
    </el-form-item>
    <el-form-item label="会议酒店id" prop="hotelActivityId">
      <el-input v-model="dataForm.hotelActivityId" placeholder="会议酒店id"></el-input>
    </el-form-item>
    <el-form-item label="房号" prop="number">
      <el-input v-model="dataForm.number" placeholder="房号"></el-input>
    </el-form-item>
    <el-form-item label="是否分配" prop="isAssign">
      <el-input v-model="dataForm.isAssign" placeholder="是否分配"></el-input>
    </el-form-item>
    <el-form-item label="酒店订单ID" prop="hotelOrderId">
      <el-input v-model="dataForm.hotelOrderId" placeholder="酒店订单ID"></el-input>
    </el-form-item>
    <el-form-item label="酒店订单详情ID" prop="hotelOrderDetailId">
      <el-input v-model="dataForm.hotelOrderDetailId" placeholder="酒店订单详情ID"></el-input>
    </el-form-item>
    <el-form-item label="房号ID" prop="hotelActivityRoomNumberId">
      <el-input v-model="dataForm.hotelActivityRoomNumberId" placeholder="房号ID"></el-input>
    </el-form-item>
    <el-form-item label="入住状态" prop="status">
      <el-input v-model="dataForm.status" placeholder="入住状态"></el-input>
    </el-form-item>
    <el-form-item label="房间类型：0-整间，1-男床位，2-女床位" prop="roomType">
      <el-input v-model="dataForm.roomType" placeholder="房间类型：0-整间，1-男床位，2-女床位"></el-input>
    </el-form-item>
    <el-form-item label="入住日期" prop="inDate">
      <el-input v-model="dataForm.inDate" placeholder="入住日期"></el-input>
    </el-form-item>
    <el-form-item label="退房日期" prop="outDate">
      <el-input v-model="dataForm.outDate" placeholder="退房日期"></el-input>
    </el-form-item>
    <el-form-item label="总天数" prop="dayNumber">
      <el-input v-model="dataForm.dayNumber" placeholder="总天数"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          hotelActivityRoomId: '',
          activityId: '',
          hotelId: '',
          hotelActivityId: '',
          number: '',
          isAssign: '',
          hotelOrderId: '',
          hotelOrderDetailId: '',
          hotelActivityRoomNumberId: '',
          status: '',
          roomType: '',
          inDate: '',
          outDate: '',
          dayNumber: ''
        },
        dataRule: {
          hotelActivityRoomId: [
            { required: true, message: '会议酒店房型id不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          hotelId: [
            { required: true, message: '酒店id不能为空', trigger: 'blur' }
          ],
          hotelActivityId: [
            { required: true, message: '会议酒店id不能为空', trigger: 'blur' }
          ],
          number: [
            { required: true, message: '房号不能为空', trigger: 'blur' }
          ],
          isAssign: [
            { required: true, message: '是否分配不能为空', trigger: 'blur' }
          ],
          hotelOrderId: [
            { required: true, message: '酒店订单ID不能为空', trigger: 'blur' }
          ],
          hotelOrderDetailId: [
            { required: true, message: '酒店订单详情ID不能为空', trigger: 'blur' }
          ],
          hotelActivityRoomNumberId: [
            { required: true, message: '房号ID不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '入住状态不能为空', trigger: 'blur' }
          ],
          roomType: [
            { required: true, message: '房间类型：0-整间，1-男床位，2-女床位不能为空', trigger: 'blur' }
          ],
          inDate: [
            { required: true, message: '入住日期不能为空', trigger: 'blur' }
          ],
          outDate: [
            { required: true, message: '退房日期不能为空', trigger: 'blur' }
          ],
          dayNumber: [
            { required: true, message: '总天数不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.createOn = data.hotelActivityRoomAssign.createOn
                this.dataForm.createBy = data.hotelActivityRoomAssign.createBy
                this.dataForm.updateOn = data.hotelActivityRoomAssign.updateOn
                this.dataForm.updateBy = data.hotelActivityRoomAssign.updateBy
                this.dataForm.hotelActivityRoomId = data.hotelActivityRoomAssign.hotelActivityRoomId
                this.dataForm.activityId = data.hotelActivityRoomAssign.activityId
                this.dataForm.hotelId = data.hotelActivityRoomAssign.hotelId
                this.dataForm.hotelActivityId = data.hotelActivityRoomAssign.hotelActivityId
                this.dataForm.number = data.hotelActivityRoomAssign.number
                this.dataForm.isAssign = data.hotelActivityRoomAssign.isAssign
                this.dataForm.hotelOrderId = data.hotelActivityRoomAssign.hotelOrderId
                this.dataForm.hotelOrderDetailId = data.hotelActivityRoomAssign.hotelOrderDetailId
                this.dataForm.hotelActivityRoomNumberId = data.hotelActivityRoomAssign.hotelActivityRoomNumberId
                this.dataForm.status = data.hotelActivityRoomAssign.status
                this.dataForm.roomType = data.hotelActivityRoomAssign.roomType
                this.dataForm.inDate = data.hotelActivityRoomAssign.inDate
                this.dataForm.outDate = data.hotelActivityRoomAssign.outDate
                this.dataForm.dayNumber = data.hotelActivityRoomAssign.dayNumber
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'hotelActivityId': this.dataForm.hotelActivityId,
                'number': this.dataForm.number,
                'isAssign': this.dataForm.isAssign,
                'hotelOrderId': this.dataForm.hotelOrderId,
                'hotelOrderDetailId': this.dataForm.hotelOrderDetailId,
                'hotelActivityRoomNumberId': this.dataForm.hotelActivityRoomNumberId,
                'status': this.dataForm.status,
                'roomType': this.dataForm.roomType,
                'inDate': this.dataForm.inDate,
                'outDate': this.dataForm.outDate,
                'dayNumber': this.dataForm.dayNumber
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
