package com.cjy.pyp.modules.salesman.utils;

import com.cjy.pyp.modules.salesman.enums.CalculationTypeEnum;
import com.cjy.pyp.modules.salesman.enums.CommissionTypeEnum;
import com.cjy.pyp.modules.salesman.enums.SettlementStatusEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 佣金枚举工具类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public class CommissionEnumUtils {

    /**
     * 获取佣金类型选项
     * @return 佣金类型选项列表
     */
    public static List<Map<String, Object>> getCommissionTypeOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        for (CommissionTypeEnum typeEnum : CommissionTypeEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("code", typeEnum.getCode());
            option.put("desc", typeEnum.getDesc());
            options.add(option);
        }
        
        return options;
    }

    /**
     * 获取计算方式选项
     * @return 计算方式选项列表
     */
    public static List<Map<String, Object>> getCalculationTypeOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        for (CalculationTypeEnum typeEnum : CalculationTypeEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("code", typeEnum.getCode());
            option.put("desc", typeEnum.getDesc());
            options.add(option);
        }
        
        return options;
    }

    /**
     * 获取结算状态选项
     * @return 结算状态选项列表
     */
    public static List<Map<String, Object>> getSettlementStatusOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        
        for (SettlementStatusEnum statusEnum : SettlementStatusEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("code", statusEnum.getCode());
            option.put("desc", statusEnum.getDesc());
            options.add(option);
        }
        
        return options;
    }

    /**
     * 获取支持指定佣金类型的计算方式选项
     * @param commissionType 佣金类型
     * @return 计算方式选项列表
     */
    public static List<Map<String, Object>> getCalculationTypeOptionsByCommissionType(Integer commissionType) {
        List<Map<String, Object>> options = new ArrayList<>();
        
        CommissionTypeEnum commissionTypeEnum = CommissionTypeEnum.getByCode(commissionType);
        if (commissionTypeEnum == null) {
            return options;
        }
        
        for (CalculationTypeEnum typeEnum : CalculationTypeEnum.values()) {
            // 用户转发佣金只支持固定金额
            if (commissionTypeEnum == CommissionTypeEnum.USER_FORWARD && 
                typeEnum != CalculationTypeEnum.FIXED_AMOUNT) {
                continue;
            }
            
            Map<String, Object> option = new HashMap<>();
            option.put("code", typeEnum.getCode());
            option.put("desc", typeEnum.getDesc());
            options.add(option);
        }
        
        return options;
    }

    /**
     * 通用枚举选项构建方法
     * @param enumValues 枚举值数组
     * @param codeGetter 获取code的方法
     * @param descGetter 获取desc的方法
     * @param <T> 枚举类型
     * @return 选项列表
     */
    public static <T> List<Map<String, Object>> buildEnumOptions(T[] enumValues, 
                                                                java.util.function.Function<T, Object> codeGetter,
                                                                java.util.function.Function<T, String> descGetter) {
        List<Map<String, Object>> options = new ArrayList<>();
        
        for (T enumValue : enumValues) {
            Map<String, Object> option = new HashMap<>();
            option.put("code", codeGetter.apply(enumValue));
            option.put("desc", descGetter.apply(enumValue));
            options.add(option);
        }
        
        return options;
    }
}
