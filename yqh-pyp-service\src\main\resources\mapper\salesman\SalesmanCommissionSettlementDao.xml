<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.SalesmanCommissionSettlementDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity" id="salesmanCommissionSettlementMap">
        <result property="id" column="id"/>
        <result property="batchNo" column="batch_no"/>
        <result property="settlementDate" column="settlement_date"/>
        <result property="salesmanCount" column="salesman_count"/>
        <result property="recordCount" column="record_count"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="status" column="status"/>
        <result property="settlementType" column="settlement_type"/>
        <result property="settlementTime" column="settlement_time"/>
        <result property="remarks" column="remarks"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="queryPage" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity">
        SELECT 
            s.*,
            CASE s.status
                WHEN 0 THEN '待结算'
                WHEN 1 THEN '已结算'
                WHEN 2 THEN '已取消'
                ELSE '未知'
            END as statusDesc,
            CASE s.settlement_type
                WHEN 1 THEN '手动结算'
                WHEN 2 THEN '自动结算'
                ELSE '未知'
            END as settlementTypeDesc
        FROM salesman_commission_settlement s
        WHERE 1=1
        <if test="batchNo != null and batchNo != ''">
            AND s.batch_no LIKE CONCAT('%', #{batchNo}, '%')
        </if>
        <if test="status != null">
            AND s.status = #{status}
        </if>
        <if test="settlementType != null">
            AND s.settlement_type = #{settlementType}
        </if>
        <if test="startDate != null and startDate != ''">
            AND s.settlement_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND s.settlement_date &lt;= #{endDate}
        </if>
        <if test="appid != null and appid != ''">
            AND s.appid = #{appid}
        </if>
        ORDER BY s.create_on DESC
    </select>

    <select id="getByBatchNo" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity">
        SELECT * FROM salesman_commission_settlement 
        WHERE batch_no = #{batchNo} 
        AND appid = #{appid}
        LIMIT 1
    </select>

    <select id="getSettlementStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalBatches,
            COALESCE(SUM(total_amount), 0) as totalAmount,
            COALESCE(SUM(salesman_count), 0) as totalSalesmanCount,
            COALESCE(SUM(record_count), 0) as totalRecordCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as pendingBatches,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedBatches,
            COUNT(CASE WHEN status = 2 THEN 1 END) as cancelledBatches
        FROM salesman_commission_settlement 
        WHERE 1=1
        <if test="startDate != null and startDate != ''">
            AND settlement_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND settlement_date &lt;= #{endDate}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
    </select>

</mapper>
