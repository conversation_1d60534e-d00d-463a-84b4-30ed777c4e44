package com.cjy.pyp.modules.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.channel.entity.ChannelRefundQuotaRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * 渠道退款名额使用记录服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
public interface ChannelRefundQuotaRecordService extends IService<ChannelRefundQuotaRecordEntity> {

    /**
     * 分页查询退款名额使用记录
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 获取渠道当前已分配的退款权限数量
     */
    Integer getChannelAssignedQuotaCount(Long channelId);

    /**
     * 获取渠道内订单的退款权限排序
     */
    Integer getOrderQuotaSequence(Long channelId, Long orderId);

    /**
     * 获取渠道内按时间排序的已支付订单列表（用于权限分配）
     */
    List<Map<String, Object>> getChannelPaidOrdersForQuotaAssignment(Long channelId);

    /**
     * 批量插入退款名额使用记录
     */
    boolean batchInsert(List<ChannelRefundQuotaRecordEntity> records);

    /**
     * 删除渠道的所有退款名额记录（重新分配时使用）
     */
    boolean deleteByChannelId(Long channelId);

    /**
     * 获取渠道退款名额统计信息
     */
    Map<String, Object> getChannelRefundQuotaStats(Long channelId);

    /**
     * 查询渠道内具有退款权限的订单列表
     */
    List<Map<String, Object>> getChannelEligibleOrders(Long channelId);

    /**
     * 记录退款权限分配
     */
    void recordQuotaAssignment(Long channelId, Long orderId, String orderSn, Integer quotaSequence, String remarks);

    /**
     * 记录退款权限释放
     */
    void recordQuotaRelease(Long channelId, Long orderId, String orderSn, String remarks);
}
