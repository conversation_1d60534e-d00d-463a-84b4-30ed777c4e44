<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="库存时间" prop="stockDate">
        <el-date-picker disabled v-model="dataForm.stockDate" style="windth: 100%" type="date" value-format="yyyy/MM/dd" placeholder="库存时间"></el-date-picker>
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input v-model="dataForm.price" placeholder="库存时间对应价格">
            <template slot="append">
              RMB/间
            </template>
        </el-input>
      </el-form-item>
      <el-form-item label="总库存" prop="number">
        <el-input v-model="dataForm.number" placeholder="总库存">
            <template slot="append">
              间
            </template>
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
  </el-dialog>
</template>

<script>
import {  isDecimalByTwo,isInteger } from '@/utils/validate'
  export default {
    data() {
      var VisInteger = (rule, value, callback) => {
        if (!isInteger(value)) {
          callback(new Error("必须为数字"));
        } else {
          callback();
        }
      };
      var VisDecimalByTwo = (rule, value, callback) => {
        if (!isDecimalByTwo(value)) {
          callback(new Error("必须为数字"));
        } else {
          callback();
        }
      };
      return {
        visible: false,
        hotelActivityRoom: {},
        dataForm: {
          id: 0,
          hotelActivityRoomId: '',
          activityId: '',
          hotelId: '',
          hotelActivityId: '',
          stockDate: '',
          price: '',
          bedPrice: '',
          number: 0,
          alreadyNumber: 0,
          spareNumber: 0,
        },
        dataRule: {
          stockDate: [{
            required: true,
            message: '库存时间不能为空',
            trigger: 'blur'
          }],
          price: [{
            required: true,
            message: '库存时间对应价格不能为空',
            trigger: 'blur'
          },
            {
              validator: VisDecimalByTwo,
              trigger: 'blur'
            }],
          number: [{
            required: true,
            message: '总库存不能为空',
            trigger: 'blur'
          },
            {
              validator: VisInteger,
              trigger: 'blur'
            }],
        }
      }
    },
    methods: {
      init(id, roomId,stockDate) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomstock/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.dataForm.hotelActivityRoomId = data.hotelActivityRoomStock.hotelActivityRoomId
                this.dataForm.activityId = data.hotelActivityRoomStock.activityId
                this.dataForm.hotelId = data.hotelActivityRoomStock.hotelId
                this.dataForm.hotelActivityId = data.hotelActivityRoomStock.hotelActivityId
                this.dataForm.stockDate = data.hotelActivityRoomStock.stockDate
                this.dataForm.price = data.hotelActivityRoomStock.price
                this.dataForm.bedPrice = data.hotelActivityRoomStock.bedPrice
                this.dataForm.number = data.hotelActivityRoomStock.number
                this.dataForm.alreadyNumber = data.hotelActivityRoomStock.alreadyNumber
                this.dataForm.spareNumber = data.hotelActivityRoomStock.spareNumber
                this.dataForm.createOn = data.hotelActivityRoomStock.createOn
                this.dataForm.createBy = data.hotelActivityRoomStock.createBy
                this.dataForm.updateOn = data.hotelActivityRoomStock.updateOn
                this.dataForm.updateBy = data.hotelActivityRoomStock.updateBy
              }
            })
          } else {
            this.dataForm.hotelActivityRoomId = roomId;
            this.dataForm.stockDate = stockDate;
            this.getRoomInfo();
          }
        })
      },
      getRoomInfo() {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivityroom/info/${this.dataForm.hotelActivityRoomId}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.hotelActivityRoom = data.hotelActivityRoom;
            this.dataForm.activityId = data.hotelActivityRoom.activityId
            this.dataForm.hotelId = data.hotelActivityRoom.hotelId
            this.dataForm.hotelActivityId = data.hotelActivityRoom.hotelActivityId
            this.dataForm.price = data.hotelActivityRoom.price
            this.dataForm.bedPrice = data.hotelActivityRoom.bedPrice
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomstock/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'hotelActivityId': this.dataForm.hotelActivityId,
                'stockDate': this.dataForm.stockDate,
                'price': this.dataForm.price,
                'bedPrice': this.dataForm.bedPrice,
                'number': this.dataForm.number,
                'alreadyNumber': this.dataForm.alreadyNumber,
                'spareNumber': this.dataForm.spareNumber,
              })
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
