package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 图片平台使用记录实体
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-29
 */
@Data
@TableName("activity_image_platform_usage")
public class ActivityImagePlatformUsageEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 活动ID
     */
    private Long activityId;
    
    /**
     * 图片ID
     */
    private Long imageId;
    
    /**
     * 平台类型（douyin, xiaohongshu, dianping等）
     */
    private String platform;
    
    /**
     * 该平台的使用次数
     */
    private Integer useCount;
    
    /**
     * 首次使用时间
     */
    private Date firstUsedTime;
    
    /**
     * 最后使用时间
     */
    private Date lastUsedTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
