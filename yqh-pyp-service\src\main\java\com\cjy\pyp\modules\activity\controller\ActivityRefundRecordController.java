package com.cjy.pyp.modules.activity.controller;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRefundRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRefundRecordService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 退款记录表
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@RestController
@RequestMapping("activity/refundrecord")
public class ActivityRefundRecordController {
    @Autowired
    private ActivityRefundRecordService activityRefundRecordService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:refundrecord:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = activityRefundRecordService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:refundrecord:info")
    public R info(@PathVariable("id") Long id){
		ActivityRefundRecordEntity activityRefundRecord = activityRefundRecordService.getById(id);

        return R.ok().put("activityRefundRecord", activityRefundRecord);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:refundrecord:save")
    public R save(@RequestBody ActivityRefundRecordEntity activityRefundRecord){
		activityRefundRecordService.save(activityRefundRecord);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:refundrecord:update")
    public R update(@RequestBody ActivityRefundRecordEntity activityRefundRecord){
		activityRefundRecordService.updateById(activityRefundRecord);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:refundrecord:delete")
    public R delete(@RequestBody Long[] ids){
		activityRefundRecordService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

}
