package com.cjy.pyp.modules.groupbuying.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingCouponEntity;

import java.util.List;
import java.util.Map;

/**
 * 团购券服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
public interface GroupBuyingCouponService extends IService<GroupBuyingCouponEntity> {

    /**
     * 分页查询团购券列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 根据活动ID获取团购券列表
     *
     * @param activityId 活动ID
     * @return 团购券列表
     */
    List<GroupBuyingCouponEntity> getByActivityId(Long activityId);

    /**
     * 根据活动ID和平台类型获取团购券列表
     *
     * @param activityId 活动ID
     * @param platformType 平台类型
     * @return 团购券列表
     */
    List<GroupBuyingCouponEntity> getByActivityIdAndPlatform(Long activityId, String platformType);

    /**
     * 获取活动的有效团购券（上架且在有效期内）
     *
     * @param activityId 活动ID
     * @return 有效团购券列表
     */
    List<GroupBuyingCouponEntity> getValidCoupons(Long activityId);

    /**
     * 更新团购券销量
     *
     * @param couponId 团购券ID
     * @param soldCount 销量增量
     * @return 是否更新成功
     */
    boolean updateSoldCount(Long couponId, Integer soldCount);
}
