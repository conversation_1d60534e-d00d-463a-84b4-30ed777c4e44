<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="是否出票" prop="isBuy">
            <el-select v-model="dataForm.isBuy" placeholder="是否出票" filterable>
              <el-option v-for="item in isBuy" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
            <el-select v-if="dataForm.inType == 0" v-model="dataForm.orderStatus" placeholder="订单状态" filterable>
              <el-option v-for="item in tripPlaneStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
            <el-select v-else-if="dataForm.inType == 1" v-model="dataForm.orderStatus" placeholder="订单状态" filterable>
              <el-option v-for="item in tripTrainStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input v-model="dataForm.price" placeholder="价格"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {yesOrNo} from "@/data/common"
import { guestGoType,isBuy,tripPlaneStatus,tripTrainStatus } from '@/data/activity'
export default {
  data() {
    return {
      isBuy,tripPlaneStatus,tripTrainStatus,
      times: [],
      yesOrNo,
      guestGoType,
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        isBuy: 0,
        inType: 0,
        orderStatus: 0,
        price: 0
      },
      dataRule: {
        isBuy: [
          { required: true, message: '是否购买不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
      dateChange(v) {
        this.dataForm.inStartDate = v[0];
        this.dataForm.inEndDate = v[1];
      },
    init(id,activityId,activityGuestId) {
      this.getToken();
      this.dataForm.id = id || 0
      this.dataForm.activityGuestId = activityGuestId || 0
      this.dataForm.activityId = activityId || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityguesttrip/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.isBuy = data.activityGuestTrip.isBuy
              this.dataForm.price = data.activityGuestTrip.price
              this.dataForm.orderStatus = data.activityGuestTrip.orderStatus
              this.dataForm.inType = data.activityGuestTrip.inType
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityguesttrip/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'isBuy': this.dataForm.isBuy,
              'orderStatus': this.dataForm.orderStatus,
              'price': this.dataForm.price
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
