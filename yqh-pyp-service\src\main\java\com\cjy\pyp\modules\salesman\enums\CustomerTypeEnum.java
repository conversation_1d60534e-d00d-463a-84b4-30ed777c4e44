package com.cjy.pyp.modules.salesman.enums;

/**
 * 客户类型枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public enum CustomerTypeEnum {
    
    /**
     * 微信用户
     */
    WX_USER(1, "微信用户"),
    
    /**
     * 系统用户
     */
    SYS_USER(2, "系统用户");
    
    private final Integer code;
    private final String desc;
    
    CustomerTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static CustomerTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        CustomerTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : "未知";
    }
}
