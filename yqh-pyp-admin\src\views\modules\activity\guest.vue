<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:guest:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:guest:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button @click="downloadDemo()" type="success">导入模板</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/activity/guest/importExcel?appid=' + appid" :name="'嘉宾导入'"></Upload>
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="联系人姓名">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系人电话">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="工作单位">
      </el-table-column>
      <el-table-column prop="duties" header-align="center" align="center" label="职称">
      </el-table-column>
      <el-table-column prop="idCard" header-align="center" align="center" label="身份证">
      </el-table-column>
      <el-table-column prop="idCardZheng" header-align="center" align="center" label="身份证正面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardZheng)" :src="scope.row.idCardZheng" />
          <a :href="scope.row.idCardZheng" target="_blank" v-else>{{ scope.row.idCardZheng }}</a>
        </div>
      </el-table-column>
      <el-table-column prop="idCardFan" header-align="center" align="center" label="身份证反面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardFan)" :src="scope.row.idCardFan" />
          <a :href="scope.row.idCardFan" target="_blank" v-else>{{ scope.row.idCardFan }}</a>
        </div>
      </el-table-column>
      <el-table-column prop="bank" header-align="center" align="center" label="银行卡号">
      </el-table-column>
      <el-table-column prop="kaihuhang" header-align="center" align="center" label="开户行">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './guest-add-or-update'
export default {
  data() {
    return {
      appid: '',
      dataForm: {
        name: '',
        mobile: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload: () => import('@/components/upload'),
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/guest/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'appid': this.appid
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/guest/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    downloadDemo() {
      var url = this.$http.adornUrl("/activity/guest/downloadDemo?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    }
  }
}
</script>
