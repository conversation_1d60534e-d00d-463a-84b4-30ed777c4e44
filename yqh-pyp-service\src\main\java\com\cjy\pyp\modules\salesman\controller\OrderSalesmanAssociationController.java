package com.cjy.pyp.modules.salesman.controller;


import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.salesman.service.OrderSalesmanAssociationService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订单业务员关联管理控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("salesman/orderassociation")
@Api(tags = "订单业务员关联管理")
public class OrderSalesmanAssociationController extends AbstractController {

    @Autowired
    private OrderSalesmanAssociationService orderSalesmanAssociationService;

    /**
     * 获取订单列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:orderassociation:list")
    @ApiOperation(value = "订单列表", notes = "获取订单业务员关联列表")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<ActivityRechargeRecordEntity> page = orderSalesmanAssociationService.queryOrderList(params);
        return R.okList( page);
    }

    /**
     * 获取关联统计
     */
    @RequestMapping("/stats")
    @RequiresPermissions("salesman:orderassociation:list")
    @ApiOperation(value = "关联统计", notes = "获取订单业务员关联统计数据")
    public R getStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        Map<String, Object> stats = orderSalesmanAssociationService.getAssociationStats(params);
        return R.ok().put("stats", stats);
    }

    /**
     * 手动为订单关联业务员
     */
    @RequestMapping("/associate")
    @RequiresPermissions("salesman:orderassociation:associate")
    @ApiOperation(value = "关联业务员", notes = "手动为充值订单关联业务员")
    public R associateSalesman(@RequestParam Long rechargeRecordId,
                              @RequestParam Long salesmanId,
                              @CookieValue String appid) {
        try {
            boolean success = orderSalesmanAssociationService.manualAssociateSalesman(
                    rechargeRecordId, salesmanId, appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("关联业务员失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 更换订单的业务员关联
     */
    @RequestMapping("/change")
    @RequiresPermissions("salesman:orderassociation:associate")
    @ApiOperation(value = "更换业务员", notes = "更换订单关联的业务员")
    public R changeSalesman(@RequestParam Long rechargeRecordId,
                           @RequestParam Long salesmanId,
                           @RequestParam(required = false) String reason,
                           @CookieValue String appid) {
        try {
            boolean success = orderSalesmanAssociationService.changeSalesman(
                    rechargeRecordId, salesmanId, reason, appid);
            if (success) {
                return R.ok().put("message", "更换业务员成功");
            } else {
                return R.error("更换业务员失败");
            }
        } catch (Exception e) {
            return R.error("更换业务员失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单的业务员关联
     */
    @RequestMapping("/disassociate")
    @RequiresPermissions("salesman:orderassociation:disassociate")
    @ApiOperation(value = "取消关联", notes = "取消订单的业务员关联")
    public R disassociateSalesman(@RequestParam Long rechargeRecordId,
                                 @CookieValue String appid) {
        try {
            boolean success = orderSalesmanAssociationService.disassociateSalesman(rechargeRecordId, appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("取消关联失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 根据用户查找关联的业务员
     */
    @RequestMapping("/findSalesmanByUser")
    @RequiresPermissions("salesman:orderassociation:info")
    @ApiOperation(value = "查找用户业务员", notes = "根据用户ID查找关联的业务员")
    public R findSalesmanByUser(@RequestParam Long userId, @CookieValue String appid) {
        Long salesmanId = orderSalesmanAssociationService.findSalesmanByUser(userId, appid);
        return R.ok().put("salesmanId", salesmanId);
    }

    /**
     * 批量为历史订单关联业务员
     */
    @RequestMapping("/batchAssociateHistorical")
    @RequiresPermissions("salesman:orderassociation:batch")
    @ApiOperation(value = "批量关联历史订单", notes = "根据当前绑定关系为历史订单补充业务员信息")
    public R batchAssociateHistoricalOrders(@CookieValue String appid) {
        try {
            Integer associatedCount = orderSalesmanAssociationService.batchAssociateHistoricalOrders(appid);
            return R.ok()
                    .put("associatedCount", associatedCount)
                    .put("message", "成功关联 " + associatedCount + " 个历史订单");
        } catch (Exception e) {
            return R.error("批量关联失败：" + e.getMessage());
        }
    }

    /**
     * 验证业务员关联的有效性
     */
    @RequestMapping("/validate")
    @RequiresPermissions("salesman:orderassociation:info")
    @ApiOperation(value = "验证关联有效性", notes = "验证订单关联的业务员是否与用户当前绑定一致")
    public R validateSalesmanAssociation(@RequestParam Long rechargeRecordId,
                                        @CookieValue String appid) {
        String validationResult = orderSalesmanAssociationService.validateSalesmanAssociation(rechargeRecordId, appid);
        
        if (validationResult == null) {
            return R.ok().put("valid", true).put("message", "关联有效");
        } else {
            return R.ok().put("valid", false).put("message", validationResult);
        }
    }

    /**
     * 为单个订单自动关联业务员
     */
    @RequestMapping("/autoAssociate")
    @RequiresPermissions("salesman:orderassociation:associate")
    @ApiOperation(value = "自动关联业务员", notes = "为单个充值订单自动关联业务员")
    public R autoAssociateSalesman(@RequestParam Long rechargeRecordId,
                                  @RequestParam Long userId,
                                  @CookieValue String appid) {
        try {
            Long salesmanId = orderSalesmanAssociationService.associateSalesmanForRechargeOrder(
                    rechargeRecordId, userId, appid);
            
            if (salesmanId != null) {
                return R.ok()
                        .put("salesmanId", salesmanId)
                        .put("message", "成功关联业务员");
            } else {
                return R.ok()
                        .put("salesmanId", null)
                        .put("message", "用户没有绑定业务员，无法关联");
            }
        } catch (Exception e) {
            return R.error("自动关联失败：" + e.getMessage());
        }
    }

    /**
     * 手动处理佣金调整
     */
    @RequestMapping("/adjustCommission")
    @RequiresPermissions("salesman:orderassociation:associate")
    @ApiOperation(value = "手动佣金调整", notes = "手动处理业务员关联变更后的佣金调整")
    public R adjustCommission(@RequestParam Long rechargeRecordId,
                             @RequestParam(required = false) Long oldSalesmanId,
                             @RequestParam(required = false) Long newSalesmanId,
                             @RequestParam(required = false) String reason,
                             @CookieValue String appid) {
        try {
            boolean success = orderSalesmanAssociationService.handleCommissionAdjustment(
                    rechargeRecordId, oldSalesmanId, newSalesmanId, reason, appid);

            if (success) {
                return R.ok().put("message", "佣金调整处理成功");
            } else {
                return R.error("佣金调整处理失败");
            }
        } catch (Exception e) {
            return R.error("佣金调整处理失败：" + e.getMessage());
        }
    }
}
