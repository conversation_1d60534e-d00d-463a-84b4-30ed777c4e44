<template>
    <div>
        <img v-if="isWeixin" style="width: 100%"
            src="http://conferencehuizhan.oss-cn-beijing.aliyuncs.com/20210601/2556ad23c7734aae8bc42e1ab2dd7d36.png"
            alt="微信打开" />
        <div v-else v-html="form"></div>
    </div>
</template>

<script>
import { isWeixin } from "@/js/validate"
export default {
    name: "openBrowse",
    data() {
        return {
            form: '',
            isWeixin: isWeixin()
        }
    },
    mounted() {
        if (!this.isWeixin) {
            document.forms[document.forms.length > 1 ? document.forms.length - 1 : 0].submit();
        }
    },
    created() {
        document.title = "请在浏览器中打开"
        this.form = decodeURIComponent(this.$route.query.form)
    }

}
</script>

<style scoped></style>
