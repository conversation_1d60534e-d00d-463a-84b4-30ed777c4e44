<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="供应商文件名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="供应商文件名称"></el-input>
      </el-form-item>
      <el-form-item label="文件地址" prop="url">
        <el-upload list-type="picture-card" :before-upload="checkFileSize" :on-success="appSuccessHandle"
          :file-list="appFileList" :action="url">
          <i slot="default" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}">
            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handleAppPictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-delete" @click="handleAppRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
        <el-dialog :visible.sync="imgAppDialogVisible">
          <img width="100%" :src="dialogAppImageUrl" alt="">
        </el-dialog>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      appFileList: [],
      url: '',
      imgAppDialogVisible: false,
      dialogAppImageUrl: '',
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        name: '',
        url: '',
        supplierId: '',
        appid: '',
        companyRoleId: ''
      },
      dataRule: {
        name: [
          { required: true, message: '供应商文件名称不能为空', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '文件地址不能为空', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '供应商名称不能为空', trigger: 'blur' }
        ],
        appid: [
          { required: true, message: '公司ID不能为空', trigger: 'blur' }
        ],
        companyRoleId: [
          { required: true, message: '公司权限ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
  init (id,supplierId) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
      this.appFileList = [];
      this.getToken();
      this.dataForm.id = id || 0
      this.dataForm.supplierId = supplierId
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/supplier/supplierthing/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.appFileList = data.supplierThing.appFileList
              this.dataForm.name = data.supplierThing.name
              this.dataForm.url = data.supplierThing.url
              this.dataForm.supplierId = data.supplierThing.supplierId
              this.dataForm.appid = data.supplierThing.appid
              this.dataForm.companyRoleId = data.supplierThing.companyRoleId
            }
          })
        } else {
          this.dataForm.appid = this.$cookie.get("appid");
          this.dataForm.companyRoleId = this.$cookie.get("companyRoleId");
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      // if (file.size / 1024 > 100) {
      //   // 100kb不压缩
      //   if (
      //     file.type == "image/jpg" ||
      //     file.type == "image/jpeg" ||
      //     file.type == "image/png" ||
      //     file.type == "image/gif"
      //   ) {
      //     return new Promise((resolve, reject) => {
      //       new Compressor(file, {
      //         quality: 0.3,
      //         success(result) {
      //           console.log(result)
      //           resolve(result)
      //         },
      //         error(e) {
      //           console.log(e)
      //         }
      //       })
      //     })
      //   }
      // }
      return true
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      this.appFileList = fileList;
      this.successNum++;
      if (response && response.code === 200) {
        if (!this.dataForm.url || this.dataForm.url.length == 0) {
          this.dataForm.url = response.url
        } else {
          this.dataForm.url += "," + response.url;
        }
      } else {
        this.$message.error(response.msg);
      }
    },
    handleAppPictureCardPreview(file) {
      this.dialogAppImageUrl = file.url;
      this.imgAppDialogVisible = true;
    },
    handleAppRemove(file) {
      this.dataForm.url = (',' + this.dataForm.url + ',').replace(',' + file.url + ',', ',').substr(1).replace(/,$/, '')
      this.appFileList.splice(this.appFileList.indexOf(file), 1)
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/supplier/supplierthing/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'url': this.dataForm.url,
              'supplierId': this.dataForm.supplierId,
              'appid': this.dataForm.appid,
              'companyRoleId': this.dataForm.companyRoleId
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
