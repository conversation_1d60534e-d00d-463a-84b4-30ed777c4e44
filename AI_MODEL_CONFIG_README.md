# AI模型配置系统

## 概述

本系统将原有的硬编码DeepSeek API配置改为数据库配置，支持多种AI模型的动态配置和管理，包括DeepSeek和Kimi模型。

## 主要功能

1. **数据库配置管理**: 所有AI模型配置存储在数据库中，支持动态修改
2. **多模型支持**: 支持DeepSeek和Kimi(Moonshot)模型
3. **统一接口**: 提供统一的AI服务接口，自动路由到对应的模型服务
4. **管理后台**: 提供完整的管理界面，支持模型的增删改查
5. **默认模型**: 支持设置默认模型，未指定模型时自动使用
6. **状态管理**: 支持启用/禁用模型
7. **连接测试**: 支持测试模型连接是否正常

## 数据库表结构

### ai_model_config 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| model_code | varchar(50) | 模型编码 |
| model_name | varchar(100) | 模型名称 |
| provider | varchar(50) | 服务提供商 |
| api_url | varchar(500) | API接口地址 |
| api_key | varchar(500) | API密钥 |
| max_tokens | int | 最大token数 |
| temperature | decimal(3,2) | 创造性参数 |
| timeout | int | 超时时间(毫秒) |
| max_retries | int | 最大重试次数 |
| retry_delay | int | 重试延迟(毫秒) |
| status | tinyint | 状态(0:禁用,1:启用) |
| is_default | tinyint | 是否默认模型 |
| sort_order | int | 排序 |
| config_json | text | 扩展配置 |
| remark | varchar(500) | 备注 |

## 使用方法

### 1. 管理后台配置

访问管理后台 -> AI模型配置，可以进行以下操作：

- **查看模型列表**: 显示所有配置的AI模型
- **新增模型**: 添加新的AI模型配置
- **修改模型**: 编辑现有模型配置
- **删除模型**: 删除不需要的模型配置
- **启用/禁用**: 控制模型的可用状态
- **设置默认**: 设置默认使用的模型
- **测试连接**: 测试模型API连接是否正常

### 2. 代码中使用

#### 使用统一AI服务接口

```java
@Autowired
private AiModelService aiModelService;

// 使用默认模型生成文本
String result = aiModelService.generateText("你的提示词");

// 使用指定模型生成文本
String result = aiModelService.generateText("你的提示词", "deepseek-chat");

// 获取可用模型列表
List<AiModelConfigEntity> models = aiModelService.getAvailableModels();

// 获取默认模型
AiModelConfigEntity defaultModel = aiModelService.getDefaultModel();

// 检查模型是否可用
boolean available = aiModelService.isModelAvailable("deepseek-chat");
```

#### Web API接口

```javascript
// 获取可用模型列表
GET /web/aimodel/available

// 获取默认模型
GET /web/aimodel/default
```

### 3. 预置模型配置

系统预置了以下模型配置：

#### DeepSeek模型
- **deepseek-chat**: DeepSeek对话模型 (默认)
- **deepseek-coder**: DeepSeek代码模型

#### Kimi模型
- **moonshot-v1-8k**: Kimi 8K上下文模型
- **moonshot-v1-32k**: Kimi 32K上下文模型
- **moonshot-v1-128k**: Kimi 128K上下文模型

## 配置说明

### DeepSeek配置
- **API地址**: https://api.deepseek.com
- **API密钥**: 需要在DeepSeek官网申请
- **模型编码**: deepseek-chat, deepseek-coder

### Kimi配置
- **API地址**: https://api.moonshot.cn
- **API密钥**: 需要在Moonshot官网申请
- **模型编码**: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k

## 迁移说明

### 从旧版本迁移

1. **数据库迁移**: 运行 `V1.0.11__create_ai_model_config.sql` 脚本
2. **配置迁移**: 原有的 `application.yml` 中的DeepSeek配置已迁移到数据库
3. **代码更新**: 原有使用 `DeepSeekApiService` 的代码已更新为使用 `AiModelService`

### 配置API密钥

1. 登录管理后台
2. 进入"AI模型配置"页面
3. 编辑对应的模型配置
4. 填入正确的API密钥
5. 启用模型并设置为默认(如需要)

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要泄露给他人
2. **模型限制**: 不同模型有不同的token限制和调用频率限制
3. **网络连接**: 确保服务器能够访问对应的API地址
4. **默认模型**: 系统必须至少有一个启用的默认模型
5. **测试连接**: 配置完成后建议先测试连接再正式使用

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查API密钥是否正确
   - 检查网络连接是否正常
   - 检查API地址是否正确

2. **生成文本失败**
   - 检查模型是否启用
   - 检查API密钥是否有效
   - 检查token限制是否超出

3. **找不到默认模型**
   - 确保至少有一个模型设置为默认
   - 确保默认模型处于启用状态

### 日志查看

系统会记录详细的日志信息，可以通过日志查看具体的错误原因：

```
2025-08-04 10:00:00 INFO  - 设置默认AI模型: deepseek-chat
2025-08-04 10:00:01 ERROR - DeepSeek模型连接测试失败: deepseek-chat
```

## 扩展开发

如需添加新的AI模型提供商，请按以下步骤：

1. 在 `AiModelServiceImpl` 中添加新的provider处理逻辑
2. 创建对应的模型服务类(参考 `DeepSeekModelService`)
3. 在数据库中添加新的模型配置记录
4. 更新管理后台的提供商选项

## 版本历史

- **v1.0.11**: 初始版本，支持DeepSeek和Kimi模型配置
