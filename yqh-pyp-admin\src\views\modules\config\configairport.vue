<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="关键词" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('config:configairport:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('config:configairport:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
          <el-table-column
        prop="createOn"
                :show-overflow-tooltip="true"
                header-align="center"
        align="center"
        label="创建时间">
      </el-table-column>
            <el-table-column
        prop="updateOn"
                :show-overflow-tooltip="true"
                header-align="center"
        align="center"
        label="更新时间">
      </el-table-column>
            <el-table-column
        prop="airportName"
                header-align="center"
        align="center"
        label="机场名称">
      </el-table-column>
          <el-table-column
        prop="airportShortName"
                header-align="center"
        align="center"
        label="机场简称">
      </el-table-column>
          <el-table-column
        prop="airportCode"
                header-align="center"
        align="center"
        label="机场三字码">
      </el-table-column>
          <el-table-column
        prop="cityName"
                header-align="center"
        align="center"
        label="城市名称">
      </el-table-column>
          <el-table-column
        prop="cityCode"
                header-align="center"
        align="center"
        label="城市三字码">
      </el-table-column>
          <el-table-column
        prop="cityPinYin"
                header-align="center"
        align="center"
        label="城市拼音">
      </el-table-column>
          <el-table-column
        prop="cityShortChar"
                header-align="center"
        align="center"
        label="城市拼音简称">
      </el-table-column>
          <el-table-column
        prop="countryName"
                header-align="center"
        align="center"
        label="所属国家名称">
      </el-table-column>
          <el-table-column
        prop="countryCode"
                header-align="center"
        align="center"
        label="所属国家代码">
      </el-table-column>
          <el-table-column
        prop="continent"
                header-align="center"
        align="center"
        label="所属洲">
      </el-table-column>
          <el-table-column
        prop="isInternal"
                header-align="center"
        align="center"
        label="是否国内（大陆）机场城市">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './configairport-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          name: '',
          appid: '',
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      onSearch() {
        this.pageIndex = 1;
        this.getDataList();
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/config/configairport/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'name': this.dataForm.name,
            'appid': this.$cookie.get('appid'),
          })
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/config/configairport/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
