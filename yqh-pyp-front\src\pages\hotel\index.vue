<template>
    <div>
        <van-search v-model="dataForm.name" placeholder="请输入您要搜索的酒店名称" show-action shape="round">
            <div slot="action" @click="onSearch" class="search-text">搜索</div>
        </van-search>
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">

            <div style="margin-top: 8px" class="nav-title">
                <div class="color"></div>
                <div class="text">酒店列表</div>
            </div>
            <van-card style="background: white" v-for="item in dataList" :key="item.id"
                :thumb="!item.hotelImg ? 'van-icon' : item.hotelImg">
                <div slot="title" style="font-size: 18px">{{ item.hotelName }}</div>
                <div v-if="item.hotelStar" slot="desc" style="padding-top: 10px;font-size: 14px;color:grey">
                    <van-rate readonly v-model="item.hotelStar" :size="10" color="#ffd21e" void-icon="star"
                        void-color="#eee" />
                </div>
                <div slot="price" style="font-size: 14px">{{ item.hotelAddress }}</div>
                <template #tags>
                    <van-tag style="margin: 5px 10px 5px 0px" size="medium" plain type="danger"
                        v-for="chi in item.roomTagName" :key="chi">{{ chi }}</van-tag>
                    <van-tag style="margin: 5px 10px 5px 0px" size="medium" plain type="danger" v-if="item.brief">{{
                        item.brief }}</van-tag>
                </template>
                <template #num>
                    <van-button
                        @click="$router.push({ name: 'hotelRoom', query: { id: item.id, activityId: item.activityId } })"
                        v-if="item.roomTagName && item.roomTagName.length > 0" size="small" round type="primary"
                        plain>选择房型</van-button>
                    <van-button
                        @click="$router.push({ name: 'hotelDetail', query: { id: item.id, activityId: item.activityId } })"
                        v-if="item.isDetail" style="margin-left: 10px" size="small" round type="info"
                        plain>详细介绍</van-button>
                    <van-button @click="callPhone(item.mobile)" style="margin-left: 10px" v-if="item.isMobile"
                        size="small" round type="primary" plain>联系酒店</van-button>
                </template>
            </van-card>
        </van-list>
        <!-- 返回按钮 -->
        <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    </div>
</template>

<script>
import date from "@/js/date.js";
export default {
    data() {
        return {
            openid: undefined,
            activityId: undefined,
            dataForm: {
                name: '',
                status: 1
            },
            activityInfo: {},
            loading: false,
            finished: false,
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
        }
    },
    mounted() {
        document.title = "预定酒店";
        this.activityId = this.$route.query.id;
        this.openid = this.$cookie.get("openid")
        this.$wxShare(); //加载微信分享
        this.getActivityInfo();
    },
    methods: {
        onSearch() {
            this.pageIndex = 1;
            this.dataList = [];
            this.getActivityList();
        },
        onLoad() {
            this.getActivityList();
        },
        getActivityInfo() {
            this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then(res => {
                if (res.code == 200) {
                    this.activityInfo = res.activity
                    this.activityInfo.backImg =
                        this.activityInfo.backImg ||
                        "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
                    if (res.activity.hotelNeedApply) {
                        // 如果酒店预订，需要报名
                        this.checkApply();
                    } else {

                        this.loadActivityDo();
                    }
                } else {
                    this.activityInfo = {}
                }
            });
        },
        checkApply() {
            this.$fly
                .get("/pyp/web/activity/activityuserapplyorder/checkApply", {
                    activityId: this.activityId,
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.isPay = res.isPay;
                        this.$store.commit("apply/update", this.isPay);
                        if (res.isPay == 1 && res.verifyStatus == 1) {
                            this.loadActivityDo();
                        } else {
                            // 缓存重定向地址
                            sessionStorage.setItem("returnUrl", encodeURIComponent(window.location.href))
                            this.$router.push({
                                name: "applyIndex",
                                query: {
                                    id: this.activityId,
                                },
                            });
                        }
                    } else {
                        vant.Toast(res.msg);
                    }
                });
        },
        loadActivityDo() {

            if (this.activityInfo.onlyOneHotel) {

                this.$fly
                    .get("/pyp/web/hotel/hotelorder/findMyActivityOrder", {
                        activityId: this.activityId,
                    })
                    .then((res) => {
                        if (res.code == 200) {
                            if (res.result && res.result.length > 0) {
                                vant.Toast("已提交酒店信息");
                                this.$router.push({
                                    name: "hotelSuccess",
                                    query: {
                                        orderId: res.result[0].id,
                                        id: this.activityId,
                                    }
                                })
                            } else {

                                this.getShareAndNotify();
                            }
                        } else {
                            vant.Toast(res.msg);
                        }
                    });
            } else {
                this.getShareAndNotify();
            }
        },
        getShareAndNotify() {

            if (this.activityInfo.hotelNotify) {
                vant.Dialog.alert({
                    title: '预订须知',
                    message: this.activityInfo.hotelNotify,
                }).then(() => {
                    // on close
                });
            }
            let startTime = date.formatDate.format(
                new Date(this.activityInfo.startTime),
                "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
                new Date(this.activityInfo.endTime),
                "MM月dd日"
            );
            if (startTime.includes(endTime)) {
                let desc =
                    "时间:" +
                    startTime +
                    "\n地址:" +
                    this.activityInfo.address;
                this.$wxShare(
                    "预定酒店-" + this.activityInfo.name,
                    (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                    desc
                ); //加载微信分享
            } else {
                let desc =
                    "时间:" +
                    startTime +
                    "-" +
                    endTime +
                    "\n地址:" +
                    this.activityInfo.address;
                this.$wxShare(
                    "预定酒店-" + this.activityInfo.name,
                    (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                    desc
                ); //加载微信分享
            }
        },
        getActivityList() {
            this.$fly.get('/pyp/web/hotel/hotelactivity/list', {
                'page': this.pageIndex,
                'limit': this.pageSize,
                'activityId': this.activityId,
                'status': this.dataForm.status,
                'name': this.dataForm.name
            }).then(res => {
                this.loading = false;
                if (res.code == 200) {
                    if (res.page.list && res.page.list.length > 0) {
                        res.page.list.forEach(e => {
                            e.roomTagName = e.roomTagName ? e.roomTagName.split(",") : [];
                            this.dataList.push(e);
                        })
                        this.totalPage = res.page.totalPage
                        this.pageIndex++;
                        this.loading = false
                        if (this.totalPage < this.pageIndex) {
                            this.finished = true
                        } else {
                            this.finished = false
                        }
                    } else {
                        this.finished = true;
                    }
                } else {
                    vant.Toast(res.msg);
                    this.dataList = []
                    this.totalPage = 0
                    this.finished = true
                }
            });
        },
        callPhone(v) {
            window.location.href = 'tel:' + v;
        },
        cmsTurnBack() {
            this.$router.go(-1);
        }
    }
}
</script>