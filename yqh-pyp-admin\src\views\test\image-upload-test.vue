<template>
  <div class="image-upload-test">
    <h2>图片上传弹窗组件测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>多图片选择测试</span>
      </div>
      <el-button type="primary" @click="openMultipleModal">
        <i class="el-icon-picture"></i> 选择多张图片 (最多9张)
      </el-button>
      <div v-if="multipleImages.length > 0" class="selected-images">
        <h4>已选择 {{ multipleImages.length }} 张图片：</h4>
        <div class="image-list">
          <div v-for="(image, index) in multipleImages" :key="index" class="image-item">
            <img :src="image.url" :alt="image.url" />
            <div class="image-info">
              <p>{{ image.url }}</p>
              <el-button size="mini" type="danger" @click="removeMultipleImage(index)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>单图片选择测试</span>
      </div>
      <el-button type="primary" @click="openSingleModal">
        <i class="el-icon-picture"></i> 选择单张图片
      </el-button>
      <div v-if="singleImage" class="selected-images">
        <h4>已选择图片：</h4>
        <div class="image-list">
          <div class="image-item">
            <img :src="singleImage.url" :alt="singleImage.url" />
            <div class="image-info">
              <p>{{ singleImage.url }}</p>
              <el-button size="mini" type="danger" @click="removeSingleImage">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <el-card>
      <div slot="header">
        <span>小尺寸图片测试 (最大1MB)</span>
      </div>
      <el-button type="primary" @click="openSmallModal">
        <i class="el-icon-picture"></i> 选择小尺寸图片
      </el-button>
      <div v-if="smallImages.length > 0" class="selected-images">
        <h4>已选择 {{ smallImages.length }} 张图片：</h4>
        <div class="image-list">
          <div v-for="(image, index) in smallImages" :key="index" class="image-item">
            <img :src="image.url" :alt="image.url" />
            <div class="image-info">
              <p>{{ image.url }}</p>
              <el-button size="mini" type="danger" @click="removeSmallImage(index)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 图片上传弹窗 -->
    <ImageUploadModal
      :visible.sync="imageModalVisible"
      :multiple="currentConfig.multiple"
      :max-count="currentConfig.maxCount"
      :max-size="currentConfig.maxSize"
      :default-images="currentConfig.defaultImages"
      @confirm="handleImageConfirm"
    />
  </div>
</template>

<script>
export default {
  name: 'ImageUploadTest',
  components: {
    ImageUploadModal: () => import("@/components/image-upload-modal")
  },
  data() {
    return {
      imageModalVisible: false,
      multipleImages: [],
      singleImage: null,
      smallImages: [],
      currentConfig: {
        multiple: true,
        maxCount: 9,
        maxSize: 2,
        defaultImages: [],
        type: 'multiple'
      }
    }
  },
  methods: {
    openMultipleModal() {
      this.currentConfig = {
        multiple: true,
        maxCount: 9,
        maxSize: 2,
        defaultImages: this.multipleImages,
        type: 'multiple'
      }
      this.imageModalVisible = true
    },
    
    openSingleModal() {
      this.currentConfig = {
        multiple: false,
        maxCount: 1,
        maxSize: 2,
        defaultImages: this.singleImage ? [this.singleImage] : [],
        type: 'single'
      }
      this.imageModalVisible = true
    },
    
    openSmallModal() {
      this.currentConfig = {
        multiple: true,
        maxCount: 5,
        maxSize: 1,
        defaultImages: this.smallImages,
        type: 'small'
      }
      this.imageModalVisible = true
    },
    
    handleImageConfirm(images) {
      switch (this.currentConfig.type) {
        case 'multiple':
          this.multipleImages = images
          break
        case 'single':
          this.singleImage = images.length > 0 ? images[0] : null
          break
        case 'small':
          this.smallImages = images
          break
      }
    },
    
    removeMultipleImage(index) {
      this.multipleImages.splice(index, 1)
    },
    
    removeSingleImage() {
      this.singleImage = null
    },
    
    removeSmallImage(index) {
      this.smallImages.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.image-upload-test {
  padding: 20px;
}

.selected-images {
  margin-top: 20px;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.image-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.image-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.image-info {
  flex: 1;
}

.image-info p {
  margin: 0 0 10px 0;
  word-break: break-all;
  color: #666;
  font-size: 12px;
}
</style>
