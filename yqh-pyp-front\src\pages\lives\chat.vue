<template>
  <div>
    <div class="message-list" ref="message-list">
      <van-pull-refresh
        v-model="loading"
        :disabled="finished"
        @refresh="getChatMsg"
      >
        <div v-for="message in chatMsg" :key="message.uuid">
          <div v-if="!message.isBack" class="message-box">
            <img @click="taboo(message)"
              class="message-img"
              :src="message.avatar"
              @error="imgError(message)"
            />
            <div class="message-item">
              <div
                style="display: flex; align-items: center; margin-bottom: 4px"
              >
                <!-- <div class="message-nick admin" v-if="message.nameCard == 'Admin'">{{$t('app.admin')}}</div>
                <div class="message-nick admin" v-else-if="message.nameCard == 'Owner'">{{$t('app.owner')}}</div> -->
                <div class="message-nick">{{ message.username }}</div>
                <van-tag
                  style="margin-left: 5px"
                  type="success"
                  v-if="message.roleId && message.roleId == 1"
                  >管理员</van-tag
                >
                <div class="message-date">{{ message.createOn }}</div>
              </div>
              <div class="message-container" @click="messageHandler(message)">
                <div class="triangle"></div>
                <template>
                  <img
                    v-if="message.type == 'img'"
                    :src="message.msg"
                    width="100px"
                    height="100px"
                  />
                  <span v-else class="message-text">{{ message.msg }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </van-pull-refresh>
    </div>
    <van-field
      type="textarea"
      rows="1"
      :autosize="height"
      v-model="messageContent"
      center
      class="tabbar"
      placeholder="我也要发言"
    >
      <template #left-icon>
        <div style="display: flex">
          <van-icon name="smile-o" @click="emojiShow = !emojiShow"></van-icon>
          <van-uploader :after-read="afterRead" :before-read="beforeRead" accept="image/*">
            <van-icon
              slot="default"
              name="photo-o"
              style="margin-left: 5px"
            ></van-icon>
          </van-uploader>
        </div>
      </template>
      <template #button>
        <van-button
          class="send"
          ref="mobileInput"
          @click="sendTextMessage"
          id="sendInput"
          >发送</van-button
        >
      </template>
    </van-field>
    <div class="emojis" v-show="emojiShow">
      <div
        v-for="item in emoji"
        class="emoji"
        :key="item"
        @click="chooseEmoji(item)"
      >
        {{ item }}
      </div>
    </div>
    <!-- <van-action-sheet
      v-model="showActionSheet"
      :actions="actions"
      cancel-text="取消"
      close-on-click-action
      @select="select"
    /> -->
  </div>
</template>

<script>
import { data } from "@/data/emoji";
export default {
  data() {
    return {
      emoji: [...data],
      actions: [{ name: "撤回" }, { name: "预览图片" }],
      emojiShow: false,
      showActionSheet: false,
      timer: "",
      isShowScrollButtomTips: false,
      preScrollHeight: 0,
      loading: false,
      finished: false,
      dateCompare: 0,
      chatMsg: [],
      uuid: [],
      pageIndex: 2,
      pageSize: 5,
      totalPage: 0,
      height: { maxHeight: 75 },
      messageContent: "",
      indexMessage: {},
    };
  },
  props: ["pushKey", "activityId"],
  mounted() {
    this.firstGetChatMsg();
    //刷新
    window.addEventListener("beforeunload", this.clear);
    this.timer = setInterval(() => {
      console.log("---------------------定时器执行---------------------");
      this.refreshMsg();
    }, 10 * 1000);
  },
  updated() {
    this.keepMessageListOnButtom();
  },
  beforeDestroy() {},
  methods: {
    imgError(item) {
      // item.avatar = require('../../assets/image/default.png')
    },
    getChatMsg() {
      this.$fly
        .get("/pyp/web/chat/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          pushKey: this.pushKey,
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.list && res.list.length > 0) {
              res.list.forEach((e) => {
                if (!this.uuid.includes(e.uuid)) {
                  this.chatMsg.unshift(e);
                  this.uuid.push(e.uuid);
                }
              });
              this.totalPage = res.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            this.chatMsg = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    firstGetChatMsg() {
      this.$fly
        .get("/pyp/web/chat/list", {
          page: 1,
          limit: 5,
          pushKey: this.pushKey,
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.list && res.list.length > 0) {
              res.list.forEach((e) => {
                if (!this.uuid.includes(e.uuid)) {
                  this.chatMsg.unshift(e);
                  this.uuid.push(e.uuid);
                }
              });
            } else {
              this.finished = true;
            }
          } else {
            this.chatMsg = [];
            this.finished = true;
          }
        });
    },
    refreshMsg() {
      this.$fly
        .get("/pyp/web/chat/list", {
          page: 1,
          limit: 5,
          pushKey: this.pushKey,
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.list && res.list.length > 0) {
              res.list.forEach((e) => {
                if (!this.uuid.includes(e.uuid)) {
                  this.chatMsg.push(e);
                  this.uuid.push(e.uuid);
                }
              });
            } else {
            this.chatMsg = [];
            this.finished = true;

            }
          } else {
            this.chatMsg = [];
            this.finished = true;
          }
        });
    },
    handleLine() {
      this.messageContent += "\n";
    },
    handleEnter() {
      this.sendTextMessage();
    },
    sendTextMessage() {
      window.scroll(0, 0); //ios键盘回落
      if (
        this.messageContent === "" ||
        this.messageContent.trim().length === 0
      ) {
        this.messageContent = "";
        vant.Toast("不能发送空消息");
        return false;
      }
      this.$fly
        .get("/pyp/web/chat/send", {
          pushKey: this.pushKey,
          msg: this.messageContent,
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.emojiShow = false;
            this.refreshMsg();
            this.$nextTick(() => {
              let ele = this.$refs["message-list"];
              console.log(ele);
              ele.scrollTop = ele.scrollHeight;
            });
          } else {
          }
        });
      this.messageContent = "";
    },
    // 如果滚到底部就保持在底部，否则提示是否要滚到底部
    keepMessageListOnButtom() {
      let messageListNode = this.$refs["message-list"];
      if (!messageListNode) {
        return;
      }
      // 距离底部20px内强制滚到底部,否则提示有新消息
      if (
        this.preScrollHeight -
          messageListNode.clientHeight -
          messageListNode.scrollTop <
        20
      ) {
        this.$nextTick(() => {
          messageListNode.scrollTop = messageListNode.scrollHeight + 60;
        });
        this.isShowScrollButtomTips = false;
      } else {
        this.isShowScrollButtomTips = true;
      }
      this.preScrollHeight = messageListNode.scrollHeight;
    },
    // async beforeunloadHandler(e) {
    //   this.timer = clearInterval(this.timer);
    // },
    async clear() {
      // window.removeEventListener("beforeunload", this.beforeunloadHandler);
      this.timer = clearInterval(this.timer);
    },
    select(index) {
      if (index == 0) {
        this.back(this.indexMessage.index);
      } else if (index == 1) {
        vant.ImagePreview({
          images: [this.indexMessage.msg], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
    messageHandler(v) {
      this.indexMessage = v;
      if (v.type == "img") {
        this.$emit('showImage');
        // this.showActionSheet = true;
      } else {
        this.back(v.index);
      }
    },
    back(v) {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认撤回消息?",
      })
        .then(() => {
          this.$fly
            .get("/pyp/web/chat/back", {
              index: v,
              pushKey: this.pushKey,
              activityId: this.activityId,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("撤回成功");
                this.chatMsg = [];
                this.uuid = [];
                this.pageIndex = 1;
                this.finished = false;
                this.refreshMsg();
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => {});
    },
    taboo(v) {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认禁言该用户?",
      })
        .then(() => {
          this.$fly
            .get("/pyp/web/chat/taboo", {
              userId: v.userId,
              activityId: this.activityId,
              pushKey: this.pushKey,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("禁言成功");
                this.chatMsg = [];
                this.uuid = [];
                this.pageIndex = 1;
                this.finished = false;
                this.refreshMsg();
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => {});
    },
    chooseEmoji(item) {
      this.messageContent += item;
    },
    afterRead(e) {
      e.status = "uploading";
      e.message = "上传中...";
      let formData = new FormData();
      formData.append("pushKey", this.pushKey);
      formData.append("activityId", this.activityId);
      formData.append("file", e.file);

      this.$fly.post("/pyp/web/chat/sendImage", formData).then((res) => {
        if (res && res.code === 200) {
          this.refreshMsg();
          this.$nextTick(() => {
            let ele = this.$refs["message-list"];
            console.log(ele);
            ele.scrollTop = ele.scrollHeight;
          });
        }
      });
    },
    beforeRead(file) {
      // if (file.type !== 'image/jpeg') {
      //   Toast('请上传 jpg 格式图片');
      //   return false;
      // }
      return true;
    },
  },
  //进入其他页
  async destroyed() {
    console.log("destroyed");
    this.clear();
  },
};
</script>
<style lang="less" scoped>
.emojis {
  height: 160px;
  box-sizing: content-box;
  display: flex;
  flex-wrap: wrap;
  overflow-y: scroll;
  position: fixed;
  width: 100%;
  bottom: 50px;
  z-index: 9;
  background: #f6f6f6;
  padding-top: 10px;
}
.message-list {
  position: absolute;
  z-index: 1;
  width: 100%;
  top: 0;
  bottom: 55px;
  overflow: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch; //ios卡顿
  padding: 5px 20px //5px 20px 0px 40px
;
  /*margin-bottom 55px*/
}
.message-box {
  font-family: Microsoft YaHei, Arial, Helvetica, sans-serif, SimSun;
  display: flex;
  .message-item {
    font-size: 14px;
    padding: 4px 8px;
    position: relative;
    line-height: 18px;
    word-wrap: break-word;
    white-space: normal;
    width: 90%;
    margin-left: 2px;
    .message-nick,
    .message-date {
      font-size: 12px;
      line-height: 23px;
      color: black;
    }
    .admin {
      border: 1px solid #dff4e5;
      background-color: green;
      border-color: green;
      color: white;
      border-radius: 4px;
      padding: 0px 5px;
      margin-right: 5px;
    }
    .owner {
      border: 1px solid #dff4e5;
      background-color: blue;
      border-color: blue;
      color: white;
      border-radius: 4px;
      padding: 0px 5px;
      margin-right: 5px;
    }
    .message-date {
      margin-left: 5px;
    }
    .message-container {
      display: inline-block;
      position: relative;
      background-color: white;
      // border-radius: 12px;
      /*border-top-left-radius:0*/
      padding: 8px 11px;
      .triangle {
        // width: 0;
        // height: 0;
        // border-top:5px solid transparent;
        // border-bottom: 10px solid transparent;
        // border-right: 10px solid black;
        // border-bottom-right-radius: 2px;
        // position: absolute;
        // left: -8px;
        // top: 6px;
      }
    }
  }
  .tip-text,
  .tip-leave {
    font-size: 14px;
    position: relative;
    line-height: 18px;
    word-wrap: break-word;
    white-space: normal;
    /*margin 0 auto*/
    color: rgb(245, 166, 35); //#258ff3//#fea602
    .tips-img {
      display: inline-block;
      width: 20px;
      vertical-align: center;
    }
  }
  .tip-text {
    padding: 4px 35px;
  }
  .tip-leave {
    padding: 4px 40px;
  }
  .message-text {
    font-size: 14px;
    white-space: normal;
    word-break: break-all;
    word-wrap: break-word;
    color: black;
  }
}
.message-img {
  display: inline-block;
  min-width: 40px;
  max-width: 40px;
  height: 40px;
  border-radius: 50%;
}
.emoji {
  height: 40px;
  width: 40px;
  box-sizing: border-box;
  text-align: center;
}
.send-header-box {
  z-index: 212;
  height: 42px;
  position: fixed;
  /*margin:0 auto;*/
  width: 100%;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
}
.send-header-bar {
  z-index: 211;
  height: 42px;
  position: fixed;
  /*margin:0 auto;*/
  width: 100%;
  padding: 2px 5%;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  // background-color #09090c
}
.send-header-bar span {
  display: flex;
  justify-content: center;
  /*align-items center*/
  /*line-height: 24px;*/
}
.send-header-bar i {
  cursor: pointer;
  font-size: 28px;
  color: rgb(245, 166, 35);
  /*line-height 24px*/
  margin: 0 12px 0 0;
}

.send-header-bar i:hover {
  // color #FFFFFF
}
.tabbar {
  background: rgba(255, 255, 255, 1);
  position: absolute;
  bottom: 0;
  left: 0;
  /*height: 50px;*/
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  /deep/ .van-field__control {
    background: #f6f6f6;
    // border-radius: 10px;
    padding: 6px;
  }
  /deep/ .van-field__left-icon .van-icon {
    font-size: 25px;
    color: gray;
  }
}
.van-cell {
  padding: 10px 5px;
}
.send {
  background: #11aa66;
  border-radius: 20px;
  font-size: 14px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  padding: 0 20px;
  line-height: 36px;
  height: 36px;
}
</style>