package com.cjy.pyp.modules.salesman.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信用户业务员绑定前端API控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("web/salesman/binding")
@Api(tags = "微信用户业务员绑定前端API")
public class WebWxUserSalesmanBindingController extends AbstractController {

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;

    /**
     * 查询当前用户的业务员绑定
     */
    @RequestMapping("/myBinding")
    @ApiOperation(value = "我的业务员", notes = "查询当前用户绑定的业务员信息")
    public R getMyBinding(@CookieValue String appid) {
        Long wxUserId = getUserId();
        WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
        return R.ok().put("binding", binding);
    }

    /**
     * 通过二维码绑定业务员
     */
    @RequestMapping("/bindByQrCode")
    @ApiOperation(value = "二维码绑定", notes = "通过扫描二维码绑定业务员")
    public R bindByQrCode(@RequestParam String qrCodeContent, @CookieValue String appid) {
        try {
            Long wxUserId = getUserId();
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.bindByQrCode(
                    wxUserId, qrCodeContent, appid);
            return R.ok().put("binding", binding);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 通过邀请码绑定业务员
     */
    @RequestMapping("/bindByInviteCode")
    @ApiOperation(value = "邀请码绑定", notes = "通过邀请码绑定业务员")
    public R bindByInviteCode(@RequestParam String inviteCode, @CookieValue String appid) {
        try {
            Long wxUserId = getUserId();
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.bindByInviteCode(
                    wxUserId, inviteCode, appid);
            return R.ok().put("binding", binding);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 解除当前绑定
     */
    @RequestMapping("/unbind")
    @ApiOperation(value = "解除绑定", notes = "解除当前的业务员绑定")
    public R unbind(@RequestParam(required = false) String reason, @CookieValue String appid) {
        try {
            Long wxUserId = getUserId();
            
            // 先查询当前绑定
            WxUserSalesmanBindingEntity currentBinding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
            if (currentBinding == null) {
                return R.error("当前没有绑定业务员");
            }
            
            boolean success = wxUserSalesmanBindingService.unbindSalesman(
                    wxUserId, currentBinding.getSalesmanId(), 
                    reason != null ? reason : "用户主动解除绑定", appid);
            
            if (success) {
                return R.ok();
            } else {
                return R.error("解除绑定失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 检查绑定状态
     */
    @RequestMapping("/checkBinding")
    @ApiOperation(value = "检查绑定状态", notes = "检查当前用户是否已绑定业务员")
    public R checkBinding(@CookieValue String appid) {
        Long wxUserId = getUserId();
        WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
        
        boolean isBound = binding != null;
        return R.ok()
                .put("isBound", isBound)
                .put("binding", binding);
    }

    /**
     * 获取业务员联系信息
     */
    @RequestMapping("/getSalesmanContact")
    @ApiOperation(value = "业务员联系方式", notes = "获取绑定业务员的联系信息")
    public R getSalesmanContact(@CookieValue String appid) {
        Long wxUserId = getUserId();
        WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
        
        if (binding == null) {
            return R.error("当前没有绑定业务员");
        }
        
        // 只返回必要的联系信息，不返回敏感数据
        return R.ok()
                .put("salesmanName", binding.getSalesmanName())
                .put("salesmanCode", binding.getSalesmanCode())
                .put("salesmanMobile", binding.getSalesmanMobile())
                .put("bindingTime", binding.getBindingTime());
    }

    /**
     * 验证邀请码有效性
     */
    @RequestMapping("/validateInviteCode")
    @ApiOperation(value = "验证邀请码", notes = "验证邀请码是否有效")
    public R validateInviteCode(@RequestParam String inviteCode, @CookieValue String appid) {
        try {
            // 这里需要实现邀请码验证逻辑
            // 暂时返回成功，实际需要根据业务逻辑实现
            return R.ok().put("valid", true).put("message", "邀请码有效");
        } catch (Exception e) {
            return R.error("邀请码验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取绑定历史
     */
    @RequestMapping("/getBindingHistory")
    @ApiOperation(value = "绑定历史", notes = "获取用户的绑定变更历史")
    public R getBindingHistory(@CookieValue String appid) {
        // 这个功能需要在WxUserSalesmanBindingLogService中实现
        Long wxUserId = getUserId();
        return R.ok().put("message", "绑定历史功能待实现");
    }
}
