<template>
  <div :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="会议名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="会议名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会议编号" prop="code">
            <el-input v-model="dataForm.code" placeholder="会议编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会议日期" prop="times">
            <el-date-picker v-model="dataForm.times" style="width: 100%" @change="dateChange" type="datetimerange"
              value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="会议类型" prop="activityType">
            <el-select v-model="dataForm.activityType" filterable>
              <el-option v-for="item in activityType" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户" prop="clientId">
            <el-select v-model="dataForm.clientId" filterable>
              <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="科室" prop="duties">
            <el-input v-model="dataForm.duties" placeholder="会议名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户类型" prop="clientType">
            <el-select v-model="dataForm.clientType" filterable>
              <el-option v-for="item in clientType" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新/老客户" prop="oldOrNew">
            <el-select v-model="dataForm.oldOrNew" filterable>
              <el-option v-for="item in oldOrNew" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item></el-col>
        <el-col :span="8">
          <el-form-item label="主题主持劳务费" prop="topicGuest">
            <el-input v-model="dataForm.topicGuest" placeholder="主题主持劳务费"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主题讨论劳务费" prop="topicDiscuss">
            <el-input v-model="dataForm.topicDiscuss" placeholder="主题主席劳务费"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主题主席劳务费" prop="topicSpeaker">
            <el-input v-model="dataForm.topicSpeaker" placeholder="主题主席劳务费"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="日程讲者劳务费" prop="scheduleGuest">
            <el-input v-model="dataForm.scheduleGuest" placeholder="日程讲者劳务费"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="日程主持劳务费" prop="scheduleSpeaker">
            <el-input v-model="dataForm.scheduleSpeaker" placeholder="日程主持劳务费"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="日程讨论劳务费" prop="scheduleDiscuss">
            <el-input v-model="dataForm.scheduleDiscuss" placeholder="日程讨论劳务费"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="区域地址" prop="cityId">
        <el-select v-model="dataForm.provinceId" placeholder="省" @change="provinceChange" filterable>
          <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select style="margin-left: 10px;" v-model="dataForm.cityId" placeholder="市" @change="getCityName"
          filterable>
          <el-option v-for="item in cities" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="dataForm.address" placeholder="详细地址"></el-input>
      </el-form-item>
      <!-- <el-form-item label="会议图片">
        <el-upload list-type="picture-card" :before-upload="checkFileSize" :on-success="successHandle"
          :file-list="fileList" :action="url">
          <i slot="default" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}">
            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
        <el-dialog :visible.sync="imgDialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <div style="color: red">建议尺寸：1920*1080，大小2mb以下</div>
      </el-form-item>
      <el-form-item label="手机端图片">
        <el-upload list-type="picture-card" :before-upload="checkFileSize" :on-success="appSuccessHandle"
          :file-list="appFileList" :action="url">
          <i slot="default" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}">
            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handleAppPictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-delete" @click="handleAppRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
        <el-dialog :visible.sync="imgAppDialogVisible">
          <img width="100%" :src="dialogAppImageUrl" alt="">
        </el-dialog>
        <div style="color: red">建议尺寸：1920*1080，大小2mb以下</div>
      </el-form-item>
      <el-row>
        <el-col :span="8">
      <el-form-item label="背景图" prop="background">
        <el-upload class="avatar-uploader" list-type="picture-card" :before-upload="checkFileSize"
          :show-file-list="false" accept=".jpg, .jpeg, .png, .gif" :on-success="backgroundSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.background" :src="dataForm.background" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <div style="color: red">建议尺寸：1080*1920，大小1mb以下</div>
      </el-form-item></el-col>
      <el-col :span="8">
      <el-form-item label="报名成功二维码" prop="subscribeImg">
        <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
          accept=".jpg, .jpeg, .png, .gif" :before-upload="checkFileSize" :on-success="subscribeImgSuccessHandle"
          :action="url">
          <img width="100px" v-if="dataForm.subscribeImg" :src="dataForm.subscribeImg" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <div style="color: red">Tips：可上传公众号关注二维码，报名成功可引导用户关注公众号</div></el-col>
      <el-col :span="8">
      <el-form-item label="报名背景图" prop="applyBackground">
        <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
          accept=".jpg, .jpeg, .png, .gif" :before-upload="checkFileSize" :on-success="applyBackgroundSuccessHandle"
          :action="url">
          <img width="100px" v-if="dataForm.applyBackground" :src="dataForm.applyBackground" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <div style="color: red">建议尺寸：1080*1920，大小1mb以下</div></el-col>
    </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="显示倒计时" prop="isCountdown">
            <el-select v-model="dataForm.isCountdown" placeholder="显示倒计时" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="显示报名人数" prop="showApplyNumber">
            <el-select v-model="dataForm.showApplyNumber" placeholder="显示报名人数" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="酒店需要报名" prop="hotelNeedApply">
            <el-select v-model="dataForm.hotelNeedApply" placeholder="酒店需要报名" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="直播需要报名" prop="liveNeedApply">
            <el-select v-model="dataForm.liveNeedApply" placeholder="直播需要报名" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="报名成功直播" prop="applySuccessLive">
            <el-select v-model="dataForm.applySuccessLive" placeholder="报名成功显示直播" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="报名成功酒店" prop="applySuccessHotel">
            <el-select v-model="dataForm.applySuccessHotel" placeholder="报名成功显示酒店" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="只能订一次酒店" prop="onlyOneHotel">
            <el-select v-model="dataForm.onlyOneHotel" placeholder="只能订一次酒店" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="取消报名同步取消酒店" prop="cancelApplyHotel">
            <el-select v-model="dataForm.cancelApplyHotel" placeholder="取消报名同步取消酒店" filterable>
              <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属分类" prop="configActivityTypeId">
            <el-select v-model="dataForm.configActivityTypeId" placeholder="所属分类" filterable>
              <el-option v-for="item in configActivityType" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="会议模板" prop="templateId">
            <el-select v-model="dataForm.templateId" placeholder="会议模板" filterable>
              <el-tooltip placement="right" v-for="item in sysTemplate" :key="item.templateCode">
                <div slot="content">
                  <img style="height: 600px;" :src="item.url" alt="">
                </div>
                <el-option :label="item.name" :value="item.templateCode"></el-option>
              </el-tooltip>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="九宫格字体颜色" prop="fontColor">
            <el-color-picker v-model="dataForm.fontColor"></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="九宫格底部展示" prop="type">
            <el-select v-model="dataForm.type" placeholder="类型" filterable multiple>
              <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="底部颜色配置" prop="bottomColor">
            <el-color-picker v-model="dataForm.bottomColor"></el-color-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="返回图标" prop="backImg">
            <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
              accept=".jpg, .jpeg, .png, .gif" :before-upload="checkFileSize" :on-success="backImgSuccessHandle"
              :action="url">
              <img width="100px" v-if="dataForm.backImg" :src="dataForm.backImg" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div style="color: red">建议尺寸：400*400，大小100kb以下</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="返回地址" prop="backUrl">
            <el-input v-model="dataForm.backUrl" placeholder="返回地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="背景音乐" prop="musicUrl">
            <el-select v-model="dataForm.musicUrl" placeholder="背景音乐" filterable>
              <el-option label="无" value=""></el-option>
              <el-option v-for="item in music" :key="item.url" :label="item.name" :value="item.url"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row >
        <el-col :span="6">
          <el-form-item label="广告底部颜色" prop="adColor">
            <el-color-picker v-model="dataForm.adColor"></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="广告停留时间" prop="adTime">
            <el-input v-model="dataForm.adTime" placeholder="广告停留时间"><template slot="append">
                秒
              </template></el-input>
</el-form-item>
</el-col>
<el-col :span="6">
  <el-form-item label="首页进入广告" prop="ad">
    <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false" accept=".jpg, .jpeg, .png, .gif"
      :before-upload="checkFileSize" :on-success="adSuccessHandle" :action="url">
      <img width="100px" v-if="dataForm.ad" :src="dataForm.ad" class="avatar">
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
    <div style="color: red">建议尺寸：1000*2000，大小2mb以下</div>
  </el-form-item>
</el-col>
<el-col :span="6">
  <el-form-item label="分享图" prop="shareUrl">
    <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false" accept=".jpg, .jpeg, .png, .gif"
      :before-upload="checkFileSize" :on-success="shareUrlSuccessHandle" :action="url">
      <img width="100px" v-if="dataForm.shareUrl" :src="dataForm.shareUrl" class="avatar">
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
    <div style="color: red">建议尺寸：400*400，大小300kb以下</div>
  </el-form-item>
</el-col>
</el-row>
<el-form-item label="报名须知" prop="applyNotify">
  <tinymce-editor ref="editor" v-model="dataForm.applyNotify"></tinymce-editor>
</el-form-item>
<el-form-item label="酒店预订须知" prop="hotelNotify">
  <tinymce-editor ref="editor" v-model="dataForm.hotelNotify"></tinymce-editor>
</el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="turnBack()">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </div>
</template>

<script>
import Compressor from 'compressorjs';
import { yesOrNo } from "@/data/common"
export default {
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor"),
    OssUploader: () =>
      import('../oss/oss-uploader')
  },
  data() {
    var conferenceCode = (rule, value, callback) => {
      var reg = /^[^\u4e00-\u9fa5]{3,10}$/;
      if (!reg.test(value)) {
        callback(new Error("会议编号3到10位，不能包含中文"));
      } else {
        callback()
      }
    };
    var faceRateRule = (rule, value, callback) => {
      var reg = /^(100)$|^((\d|[1-9]\d)(\.\d{1,2})?)$/;
      if (value && !reg.test(value)) {
        callback(new Error("请填入0-100之间的数，最多保留2位小数"));
      } else {
        callback()
      }
    };
    return {
      activityType: [],
      client: [],
      clientType: [],
      oldOrNew: [],
      yesOrNo: yesOrNo,
      btnLoading: false,
      configActivityType: [],
      appFileList: [],
      con: [],
      fileList: [],
      cityName: "",
      imgDialogVisible: false,
      imgAppDialogVisible: false,
      dialogImageUrl: '',
      dialogAppImageUrl: '',
      music: [],
      provinces: [],
      cities: [],
      url: '',
      dataForm: {
        id: 0,
        times: [],
        code: '',
        name: '',
        startTime: '',
        endTime: '',
        provinceId: '',
        cityId: '',
        pcBanner: '',
        mobileBanner: '',
        background: '',
        type: ['0'],
        subscribeImg: '',
        applyBackground: '',
        backImg: '',
        pvCount: '',
        uvCount: '',
        longitude: '',
        latitude: '',
        address: '',
        bottomColor: '',
        fontColor: '',
        templateId: '1',
        applyNotify: '',
        hotelNotify: '',
        turnurl: '',
        isCountdown: 1,
        isShow: 1,
        showSub: 0,
        isHot: 0,
        isIndex: 0,
        appid: '',
        topicGuest: 0,
        topicSpeaker: 0,
        topicDiscuss: 0,
        scheduleGuest: 0,
        scheduleSpeaker: 0,
        scheduleDiscuss: 0,
        introduction: '',
        showApplyNumber: 0,
        hotelNeedApply: 0,
        liveNeedApply: 1,
        applySuccessLive: 1,
        applySuccessHotel: 1,
        onlyOneHotel: 0,
        cancelApplyHotel: 0,
        backUrl: '',
        musicUrl: '',
        ad: '',
        adColor: '',
        adTime: '',
        shareUrl: '',
        configActivityTypeId: '',
        clientId: '',
        clientType: '',
        duties: '',
        activityType: '',
        oldOrNew: '',
      },
      typeList: [
        { id: '0', name: '自定义' },
        { id: '1', name: 'banner广告' },
        { id: '2', name: '文件下载' },
      ],
      templateList: [
        { id: '1', name: '模板1-传统九宫格' },
        { id: '2', name: '模板2-含背景九宫格' },
        { id: '31', name: '模板3-4114布局' },
        { id: '4', name: '模板4-圆形九宫格' },
        { id: '5', name: '模板5-纯图标九宫格' },
        { id: '61', name: '模板61-3-3(1-2)-3-1布局' },
        { id: '62', name: '模板62-3-3(1-2)-2布局' },
        { id: '63', name: '模板63-3-3(1-2)-2-2-2布局' },
      ],
      sysTemplate: [],
      dataRule: {
        code: [{
          required: true,
          message: '会议编号不能为空',
          trigger: 'blur'
        }, {
          validator: conferenceCode,
          trigger: "blur"
        }],
        name: [{
          required: true,
          message: '会议名称不能为空',
          trigger: 'blur'
        }],
        times: [{
          required: true,
          message: '会议时间不能为空',
          trigger: 'blur'
        }],
        provinceId: [{
          required: true,
          message: '省份不能为空',
          trigger: 'blur'
        }],
        cityId: [{
          required: true,
          message: '城市不能为空',
          trigger: 'blur'
        }],
        pcBanner: [{
          required: true,
          message: '会议图片不能为空',
          trigger: 'blur'
        }],
        mobileBanner: [{
          required: true,
          message: '手机端图片不能为空',
          trigger: 'blur'
        }],
        isShow: [{
          required: true,
          message: '公众号显示不能为空',
          trigger: 'blur'
        }],
      }
    }
  },

  activated() {
    this.init()
  },
  methods: {
    init() {
      this.dataForm.id = this.$route.query.id || 0;
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.appFileList = [];
      this.fileList = [];
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.appid = this.$cookie.get("appid");
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$set(this.dataForm, "times", [data.activity.startTime, data.activity.endTime])
              this.appFileList = data.activity.appFileList
              this.fileList = data.activity.fileList
              this.dataForm.code = data.activity.code
              this.dataForm.name = data.activity.name
              this.dataForm.startTime = data.activity.startTime
              this.dataForm.endTime = data.activity.endTime
              this.dataForm.provinceId = data.activity.provinceId
              this.dataForm.cityId = data.activity.cityId
              this.dataForm.pcBanner = data.activity.pcBanner
              this.dataForm.mobileBanner = data.activity.mobileBanner
              this.dataForm.subscribeImg = data.activity.subscribeImg
              this.dataForm.applyBackground = data.activity.applyBackground
              this.dataForm.backImg = data.activity.backImg
              this.dataForm.background = data.activity.background
              this.dataForm.type = data.activity.type ? data.activity.type.split(',') : []
              this.dataForm.pvCount = data.activity.pvCount
              this.dataForm.uvCount = data.activity.uvCount
              this.dataForm.longitude = data.activity.longitude
              this.dataForm.latitude = data.activity.latitude
              this.dataForm.address = data.activity.address
              this.dataForm.introduction = data.activity.introduction
              this.dataForm.bottomColor = data.activity.bottomColor
              this.dataForm.fontColor = data.activity.fontColor
              this.dataForm.templateId = data.activity.templateId
              this.dataForm.applyNotify = data.activity.applyNotify
              this.dataForm.hotelNotify = data.activity.hotelNotify
              this.dataForm.isCountdown = data.activity.isCountdown
              this.dataForm.isShow = data.activity.isShow
              this.dataForm.showSub = data.activity.showSub
              this.dataForm.isHot = data.activity.isHot
              this.dataForm.isIndex = data.activity.isIndex
              this.dataForm.appid = data.activity.appid
              this.dataForm.topicGuest = data.activity.topicGuest
              this.dataForm.topicSpeaker = data.activity.topicSpeaker
              this.dataForm.topicDiscuss = data.activity.topicDiscuss
              this.dataForm.scheduleGuest = data.activity.scheduleGuest
              this.dataForm.scheduleSpeaker = data.activity.scheduleSpeaker
              this.dataForm.scheduleDiscuss = data.activity.scheduleDiscuss
              this.dataForm.showApplyNumber = data.activity.showApplyNumber
              this.dataForm.hotelNeedApply = data.activity.hotelNeedApply
              this.dataForm.liveNeedApply = data.activity.liveNeedApply
              this.dataForm.applySuccessLive = data.activity.applySuccessLive
              this.dataForm.applySuccessHotel = data.activity.applySuccessHotel
              this.dataForm.onlyOneHotel = data.activity.onlyOneHotel
              this.dataForm.cancelApplyHotel = data.activity.cancelApplyHotel
              this.dataForm.turnurl = data.activity.turnurl
              this.dataForm.backUrl = data.activity.backUrl
              this.dataForm.musicUrl = data.activity.musicUrl
              this.dataForm.ad = data.activity.ad
              this.dataForm.adColor = data.activity.adColor
              this.dataForm.adTime = data.activity.adTime
              this.dataForm.shareUrl = data.activity.shareUrl
              this.dataForm.configActivityTypeId = data.activity.configActivityTypeId
              this.dataForm.clientId = data.activity.clientId
              this.dataForm.clientType = data.activity.clientType
              this.dataForm.duties = data.activity.duties
              this.dataForm.activityType = data.activity.activityType
              this.dataForm.oldOrNew = data.activity.oldOrNew
              this.$http({
                url: this.$http.adornUrl(
                  `/sys/region/pid/${this.dataForm.provinceId}`
                ),
                method: "get",
                params: this.$http.adornParams()
              }).then(({
                data
              }) => {
                if (data && data.code === 200) {
                  this.cities = data.list;
                } else {
                  this.cities = [];
                }
              });
            }
          })
        }
      })
      this.getProvinces();
      this.getResult();
      this.getTempalte();
      this.getConfigActivityType();
      this.findClient();
    },
    findClient() {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
          }
        })
    },
    getResult() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'clientType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.clientType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'oldOrNew',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.oldOrNew = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'activityType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
    },
    turnBack() {
      this.$router.go(-1);
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'code': this.dataForm.code,
              'name': this.dataForm.name,
              'startTime': this.dataForm.startTime,
              'endTime': this.dataForm.endTime,
              'provinceId': this.dataForm.provinceId,
              'cityId': this.dataForm.cityId,
              'pcBanner': this.dataForm.pcBanner,
              'mobileBanner': this.dataForm.mobileBanner,
              'subscribeImg': this.dataForm.subscribeImg,
              'applyBackground': this.dataForm.applyBackground,
              'backImg': this.dataForm.backImg,
              'background': this.dataForm.background,
              'type': this.dataForm.type.toString(),
              'pvCount': this.dataForm.pvCount,
              'uvCount': this.dataForm.uvCount,
              'longitude': this.dataForm.longitude,
              'latitude': this.dataForm.latitude,
              'address': this.dataForm.address,
              'introduction': this.dataForm.introduction,
              'bottomColor': this.dataForm.bottomColor,
              'fontColor': this.dataForm.fontColor,
              'templateId': this.dataForm.templateId,
              'applyNotify': this.dataForm.applyNotify,
              'hotelNotify': this.dataForm.hotelNotify,
              'isCountdown': this.dataForm.isCountdown,
              'isShow': this.dataForm.isShow,
              'showSub': this.dataForm.showSub,
              'isIndex': this.dataForm.isIndex,
              'isHot': this.dataForm.isHot,
              'appid': this.dataForm.appid,
              'topicGuest': this.dataForm.topicGuest,
              'topicSpeaker': this.dataForm.topicSpeaker,
              'topicDiscuss': this.dataForm.topicDiscuss,
              'scheduleGuest': this.dataForm.scheduleGuest,
              'scheduleSpeaker': this.dataForm.scheduleSpeaker,
              'scheduleDiscuss': this.dataForm.scheduleDiscuss,
              'showApplyNumber': this.dataForm.showApplyNumber,
              'hotelNeedApply': this.dataForm.hotelNeedApply,
              'liveNeedApply': this.dataForm.liveNeedApply,
              'applySuccessLive': this.dataForm.applySuccessLive,
              'applySuccessHotel': this.dataForm.applySuccessHotel,
              'onlyOneHotel': this.dataForm.onlyOneHotel,
              'cancelApplyHotel': this.dataForm.cancelApplyHotel,
              'turnurl': this.dataForm.turnurl,
              'backUrl': this.dataForm.backUrl,
              'musicUrl': this.dataForm.musicUrl,
              'ad': this.dataForm.ad,
              'ad': this.dataForm.ad,
              'adColor': this.dataForm.adColor,
              'adTime': this.dataForm.adTime,
              'shareUrl': this.dataForm.shareUrl,
              'configActivityTypeId': this.dataForm.configActivityTypeId,
              'clientId': this.dataForm.clientId,
              'clientType': this.dataForm.clientType,
              'duties': this.dataForm.duties,
              'activityType': this.dataForm.activityType,
              'oldOrNew': this.dataForm.oldOrNew,
            })
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.turnBack();
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getProvinces() {
      this.$http({
        url: this.$http.adornUrl("/sys/region/pid/100000"),
        method: "get",
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.provinces = data.list;
        } else {
          this.provinces = [];
        }
      });
    },
    getMusic() {
      this.$http({
        url: this.$http.adornUrl("/sys/sysmusic/findAll"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.music = data.result;
        } else {
          this.music = [];
        }
      });
    },
    getTempalte() {
      this.$http({
        url: this.$http.adornUrl("/sys/systemplate/findAll"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.sysTemplate = data.result;
        } else {
          this.sysTemplate = [];
        }
      });
    },
    getConfigActivityType() {
      this.$http({
        url: this.$http.adornUrl("/config/configactivitytype/findByAppid"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.configActivityType = data.result;
        } else {
          this.configActivityType = [];
        }
      });
    },
    provinceChange(val) {
      if (val === undefined) {
        return;
      }
      this.cities = {};
      this.dataForm.cityId = undefined;
      this.dataForm.jieSongCityName = [];
      this.$http({
        url: this.$http.adornUrl(`/sys/region/pid/${val}`),
        method: "get",
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.cities = data.list;
        } else {
          this.cities = [];
        }
      });
    },
    getCityName(v) {
      var obj = this.cities.find((item) => { //这里的userList就是上面遍历的数据源
        return item.id === v; //筛选出匹配数据
      });
      this.cityName = obj.name.replace("市", ""); //我这边的name就是对应label的
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // 上传成功
    successHandle(response, file, fileList) {
      this.fileList = fileList;
      this.successNum1++;
      if (response && response.code === 200) {
        if (!this.dataForm.pcBanner || this.dataForm.pcBanner.length == 0) {
          this.dataForm.pcBanner = response.url
        } else {
          this.dataForm.pcBanner += "," + response.url;
        }
      } else {
        this.$message.error(response.msg);
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      this.appFileList = fileList;
      this.successNum++;
      if (response && response.code === 200) {
        if (!this.dataForm.mobileBanner || this.dataForm.mobileBanner.length == 0) {
          this.dataForm.mobileBanner = response.url
        } else {
          this.dataForm.mobileBanner += "," + response.url;
        }
      } else {
        this.$message.error(response.msg);
      }
    },
    // 上传成功（背景）
    backgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.background = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    // 上传成功（报名完关注二维码）
    subscribeImgSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.subscribeImg = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    applyBackgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.applyBackground = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    backImgSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.backImg = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    adSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.ad = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    shareUrlSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.shareUrl = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    handleRemove(file) {
      this.dataForm.pcBanner = (',' + this.dataForm.pcBanner + ',').replace(',' + file.url + ',', ',').substr(1).replace(/,$/, '')
      this.fileList.splice(this.fileList.indexOf(file), 1)
    },
    handleAppRemove(file) {
      this.dataForm.mobileBanner = (',' + this.dataForm.mobileBanner + ',').replace(',' + file.url + ',', ',').substr(1).replace(/,$/, '')
      this.appFileList.splice(this.appFileList.indexOf(file), 1)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.imgDialogVisible = true;
    },
    handleAppPictureCardPreview(file) {
      this.dialogAppImageUrl = file.url;
      this.imgAppDialogVisible = true;
    },
    dateChange(v) {
      this.dataForm.startTime = v[0];
      this.dataForm.endTime = v[1];
      console.log(v)
    }
  }
}
</script>
