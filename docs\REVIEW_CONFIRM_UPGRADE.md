# 点评功能确认跳转升级文档

## 升级概述

将点评类功能（抖音点评、大众点评、美团点评、携程点评等）从"点击即消耗"改为"点击确认跳转后再消耗"的模式，提供更好的用户体验。

## 主要改进

### 1. 用户体验优化

**原流程**：
1. 用户点击点评按钮
2. 立即消耗转发次数和文案图片
3. 显示内容预览弹窗
4. 用户确认后跳转

**新流程**：
1. 用户点击点评按钮
2. 显示内容预览弹窗（不消耗次数）
3. 用户确认跳转
4. 消耗转发次数和文案图片
5. 执行跳转

### 2. 技术实现

#### 后端API改进

为每个平台添加了两套API：

1. **预览API**（不消耗次数）：
   - `/pyp/web/activity/review/douyin/preview`
   - `/pyp/web/activity/review/dianping/preview`
   - `/pyp/web/activity/review/meituan/preview`
   - `/pyp/web/activity/review/ctrip/preview`

2. **确认API**（消耗次数）：
   - `/pyp/web/activity/review/douyin/confirm`
   - `/pyp/web/activity/review/dianping/confirm`
   - `/pyp/web/activity/review/meituan/confirm`
   - `/pyp/web/activity/review/ctrip/confirm`

#### 前端逻辑改进

1. **showReviewContent方法**：
   - 改为调用预览API
   - 不再消耗次数，只显示内容

2. **confirmReviewJump方法**：
   - 新增确认API调用
   - 成功后才执行跳转
   - 失败时显示错误信息

3. **UI改进**：
   - 确认按钮文本改为"确认跳转（消耗次数）"
   - 明确告知用户何时消耗次数

## 兼容性

### 向后兼容

保留了原有的API端点，确保旧版本客户端仍能正常工作：
- `/pyp/web/activity/review/douyin`
- `/pyp/web/activity/review/dianping`
- `/pyp/web/activity/review/meituan`
- `/pyp/web/activity/review/ctrip`

### 新旧版本对比

| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 点击按钮 | 立即消耗次数 | 仅显示预览 |
| 内容预览 | 已消耗次数 | 未消耗次数 |
| 确认跳转 | 直接跳转 | 消耗次数后跳转 |
| 取消操作 | 次数已消耗 | 次数未消耗 |

## 测试验证

### 测试场景

1. **正常流程测试**：
   - 点击点评按钮 → 显示预览 → 确认跳转 → 消耗次数 → 成功跳转

2. **取消操作测试**：
   - 点击点评按钮 → 显示预览 → 取消 → 次数未消耗

3. **次数不足测试**：
   - 点击点评按钮 → 显示预览 → 确认跳转 → 提示次数不足 → 跳转失败

4. **网络异常测试**：
   - 确认跳转时网络异常 → 显示错误信息 → 跳转失败

### 验证要点

1. **次数消耗时机**：确认只有在用户点击"确认跳转"后才消耗次数
2. **内容显示**：预览阶段能正常显示文案和图片
3. **错误处理**：次数不足或网络异常时的错误提示
4. **兼容性**：旧版本API仍能正常工作

## 部署注意事项

1. **数据库**：无需修改数据库结构
2. **缓存**：无需清理缓存
3. **配置**：无需修改配置文件
4. **依赖**：无新增依赖项

## 影响范围

### 修改文件

**后端**：
- `yqh-pyp-service/src/main/java/com/cjy/pyp/modules/activity/web/WebActivityReviewController.java`

**前端**：
- `yqh-pyp-front/src/pages/cms/Index.vue`

### 影响功能

- 抖音点评
- 大众点评  
- 美团点评
- 携程点评
- 携程笔记

## 用户收益

1. **避免误操作**：用户可以先预览内容再决定是否跳转
2. **节省次数**：取消操作不会消耗次数
3. **更好体验**：明确的操作流程和提示信息
4. **降低成本**：减少因误操作导致的次数浪费
