// import fly from 'flyio'
import router from '@/router'
import { isMobilePhone } from "@/js/validate";
import store from '../store'
import { handleTokenExpired, safeNavigateToLogin, handleNetworkError } from './errorHandler'

fly.config.timeout = 1000000;

//添加请求拦截器
fly.interceptors.request.use((request) => {
    request.headers['token'] = Vue.cookie.get('token') // 请求头带上token
    //可以显式返回request, 也可以不返回，没有返回值时拦截器中默认返回request
    return request;
})

//添加响应拦截器，响应拦截器会在then/catch处理之前执行
fly.interceptors.response.use(
    (response) => {
        if (response.status == 200) {
            let result = response.data;
            // console.log(result)
            // 401 没登录拦截到wxLogin
            if (result.code == 401) {
                console.log('Token失效，准备跳转登录页面');
                console.log('当前页面URL:', window.location.href);

                // 处理token失效
                handleTokenExpired();

                // 检查是否已经在登录页面，避免重复跳转
                if (window.location.href.includes('/mobileLogin') || window.location.href.includes('/wxLogin')) {
                    console.log('已在登录页面，跳过重复跳转');
                    return result;
                }

                if(isMobilePhone()) {
                    // 如果是手机端，安全跳转到登录页面
                    console.log('手机端，跳转到登录页面:', window.location.href);
                    safeNavigateToLogin(router, window.location.href);
                } else {
                    // 如果是电脑端，
                    store.commit("user/changePcLogin", true);
                }
            }
            if (result.code != 0 && result.code != 200 && result.code != 400) {
                console.error(result.msg);
            }
            return result
        } else {
            console.error('服务端出错');
        }
    },
    (err) => {
        //发生网络错误后会走到这里
        handleNetworkError(err);
        return Promise.reject(err);
    }
)

export default fly