<template>
  <el-dialog
    :title="!dataForm.userId ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="dataForm.username" placeholder="用户名" :disabled="!!dataForm.userId"></el-input>
        <span v-if="!!dataForm.userId" style="color: #999; font-size: 12px;">用户名不可修改</span>
      </el-form-item>
      <el-form-item label="所属渠道" prop="channelId">
        <el-select v-model="dataForm.channelId" placeholder="请选择所属渠道">
          <el-option
            v-for="channel in channelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password" v-if="!dataForm.userId">
        <el-input v-model="dataForm.password" type="password" placeholder="密码（留空默认123456）"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="password" v-if="dataForm.userId">
        <el-input v-model="dataForm.password" type="password" placeholder="新密码（留空不修改）"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        userId: 0,
        username: '',
        channelId: '',
        email: '',
        mobile: '',
        password: '',
        status: 1
      },
      channelList: [],
      dataRule: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        channelId: [
          { required: true, message: '请选择所属渠道', trigger: 'change' }
        ],
        email: [
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ],
        mobile: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.userId = id || 0
      this.visible = true
      this.getChannelList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.userId) {
          this.$http({
            url: this.$http.adornUrl(`/channel/admin/info/${this.dataForm.userId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.username = data.user.username
              this.dataForm.channelId = data.user.channelId
              this.dataForm.email = data.user.email
              this.dataForm.mobile = data.user.mobile
              this.dataForm.status = data.user.status
              this.dataForm.password = '' // 编辑时密码置空
            }
          })
        }
      })
    },
    // 获取渠道列表
    getChannelList() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.channelList || []
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/channel/admin/${!this.dataForm.userId ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'userId': this.dataForm.userId || undefined,
              'username': this.dataForm.username,
              'channelId': this.dataForm.channelId,
              'email': this.dataForm.email,
              'mobile': this.dataForm.mobile,
              'password': this.dataForm.password,
              'status': this.dataForm.status
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
