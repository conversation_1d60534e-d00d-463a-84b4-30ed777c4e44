<template>
  <div>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="匹配金额" prop="price">
        <el-input v-model="dataForm.price" placeholder="匹配金额"></el-input>
      </el-form-item>
      <el-form-item label="摘要" prop="name">
        <el-input v-model="dataForm.name" placeholder="摘要"></el-input>
      </el-form-item>
      <el-form-item :label="'关联往来款'" prop="priceTransformId">
        <el-select v-model="dataForm.priceTransformId" :placeholder="'关联往来款'" filterable>
          <el-option v-for="item in priceTransform" :key="item.id" :label="item.transformUnitName + '-' + item.price" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</div>
</template>

<script>
export default {
  data() {
    return {
      priceTransform: [],
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        type: '',
        name: '',
        price: '',
        priceConfigId: '',
        activityId: '',
        priceTransformId: '',
        matchType: 0,
        clientId: "",
        supplierId: "",
      },
      dataRule: {
        name: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        priceConfigId: [
          { required: true, message: '科目不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '会议ID不能为空', trigger: 'blur' }
        ],
        priceTransformId: [
          { required: true, message: '所属会议结算单不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.$http({
          url: this.$http.adornUrl(`/price/pricewater/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.name = data.priceWater.name
            this.dataForm.type = data.priceWater.type
            this.dataForm.price = data.priceWater.price - data.priceWater.matchPrice
            this.dataForm.priceTransformId = data.priceWater.priceTransformId
            this.getPriceTransform();
          }
        })
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    getPriceTransform() {
      this.$http({
        url: this.$http.adornUrl("/price/pricetransform/findNoPay"),
        method: "get",
        params: this.$http.adornParams({
          type: this.dataForm.type
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceTransform = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricewater/matchTransform`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'price': this.dataForm.price,
              'appid': this.$cookie.get('appid'),
              'priceTransformId': this.dataForm.priceTransformId,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
