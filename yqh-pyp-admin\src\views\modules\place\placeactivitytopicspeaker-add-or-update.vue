<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="排序，数值越小越靠前" prop="orderBy">
      <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
    </el-form-item>
    <el-form-item label="嘉宾ID" prop="activityGuestId">
      <el-input v-model="dataForm.activityGuestId" placeholder="嘉宾ID"></el-input>
    </el-form-item>
    <el-form-item label="主题ID" prop="placeActivityTopicId">
      <el-input v-model="dataForm.placeActivityTopicId" placeholder="主题ID"></el-input>
    </el-form-item>
    <el-form-item label="联系人姓名" prop="name">
      <el-input v-model="dataForm.name" placeholder="联系人姓名"></el-input>
    </el-form-item>
    <el-form-item label="联系人电话" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系人电话"></el-input>
    </el-form-item>
    <el-form-item label="工作单位" prop="unit">
      <el-input v-model="dataForm.unit" placeholder="工作单位"></el-input>
    </el-form-item>
    <el-form-item label="职称" prop="duties">
      <el-input v-model="dataForm.duties" placeholder="职称"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          orderBy: '',
          activityGuestId: '',
          placeActivityTopicId: '',
          name: '',
          mobile: '',
          unit: '',
          duties: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          orderBy: [
            { required: true, message: '排序，数值越小越靠前不能为空', trigger: 'blur' }
          ],
          activityGuestId: [
            { required: true, message: '嘉宾ID不能为空', trigger: 'blur' }
          ],
          placeActivityTopicId: [
            { required: true, message: '主题ID不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '联系人姓名不能为空', trigger: 'blur' }
          ],
          mobile: [
            { required: true, message: '联系人电话不能为空', trigger: 'blur' }
          ],
          unit: [
            { required: true, message: '工作单位不能为空', trigger: 'blur' }
          ],
          duties: [
            { required: true, message: '职称不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicspeaker/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.placeActivityTopicSpeaker.activityId
                this.dataForm.createOn = data.placeActivityTopicSpeaker.createOn
                this.dataForm.createBy = data.placeActivityTopicSpeaker.createBy
                this.dataForm.updateOn = data.placeActivityTopicSpeaker.updateOn
                this.dataForm.updateBy = data.placeActivityTopicSpeaker.updateBy
                this.dataForm.orderBy = data.placeActivityTopicSpeaker.orderBy
                this.dataForm.activityGuestId = data.placeActivityTopicSpeaker.activityGuestId
                this.dataForm.placeActivityTopicId = data.placeActivityTopicSpeaker.placeActivityTopicId
                this.dataForm.name = data.placeActivityTopicSpeaker.name
                this.dataForm.mobile = data.placeActivityTopicSpeaker.mobile
                this.dataForm.unit = data.placeActivityTopicSpeaker.unit
                this.dataForm.duties = data.placeActivityTopicSpeaker.duties
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicspeaker/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'orderBy': this.dataForm.orderBy,
                'activityGuestId': this.dataForm.activityGuestId,
                'placeActivityTopicId': this.dataForm.placeActivityTopicId,
                'name': this.dataForm.name,
                'mobile': this.dataForm.mobile,
                'unit': this.dataForm.unit,
                'duties': this.dataForm.duties
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
