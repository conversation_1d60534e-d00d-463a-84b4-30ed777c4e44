<template>
  <el-dialog :title="'导入'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      
      <el-form-item label="银行账户" prop="priceBankId">
      <el-select v-model="priceBankId" placeholder="选择要导入的银行账户" filterable>
        <el-option v-for="item in priceBank" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :disabled="!priceBankId" >
            <Upload @uploaded="getDataList" :url="'/price/pricewater/importExcel?appid=' + appid + '&priceBankId=' + priceBankId" :name="'银行流水导入'"></Upload>
        </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      priceBankId: '',
      appid: '',
      priceBank: [],
    }
  },
    components: {
      Upload: () => import('@/components/upload')
    },
  methods: {
    init() {
      this.visible = true
      this.appid = this.$cookie.get("appid");
      this.findPriceBank();
    },
    findPriceBank() {
      this.$http({
        url: this.$http.adornUrl('/price/pricebank/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.priceBank = data.result
          if(this.priceBank.length > 0) {
            this.priceBankId = this.priceBank[0].id
          }
        }
      })
    },
    getDataList() {
      this.visible = false;
      this.$emit('refreshDataList')
    }
  }
}
</script>
