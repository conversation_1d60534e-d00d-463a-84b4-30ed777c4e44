<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="模板图片地址" prop="url">
      <el-input v-model="dataForm.url" placeholder="模板图片地址"></el-input>
    </el-form-item>
    <el-form-item label="模板名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="模板名称"></el-input>
    </el-form-item>
    <el-form-item label="模板编号" prop="templateCode">
      <el-input v-model="dataForm.templateCode" placeholder="模板编号"></el-input>
    </el-form-item>
    <el-form-item label="是否显示" prop="isShow">
      <el-input v-model="dataForm.isShow" placeholder="是否显示"></el-input>
    </el-form-item>
    <el-form-item label="描述信息" prop="biref">
      <el-input v-model="dataForm.biref" placeholder="描述信息"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
                    
          url: '',
    
          name: '',
    
          templateCode: '',
    
          isShow: '',
    
          biref: ''
        },
        dataRule: {
          url: [
            { required: true, message: '模板图片地址不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '模板名称不能为空', trigger: 'blur' }
          ],
          templateCode: [
            { required: true, message: '模板编号不能为空', trigger: 'blur' }
          ],
          isShow: [
            { required: true, message: '是否显示不能为空', trigger: 'blur' }
          ],
          biref: [
            { required: true, message: '描述信息不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sys/systemplate/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.url = data.sysTemplate.url
                this.dataForm.name = data.sysTemplate.name
                this.dataForm.templateCode = data.sysTemplate.templateCode
                this.dataForm.isShow = data.sysTemplate.isShow
                this.dataForm.biref = data.sysTemplate.biref
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/sys/systemplate/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                                                                            'url': this.dataForm.url,
                            'name': this.dataForm.name,
                            'templateCode': this.dataForm.templateCode,
                            'isShow': this.dataForm.isShow,
                            'biref': this.dataForm.biref
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
