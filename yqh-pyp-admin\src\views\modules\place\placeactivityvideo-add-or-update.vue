<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="视频fileID" prop="fileId">
      <el-input v-model="dataForm.fileId" placeholder="视频fileID"></el-input>
    </el-form-item>
    <el-form-item label="视频名称" prop="filename">
      <el-input v-model="dataForm.filename" placeholder="视频名称"></el-input>
    </el-form-item>
    <el-form-item label="文件大小 单位 bit" prop="fileSize">
      <el-input v-model="dataForm.fileSize" placeholder="文件大小 单位 bit"></el-input>
    </el-form-item>
    <el-form-item label="视频时长 单位 秒" prop="duration">
      <el-input v-model="dataForm.duration" placeholder="视频时长 单位 秒"></el-input>
    </el-form-item>
    <el-form-item label="视频url" prop="mediaUrl">
      <el-input v-model="dataForm.mediaUrl" placeholder="视频url"></el-input>
    </el-form-item>
    <el-form-item label="帧率" prop="frameRate">
      <el-input v-model="dataForm.frameRate" placeholder="帧率"></el-input>
    </el-form-item>
    <el-form-item label="排序" prop="paixu">
      <el-input v-model="dataForm.paixu" placeholder="排序"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          fileId: '',
          filename: '',
          fileSize: '',
          duration: '',
          mediaUrl: '',
          expireTime: '',
          streamName: '',
          frameRate: '',
          paixu: 0,
          activityId: '',
          placeId: ''
        },
        dataRule: {
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivityvideo/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.fileId = data.placeActivityVideo.fileId
                this.dataForm.filename = data.placeActivityVideo.filename
                this.dataForm.fileSize = data.placeActivityVideo.fileSize
                this.dataForm.duration = data.placeActivityVideo.duration
                this.dataForm.mediaUrl = data.placeActivityVideo.mediaUrl
                this.dataForm.expireTime = data.placeActivityVideo.expireTime
                this.dataForm.streamName = data.placeActivityVideo.streamName
                this.dataForm.frameRate = data.placeActivityVideo.frameRate
                this.dataForm.paixu = data.placeActivityVideo.paixu
                this.dataForm.activityId = data.placeActivityVideo.activityId
                this.dataForm.placeId = data.placeActivityVideo.placeId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivityvideo/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'fileId': this.dataForm.fileId,
                'filename': this.dataForm.filename,
                'fileSize': this.dataForm.fileSize,
                'duration': this.dataForm.duration,
                'mediaUrl': this.dataForm.mediaUrl,
                'expireTime': this.dataForm.expireTime,
                'streamName': this.dataForm.streamName,
                'frameRate': this.dataForm.frameRate,
                'paixu': this.dataForm.paixu,
                'activityId': this.dataForm.activityId,
                'placeId': this.dataForm.placeId
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
