package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.salesman.entity.SalesmanQrcodeEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanQrcodeService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 业务员二维码管理控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("salesman/qrcode")
@Api(tags = {"业务员二维码管理-管理后台"})
public class SalesmanQrcodeController extends AbstractController {
    
    @Autowired
    private SalesmanQrcodeService salesmanQrcodeService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:qrcode:list")
    @ApiOperation(value = "二维码列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        PageUtils page = salesmanQrcodeService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 根据业务员ID查询二维码列表
     */
    @RequestMapping("/findBySalesmanId/{salesmanId}")
    @ApiOperation(value = "根据业务员ID查询二维码列表", notes = "")
    public R findBySalesmanId(@PathVariable("salesmanId") Long salesmanId) {
        List<SalesmanQrcodeEntity> list = salesmanQrcodeService.findBySalesmanId(salesmanId);
        return R.ok().put("result", list);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:qrcode:info")
    @ApiOperation(value = "二维码信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        SalesmanQrcodeEntity qrcode = salesmanQrcodeService.getById(id);
        return R.ok().put("qrcode", qrcode);
    }

    /**
     * 生成二维码
     */
    @RequestMapping("/generate")
    @RequiresPermissions("salesman:qrcode:generate")
    @SysLog("生成业务员二维码")
    @ApiOperation(value = "生成二维码", notes = "")
    public R generate(@RequestParam("salesmanId") Long salesmanId, 
                     @RequestParam(value = "activityId", required = false) Long activityId,
                     @CookieValue String appid) {
        try {
            SalesmanQrcodeEntity qrcode = salesmanQrcodeService.generateQrcode(salesmanId, activityId, appid);
            return R.ok().put("qrcode", qrcode);
        } catch (Exception e) {
            return R.error("生成二维码失败：" + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("salesman:qrcode:delete")
    @SysLog("删除业务员二维码")
    @ApiOperation(value = "删除二维码", notes = "")
    public R delete(@RequestBody Long[] ids) {
        salesmanQrcodeService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 启用/禁用二维码
     */
    @RequestMapping("/updateStatus")
    @RequiresPermissions("salesman:qrcode:update")
    @SysLog("更新二维码状态")
    @ApiOperation(value = "更新二维码状态", notes = "")
    public R updateStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        SalesmanQrcodeEntity qrcode = new SalesmanQrcodeEntity();
        qrcode.setId(id);
        qrcode.setStatus(status);
        salesmanQrcodeService.updateById(qrcode);
        return R.ok();
    }
}
