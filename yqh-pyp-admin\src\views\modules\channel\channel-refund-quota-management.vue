<template>
  <el-dialog
    :title="`退款名额管理 - ${channelName}`"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    
    <!-- 名额设置区域 -->
    <el-card class="quota-settings" style="margin-bottom: 20px;">
      <div slot="header">
        <span>名额设置</span>
        <el-button style="float: right; padding: 3px 0" type="text" 
          @click="refreshQuotaSettings">刷新</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form :model="quotaForm" :rules="quotaRules" ref="quotaForm" label-width="120px">
            <el-form-item label="启用名额控制" prop="enabled">
              <el-switch v-model="quotaForm.enabled" 
                active-text="启用" inactive-text="禁用"></el-switch>
            </el-form-item>
            <el-form-item label="退款名额" prop="refundQuota" v-if="quotaForm.enabled">
              <el-input-number v-model="quotaForm.refundQuota" 
                :min="0" :max="9999" placeholder="请输入退款名额"></el-input-number>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveQuotaSettings" :loading="quotaSaving">保存设置</el-button>
              <el-button type="warning" @click="batchUpdatePermissions" :loading="batchUpdating">重新分配权限</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div class="quota-stats">
            <h4>名额使用统计</h4>
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.total_quota || 0 }}</div>
                  <div class="stat-label">总名额</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.used_quota || 0 }}</div>
                  <div class="stat-label">已使用</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.available_quota || 0 }}</div>
                  <div class="stat-label">可用</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10" style="margin-top: 10px;">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.assigned_orders || 0 }}</div>
                  <div class="stat-label">有权限订单</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.refunded_orders || 0 }}</div>
                  <div class="stat-label">已退款订单</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ quotaStats.total_paid_orders || 0 }}</div>
                  <div class="stat-label">总已支付订单</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 具有退款权限的订单 -->
      <el-tab-pane label="有权限订单" name="eligible">
        <el-table :data="eligibleOrders" border v-loading="eligibleLoading" style="width: 100%;">
          <el-table-column prop="order_sn" header-align="center" align="center" label="订单号" width="180">
          </el-table-column>
          <el-table-column prop="user_name" header-align="center" align="center" label="用户">
          </el-table-column>
          <el-table-column prop="user_mobile" header-align="center" align="center" label="手机号">
          </el-table-column>
          <el-table-column prop="salesman_name" header-align="center" align="center" label="业务员">
          </el-table-column>
          <el-table-column prop="pay_amount" header-align="center" align="center" label="支付金额">
            <template slot-scope="scope">
              <span>¥{{ (scope.row.pay_amount || 0).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quota_sequence" header-align="center" align="center" label="权限序号" width="80">
            <template slot-scope="scope">
              <el-tag size="small" type="success">{{ scope.row.quota_sequence }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_on" header-align="center" align="center" label="下单时间" width="160">
          </el-table-column>
          <el-table-column header-align="center" align="center" label="操作" width="120">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="releasePermission(scope.row)">释放权限</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 名额使用记录 -->
      <el-tab-pane label="使用记录" name="records">
        <div style="margin-bottom: 10px;">
          <el-form :inline="true" :model="recordForm">
            <el-form-item label="订单号">
              <el-input v-model="recordForm.orderSn" placeholder="订单号" clearable style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="操作类型">
              <el-select v-model="recordForm.actionType" placeholder="操作类型" clearable style="width: 120px;">
                <el-option label="分配权限" value="1"></el-option>
                <el-option label="释放权限" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button @click="getRecordList">查询</el-button>
              <el-button @click="resetRecordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <el-table :data="recordList" border v-loading="recordLoading" style="width: 100%;">
          <el-table-column prop="order_sn" header-align="center" align="center" label="订单号" width="180">
          </el-table-column>
          <el-table-column prop="user_name" header-align="center" align="center" label="用户">
          </el-table-column>
          <el-table-column prop="salesman_name" header-align="center" align="center" label="业务员">
          </el-table-column>
          <el-table-column prop="action_type" header-align="center" align="center" label="操作类型" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.action_type === 1 ? 'success' : 'warning'" size="small">
                {{ scope.row.action_type === 1 ? '分配权限' : '释放权限' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="quota_sequence" header-align="center" align="center" label="序号" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.quota_sequence">{{ scope.row.quota_sequence }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" header-align="center" align="center" label="操作时间" width="160">
          </el-table-column>
          <el-table-column prop="remarks" header-align="center" align="center" label="备注">
          </el-table-column>
        </el-table>
        
        <el-pagination
          @size-change="recordSizeChangeHandle"
          @current-change="recordCurrentChangeHandle"
          :current-page="recordPageIndex"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="recordPageSize"
          :total="recordTotalPage"
          layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-tab-pane>
    </el-tabs>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      channelId: null,
      channelName: '',
      activeTab: 'eligible',
      
      // 名额设置
      quotaForm: {
        enabled: false,
        refundQuota: 0
      },
      quotaRules: {
        refundQuota: [
          { required: true, message: '请输入退款名额', trigger: 'blur' },
          { type: 'number', min: 0, message: '退款名额不能小于0', trigger: 'blur' }
        ]
      },
      quotaSaving: false,
      batchUpdating: false,
      quotaStats: {},
      
      // 有权限订单
      eligibleOrders: [],
      eligibleLoading: false,
      
      // 使用记录
      recordForm: {
        orderSn: '',
        actionType: ''
      },
      recordList: [],
      recordPageIndex: 1,
      recordPageSize: 10,
      recordTotalPage: 0,
      recordLoading: false
    }
  },
  methods: {
    init(channelId, channelName) {
      this.channelId = channelId
      this.channelName = channelName
      this.visible = true
      this.activeTab = 'eligible'
      
      this.$nextTick(() => {
        this.refreshQuotaSettings()
        this.getEligibleOrders()
      })
    },
    
    // 刷新名额设置
    refreshQuotaSettings() {
      this.$http({
        url: this.$http.adornUrl(`/channel/refund-permission/quota-usage/${this.channelId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.quotaStats = data.data || {}
          
          // 获取渠道详细信息来设置表单
          this.$http({
            url: this.$http.adornUrl(`/channel/channel/info/${this.channelId}`),
            method: 'get'
          }).then(({ data: channelData }) => {
            if (channelData && channelData.code === 200) {
              const channel = channelData.channel
              this.quotaForm.enabled = channel.refundQuotaEnabled === 1
              this.quotaForm.refundQuota = channel.refundQuota || 0
            }
          })
        }
      })
    },
    
    // 保存名额设置
    saveQuotaSettings() {
      this.$refs.quotaForm.validate((valid) => {
        if (valid) {
          this.quotaSaving = true
          this.$http({
            url: this.$http.adornUrl('/channel/refund-permission/update-quota'),
            method: 'post',
            data: this.$http.adornData({
              channelId: this.channelId,
              refundQuota: this.quotaForm.refundQuota,
              enabled: this.quotaForm.enabled
            })
          }).then(({ data }) => {
            this.quotaSaving = false
            if (data && data.code === 200) {
              this.$message.success('设置保存成功')
              this.refreshQuotaSettings()
              this.getEligibleOrders()
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg || '保存失败')
            }
          }).catch(() => {
            this.quotaSaving = false
          })
        }
      })
    },
    
    // 批量更新权限
    batchUpdatePermissions() {
      this.$confirm('确定要重新分配该渠道的退款权限吗？这将清除现有权限分配并重新计算。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchUpdating = true
        this.$http({
          url: this.$http.adornUrl(`/channel/refund-permission/batch-update/${this.channelId}`),
          method: 'post'
        }).then(({ data }) => {
          this.batchUpdating = false
          if (data && data.code === 200) {
            this.$message.success('权限重新分配成功')
            this.refreshQuotaSettings()
            this.getEligibleOrders()
            this.$emit('refreshDataList')
          } else {
            this.$message.error(data.msg || '重新分配失败')
          }
        }).catch(() => {
          this.batchUpdating = false
        })
      })
    },
    
    // 获取有权限订单
    getEligibleOrders() {
      this.eligibleLoading = true
      this.$http({
        url: this.$http.adornUrl(`/channel/refund-permission/eligible-orders/${this.channelId}`),
        method: 'get'
      }).then(({ data }) => {
        this.eligibleLoading = false
        if (data && data.code === 200) {
          this.eligibleOrders = data.data || []
        }
      }).catch(() => {
        this.eligibleLoading = false
      })
    },
    
    // 释放权限
    releasePermission(row) {
      this.$confirm(`确定要释放订单 ${row.order_sn} 的退款权限吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/channel/refund-permission/release-permission/${row.order_id}`),
          method: 'post'
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message.success('权限释放成功')
            this.refreshQuotaSettings()
            this.getEligibleOrders()
            this.$emit('refreshDataList')
          } else {
            this.$message.error(data.msg || '释放失败')
          }
        })
      })
    },
    
    // Tab切换
    handleTabClick(tab) {
      if (tab.name === 'records') {
        this.getRecordList()
      }
    },
    
    // 获取记录列表
    getRecordList() {
      this.recordLoading = true
      this.$http({
        url: this.$http.adornUrl('/channel/refund-permission/quota-records'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.recordPageIndex,
          limit: this.recordPageSize,
          channelId: this.channelId,
          orderSn: this.recordForm.orderSn,
          actionType: this.recordForm.actionType
        })
      }).then(({ data }) => {
        this.recordLoading = false
        if (data && data.code === 200) {
          this.recordList = data.page.list
          this.recordTotalPage = data.page.totalCount
        }
      }).catch(() => {
        this.recordLoading = false
      })
    },
    
    // 重置记录查询表单
    resetRecordForm() {
      this.recordForm.orderSn = ''
      this.recordForm.actionType = ''
      this.recordPageIndex = 1
      this.getRecordList()
    },
    
    // 记录分页
    recordSizeChangeHandle(val) {
      this.recordPageSize = val
      this.recordPageIndex = 1
      this.getRecordList()
    },
    recordCurrentChangeHandle(val) {
      this.recordPageIndex = val
      this.getRecordList()
    }
  }
}
</script>

<style scoped>
.quota-stats {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style>
