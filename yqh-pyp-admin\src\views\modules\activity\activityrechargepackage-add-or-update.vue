<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="套餐名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="套餐名称"></el-input>
      </el-form-item>
      <el-form-item label="套餐描述" prop="description">
        <el-input v-model="dataForm.description" type="textarea" :rows="3" placeholder="套餐描述"></el-input>
      </el-form-item>
      <el-form-item label="套餐类型" prop="packageType">
        <el-radio-group v-model="dataForm.packageType" @change="onPackageTypeChange">
          <el-radio :label="1">充值次数套餐</el-radio>
          <el-radio :label="2">创建活动套餐</el-radio>
          <el-radio :label="3">活动续费套餐</el-radio>
        </el-radio-group>
        <div style="color: #909399; font-size: 12px; margin-top: 5px;">
          充值次数套餐：为现有活动增加使用次数；创建活动套餐：购买后自动创建新活动；活动续费套餐：延长活动有效期
        </div>
      </el-form-item>
      <el-form-item label="充值次数" prop="countValue">
        <el-input-number v-model="dataForm.countValue" :min="1" :max="10000" placeholder="充值次数"></el-input-number>
      </el-form-item>
      <el-form-item label="现价(元)" prop="price">
        <el-input-number v-model="dataForm.price" :precision="2" :min="0.01" :max="9999.99" placeholder="现价" @change="onPriceChange"></el-input-number>
      </el-form-item>
      <el-form-item label="原价(元)" prop="originalPrice">
        <el-input-number v-model="dataForm.originalPrice" :precision="2" :min="0" :max="9999.99" placeholder="原价(可选)" @change="onOriginalPriceChange"></el-input-number>
      </el-form-item>
      <el-form-item label="折扣率" prop="discountRate">
        <el-input-number v-model="dataForm.discountRate" :precision="2" :min="0.01" :max="1" :step="0.01" placeholder="折扣率(0.01-1)" @change="onDiscountRateChange"></el-input-number>
        <div style="color: #909399; font-size: 12px; margin-top: 5px;">
          例如：0.8表示8折，1表示无折扣。修改折扣率会自动计算现价，修改现价会自动计算折扣率
        </div>
      </el-form-item>
      <el-form-item label="有效期(天)" prop="validDays" v-if="dataForm.packageType !== 3">
        <el-input-number v-model="dataForm.validDays" :min="1" :max="3650" placeholder="有效期天数"></el-input-number>
      </el-form-item>
      <el-form-item label="续费天数" prop="renewalDays" v-if="dataForm.packageType === 3">
        <el-input-number v-model="dataForm.renewalDays" :min="1" :max="3650" placeholder="续费天数"></el-input-number>
        <div style="color: #909399; font-size: 12px; margin-top: 5px;">
          购买此套餐后，活动有效期将延长指定天数
        </div>
      </el-form-item>
      <el-form-item label="是否热门" prop="isHot">
        <el-switch v-model="dataForm.isHot" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="是否推荐" prop="isRecommended">
        <el-switch v-model="dataForm.isRecommended" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <el-switch v-model="dataForm.status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number v-model="dataForm.sortOrder" :min="0" placeholder="排序，数值越小越靠前"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      isCalculating: false, // 防止循环计算的标志
      dataForm: {
        repeatToken: '',
        id: 0,
        name: '',
        description: '',
        packageType: 1,
        countValue: 1,
        price: 0.01,
        originalPrice: null,
        discountRate: 1,
        validDays: 365,
        renewalDays: 30,
        isHot: 0,
        isRecommended: 0,
        status: 1,
        sortOrder: 0
      },
      dataRule: {
        name: [
          { required: true, message: '套餐名称不能为空', trigger: 'blur' }
        ],
        packageType: [
          { required: true, message: '套餐类型不能为空', trigger: 'change' }
        ],
        countValue: [
          { required: true, message: '充值次数不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '现价不能为空', trigger: 'blur' }
        ],
        validDays: [
          { required: true, message: '有效期不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken()
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/rechargepackage/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.rechargePackage.name
              this.dataForm.description = data.rechargePackage.description
              this.dataForm.packageType = data.rechargePackage.packageType || 1
              this.dataForm.countValue = data.rechargePackage.countValue
              this.dataForm.price = data.rechargePackage.price
              this.dataForm.originalPrice = data.rechargePackage.originalPrice
              this.dataForm.discountRate = data.rechargePackage.discountRate
              this.dataForm.validDays = data.rechargePackage.validDays
              this.dataForm.renewalDays = data.rechargePackage.renewalDays || 30
              this.dataForm.isHot = data.rechargePackage.isHot
              this.dataForm.isRecommended = data.rechargePackage.isRecommended
              this.dataForm.status = data.rechargePackage.status
              this.dataForm.sortOrder = data.rechargePackage.sortOrder
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl('/common/createToken'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result
        }
      })
    },
    // 折扣率改变时计算现价
    onDiscountRateChange(value) {
      if (this.isCalculating) return
      if (value && this.dataForm.originalPrice && this.dataForm.originalPrice > 0) {
        this.isCalculating = true
        this.dataForm.price = parseFloat((this.dataForm.originalPrice * value).toFixed(2))
        this.$nextTick(() => {
          this.isCalculating = false
        })
      }
    },
    // 现价改变时计算折扣率
    onPriceChange(value) {
      if (this.isCalculating) return
      if (value && this.dataForm.originalPrice && this.dataForm.originalPrice > 0) {
        this.isCalculating = true
        this.dataForm.discountRate = parseFloat((value / this.dataForm.originalPrice).toFixed(2))
        // 确保折扣率在合理范围内
        if (this.dataForm.discountRate > 1) {
          this.dataForm.discountRate = 1
        }
        if (this.dataForm.discountRate < 0.01) {
          this.dataForm.discountRate = 0.01
        }
        this.$nextTick(() => {
          this.isCalculating = false
        })
      }
    },
    // 原价改变时重新计算现价
    onOriginalPriceChange(value) {
      if (this.isCalculating) return
      if (value && this.dataForm.discountRate && this.dataForm.discountRate > 0) {
        this.isCalculating = true
        this.dataForm.price = parseFloat((value * this.dataForm.discountRate).toFixed(2))
        this.$nextTick(() => {
          this.isCalculating = false
        })
      }
    },
    // 套餐类型改变
    onPackageTypeChange(value) {
      if (value === 3) {
        // 续费套餐，设置默认值
        this.dataForm.countValue = 0
        this.dataForm.renewalDays = 30
      } else {
        // 其他类型套餐
        this.dataForm.countValue = 1
        this.dataForm.validDays = 365
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/rechargepackage/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'description': this.dataForm.description,
              'packageType': this.dataForm.packageType,
              'countValue': this.dataForm.countValue,
              'price': this.dataForm.price,
              'originalPrice': this.dataForm.originalPrice,
              'discountRate': this.dataForm.discountRate,
              'validDays': this.dataForm.validDays,
              'renewalDays': this.dataForm.renewalDays,
              'isHot': this.dataForm.isHot,
              'isRecommended': this.dataForm.isRecommended,
              'status': this.dataForm.status,
              'sortOrder': this.dataForm.sortOrder
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken()
              }
            }
          })
        }
      })
    }
  }
}
</script>
