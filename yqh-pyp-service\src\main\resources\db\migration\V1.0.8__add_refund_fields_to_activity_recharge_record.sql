-- 为activity_recharge_record表添加退款相关字段

-- 1. 添加退款金额字段
ALTER TABLE `activity_recharge_record` 
ADD COLUMN `refund_amount` decimal(10,2) DEFAULT NULL COMMENT '退款金额' AFTER `pay_amount`;

-- 2. 添加退款时间字段
ALTER TABLE `activity_recharge_record` 
ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退款时间' AFTER `refund_amount`;

-- 3. 添加索引优化查询性能
ALTER TABLE `activity_recharge_record` 
ADD INDEX `idx_refund_status` (`status`, `refund_time`);

-- 4. 更新状态枚举注释
ALTER TABLE `activity_recharge_record` 
MODIFY COLUMN `status` int(11) DEFAULT NULL COMMENT '状态：0-待支付，1-已支付，2-已取消，3-已退款，4-退款中';
