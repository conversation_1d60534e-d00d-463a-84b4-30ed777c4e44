<template>
  <el-dialog title="获取直播间流量" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="查询时间">
        <!-- :disabled="dataForm.pushStatus == 1" -->
        <el-date-picker style="width: 100%" v-model="dataForm.startTime" @change="dateChange" type="date" placeholder="请选择查询时间"
          value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <div class="mod-user">
        <div style="display: flex;align-items: center;justify-content: center;">
          <div style="margin-left: 20px;font-size: 24px;font-weight: 600;">直播间流量：{{ number }}G</div>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {addMin} from "@/utils/date.js";
export default {
  data() {
    return {
      wxAccount: {},
      appid: '',
      number: 0,
      visible: false,
      dataForm: {
        id: 0,
        startTime: '',
        endTime: '',
        pushKey:''
      },
      dataRule: {
        
        startTime: [
          { required: true, message: "推流过期时间不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init(id) {
      this.appid = this.$cookie.get("appid");
      this.dataForm.id = id;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.getPlaceInfo();
      });
    },
    dateChange(val) {
      this.dataForm.endTime = addMin(val,1440, "yyyy-MM-dd hh:mm:ss");
    },
    getPlaceInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/info/${this.dataForm.id}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.pushKey = data.placeActivity.pushKey;
        }
      });
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
          this.getPlaceInfo();
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/place/placeactivityvideo/getLiveNumber`),
            method: "get",
            params: this.$http.adornParams({
              pushKey: this.dataForm.pushKey,
              startTime: this.dataForm.startTime,
              endTime: this.dataForm.endTime,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              // this.$message({
              //   message: "操作成功",
              //   type: "success",
              //   duration: 300,
              //   onClose: () => {
                  this.number = data.result;
                // },
              // });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
  },
};
</script>
