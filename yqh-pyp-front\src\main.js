// import Vue from 'vue'
import App from './App.vue'
import VueCookie from 'vue-cookie'
import router from './router'
import request from './js/request'
import wxShare from './js/wxShare'
import store from './store'
import { setupGlobalErrorHandler } from './js/errorHandler'

Vue.use(VueCookie)
Vue.config.productionTip = false
Vue.prototype.$fly = request
Vue.prototype.$toast = vant.Toast
Vue.prototype.$wxShare = wxShare

// 设置全局错误处理器
setupGlobalErrorHandler()

// 设置Vue错误处理器
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err, info);
  // 可以在这里添加错误上报逻辑
}

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

vant.Toast.setDefaultOptions({position:'bottom'})