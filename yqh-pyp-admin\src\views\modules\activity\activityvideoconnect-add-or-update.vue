<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
    <el-form-item label="会议id" prop="activityId">
              <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
          </el-form-item>
    <el-form-item label="" prop="paixu">
              <el-input v-model="dataForm.paixu" placeholder=""></el-input>
          </el-form-item>
    <el-form-item label="" prop="activityVideoId">
              <el-input v-model="dataForm.activityVideoId" placeholder=""></el-input>
          </el-form-item>
    <el-form-item label="" prop="connectActivityVideoId">
              <el-input v-model="dataForm.connectActivityVideoId" placeholder=""></el-input>
          </el-form-item>
    <el-form-item label="" prop="activityImageId">
              <el-input v-model="dataForm.activityImageId" placeholder=""></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
    
          activityId: '',
                    
          paixu: '',
    
          activityVideoId: '',
    
          connectActivityVideoId: '',
    
          activityImageId: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          paixu: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          activityVideoId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          connectActivityVideoId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          activityImageId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityvideoconnect/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityVideoConnect.activityId
                this.dataForm.paixu = data.activityVideoConnect.paixu
                this.dataForm.activityVideoId = data.activityVideoConnect.activityVideoId
                this.dataForm.connectActivityVideoId = data.activityVideoConnect.connectActivityVideoId
                this.dataForm.activityImageId = data.activityVideoConnect.activityImageId
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityvideoconnect/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                            'activityId': this.dataForm.activityId,
                                                                                                                            'paixu': this.dataForm.paixu,
                            'activityVideoId': this.dataForm.activityVideoId,
                            'connectActivityVideoId': this.dataForm.connectActivityVideoId,
                            'activityImageId': this.dataForm.activityImageId,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
