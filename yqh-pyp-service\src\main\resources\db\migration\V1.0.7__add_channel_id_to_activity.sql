-- 为活动表添加渠道ID字段，实现渠道权限管理
-- 版本: V1.0.7
-- 作者: cjy
-- 日期: 2025-01-23

-- 1. 为 tb_activity 表添加 channel_id 字段
ALTER TABLE `tb_activity` ADD COLUMN `channel_id` bigint(20) DEFAULT NULL COMMENT '所属渠道ID（通过创建者的渠道归属确定）' AFTER `appid`;
ALTER TABLE `tb_activity` ADD KEY `idx_tb_activity_channel_id` (`channel_id`);
ALTER TABLE `tb_activity` ADD CONSTRAINT `fk_tb_activity_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE SET NULL;

-- 2. 为 wx_user 表添加 channel_id 字段
ALTER TABLE `wx_user` ADD COLUMN `channel_id` bigint(20) DEFAULT NULL COMMENT '所属渠道ID（通过业务员绑定关系确定）' AFTER `appid`;
ALTER TABLE `wx_user` ADD KEY `idx_wx_user_channel_id` (`channel_id`);
ALTER TABLE `wx_user` ADD CONSTRAINT `fk_wx_user_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE SET NULL;

-- 3. 为 activity_recharge_record 表添加索引优化（如果还没有的话）
-- 这个表已经有 appid 字段，可以通过关联查询获取渠道信息
ALTER TABLE `activity_recharge_record` ADD KEY `idx_activity_recharge_record_appid_salesman` (`appid`, `salesman_id`) IF NOT EXISTS;

-- 4. 历史数据处理：根据微信用户的业务员绑定关系更新微信用户的渠道ID
UPDATE `wx_user` wu
INNER JOIN `wx_user_salesman_binding` wsb ON wu.id = wsb.wx_user_id
INNER JOIN `salesman` s ON wsb.salesman_id = s.id
SET wu.channel_id = s.channel_id
WHERE wu.channel_id IS NULL
  AND wsb.status = 1
  AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
  AND s.channel_id IS NOT NULL;

-- 5. 历史数据处理：根据活动创建者的渠道归属更新活动的渠道ID
-- 这个脚本会将活动的渠道ID设置为创建者所属的渠道ID
UPDATE `tb_activity` a
INNER JOIN `wx_user_salesman_binding` wsb ON a.create_by = wsb.wx_user_id
INNER JOIN `salesman` s ON wsb.salesman_id = s.id
SET a.channel_id = s.channel_id
WHERE a.channel_id IS NULL
  AND wsb.status = 1
  AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
  AND s.channel_id IS NOT NULL;

-- 6. 对于没有业务员绑定关系的活动，尝试通过系统用户的渠道归属来设置
UPDATE `tb_activity` a
INNER JOIN `sys_user` su ON a.create_by = su.user_id
SET a.channel_id = su.channel_id
WHERE a.channel_id IS NULL
  AND su.channel_id IS NOT NULL;

-- 7. 创建视图用于快速查询渠道相关的活动统计（可选）
CREATE OR REPLACE VIEW `v_channel_activity_stats` AS
SELECT 
    c.id as channel_id,
    c.name as channel_name,
    COUNT(a.id) as total_activities,
    COUNT(CASE WHEN a.is_expired = 0 THEN 1 END) as active_activities,
    COUNT(CASE WHEN a.is_expired = 1 THEN 1 END) as expired_activities,
    SUM(COALESCE(a.all_count, 0)) as total_all_count,
    SUM(COALESCE(a.use_count, 0)) as total_use_count,
    SUM(COALESCE(a.pv_count, 0)) as total_pv_count,
    SUM(COALESCE(a.uv_count, 0)) as total_uv_count
FROM `channel` c
LEFT JOIN `tb_activity` a ON c.id = a.channel_id
WHERE c.status = 1
GROUP BY c.id, c.name;

-- 8. 创建渠道客户统计视图
CREATE OR REPLACE VIEW `v_channel_customer_stats` AS
SELECT
    c.id as channel_id,
    c.name as channel_name,
    COUNT(wu.id) as total_customers,
    COUNT(CASE WHEN wu.status = 1 THEN 1 END) as active_customers,
    COUNT(CASE WHEN wsb.id IS NOT NULL THEN 1 END) as bound_customers,
    COUNT(CASE WHEN arr.id IS NOT NULL THEN 1 END) as paying_customers
FROM `channel` c
LEFT JOIN `wx_user` wu ON c.id = wu.channel_id
LEFT JOIN `wx_user_salesman_binding` wsb ON wu.id = wsb.wx_user_id AND wsb.status = 1
LEFT JOIN `activity_recharge_record` arr ON wu.id = arr.user_id AND arr.status = 1
WHERE c.status = 1
GROUP BY c.id, c.name;

-- 9. 添加注释说明
ALTER TABLE `tb_activity` COMMENT = '活动表，包含渠道归属信息用于权限控制';
ALTER TABLE `wx_user` COMMENT = '微信用户表，包含渠道归属信息用于权限控制';
