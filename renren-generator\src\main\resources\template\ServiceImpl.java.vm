package ${package}.${moduleName}.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${mainPath}.common.utils.PageUtils;
import ${mainPath}.common.utils.Query;

import ${package}.${moduleName}.dao.${className}Dao;
import ${package}.${moduleName}.entity.${className}Entity;
import ${package}.${moduleName}.service.${className}Service;


@Service("${classname}Service")
public class ${className}ServiceImpl extends ServiceImpl<${className}Dao, ${className}Entity> implements ${className}Service {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<${className}Entity> page = this.page(
                new Query<${className}Entity>().getPage(params),
                new QueryWrapper<${className}Entity>()
        );

        return new PageUtils(page);
    }

    @Override
    public List<${className}Entity> findByIds(List<Long> ids) {
        return ids.size() > 0 ? this.listByIds(ids) : new ArrayList<>();
    }

}