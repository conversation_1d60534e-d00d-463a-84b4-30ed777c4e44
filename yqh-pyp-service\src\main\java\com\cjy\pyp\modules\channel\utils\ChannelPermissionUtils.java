package com.cjy.pyp.modules.channel.utils;

import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.sys.service.SysRoleService;
import com.cjy.pyp.modules.sys.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 渠道权限工具类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@Component
public class ChannelPermissionUtils {

    @Autowired
    private ChannelService channelService;
    
    @Autowired
    private SysUserRoleService sysUserRoleService;

    /**
     * 渠道管理员角色ID
     */
    public static final Long CHANNEL_ADMIN_ROLE_ID = 1000000000000000001L;

    /**
     * 检查用户是否为渠道管理员
     * @param userId 用户ID
     * @return 是否为渠道管理员
     */
    public boolean isChannelAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        
        List<Long> roleIds = sysUserRoleService.queryRoleIdList(userId);
        return roleIds.contains(CHANNEL_ADMIN_ROLE_ID);
    }

    /**
     * 检查渠道管理员是否有权限访问指定业务员
     * @param user 当前用户
     * @param salesmanId 业务员ID
     * @return 是否有权限
     */
    public boolean canAccessSalesman(SysUserEntity user, Long salesmanId) {
        if (user == null || salesmanId == null) {
            return false;
        }

        // 如果不是渠道管理员，按原有逻辑处理
        if (!isChannelAdmin(user.getUserId())) {
            return true; // 非渠道管理员按原有权限控制
        }

        // 渠道管理员只能访问自己渠道的业务员
        if (user.getChannelId() == null) {
            return false;
        }

        return channelService.hasAccessToSalesman(user.getChannelId(), salesmanId);
    }

    /**
     * 检查渠道管理员是否有权限访问指定活动
     * @param user 当前用户
     * @param activityId 活动ID
     * @return 是否有权限
     */
    public boolean canAccessActivity(SysUserEntity user, Long activityId) {
        if (user == null || activityId == null) {
            return false;
        }

        // 如果不是渠道管理员，按原有逻辑处理
        if (!isChannelAdmin(user.getUserId())) {
            return true; // 非渠道管理员按原有权限控制
        }

        // 渠道管理员只能访问自己渠道相关的活动
        if (user.getChannelId() == null) {
            return false;
        }

        return channelService.hasAccessToActivity(user.getChannelId(), activityId);
    }

    /**
     * 获取渠道管理员可访问的渠道ID列表（包括子渠道）
     * @param user 当前用户
     * @return 渠道ID列表
     */
    public List<Long> getAccessibleChannelIds(SysUserEntity user) {
        if (user == null || user.getChannelId() == null) {
            return null;
        }

        if (!isChannelAdmin(user.getUserId())) {
            return null; // 非渠道管理员返回null，表示无限制
        }

        // 返回当前渠道及其所有子渠道
        List<Long> channelIds = channelService.getAllChildChannelIds(user.getChannelId());
        channelIds.add(0, user.getChannelId()); // 添加当前渠道到列表开头
        return channelIds;
    }

    /**
     * 检查渠道管理员是否有权限管理指定渠道
     * @param user 当前用户
     * @param targetChannelId 目标渠道ID
     * @return 是否有权限
     */
    public boolean canManageChannel(SysUserEntity user, Long targetChannelId) {
        if (user == null || targetChannelId == null) {
            return false;
        }

        // 如果不是渠道管理员，按原有逻辑处理
        if (!isChannelAdmin(user.getUserId())) {
            return true; // 非渠道管理员按原有权限控制
        }

        // 渠道管理员只能管理自己的渠道和子渠道
        if (user.getChannelId() == null) {
            return false;
        }

        // 检查是否为当前渠道或子渠道
        if (user.getChannelId().equals(targetChannelId)) {
            return true;
        }

        List<Long> childChannelIds = channelService.getAllChildChannelIds(user.getChannelId());
        return childChannelIds.contains(targetChannelId);
    }

    /**
     * 检查渠道管理员是否有权限访问指定订单
     * @param user 当前用户
     * @param orderId 订单ID
     * @return 是否有权限
     */
    public boolean canAccessOrder(SysUserEntity user, Long orderId) {
        if (user == null || orderId == null) {
            return false;
        }

        // 如果不是渠道管理员，按原有逻辑处理
        if (!isChannelAdmin(user.getUserId())) {
            return true; // 非渠道管理员按原有权限控制
        }

        // 渠道管理员只能访问自己渠道相关的订单
        if (user.getChannelId() == null) {
            return false;
        }

        return channelService.hasAccessToOrder(user.getChannelId(), orderId);
    }

    /**
     * 检查渠道管理员是否有权限访问指定客户
     * @param user 当前用户
     * @param wxUserId 微信用户ID
     * @return 是否有权限
     */
    public boolean canAccessCustomer(SysUserEntity user, Long wxUserId) {
        if (user == null || wxUserId == null) {
            return false;
        }

        // 如果不是渠道管理员，按原有逻辑处理
        if (!isChannelAdmin(user.getUserId())) {
            return true; // 非渠道管理员按原有权限控制
        }

        // 渠道管理员只能访问自己渠道业务员绑定的客户
        if (user.getChannelId() == null) {
            return false;
        }

        return channelService.hasAccessToCustomer(user.getChannelId(), wxUserId);
    }

    /**
     * 为查询参数添加渠道权限过滤
     * @param user 当前用户
     * @param params 查询参数
     */
    public void applyChannelFilter(SysUserEntity user, java.util.Map<String, Object> params) {
        if (user == null || params == null) {
            return;
        }

        // 只对渠道管理员应用过滤
        if (isChannelAdmin(user.getUserId()) && user.getChannelId() != null) {
            List<Long> accessibleChannelIds = getAccessibleChannelIds(user);
            if (accessibleChannelIds != null && !accessibleChannelIds.isEmpty()) {
                params.put("channelIds", accessibleChannelIds);
            }
        }
    }
}
