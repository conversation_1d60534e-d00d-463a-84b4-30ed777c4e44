<template>
  <div class="mod-config">
    <div style="text-align: center; padding: 20px; font-weight: bold; font-size: 28px">创建活动套餐购买</div>
    
    <!-- 套餐选择 -->
    <el-card class="package-selection" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">选择套餐</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8" v-for="pkg in packageList" :key="pkg.id">
          <el-card 
            :class="['package-card', { 'selected': selectedPackage && selectedPackage.id === pkg.id }]"
            @click.native="selectPackage(pkg)"
            style="cursor: pointer; margin-bottom: 15px;">
            <div class="package-header">
              <h3>{{ pkg.name }}</h3>
              <el-tag v-if="pkg.isHot" type="danger" size="small">热门</el-tag>
              <el-tag v-if="pkg.isRecommended" type="success" size="small">推荐</el-tag>
            </div>
            <div class="package-content">
              <p class="package-description">{{ pkg.description }}</p>
              <div class="package-details">
                <p><strong>使用次数：</strong>{{ pkg.countValue }}次</p>
                <p><strong>有效期：</strong>{{ pkg.validDays }}天</p>
              </div>
              <div class="package-price">
                <span class="current-price">¥{{ pkg.price }}</span>
                <span v-if="pkg.originalPrice && pkg.originalPrice > pkg.price" class="original-price">¥{{ pkg.originalPrice }}</span>
                <span v-if="pkg.discountRate && pkg.discountRate < 1" class="discount">{{ (pkg.discountRate * 10).toFixed(1) }}折</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 活动信息填写 -->
    <el-card v-if="selectedPackage" class="activity-form">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">活动信息</span>
      </div>
      <el-form :model="orderForm" :rules="orderRules" ref="orderForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="activityName">
              <el-input v-model="orderForm.activityName" placeholder="请输入活动名称" maxlength="50" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动模板" prop="activityTemplateId">
              <el-select v-model="orderForm.activityTemplateId" placeholder="选择活动模板（可选）" clearable style="width: 100%">
                <el-option
                  v-for="template in templateList"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id">
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                选择模板可以快速复制活动配置，不选择则使用默认配置
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="orderForm.remarks" type="textarea" :rows="3" placeholder="备注信息（可选）" maxlength="200" show-word-limit></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单确认 -->
    <el-card v-if="selectedPackage" class="order-summary" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">订单确认</span>
      </div>
      <div class="summary-content">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="order-details">
              <h4>{{ selectedPackage.name }}</h4>
              <p>{{ selectedPackage.description }}</p>
              <p><strong>活动名称：</strong>{{ orderForm.activityName || '未填写' }}</p>
              <p><strong>使用次数：</strong>{{ selectedPackage.countValue }}次</p>
              <p><strong>有效期：</strong>{{ selectedPackage.validDays }}天</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="price-summary">
              <div class="total-price">
                <span class="label">总计：</span>
                <span class="price">¥{{ selectedPackage.price }}</span>
              </div>
              <el-button type="primary" size="large" @click="createOrder" :loading="submitting" style="width: 100%; margin-top: 15px;">
                立即购买
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 返回按钮 -->
    <div style="text-align: center; margin-top: 30px;">
      <el-button type="success" @click="$router.go(-1)">返回</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      packageList: [],
      templateList: [],
      selectedPackage: null,
      submitting: false,
      orderForm: {
        activityName: '',
        activityTemplateId: '',
        remarks: ''
      },
      orderRules: {
        activityName: [
          { required: true, message: '活动名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '活动名称长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  activated() {
    this.getPackageList()
    this.getTemplateList()
  },
  methods: {
    // 获取创建活动套餐列表
    getPackageList() {
      // 优先使用专门的API接口
      this.$http({
        url: this.$http.adornUrl('/web/activity/recharge/packages/createActivity'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.packageList = data.packages || []
        } else {
          // 如果专门的接口不可用，回退到通用接口
          this.$http({
            url: this.$http.adornUrl('/activity/rechargepackage/list'),
            method: 'get',
            params: this.$http.adornParams({
              'page': 1,
              'limit': 100,
              'packageType': 2, // 只获取创建活动套餐
              'status': 1 // 只获取启用的套餐
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.packageList = data.page.list
            } else {
              this.packageList = []
            }
          })
        }
      }).catch(() => {
        // 如果专门的接口出错，回退到通用接口
        this.$http({
          url: this.$http.adornUrl('/activity/rechargepackage/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': 1,
            'limit': 100,
            'packageType': 2, // 只获取创建活动套餐
            'status': 1 // 只获取启用的套餐
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.packageList = data.page.list
          } else {
            this.packageList = []
          }
        })
      })
    },
    // 获取活动模板列表
    getTemplateList() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 100,
          'isTemplate': 1 // 只获取模板活动
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.templateList = data.page.list
        } else {
          this.templateList = []
        }
      })
    },
    // 选择套餐
    selectPackage(pkg) {
      this.selectedPackage = pkg
      // 清空表单
      this.orderForm = {
        activityName: '',
        activityTemplateId: '',
        remarks: ''
      }
      this.$nextTick(() => {
        if (this.$refs.orderForm) {
          this.$refs.orderForm.clearValidate()
        }
      })
    },
    // 创建订单
    createOrder() {
      this.$refs.orderForm.validate((valid) => {
        if (valid) {
          this.submitting = true
          
          // 获取防重令牌
          this.$http({
            url: this.$http.adornUrl('/common/createToken'),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              const repeatToken = data.result
              
              // 创建订单
              this.$http({
                url: this.$http.adornUrl('/web/activity/recharge/createActivityPackageOrder'),
                method: 'post',
                data: this.$http.adornData({
                  packageId: this.selectedPackage.id,
                  activityName: this.orderForm.activityName,
                  activityTemplateId: this.orderForm.activityTemplateId || undefined,
                  remarks: this.orderForm.remarks,
                  repeatToken: repeatToken
                })
              }).then(({ data }) => {
                this.submitting = false
                if (data && data.code === 200) {
                  this.$message({
                    message: '订单创建成功！订单号：' + data.orderSn,
                    type: 'success',
                    duration: 3000
                  })
                  // 跳转到充值记录页面
                  this.$router.push({ name: 'activity-activityrechargerecord' })
                } else {
                  this.$message.error(data.msg || '订单创建失败')
                }
              }).catch(() => {
                this.submitting = false
              })
            } else {
              this.submitting = false
              this.$message.error('获取令牌失败')
            }
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.package-card {
  transition: all 0.3s;
  border: 2px solid transparent;
}

.package-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.package-card.selected {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.package-header h3 {
  margin: 0;
  color: #303133;
}

.package-description {
  color: #606266;
  margin-bottom: 15px;
  min-height: 40px;
}

.package-details p {
  margin: 5px 0;
  color: #909399;
}

.package-price {
  text-align: right;
  margin-top: 15px;
}

.current-price {
  font-size: 24px;
  font-weight: bold;
  color: #f56c6c;
}

.original-price {
  font-size: 14px;
  color: #909399;
  text-decoration: line-through;
  margin-left: 10px;
}

.discount {
  font-size: 12px;
  color: #67c23a;
  margin-left: 10px;
}

.order-details h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.order-details p {
  margin: 5px 0;
  color: #606266;
}

.price-summary {
  text-align: right;
}

.total-price {
  font-size: 18px;
  margin-bottom: 15px;
}

.total-price .label {
  color: #606266;
}

.total-price .price {
  font-weight: bold;
  color: #f56c6c;
  font-size: 24px;
}
</style>
