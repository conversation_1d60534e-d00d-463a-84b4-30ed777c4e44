<template>
  <van-dialog v-model="showPcLogin" title="登录操作" @confirm="login" width="400px">
    <div class="text-center padding">
      <van-cell-group inset>
        <van-field v-model="mobile" name="手机号" label="手机号" required placeholder="手机号"
          :rules="[{ required: true, message: '请填写手机号' }]" />
        <van-field v-if="activityId != '1736999159118508033'" v-model="code" center clearable maxlength="6" label="短信验证码" required placeholder="请输入短信验证码">
          <template #button>
            <van-button size="small" type="primary" :disabled="waiting" @click="doSendSmsCode()">
              <span v-if="waiting">{{ waitingTime }}秒后重发</span>
              <span v-else style="font-size: 13px">获取验证码</span>
            </van-button></template>
        </van-field>
      </van-cell-group>
    </div>
  </van-dialog>
</template>

<script>
import { isMobile } from "@/js/validate";
export default {
  name: "pclogin",
  computed: {
    showPcLogin: {
      get() {
        return this.$store.state.user.showPcLogin;
      },
      set(val) {
        this.$store.commit("user/changePcLogin", val);
      },
    },
  },
  data() {
    return {
      activityId: undefined,
      waiting: false,
      waitingTime: 60,
      mobile: "",
      code: "",
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
  },
  methods: {
    login() {
      if (!this.mobile) {
        vant.Toast("请输入手机号");
        return false;
      }
      if (!isMobile(this.mobile)) {
        vant.Toast("手机号格式错误");
        return false;
      }
      if (!this.code && this.activityId != 1736999159118508033) {
        vant.Toast("请输入验证码");
        return false;
      }
      if (!/^\d{6}$/.test(this.code) && this.activityId != 1736999159118508033) {
        vant.Toast("验证码错误");
        return false;
      }
      // 保存
      this.$fly
        .post(
          "/pyp/web/user/pcLogin",
          {
            mobile: this.mobile,
            code: this.activityId == 1736999159118508033 ? '121212' : this.code,
          }
        )
        .then((res) => {
          if (res && res.code === 200) {
            vant.Toast("登录成功");
            this.$store.commit("user/update", res.userInfo);
            this.showPcLogin = false;
            if (this.$route.name == 'applyIndex' || this.$route.name == 'livesDetail') {
              location.reload();
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    doSendSmsCode() {
      if (!this.mobile) {
        vant.Toast("请输入手机号");
        return false;
      }
      if (!isMobile(this.mobile)) {
        vant.Toast("手机号格式错误");
        return false;
      }
      this.$fly
        .post("/pyp/sms/sms/send", {
          mobile: this.mobile,
          activityId: this.activityId,
        })
        .then((res) => {
          if (res && res.code === 200) {
            this.countdown();
            vant.Toast("发送验证码成功");
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    countdown() {
      this.waiting = true
      let clock = window.setInterval(() => {
        this.waitingTime--
        if (this.waitingTime < 0) {
          window.clearInterval(clock)
          this.waitingTime = 60
          this.waiting = false
        }
      }, 1000)
    },
  },

};
</script>