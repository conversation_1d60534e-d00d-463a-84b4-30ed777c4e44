<template>
  <div class="expiration-status">
    <!-- 过期状态显示 -->
    <div v-if="status" class="status-container">
      <!-- 已过期 -->
      <div v-if="status.isExpired" class="status-item expired">
        <van-icon name="warning-o" />
        <span class="status-text">活动已过期</span>
        <span class="status-time">{{ formatDate(status.expirationTime) }}</span>
        <van-button 
          v-if="showRenewalButton" 
          type="danger" 
          size="mini" 
          @click="$emit('renew')"
        >
          立即续费
        </van-button>
      </div>
      
      <!-- 即将过期 -->
      <div v-else-if="status.isExpiringSoon" class="status-item expiring-soon">
        <van-icon name="clock-o" />
        <span class="status-text">即将过期</span>
        <span class="status-time">剩余{{ status.remainingDays }}天</span>
        <van-button 
          v-if="showRenewalButton" 
          type="warning" 
          size="mini" 
          @click="$emit('renew')"
        >
          续费
        </van-button>
      </div>
      
      <!-- 正常状态 -->
      <div v-else class="status-item normal">
        <van-icon name="success" />
        <span class="status-text">正常使用中</span>
        <span v-if="status.expirationTime" class="status-time">
          {{ status.remainingDays }}天后过期
        </span>
        <span v-else class="status-time">永不过期</span>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-container">
      <van-loading size="16px" />
      <span>获取过期状态中...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else class="error-container">
      <van-icon name="warning-o" />
      <span>无法获取过期状态</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityExpirationStatus',
  props: {
    // 过期状态对象
    status: {
      type: Object,
      default: null
    },
    // 是否显示续费按钮
    showRenewalButton: {
      type: Boolean,
      default: true
    },
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
};
</script>

<style scoped>
.expiration-status {
  width: 100%;
}

.status-container {
  width: 100%;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  gap: 8px;
}

.status-item.expired {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.status-item.expiring-soon {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.status-item.normal {
  background-color: #f0f9ff;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

.status-text {
  font-weight: 500;
}

.status-time {
  font-size: 12px;
  opacity: 0.8;
  margin-left: auto;
  margin-right: 8px;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  color: #909399;
  font-size: 14px;
  gap: 8px;
}

.error-container {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .status-item {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .status-time {
    font-size: 11px;
  }
}
</style>
