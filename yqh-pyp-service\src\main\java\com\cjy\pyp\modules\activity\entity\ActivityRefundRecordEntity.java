package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款记录表
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@TableName("activity_refund_record")
public class ActivityRefundRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 充值记录ID
     */
    private Long rechargeRecordId;

    /**
     * 原订单号
     */
    private String originalOrderSn;

    /**
     * 退款单号
     */
    private String refundOrderSn;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 原支付金额
     */
    private BigDecimal originalAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 支付方式：1-微信支付，2-支付宝
     */
    private Integer payType;

    /**
     * 退款状态：0-申请中，1-审核通过，2-退款成功，3-退款失败，4-审核拒绝
     */
    private Integer status;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 第三方退款交易号
     */
    private String refundTransactionId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 退款完成时间
     */
    private Date refundTime;

    /**
     * 审核人ID
     */
    private Long auditBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remarks;
}
