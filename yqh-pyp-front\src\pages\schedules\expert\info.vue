<template>
    <div :class="isMobilePhone ? '' : 'pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <van-card style="background: white"
            :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
            <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
            <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary"
                    plain>{{
                        guestInfo.unit }}</van-tag>
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning"
                    plain>{{
                        guestInfo.duties }}</van-tag>
            </div>
        </van-card>
        <div style="margin-top: 8px" class="nav-title">
            <div class="color"></div>
            <div class="text">专家基本信息确认</div>
        </div>
        <van-cell-group inset>
            <van-field v-model="guestInfo.name" name="专家姓名" label="专家姓名" required
                :rules="[{ required: true, message: '请填写专家姓名' }]">
            </van-field>
            <van-field v-model="guestInfo.mobile" name="联系方式" label="联系方式" required
                :rules="[{ required: true, message: '请填写联系方式' }]">
            </van-field>

            <van-cell center title="是否参会">
                <template #right-icon>
                    <van-switch v-model="guestInfo.isAttend" size="24" />
                </template>
            </van-cell>
            <div v-if="guestInfo.isAttend" style="font-size: 12px;padding: 5px 10px;color: red;">
                专家头像用于简介（必填）
            </div>
            <van-cell title="专家头像" :required="guestInfo.isAttend">
                <van-uploader :after-read="afterRead" name="avatar" :before-read="beforeRead" accept="image/*">
                    <van-icon v-if="!guestInfo.avatar" slot="default" name="plus" size="50px"
                        style="margin-left: 5px"></van-icon>
                    <van-image v-else height="50px" :src="guestInfo.avatar" fit="contain" />
                </van-uploader>
            </van-cell>
            <van-field v-model="guestInfo.unit" name="工作单位" label="工作单位" required
                :rules="[{ required: true, message: '请填写工作单位' }]">
            </van-field>
            <!-- <van-cell title="职务/职称" :value="guestInfo.duties" @click="dutiesShow = true" is-link required>
            </van-cell> -->

            <van-field v-model="guestInfo.duties" center clearable label="职务/职称" required placeholder="请输入职务/职称">
                <template #button>
                    <van-button size="small" type="primary" @click="dutiesShow = true">
                        <span style="font-size: 13px">选择职务/职称</span>
                    </van-button></template>
            </van-field>
            <van-cell v-model="guestInfo.areaName" required title="地区" is-link @click="areaShow = true" />

            <div style="font-size: 12px;padding: 10px;color: red;">
                专家简介如果是文件，就使用上传文件，如果是文本，就使用文本框（必填）
            </div>
            <van-field name="radio" label="专家简介" :required="guestInfo.isAttend">
                <template #input>
                    <van-radio-group v-model="contentType" direction="horizontal">
                        <van-radio :name="1">文件上传</van-radio>
                        <van-radio :name="2">文本输入</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-if="contentType == 2" v-model="guestInfo.content" name="专家简介" rows="6" type="textarea" label=""
                :required="guestInfo.isAttend" :rules="[{ required: contentType == 2 && guestInfo.isAttend, message: '请填写专家简介' }]">
            </van-field>
            <van-cell title="" v-if="contentType == 1">
                <van-uploader v-if="!guestInfo.contentFile" :after-read="afterRead" name="contentFile"
                    :before-read="beforeRead" accept="*">
                    <template>
                        <van-icon name="description" size="40px" style="margin-left: 5px" />
                        <div style="font-size: 12px">点击上传文件（必填）</div>
                    </template>
                </van-uploader>
                <div v-if="guestInfo.contentFile" class="file-info">
                    <template v-if="isImage(guestInfo.contentFile)">
                        <van-image height="40px" @click.stop="preImage(guestInfo.contentFile)"
                            :src="guestInfo.contentFile" fit="contain" />
                    </template>
                    <template v-else>
                        <van-icon name="description" size="20px" />
                        <span class="file-text">已上传文件</span>
                    </template>
                    <van-icon name="cross" @click.stop="removeFile" class="remove-icon" />
                </div>
            </van-cell>
        </van-cell-group>
        <div style="margin: 16px">
            <van-button round block type="info" @click="submit" :loading="loading" loading-text="提交中">{{
                guestInfo.isInfo
                    ? '核对无误，请点击确认'
                    : '提交' }}</van-button>
        </div>
        <div style="margin: 16px">
            <van-button round block type="primary" @click="
                $router.replace({
                    path: '/schedules/expertIndex',
                    query: { detailId: id },
                });">返回上一页面</van-button>
        </div>
        <!-- 省市区弹窗 -->
        <van-popup v-model="areaShow" position="bottom" :style="{ height: '45%' }">
            <van-area @cancel="areaShow = false" @confirm="areaSelect" title="区域选择" :area-list="areaList"
                :value="areaCode" />
        </van-popup>
        <van-action-sheet v-model="idCardTypeShow" :actions="idCardType" @select="idCardTypeSelect" />
        <van-action-sheet v-model="dutiesShow" :actions="dutiesOptions" @select="dutiesSelect" />
    </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone, isMobile } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import SignCanvas from "sign-canvas";
import { idCardType } from "@/data/common";
import { areaList } from "@vant/area-data";
import Compressor from "compressorjs";
export default {
    components: { pcheader, SignCanvas },
    data() {
        return {
            contentType: 2,
            areaList,
            areaCode: '',
            idCardType,
            loading: false,
            areaShow: false,
            idCardTypeShow: false,
            dutiesShow: false,
            isMobilePhone: isMobilePhone(),
            openid: undefined,
            activeName: ["1", "2", "3"],
            activityId: undefined,
            id: undefined,
            guestInfo: {
                isAttend: true
            },
            schedule: [],
            scheduleDiscuss: [],
            scheduleSpeaker: [],
            topic: [],
            topicSpeaker: [],
            activityInfo: {},
            value: null,
            dutiesOptions: [
                // 主任医师、副主任医师、主治医师、医师、主任护师、副主任护师、主管护师、护师、主任药师、副主任药师、主管药师、药师
                { name: '主任医师' },
                { name: '副主任医师' },
                { name: '主治医师' },
                { name: '医师' },
                { name: '主任护师' },
                { name: '副主任护师' },
                { name: '主管护师' },
                { name: '护师' },
                { name: '主任药师' },
                { name: '副主任药师' },
                { name: '主管药师' },
                { name: '药师' },

            ],
            options: {
                isFullScreen: true, ////是否全屏手写 [Boolean] 可选
                isFullCover: false, //是否全屏模式下覆盖所有的元素 [Boolean]   可选
                isDpr: false, //是否使用dpr兼容高分屏 [Boolean] 可选
                lastWriteSpeed: 1, //书写速度 [Number] 可选
                lastWriteWidth: 2, //下笔的宽度 [Number] 可选
                lineCap: "round", //线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square]	正方形线帽
                lineJoin: "bevel", //线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
                canvasWidth: 350, //canvas宽高 [Number] 可选
                canvasHeight: 370, //高度  [Number] 可选
                isShowBorder: false, //是否显示边框 [可选]
                bgColor: "#ffffff", //背景色 [String] 可选
                borderWidth: 1, // 网格线宽度  [Number] 可选
                borderColor: "#ff787f", //网格颜色  [String] 可选
                writeWidth: 5, //基础轨迹宽度  [Number] 可选
                maxWriteWidth: 30, // 写字模式最大线宽  [Number] 可选
                minWriteWidth: 5, // 写字模式最小线宽  [Number] 可选
                writeColor: "#101010", // 轨迹颜色  [String] 可选
                isSign: true, //签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
                imgType: "png", //下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
            },
        };
    },
    mounted() {
        // this.activityId = this.$route.query.id;
        this.id = this.$route.query.detailId;
        this.openid = this.$cookie.get("openid");
        this.getActivityList();
        this.getTopicAndSchedule();
    },
    methods: {
        isImage(fileName) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            const fileExtension = fileName.split('.').pop().toLowerCase();
            return imageExtensions.includes(fileExtension);
        },
        preImage(v) {
            vant.ImagePreview({
                images: [v], // 图片集合
                closeable: true, // 关闭按钮
            });
        },
        getActivityInfo() {
            this.$fly
                .get(`/pyp/activity/activity/info/${this.activityId}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.activityInfo = res.activity;
                        this.activityInfo.backImg =
                            this.activityInfo.backImg ||
                            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
                        document.title = "专家基本信息确认-" + this.guestInfo.name;
                        let startTime = date.formatDate.format(
                            new Date(this.activityInfo.startTime),
                            "yyyy年MM月dd日"
                        );
                        let endTime = date.formatDate.format(
                            new Date(this.activityInfo.endTime),
                            "MM月dd日"
                        );
                        if (startTime.includes(endTime)) {
                            let desc =
                                "时间:" +
                                startTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                this.guestInfo.name + "-专家基本信息确认-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        } else {
                            let desc =
                                "时间:" +
                                startTime +
                                "-" +
                                endTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                this.guestInfo.name + "-专家基本信息确认-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        }
                    } else {
                        vant.Toast(res.msg);
                        this.activityInfo = {};
                    }
                });
        },
        getTopicAndSchedule() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getTopicAndSchedule/${this.id}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.topic = res.result.topic;
                        this.topicSpeaker = res.result.topicSpeaker;
                        this.schedule = res.result.schedule;
                        this.scheduleSpeaker = res.result.scheduleSpeaker;
                        this.scheduleDiscuss = res.result.scheduleDiscuss;
                    } else {
                        vant.Toast(res.msg);
                        this.activityInfo = {};
                    }
                });
        },
        getActivityList() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
                .then((res) => {
                    if (res.code == 200) {
                        this.guestInfo = res.result;
                        this.activityId = res.result.activityId;
                        this.guestInfo.isAttend = res.result.isAttend ? true : false;
                        this.getActivityInfo();
                    } else {
                        vant.Toast(res.msg);
                        this.guestInfo = {};
                    }
                });
        },
        showTask() {
            this.$router.push({
                name: "schedulesExpertDetail",
                query: { detailId: this.id, id: this.activityId },
            });
        },
        cmsTurnBack() {
            if (this.activityInfo.backUrl) {
                window.open(this.activityInfo.backUrl);
            } else {
                this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
            }
        },
        afterRead(e, name) {
            console.log(e);
            let filedName = name.name;
            e.status = "uploading";
            e.message = "上传中...";
            let formData = new FormData();
            // formData.append("pushKey", this.pushKey);
            // formData.append("activityId", this.activityId);
            let file = e.file;
            var that = this;
            // 判断文件类型
            if (file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/jpg') {
                formData.append("file", file);
                that.$fly.post("/pyp/web/upload", formData).then((res) => {
                    if (res && res.code === 200) {
                        that.$set(that.guestInfo, filedName, res.result);
                    }
                });
            } else {
                console.log(file);
                new Compressor(file, {
                    quality: 0.7,
                    success(result) {
                        formData.append("file", new window.File([result], file.name, { type: file.type }));

                        that.$fly.post("/pyp/web/upload", formData).then((res) => {
                            if (res && res.code === 200) {
                                that.$set(that.guestInfo, filedName, res.result);
                            }
                        });
                    }
                });
            }
        },
        beforeRead(file) {
            // if (file.type !== 'image/jpeg') {
            //   Toast('请上传 jpg 格式图片');
            //   return false;
            // }
            return true;
        },
        dutiesSelect(v) {
            this.dutiesShow = false;
            this.guestInfo.duties = v.name;
        },
        idCardTypeSelect(v) {
            this.idCardTypeShow = false;
            this.guestInfo.idCardType = v.name;
        },
        areaSelect(v) {
            let areaCode = v[0].code + ',' + v[1].code + ',' + v[2].code;
            let areaName = v[0].name + ',' + v[1].name + ',' + v[2].name;
            this.areaCode = v[2].code;
            this.$set(this.guestInfo, 'area', areaCode);
            this.$set(this.guestInfo, 'areaName', areaName);
            this.areaShow = false;
        },
        submit() {
            if (!this.guestInfo.name) {
                vant.Toast("请输入专家姓名");
                return false;
            }
            if (!this.guestInfo.mobile) {
                vant.Toast("请输入联系方式");
                return false;
            }
            if (!isMobile(this.guestInfo.mobile)) {
                vant.Toast("请输入正确的手机号");
                return false;
            }
            if (!this.guestInfo.avatar && this.guestInfo.isAttend) {
                vant.Toast("请选择专家头像");
                return false;
            }
            if (!this.guestInfo.unit) {
                vant.Toast("请输入工作单位");
                return false;
            }
            if (!this.guestInfo.duties) {
                vant.Toast("请输入职务/职称");
                return false;
            }
            if (!this.guestInfo.area) {
                vant.Toast("请选择地区");
                return false;
            }
            if (this.contentType == 1 && !this.guestInfo.contentFile && this.guestInfo.isAttend) {
                vant.Toast("请上传专家简介文件");
                return false;
            }
            if (this.contentType == 2 && !this.guestInfo.content && this.guestInfo.isAttend) {
                vant.Toast("请填写专家简介");
                return false;
            }
            this.loading = true;
            // 保存
            this.guestInfo.isInfo = 1;
            this.guestInfo.isInfoTime = date.formatDate.format(new Date(), "yyyy/MM/dd hh:mm:ss");
            this.guestInfo.isAttend = this.guestInfo.isAttend ? 1 : 0;
            this.$fly
                .post(
                    "/pyp/web/activity/activityguest/updateInfo",
                    this.guestInfo
                )
                .then((res) => {
                    this.loading = false;
                    if (res && res.code === 200) {
                        vant.Dialog.confirm({
                            title: "更新成功",
                            message: "点击确定，返回继续完善其他信息",
                        })
                            .then(() => {
                                this.$router.replace({
                                    path: '/schedules/expertIndex',
                                    query: { detailId: this.id },
                                });
                            })
                            .catch(() => {
                                this.getActivityList();
                            });
                        // 绑定手机
                    } else {
                        vant.Toast(res.msg);
                    }
                });
        },
        removeFile() {
            this.guestInfo.contentFile = '';
        }
    },
};
</script>
<style lang="less" scoped>
.transparent {
    /deep/.van-collapse-item__content {
        background: transparent;
    }
}

.content {
    /deep/ img {
        max-width: 100%;
        height: auto;
    }
}

.van-card__thumb /deep/ img {
    object-fit: contain !important;
}

.van-cell__title {
    flex: none;
    box-sizing: border-box;
    width: 6.2em;
    margin-right: 12px;
    color: #646566;
    text-align: left;
    word-wrap: break-word;
    line-height: 33px;
}

.van-cell__value {
    text-align: left;
    display: flex;
    align-items: center;
}

.sign {
    position: fixed;
    top: 0;
    background: white;
    height: 100%;

    width: 100%;

    .button {
        text-align: center;
        width: 30%;
        height: 40px;
        line-height: 40px;
        background: #4485ff;
        border-radius: 20px;
        text-align: center;
        color: white;
    }
}

.sign-btns {
    display: flex;
    justify-content: space-between;

    #clear,
    #clear1,
    #save {
        display: inline-block;
        padding: 5px 10px;
        width: 76px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #eee;
        background: #e1e1e1;
        border-radius: 10px;
        text-align: center;
        margin: 20px auto;
        cursor: pointer;
    }
}

.file-info {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    margin-top: 10px;
}

.file-text {
    margin-left: 5px;
    flex: 1;
}

.remove-icon {
    margin-left: 10px;
    cursor: pointer;
    transition: color 0.3s;
}

.remove-icon:hover {
    color: #ff4d4f;
}
</style>