<template>
    <div>
        
        <!-- 微信openid：{{wxOpenid}} -->
    
    </div>
</template>
<script>
    import wxAuth from '../js/wxAuth.js'

    export default {
        components: {
            
        },
        data() {
            return {
                wxOpenid:'',
                returnUrl: '',
            }
        },
        mounted() {
            
            this.returnUrl = decodeURIComponent(this.$route.query.returnUrl);
            document.title="微信授权登录中......"
            wxAuth().then(openid => {
                location.href = this.returnUrl;
            });
        },
        methods: {
            
        }
    }
</script>
<style scoped>
    .page {
        background-color: #f0f3f5;
    }
</style>
