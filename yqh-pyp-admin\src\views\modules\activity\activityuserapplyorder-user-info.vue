<template>
  <el-dialog title="查看个人信息" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="activityUserEntity" ref="activityUserEntity" label-width="120px">
      <el-form-item label="用户名" prop="contact">
        <el-input v-model="activityUserEntity.contact" disabled placeholder="用户名" filterable />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input v-model="activityUserEntity.mobile" disabled placeholder="手机" filterable />
      </el-form-item>
      <el-form-item label="报名状态">
        <el-tag type="primary" :class="'tag-color tag-color-' + (activityUserEntity.status)">{{ activityUserEntity.status |
          statusFilter}}</el-tag>
      </el-form-item>
      <div v-for="item in applyActivityConfigEntities" :key="item.id">
        <el-form-item v-if="item.type == 0" :label="item.finalName" :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请输入' + item.finalName, trigger: 'blur' }]">
          <!-- 如果是填空 -->
          <el-input v-model="activityUserEntity[item.applyConfigFieldName]" :placeholder="item.finalName" filterable />
        </el-form-item>
        <!-- 如果是单选 -->
        <el-form-item v-if="item.type == 1" :label="item.finalName" :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请选择' + item.finalName, trigger: 'blur' }]">
          <el-select v-model="activityUserEntity[item.applyConfigFieldName]" :placeholder="item.finalName" filterable>
            <el-option v-for="item1 in item.selectData" :key="item1" :label="item1" :value="item1"></el-option>
          </el-select>
        </el-form-item>
        <!-- 如果是多选 -->
        <el-form-item v-if="item.type == 2" :label="item.finalName" :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请选择' + item.finalName, trigger: 'blur' }]">
          <el-select v-model="activityUserEntity[item.applyConfigFieldName]" multiple :placeholder="item.finalName"
            filterable>
            <el-option v-for="item1 in item.selectData" :key="item1" :label="item1" :value="item1"></el-option>
          </el-select>
        </el-form-item>
        <!-- 如果是日期 -->
        <el-form-item v-if="item.type == 5" :label="item.finalName" :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请选择' + item.finalName, trigger: 'blur' }]">
          <el-date-picker  v-model="activityUserEntity[item.applyConfigFieldName]"
           type="date" value-format="yyyy/MM/dd" :placeholder="item.finalName"></el-date-picker>
        </el-form-item>
        <!-- 如果是area--省市区 -->
        <el-form-item v-if="item.applyConfigFieldName == 'area'" :label="item.finalName"
          :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请选择' + item.finalName, trigger: 'blur' }]">
          <el-cascader style="width: 100%" size="large" :options="options"
            v-model="activityUserEntity[item.applyConfigFieldName]" @change="handleChange">
          </el-cascader>
        </el-form-item>
        <!-- 如果是isHotel-是否酒店预定 -->
        <el-form-item v-if="item.applyConfigFieldName == 'isHotel'" :label="item.finalName"
          :prop="item.applyConfigFieldName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请选择' + item.finalName, trigger: 'blur' }]">
            <el-select v-model="activityUserEntity[item.applyConfigFieldName]" placeholder="公众号显示" filterable>
              <el-option v-for="item in yesOrNo" :key="item.value" :label="item.value" :value="item.value"></el-option>
            </el-select>
        </el-form-item>
      </div>
      <!-- 如果是发票信息 -->
      <div v-if="applyActivityChannelConfig.isInvoice">
        <el-form-item label="发票抬头" prop="invoiceName">
          <el-input v-model="activityUserEntity.invoiceName" placeholder="发票抬头" filterable />
        </el-form-item>
        <el-form-item label="发票税号" prop="invoiceCode">
          <el-input v-model="activityUserEntity.invoiceCode" placeholder="发票税号" filterable />
        </el-form-item>
        <el-form-item label="银行账户" prop="invoiceBank">
          <el-input v-model="activityUserEntity.invoiceBank" placeholder="银行账户" filterable />
        </el-form-item>
        <el-form-item label="开户行" prop="invoiceAccount">
          <el-input v-model="activityUserEntity.invoiceAccount" placeholder="开户行" filterable />
        </el-form-item>
        <el-form-item label="注册地址" prop="invoiceAddress">
          <el-input v-model="activityUserEntity.invoiceAddress" placeholder="注册地址" filterable />
        </el-form-item>
        <el-form-item label="联系方式" prop="invoiceMobile">
          <el-input v-model="activityUserEntity.invoiceMobile" placeholder="联系方式" filterable />
        </el-form-item>
      </div>
      <!-- 如果是扩展 -->
      <div v-for="item in extraResult" :key="item.id">
        <el-form-item v-if="item.type == 0" :label="item.finalName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请输入' + item.finalName, trigger: 'blur' }]">
          <!-- 如果是填空 -->
          <el-input v-model="item.value" :placeholder="item.finalName" filterable />
        </el-form-item>
        <!-- 如果是单选 -->
        <el-form-item v-if="item.type == 1" :label="item.finalName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请输入' + item.finalName, trigger: 'blur' }]">
          <el-select v-model="item.value" :placeholder="item.finalName" filterable>
            <el-option v-for="item1 in item.selectData" :key="item1" :label="item1" :value="item1"></el-option>
          </el-select>
        </el-form-item>
        <!-- 如果是日期 -->
        <el-form-item v-if="item.type == 5" :label="item.finalName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请输入' + item.finalName, trigger: 'blur' }]">
          <el-date-picker  v-model="item.value"
           type="date" value-format="yyyy/MM/dd" :placeholder="item.finalName"></el-date-picker>
        </el-form-item>
        <!-- 如果是多选 -->
        <el-form-item v-if="item.type == 2" :label="item.finalName"
          :rules="[{ required: item.required == 1 ? true : false, message: '请输入' + item.finalName, trigger: 'blur' }]">
          <el-select v-model="item.value" multiple :placeholder="item.finalName" filterable>
            <el-option v-for="item1 in item.selectData" :key="item1" :label="item1" :value="item1"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <!-- 备注 -->
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="activityUserEntity.remarks" placeholder="备注" filterable />
      </el-form-item>
      <el-form-item label="学时" prop="hours">
        <el-input v-model="activityUserEntity.hours" placeholder="学时" filterable />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { yesOrNo } from "@/data/common"
import { userStatus } from '@/data/activity.js'
import { regionData } from 'element-china-area-data'
export default {
  data() {
    return {
      yesOrNo,
      autoCompleteListView: false,
      invoicelist: [],
      visible: false,
      applyOrderId: undefined,
      applyActivityConfigEntities: [],
      options: regionData,
      extraResult: [],
      activityUserEntity: {
        id: 0,
        remarks: '',
        hours: '',
      },
      userStatus,
      applyActivityChannelConfig: {}
    }
  },
  filters: {
    statusFilter(v) {
      let data = userStatus.filter(item => item.key === v)
      if (data.length >= 1) {
        return data[0].value;
      }
    },
  },
  methods: {
    init(id) {
      this.applyOrderId = id
      this.visible = true
      this.$nextTick(() => {
        this.$refs['activityUserEntity'].resetFields()
        this.$http({
          url: this.$http.adornUrl(`/activity/activityuserapplyorder/applyUserInfoAdmin/${this.applyOrderId}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.activityUserEntity = data.activityUserEntity;
            if (this.activityUserEntity.applyActivityChannelConfigId) {
              this.getApplyActivityChannelConfig(this.activityUserEntity.applyActivityChannelConfigId);
            }
            this.activityUserEntity.area = this.activityUserEntity.area ? this.activityUserEntity.area.split(",") : [];
            this.applyActivityConfigEntities = data.applyActivityConfigEntities;
            this.extraResult = data.extraConfig;
            this.applyActivityConfigEntities.forEach((item) => {
              if (item.type == 1) {
                // 单选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
              } else if (item.type == 2) {
                // 多选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
                this.activityUserEntity[item.applyConfigFieldName] = this.activityUserEntity[item.applyConfigFieldName] ? this.activityUserEntity[item.applyConfigFieldName].replace(/，/ig, ',').split(",") : [];
              }
            });
            // 扩展
            if (this.extraResult) {
              this.extraResult.forEach((extraItem) => {
                if (extraItem.type == 1) {
                  // 单选
                  extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                } else if (extraItem.type == 2) {
                  // 多选
                  extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                  extraItem.value = extraItem.value ? extraItem.value.replace(/，/ig, ',').split(",") : [];
                }
              })
            }
          }
        })
      })
    },
    getApplyActivityChannelConfig(v) {
      this.$http({
        url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/info/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.applyActivityChannelConfig = data.applyActivityChannelConfig;
        }
      })
    },
    handleChange(value) {
      console.log(value)
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['activityUserEntity'].validate((valid) => {
        if (valid) {
          // 扩展字段校验
          let flag = true;
          if (this.extraResult && this.extraResult.length > 0) {
            this.extraResult.forEach((item) => {
              item.selectData = !item.selectData ? null : item.selectData.toString();
              if (item.type != 3 && item.required && !item.value) {
                this.$message.error("请输入" + item.finalName);
                flag = false;
              } else if (item.type == 2) {
                item.value = item.value.toString();
              }
            })
          }
          this.applyActivityConfigEntities.forEach((item) => {
            if (item.type == 2 || item.applyConfigFieldName == 'area') {
              this.activityUserEntity[item.applyConfigFieldName] = this.activityUserEntity[item.applyConfigFieldName].toString();
            }
          })
          if (!flag) {
            return false;
          }
          this.activityUserEntity.applyExtraVos = this.extraResult;
          this.activityUserEntity.area = this.activityUserEntity.area ? this.activityUserEntity.area.toString() : '';
          this.$http({
            url: this.$http.adornUrl(`/activity/activityuser/update`),
            method: 'post',
            data: this.$http.adornData(this.activityUserEntity)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>