<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="审核状态" prop="verifyStatus">
      <el-select v-model="dataForm.verifyStatus" placeholder="审核状态" filterable>
        <el-option v-for="item in verifyStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {verifyStatus} from '@/data/activity'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          verifyStatus: '',
        },
        verifyStatus,
        dataRule: {
          verifyStatus: [
            { required: true, message: '订单状态不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuserapplyorder/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.verifyStatus = data.activityUserApplyOrder.verifyStatus
              }
            })
          
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuserapplyorder/updateVerifyStatus`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'verifyStatus': this.dataForm.verifyStatus,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
