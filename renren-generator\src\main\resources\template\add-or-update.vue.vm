<template>
  <el-dialog
    :title="!dataForm.${pk.attrname} ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
#foreach($column in $columns)
#if($column.columnName != $pk.columnName && $column.attrname != 'createOn' && $column.attrname != 'createBy' && $column.attrname != 'updateOn' && $column.attrname != 'updateBy' && $column.attrname != 'appid')
    <el-form-item label="${column.comments}" prop="${column.attrname}">
      #if($column.attrType == 'Date')
        <el-date-picker v-model="dataForm.${column.attrname}" type="datetime" placeholder="请选择${column.comments}"
                        value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      #else
        <el-input v-model="dataForm.${column.attrname}" placeholder="${column.comments}"></el-input>
      #end
    </el-form-item>
#end
#end
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
#foreach($column in $columns)
#if($column.columnName == $pk.columnName)
          ${column.attrname}: 0,
#else
    #if($column.attrname != 'createOn' && $column.attrname != 'createBy' && $column.attrname != 'updateOn' && $column.attrname != 'updateBy' && $column.attrname != 'appid')

          ${column.attrname}: ''#if($velocityCount != $columns.size()),#end

#end
#end
#end
        },
        dataRule: {
#foreach($column in $columns)
#if($column.columnName != $pk.columnName && $column.attrname != 'createOn' && $column.attrname != 'createBy' && $column.attrname != 'updateOn' && $column.attrname != 'updateBy' && $column.attrname != 'appid')
          ${column.attrname}: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ]#if($velocityCount != $columns.size()),#end

#end
#end
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.${pk.attrname} = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.${pk.attrname}) {
            this.$http({
              url: #[[this.$http.adornUrl]]#(`/${moduleName}/${pathName}/info/#[[$]]#{this.dataForm.${pk.attrname}}`),
              method: 'get',
              #[[params: this.$http.adornParams()]]#
            }).then(({data}) => {
              if (data && data.code === 200) {
#foreach($column in $columns)
#if($column.columnName != $pk.columnName && $column.attrname != 'createOn' && $column.attrname != 'createBy' && $column.attrname != 'updateOn' && $column.attrname != 'updateBy' && $column.attrname != 'appid')
                this.dataForm.${column.attrname} = data.${classname}.${column.attrname}
#end
#end
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        #[[this.$refs['dataForm'].validate((valid) => {]]#
          if (valid) {
            this.$http({
              url: #[[this.$http.adornUrl]]#(`/${moduleName}/${pathName}/${!this.dataForm.${pk.attrname} ? 'save' : 'update'}`),
              method: 'post',
              #[[data: this.$http.adornData({]]#
                    'repeatToken': this.dataForm.repeatToken,
#foreach($column in $columns)
#if($column.columnName == $pk.columnName)
                '${column.attrname}': this.dataForm.${column.attrname} || undefined,
#else
            #if($column.attrname != 'createOn' && $column.attrname != 'createBy' && $column.attrname != 'updateOn' && $column.attrname != 'updateBy' && $column.attrname != 'appid')
                '${column.attrname}': this.dataForm.${column.attrname},
#else
            #if($column.attrname == 'appid')
            '${column.attrname}': this.$cookie.get('appid'),

#end
#end
#end
#end
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                #[[this.$message({]]#
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    #[[this.$emit('refreshDataList')]]#
                  }
                })
              } else {
                #[[this.$message.error(data.msg)]]#
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
