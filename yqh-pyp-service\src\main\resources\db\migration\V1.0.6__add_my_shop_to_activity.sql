-- 为tb_activity表添加我的小店功能字段

-- 添加我的小店类型字段：0-网页，1-小程序
ALTER TABLE `tb_activity` ADD COLUMN `shop_type` tinyint(1) DEFAULT 0 COMMENT '我的小店类型：0-网页，1-小程序';

-- 添加我的小店URL字段（当shop_type=0时使用）
ALTER TABLE `tb_activity` ADD COLUMN `shop_url` varchar(500) DEFAULT NULL COMMENT '我的小店网页URL';

-- 添加我的小店小程序AppID字段（当shop_type=1时使用）
ALTER TABLE `tb_activity` ADD COLUMN `shop_appid` varchar(100) DEFAULT NULL COMMENT '我的小店小程序AppID';

-- 添加我的小店小程序页面路径字段（当shop_type=1时使用）
ALTER TABLE `tb_activity` ADD COLUMN `shop_page_path` varchar(200) DEFAULT NULL COMMENT '我的小店小程序页面路径';

-- 添加是否显示我的小店字段
ALTER TABLE `tb_activity` ADD COLUMN `show_my_shop` tinyint(1) DEFAULT 0 COMMENT '是否显示我的小店：0-不显示，1-显示';

-- 添加索引优化查询性能
ALTER TABLE `tb_activity` ADD INDEX `idx_shop_type` (`shop_type`);
ALTER TABLE `tb_activity` ADD INDEX `idx_show_my_shop` (`show_my_shop`);

-- 添加携程点评广告类型配置
INSERT INTO `ad_type_config` (`type_code`, `type_name`, `platform`, `content_type`, `title_length`, `content_length`, `topics_count`, `topics_format`, `requirements`, `style`, `prompt_template`, `sort_order`, `status`) VALUES
('ctrip', '携程点评', '携程', '旅游点评', '25字以内，突出体验', '100-200字，详细评价', 4, '不带#号，用逗号分隔', '- 要有具体的旅游体验描述\n- 内容要客观真实\n- 要突出服务质量和性价比\n- 要有对其他旅客的参考价值\n- 要描述住宿、交通、景点等具体细节', '专业、详细、实用、有参考价值', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 9, 1);
