package com.cjy.pyp.modules.activity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动过期状态VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Data
public class ActivityExpirationStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 过期时间
     */
    private Date expirationTime;

    /**
     * 是否已过期
     */
    private Boolean isExpired;

    /**
     * 是否即将过期（7天内）
     */
    private Boolean isExpiringSoon;

    /**
     * 剩余天数
     */
    private Integer remainingDays;

    /**
     * 剩余小时数
     */
    private Integer remainingHours;

    /**
     * 过期状态描述
     */
    private String statusDescription;

    /**
     * 是否可以续费
     */
    private Boolean canRenew;

    /**
     * 续费提示信息
     */
    private String renewalMessage;
}
