<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="联系人姓名" prop="name">
        <el-autocomplete style="width: 100%;" v-model="dataForm.name" :fetch-suggestions="search" label="name"
          placeholder="请输入内容" @select="selectRsult">
          <div slot-scope="scope">
            <span style="float: left">{{ scope.item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ scope.item.unit }}</span>
          </div>
        </el-autocomplete></el-form-item>
      <!-- <el-form-item label="联系人姓名" prop="name">
      <el-select
        v-model="dataForm.name"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="search"
        style="width: 100%"
        @change="selectRsult"
        :loading="loading">
        <el-option
          v-for="item in searchResult"
          :key="item.id"
          :label="item.name"
          :value="item.id">
      <span style="float: left">{{ item.name }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unit }}</span>
        </el-option>
      </el-select></el-form-item> -->
      <!-- <el-row class="row" :gutter="24">
        <el-col :span="18"> -->
      <el-form-item label="联系人电话" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="联系人电话"></el-input>
      </el-form-item>
      <el-form-item label="劳务费" prop="serviceFee">
        <el-input v-model="dataForm.serviceFee" placeholder="劳务费"></el-input>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card"
          :show-file-list="false" accept=".jpg, .jpeg, .png, .gif" :on-success="appSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.avatar" :src="dataForm.avatar" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="工作单位" prop="unit">
        <el-input v-model="dataForm.unit" placeholder="工作单位"></el-input>
      </el-form-item>
      <el-form-item label="职称" prop="duties">
        <el-input v-model="dataForm.duties" placeholder="职称"></el-input>
      </el-form-item>
      <el-form-item label="排序，数值越小越靠前" prop="orderBy">
        <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
      </el-form-item>
      <el-form-item label="身份证正面" prop="idCardZheng">
        <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card"
          :show-file-list="false" accept=".jpg, .jpeg, .png, .gif" :on-success="idCardZhengSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.idCardZheng" :src="dataForm.idCardZheng" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="身份证反面" prop="idCardFan">
        <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card"
          :show-file-list="false" accept=".jpg, .jpeg, .png, .gif" :on-success="idCardFanSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.idCardFan" :src="dataForm.idCardFan" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="是否参会" prop="isAttend">
        <el-select v-model="dataForm.isAttend" placeholder="是否参会" filterable>
          <el-option label="参会" :value="1"></el-option>
          <el-option label="不参会" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="身份证类型" prop="idCardType">
        <el-select v-model="dataForm.idCardType" placeholder="身份证类型" filterable>
          <el-option v-for="item in idCardType" :key="item.name" :label="item.name" :value="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="身份证" prop="idCard">
        <el-input v-model="dataForm.idCard" placeholder="身份证"></el-input>
      </el-form-item>
      <el-form-item label="银行卡号" prop="bank">
        <el-input v-model="dataForm.bank" placeholder="银行卡号"></el-input>
      </el-form-item>
      <el-form-item label="开户行" prop="kaihuhang">
        <el-input v-model="dataForm.kaihuhang" placeholder="开户行"></el-input>
      </el-form-item>
      <el-form-item :label="'省市区'" prop="area">
        <el-cascader style="width: 100%" size="large" :options="options" v-model="dataForm.area" @change="handleChange">
        </el-cascader>
      </el-form-item>
      <el-form-item label="详细介绍" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
      </el-form-item>
      <el-form-item label="详细介绍(文件)" prop="contentFile">
        <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
          :before-upload="checkFileSize" :on-success="subscribeImgSuccessHandle" :action="url">
          <div v-if="dataForm.contentFile" class="file-display">
            <i class="el-icon-document"></i>
            <span class="file-name">{{ dataForm.contentFile }}</span>
            <el-button type="text" icon="el-icon-document-copy"
              @click.stop="copyToClipboard(dataForm.contentFile)">复制</el-button>

          </div>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { idCardType } from "@/data/common";
import Compressor from 'compressorjs';
import { regionData } from 'element-china-area-data'
export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
  data() {
    return {
      options: regionData,
      idCardType,
      visible: false,
      url: "",
      loading: false,
      searchResult: [],
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        mobile: "",
        unit: "",
        duties: "",
        wxUserId: "",
        avatar: "",
        content: "",
        isSave: false,
        orderBy: 0,
        serviceFee: 0,
        isAttend: 1,
        idCardZheng: "",
        idCardFan: "",
        bank: "",
        kaihuhang: "",
        idCardType: "",
        idCard: "",
        contentFile: "",
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" },
        ],
      },
      timeout: null,
    };
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.content = "";
      this.dataForm.id = id || 0;
      this.visible = true;
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.activityGuest.activityId;
              this.dataForm.name = data.activityGuest.name;
              this.dataForm.mobile = data.activityGuest.mobile;
              this.dataForm.unit = data.activityGuest.unit;
              this.dataForm.duties = data.activityGuest.duties;
              this.dataForm.wxUserId = data.activityGuest.wxUserId;
              this.dataForm.avatar = data.activityGuest.avatar;
              this.dataForm.content = data.activityGuest.content;
              this.dataForm.orderBy = data.activityGuest.orderBy;
              this.dataForm.idCardZheng = data.activityGuest.idCardZheng;
              this.dataForm.idCardFan = data.activityGuest.idCardFan;
              this.dataForm.bank = data.activityGuest.bank;
              this.dataForm.kaihuhang = data.activityGuest.kaihuhang;
              this.dataForm.serviceFee = data.activityGuest.serviceFee;
              this.dataForm.idCardType = data.activityGuest.idCardType;
              this.dataForm.contentFile = data.activityGuest.contentFile;
              this.dataForm.idCard = data.activityGuest.idCard;
              this.dataForm.area = data.activityGuest.area ? data.activityGuest.area.split(',') : [];
              this.dataForm.isSave = false;
              this.dataForm.isAttend = data.activityGuest.isAttend;
            }
          });
        }
      });
    },
    handleChange(value) {
      console.log(value)
    },
    search(query, cb) {
      // if(!this.dataForm.name) {
      //   this.$message.error("请输入姓名后查找");
      //   return false;
      // }
      if (query !== '') {
        this.loading = true;
        this.$http({
          url: this.$http.adornUrl(
            `/activity/guest/findByName`
          ),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.loading = false;
          if (data && data.code === 200) {
            // if(data.result.length == 1) {
            //   this.dataForm.name = data.result[0].name;
            //   this.dataForm.mobile = data.result[0].mobile;
            //   this.dataForm.unit = data.result[0].unit;
            //   this.dataForm.duties = data.result[0].duties;
            //   this.dataForm.wxUserId = data.result[0].wxUserId;
            //   this.dataForm.avatar = data.result[0].avatar;
            //   this.dataForm.content = data.result[0].content;
            //   this.dataForm.orderBy = data.result[0].orderBy;
            //   this.dataForm.idCardZheng = data.result[0].idCardZheng;
            //   this.dataForm.idCardFan = data.result[0].idCardFan;
            //   this.dataForm.bank = data.result[0].bank;
            //   this.dataForm.kaihuhang = data.result[0].kaihuhang;
            // } else {
            this.searchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.searchResult);
            }, 100 * Math.random());
            // }
          } else {
            this.$message.error(data.msg)
          }
        });
      }
    },
    createStateFilter(queryString) {
      return (state) => {
        return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    selectRsult(result) {
      this.dataForm.name = result.name;
      this.dataForm.mobile = result.mobile;
      this.dataForm.unit = result.unit;
      this.dataForm.duties = result.duties;
      this.dataForm.wxUserId = result.wxUserId;
      this.dataForm.avatar = result.avatar;
      this.dataForm.content = result.content;
      this.dataForm.orderBy = result.orderBy;
      this.dataForm.idCardZheng = result.idCardZheng;
      this.dataForm.idCardFan = result.idCardFan;
      this.dataForm.bank = result.bank;
      this.dataForm.kaihuhang = result.kaihuhang;
      this.dataForm.idCardType = result.idCardType;
      this.dataForm.contentFile = result.contentFile;
      this.dataForm.idCard = result.idCard;
      this.dataForm.area = result.area ? result.area.split(',') : [];
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              activityId: this.dataForm.activityId,
              name: this.dataForm.name,
              mobile: this.dataForm.mobile,
              unit: this.dataForm.unit,
              duties: this.dataForm.duties,
              wxUserId: this.dataForm.wxUserId,
              avatar: this.dataForm.avatar,
              isSave: this.dataForm.isSave,
              content: this.dataForm.content,
              orderBy: this.dataForm.orderBy,
              idCardZheng: this.dataForm.idCardZheng,
              idCardFan: this.dataForm.idCardFan,
              bank: this.dataForm.bank,
              kaihuhang: this.dataForm.kaihuhang,
              serviceFee: this.dataForm.serviceFee,
              idCard: this.dataForm.idCard,
              idCardType: this.dataForm.idCardType,
              contentFile: this.dataForm.contentFile,
              isAttend: this.dataForm.isAttend,
              area: this.dataForm.area ? this.dataForm.area.join(',') : '',
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else if (data.code == 405) {
              this.$confirm(data.msg, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }).then(() => {
                this.dataForm.isSave = true;
                this.dataFormSubmit();
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    // 上传之前
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB,请压缩后上传`);
        return false
      }
      // if (file.size / 1024 > 100) {
      //   // 100kb不压缩
      //   return new Promise((resolve, reject) => {
      //     new Compressor(file, {
      //       quality: 0.8,
      //       
      //       success(result) {
      //         resolve(result)
      //       }
      //     })
      //   })
      // }
      return true
    },
    // 上传成功（报名完关注二维码）
    subscribeImgSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.contentFile = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.avatar = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    // idCardZheng公众号轮播图上传成功
    idCardZhengSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.idCardZheng = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    // idCardFan公众号轮播图上传成功
    idCardFanSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.idCardFan = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    copyToClipboard(text) {
      const input = document.createElement('input');
      input.setAttribute('value', text);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message({
        message: '复制成功',
        type: 'success',
      });
    },
  },
};
</script>

<style scoped>
.file-display {
  display: flex;
  align-items: center;
}

.file-name {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}
</style>
