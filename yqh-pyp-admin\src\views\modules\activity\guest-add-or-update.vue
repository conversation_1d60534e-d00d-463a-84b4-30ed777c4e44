<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="联系人姓名" prop="name">
      <el-input v-model="dataForm.name" placeholder="联系人姓名"></el-input>
    </el-form-item>
    <el-form-item label="联系人电话" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系人电话"></el-input>
    </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <el-upload
        :before-upload="checkFileSize" 
          class="avatar-uploader"
          list-type="picture-card"
          :show-file-list="false"
          accept=".jpg, .jpeg, .png, .gif"
          :on-success="appSuccessHandle"
          :action="url"
        >
          <img
            width="100px"
            v-if="dataForm.avatar"
            :src="dataForm.avatar"
            class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
    <el-form-item label="工作单位" prop="unit">
      <el-input v-model="dataForm.unit" placeholder="工作单位"></el-input>
    </el-form-item>
    <el-form-item label="职称" prop="duties">
      <el-input v-model="dataForm.duties" placeholder="职称"></el-input>
    </el-form-item>
    <el-form-item label="身份证" prop="idCard">
      <el-input v-model="dataForm.idCard" placeholder="身份证"></el-input>
    </el-form-item>
    <el-form-item label="身份证正面" prop="idCardZheng">
          <el-upload :before-upload="checkFileSize"  class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                      accept=".jpg, .jpeg, .png, .gif" :on-success="idCardZhengSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.idCardZheng" :src="dataForm.idCardZheng" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
    </el-form-item>
    <el-form-item label="身份证反面" prop="idCardFan">
          <el-upload :before-upload="checkFileSize"  class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                      accept=".jpg, .jpeg, .png, .gif" :on-success="idCardFanSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.idCardFan" :src="dataForm.idCardFan" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
    </el-form-item>
    <el-form-item label="银行卡号" prop="bank">
      <el-input v-model="dataForm.bank" placeholder="银行卡号"></el-input>
    </el-form-item>
    <el-form-item label="开户行" prop="kaihuhang">
      <el-input v-model="dataForm.kaihuhang" placeholder="开户行"></el-input>
    </el-form-item>
      <el-form-item label="详细介绍" prop="introduction">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
  export default {
    components: {
      TinymceEditor: () =>
        import ("@/components/tinymce-editor")
    },
    data () {
      return {
        visible: false,
        url: "",
        dataForm: {
          id: 0,
          name: '',
          mobile: '',
          unit: '',
          duties: '',
          idCardZheng: '',
          idCardFan: '',
          bank: '',
          idCard: '',
          content: '',
          avatar: "",
          kaihuhang: ''
        },
        dataRule: {
          name: [
            { required: true, message: '联系人姓名不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/guest/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.name = data.guest.name
                this.dataForm.mobile = data.guest.mobile
                this.dataForm.unit = data.guest.unit
                this.dataForm.duties = data.guest.duties
                this.dataForm.idCardZheng = data.guest.idCardZheng
                this.dataForm.idCardFan = data.guest.idCardFan
                this.dataForm.bank = data.guest.bank
                this.dataForm.kaihuhang = data.guest.kaihuhang
                this.dataForm.idCard = data.guest.idCard
                this.dataForm.content = data.guest.content
                this.dataForm.avatar = data.guest.avatar
              }
            })
          }
        })
      },
      // 上传之前
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      beforeUploadHandle(file) {
        if (
          file.type !== "image/jpg" &&
          file.type !== "image/jpeg" &&
          file.type !== "image/png" &&
          file.type !== "image/gif"
        ) {
          this.$message.error("只支持jpg、png、gif格式的图片！");
          return false;
        }
      },
      // app公众号轮播图上传成功
      appSuccessHandle(response, file, fileList) {
        if (response && response.code === 200) {
          this.dataForm.avatar = response.url;
        } else {
          this.$message.error(response.msg);
        }
      },
      // idCardZheng公众号轮播图上传成功
      idCardZhengSuccessHandle(response, file, fileList) {
        if (response && response.code === 200) {
            this.dataForm.idCardZheng = response.url;
        } else {
          this.$message.error(response.msg);
        }
      },
      // idCardFan公众号轮播图上传成功
      idCardFanSuccessHandle(response, file, fileList) {
        if (response && response.code === 200) {
            this.dataForm.idCardFan = response.url;
        } else {
          this.$message.error(response.msg);
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/guest/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'mobile': this.dataForm.mobile,
                'unit': this.dataForm.unit,
                'duties': this.dataForm.duties,
                'idCardZheng': this.dataForm.idCardZheng,
                'idCardFan': this.dataForm.idCardFan,
                'bank': this.dataForm.bank,
                'idCard': this.dataForm.idCard,
                'content': this.dataForm.content,
                'avatar': this.dataForm.avatar,
                'kaihuhang': this.dataForm.kaihuhang
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
