/* html相关样式 */
#content a {
    color: #4285f4;
}
#content h1,#content h2,#content h3,#content h4,#content h5,#content h6{
    margin: 0.3rem 0;
    color: #0064A8;
    line-height: 2rem;
}
#content h1{
    font-size: 1.4rem;
}
#content h2{
    font-size: 1.2rem;
}
#content h3{
    font-size: 1.1rem;
}
#content h4,
#content h5,
#content h6 {
    font-size: 1rem;
}

#content hr {
    height: 0.2em;
    border: 0;
    color: #CCCCCC;
    background-color: #CCCCCC;
}

#content p,
#content blockquote,
#content ul,
#content ol,
#content dl,
#content li,
#content table,
#content pre {
    margin: 8px 0;
}

#content p {
    margin: 1em 0;
    line-height: 1.5rem;
}

#content pre {
    background-color: #F8F8F8;
    border: 1px solid #CCCCCC;
    border-radius: 3px;
    overflow: auto;
    padding: 5px;
}

#content blockquote {
    color: #666666;
    margin: 0;
    border-left: 0.2em #EEE solid;
}

#content ul,
#content ol {
    margin: 1em 0;
    padding: 0 0 0 2em;
}

#content li p:last-child {
    margin: 0
}

#content dd {
    margin: 0 0 0 2em;
}

#content img {
    border: 0;
    max-width: 100%;
    display: block;
    object-fit: contain;
    width: auto !important;
    height: auto !important;
}

#content table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    border: 1px solid #eee;
}

#content td {
    vertical-align: top;
    padding: 0.2em 0;
    border-top: 1px solid #EEEEEE;
}