<template>
  <div class="salesman-qrcode">
    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#1989fa" size="24px">
      正在加载...
    </van-loading>

    <!-- 非业务员提示 -->
    <div v-else-if="showNotSalesmanTip" class="not-salesman-tip">
      <div class="tip-icon">
        <van-icon name="warning-o" size="60" color="#ff6b6b" />
      </div>
      <h3>您不是业务员</h3>
      <p>此页面仅供业务员使用，请联系管理员开通业务员权限。</p>
      <van-button type="primary" size="large" @click="goHome">
        返回首页
      </van-button>
    </div>

    <!-- 业务员内容 -->
    <div v-else>
      <!-- 业务员信息卡片 -->
      <div class="salesman-card">
        <div class="avatar">
          <img :src="salesman.avatar || 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'" alt="业务员头像" @error="handleImageError">
          <div class="default-avatar" style="display: none;">
            <van-icon name="user-o" size="30" />
          </div>
        </div>
        <div class="info">
          <h2>{{ salesman.name }}</h2>
          <!-- <p class="department">{{ salesman.department }} - {{ salesman.position }}</p> -->
          <p class="contact">{{ salesman.mobile }}</p>
        </div>
      </div>

      <!-- 套餐选择 -->
      <div class="packages-section">
        <h3>选择套餐生成二维码</h3>

        <!-- 充值套餐 -->
        <div v-if="rechargePackages.length > 0" class="package-group">
          <h4>充值套餐</h4>
          <div class="package-list">
            <div
              v-for="pkg in rechargePackages"
              :key="'recharge-' + pkg.id"
              class="package-item"
              :class="{ active: selectedPackageId === pkg.id && selectedPackageType === 1 }"
              @click="selectPackage(pkg.id, 1)">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
              <div class="package-desc">{{ pkg.countValue }}次</div>
            </div>
          </div>
        </div>

        <!-- 活动套餐 -->
        <div v-if="activityPackages.length > 0" class="package-group">
          <h4>活动套餐</h4>
          <div class="package-list">
            <div
              v-for="pkg in activityPackages"
              :key="'activity-' + pkg.id"
              class="package-item"
              :class="{ active: selectedPackageId === pkg.id && selectedPackageType === 2 }"
              @click="selectPackage(pkg.id, 2)">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
              <div class="package-desc">{{ pkg.description || '创建活动' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 二维码展示 -->
      <div v-if="qrcodeContent" class="qrcode-section">
        <div class="qrcode-container">
          <vue-qrcode :options="{ width: 280 }" :value="qrcodeContent"></vue-qrcode>
        </div>
        <!-- <p class="qrcode-tip">扫描二维码，选择套餐购买</p> -->
        <p class="qrcode-tip">长按二维码，保存或转发给客户</p>
      </div>

      <!-- 操作按钮 -->
      <div v-if="qrcodeContent" class="action-buttons">
        <van-button
          type="primary"
          size="large"
          @click="shareQrcode"
          icon="share-o">
          分享
        </van-button>
        <van-button
          size="large"
          @click="copyLink"
          icon="link-o">
          复制链接
        </van-button>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section" v-if="stats">
        <h3>推广统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.scanCount || 0 }}</div>
            <div class="stat-label">扫码次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.orderCount || 0 }}</div>
            <div class="stat-label">订单数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">¥{{ stats.payAmount || 0 }}</div>
            <div class="stat-label">总金额</div>
          </div>
        </div>
      </div>

      <!-- 业务员介绍 -->
      <div class="intro-section" v-if="salesman.remarks">
        <h3>个人介绍</h3>
        <p>{{ salesman.remarks }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode'

export default {
  name: 'SalesmanQrcode',
  components: {
    VueQrcode
  },
  data() {
    return {
      loading: true,
      showNotSalesmanTip: false,
      salesman: {},
      rechargePackages: [],
      activityPackages: [],
      selectedPackageId: null,
      selectedPackageType: null, // 1-充值套餐, 2-活动套餐
      qrcodeContent: '',
      stats: null,
      userInfo: null
    }
  },
  mounted() {
    document.title ="业务员"
    this.checkUserAndLoadInfo()
  },
  methods: {
    // 检查用户身份并加载信息
    async checkUserAndLoadInfo() {
      try {
        this.loading = true

        // 先检查用户登录状态
        const loginRes = await this.$fly.get('/pyp/web/user/checkLogin')

        if (loginRes.result) {
          // 用户已登录，直接验证业务员身份
          await this.checkSalesmanAndLoadInfo()
        } else {
          // 用户未登录，尝试微信授权
          const userInfo = await this.getUserInfo()
          if (userInfo) {
            await this.checkSalesmanAndLoadInfo()
          } else {
            this.showNotSalesmanTip = true
          }
        }
      } catch (error) {
        console.error('检查用户身份失败:', error)
        this.showNotSalesmanTip = true
      } finally {
        this.loading = false
      }
    },

    getUserInfo() {
      return new Promise((resolve, reject) => {
        this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
          if (res.code == 200) {
            this.userInfo = res.data;
            resolve(res.data);
          } else {
            vant.Toast(res.msg);
            reject(new Error(res.msg));
          }
        }).catch(error => {
          reject(error);
        });
      });
    },

    // 检查是否为业务员并加载信息
    async checkSalesmanAndLoadInfo() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/scanByAuth')

        if (res.code === 200) {
          this.salesman = res.salesman
          this.rechargePackages = res.rechargePackages || []
          this.activityPackages = res.activityPackages || []
          this.stats = res.stats || null

          // 如果有套餐，默认选择第一个充值套餐
          if (this.rechargePackages.length > 0) {
            this.selectPackage(this.rechargePackages[0].id, 1)
          } else if (this.activityPackages.length > 0) {
            this.selectPackage(this.activityPackages[0].id, 2)
          }
        } else {
          // 不是业务员
          this.showNotSalesmanTip = true
        }
      } catch (error) {
        console.error('检查业务员身份失败:', error)
        this.showNotSalesmanTip = true
      }
    },

    // 选择套餐
    selectPackage(packageId, packageType) {
      this.selectedPackageId = packageId
      this.selectedPackageType = packageType

      // 构建二维码内容
      this.qrcodeContent = this.buildQrcodeContent()
    },

    // 构建二维码内容
    buildQrcodeContent() {
      if (!this.selectedPackageId) {
        return ''
      }

      return `https://yqihua.com/p_front/#/salesman/scan?packageId=${this.selectedPackageId}&salesmanId=${this.salesman.id}&packageType=${this.selectedPackageType}`
    },

    // 返回首页
    goHome() {
      this.$router.push({ name: 'index' })
    },

    // 分享二维码
    shareQrcode() {
      if (!this.qrcodeContent) {
        this.$toast('请先选择套餐')
        return
      }

      // 调用微信分享
    this.$wxShare(
        `易企化AI爆店码`,
        'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230209/e90c0d7c2ef2424285fa4ba22976bc20.png', // 分享图片，可以为空或传入图片URL
        '扫描二维码，选择套餐购买',
        this.qrcodeContent
      )
    },

    // 复制链接
    copyLink() {
      if (!this.qrcodeContent) {
        this.$toast('请先选择套餐')
        return
      }

      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.qrcodeContent).then(() => {
          this.$toast('链接已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyTextToClipboard()
        })
      } else {
        this.fallbackCopyTextToClipboard()
      }
    },

    // 兜底复制方法
    fallbackCopyTextToClipboard() {
      const textArea = document.createElement('textarea')
      textArea.value = this.qrcodeContent
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        this.$toast('链接已复制到剪贴板')
      } catch (err) {
        this.$toast('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    },

    // 处理头像加载错误
    handleImageError(event) {
      const img = event.target
      const defaultAvatar = img.nextElementSibling
      if (defaultAvatar) {
        img.style.display = 'none'
        defaultAvatar.style.display = 'flex'
      }
    }
  }
}
</script>

<style scoped>
.salesman-qrcode {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

/* 加载状态 */
.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
}

/* 非业务员提示 */
.not-salesman-tip {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 40px 20px;
  text-align: center;
  backdrop-filter: blur(10px);
  margin-top: 100px;
}

.tip-icon {
  margin-bottom: 20px;
}

.not-salesman-tip h3 {
  margin: 0 0 15px 0;
  font-size: 20px;
  font-weight: bold;
}

.not-salesman-tip p {
  margin: 0 0 30px 0;
  opacity: 0.9;
  line-height: 1.6;
}

.salesman-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.info h2 {
  margin: 0 0 5px 0;
  font-size: 20px;
  font-weight: bold;
}

.info p {
  margin: 2px 0;
  opacity: 0.9;
}

.department {
  font-size: 14px;
}

.contact {
  font-size: 13px;
}

/* 套餐选择 */
.packages-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.packages-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.package-group {
  margin-bottom: 20px;
}

.package-group:last-child {
  margin-bottom: 0;
}

.package-group h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  opacity: 0.9;
}

.package-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.package-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.package-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.package-item.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.package-name {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.package-price {
  font-size: 18px;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 5px;
}

.package-desc {
  font-size: 12px;
  opacity: 0.8;
}

.qrcode-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 20px;
  text-align: center;
}

.qrcode-container {
  margin-bottom: 15px;
}

.qrcode-tip {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.action-buttons .van-button {
  flex: 1;
}

.stats-section, .intro-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.stats-section h3, .intro-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.intro-section p {
  margin: 0;
  line-height: 1.6;
  opacity: 0.9;
}
</style>
