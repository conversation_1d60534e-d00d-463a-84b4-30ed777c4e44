package com.cjy.pyp.modules.activity.service;

import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;

import java.util.List;

/**
 * AI模型统一服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
public interface AiModelService {
    
    /**
     * 生成文本内容
     * 
     * @param prompt 提示词
     * @param modelCode 模型编码，如果为空则使用默认模型
     * @return 生成的文本内容
     * @throws Exception 调用失败时抛出异常
     */
    String generateText(String prompt, String modelCode) throws Exception;
    
    /**
     * 使用默认模型生成文本内容
     * 
     * @param prompt 提示词
     * @return 生成的文本内容
     * @throws Exception 调用失败时抛出异常
     */
    String generateText(String prompt) throws Exception;
    
    /**
     * 获取可用的模型列表
     * 
     * @return 启用的模型配置列表
     */
    List<AiModelConfigEntity> getAvailableModels();
    
    /**
     * 获取默认模型配置
     * 
     * @return 默认模型配置
     */
    AiModelConfigEntity getDefaultModel();
    
    /**
     * 检查模型是否可用
     * 
     * @param modelCode 模型编码
     * @return 是否可用
     */
    boolean isModelAvailable(String modelCode);
    
    /**
     * 测试模型连接
     * 
     * @param modelCode 模型编码
     * @return 连接是否成功
     */
    boolean testModelConnection(String modelCode);
    
    /**
     * 获取模型配置信息（用于调试）
     * 
     * @param modelCode 模型编码
     * @return 配置信息
     */
    Object getModelConfigInfo(String modelCode);
}
