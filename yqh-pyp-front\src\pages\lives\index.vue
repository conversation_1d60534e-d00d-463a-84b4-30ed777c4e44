<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <div v-if="dataList.length > 0">
      <van-card v-for="item in dataList" :key="item.id" @click="goLivesDetail(item.id)"
        style="background: white; margin-top: 10px" :thumb="item.cover ? item.cover : 'van-icon'">
        <div slot="title" style="font-size: 18px">{{ item.name }}</div>
        <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
          <van-tag style="margin: 0 10px 5px 0px" size="medium" round type="primary" plain>{{ item.playStatus |
            playStatusFilter }}</van-tag>
          <div v-if="item.playStatus != 2">直播时间：{{ item.liveTime }}</div>
        </div>
      </van-card>
    </div>
    <!-- <div v-else>
      <van-empty description="暂时没有直播" />
    </div> -->
  </div>
</template>

<script>
import date from "@/js/date.js";
import playStatus from "@/data/playStatus.json";
import { isMobilePhone } from '@/js/validate'
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: {
    pcheader
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activityId: undefined,
      playStatus: playStatus,
      loading: false,
      dataList: [],
    };
  },
  filters: {
    playStatusFilter(v) {
      let data = playStatus.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].value;
      }
    },
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid");
    this.getActivityInfo();
  },
  methods: {
    checkApply() {
      this.$fly
        .get("/pyp/web/activity/activityuserapplyorder/checkApply", {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.isPay = res.isPay;
            this.$store.commit("apply/update", this.isPay);
            
            if (res.isPay == 1 && res.verifyStatus == 1) {
              this.getActivityList();
            } else {
              // 缓存重定向地址
              sessionStorage.setItem("returnUrl", encodeURIComponent(window.location.href))
              this.$router.push({
                name: "applyIndex",
                query: {
                  id: this.activityId,
                },
              });
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            if(this.activityInfo.liveNeedApply) {
              this.checkApply();
            } else {
              this.getActivityList();
            }
            document.title = this.activityInfo.name + "直播列表";
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "正在直播-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "正在直播-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.$fly
        .get(
          `/pyp/web/place/placeactivity/findByActivityIdLive/${this.activityId}`
        )
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.dataList = res.result;
            if (this.dataList && this.dataList.length == 1) {
              this.$router.push({
                name: "livesDetail",
                query: { detailId: this.dataList[0].id, id: this.activityId },
              });
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
          }
        });
    },
    goLivesDetail(v) {
      this.$router.push({
        name: "livesDetail",
        query: { detailId: v, id: this.activityId },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/.van-card__thumb img {
    border-radius: 100%;
  }
}
</style>