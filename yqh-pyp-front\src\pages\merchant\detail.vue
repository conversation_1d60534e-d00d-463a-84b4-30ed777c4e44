<template>
  <div>
      <van-card
        style="background: white"
        :thumb="!merchantInfo.picUrl ? 'van-icon' : merchantInfo.picUrl"
      >
        <div slot="title" style="font-size: 18px">{{ merchantInfo.name }}</div>
        <div
          slot="desc"
          style="padding-top: 10px; font-size: 14px; color: grey"
        >
        <div>{{merchantInfo.brief}}</div>
        </div>
      </van-card>
    <div
      class="content"
      v-html="merchantInfo.content"
      @click="showImg($event)"
    ></div>
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
  </div>
</template>

<script>
import { isURL } from "@/js/validate";

export default {
  components: {},
  data() {
    return {
      openid: undefined,
      merchantId: undefined,
      activityId: undefined,
      cmsList: [],
      drList: [],
      merchantInfo: {},
      activityInfo: {},
    };
  },
  mounted() {
    this.openid = this.$cookie.get("openid");
    this.merchantId = this.$route.query.merchantId;
    this.activityId = this.$route.query.id;
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/web/merchant/merchant/info/${this.merchantId}`).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          document.title = this.merchantInfo.name;
          this.getActivityInfo(this.activityId);
        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    onClick(item) {
      this.merchantInfo = this.cmsList.filter((q) => q.id == item)[0];
      if (this.merchantInfo.url && isURL(this.merchantInfo.url)) {
        location.href = this.merchantInfo.url;
      } else if (
        this.merchantInfo.model &&
        this.isJSON(this.merchantInfo.model) 
      ) {
        var result = this.merchantInfo.model.replace(
          "${activityId}",
          this.merchantInfo.activityId
        );
        this.$router.push(JSON.parse(result));
      }
    },
    cmsTurnBack() {
      if(this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
    getActivityInfo(id) {
      this.$fly.get(`/pyp/activity/activity/info/${id}`).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.activityInfo = res.activity;
          this.activityInfo.backImg =
            this.activityInfo.backImg ||
            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
          this.$wxShare(
            this.activityInfo.name + "-" + this.merchantInfo.name + '-详情',
            (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
            null
          ); //加载微信分享
        } else {
          vant.Toast(res.msg);
          this.activityInfo = {};
        }
      });
    },
    isJSON(v) {
      try {
        JSON.parse(v);
        return true;
      } catch (error) {
        return false;
      }
    }, 
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  margin-top: 10px;
  background-color: white;
  /deep/ p {
    width: 100%;
  }
  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
</style>