<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.activity.dao.AiModelConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.activity.entity.AiModelConfigEntity" id="aiModelConfigMap">
        <result property="id" column="id"/>
        <result property="modelCode" column="model_code"/>
        <result property="modelName" column="model_name"/>
        <result property="provider" column="provider"/>
        <result property="apiUrl" column="api_url"/>
        <result property="apiKey" column="api_key"/>
        <result property="maxTokens" column="max_tokens"/>
        <result property="temperature" column="temperature"/>
        <result property="timeout" column="timeout"/>
        <result property="maxRetries" column="max_retries"/>
        <result property="retryDelay" column="retry_delay"/>
        <result property="status" column="status"/>
        <result property="isDefault" column="is_default"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="configJson" column="config_json"/>
        <result property="remark" column="remark"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectByModelCode" resultMap="aiModelConfigMap">
        SELECT * FROM ai_model_config WHERE model_code = #{modelCode}
    </select>

    <select id="selectEnabledModels" resultMap="aiModelConfigMap">
        SELECT * FROM ai_model_config WHERE status = 1 ORDER BY sort_order ASC, id ASC
    </select>

    <select id="selectByProvider" resultMap="aiModelConfigMap">
        SELECT * FROM ai_model_config WHERE provider = #{provider} AND status = 1 ORDER BY sort_order ASC, id ASC
    </select>

    <select id="selectDefaultModel" resultMap="aiModelConfigMap">
        SELECT * FROM ai_model_config WHERE is_default = 1 AND status = 1 LIMIT 1
    </select>

    <update id="updateDefaultModel">
        UPDATE ai_model_config SET is_default = 
        CASE 
            WHEN model_code = #{modelCode} THEN 1 
            ELSE 0 
        END
    </update>

    <update id="clearDefaultFlags">
        UPDATE ai_model_config SET is_default = 0
    </update>

</mapper>
