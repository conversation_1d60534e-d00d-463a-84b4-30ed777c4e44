<template>
  <el-dialog title="绑定变更历史" :close-on-click-modal="false" :visible.sync="visible" width="80%">

    <div class="history-header">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operationType" placeholder="全部" clearable>
            <el-option label="新增绑定" :value="1"></el-option>
            <el-option label="更换业务员" :value="2"></el-option>
            <el-option label="解除绑定" :value="3"></el-option>
            <el-option label="系统自动解绑" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="getHistoryList()">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="historyList" border v-loading="historyLoading" style="width: 100%;">
      <el-table-column prop="createOn" header-align="center" align="center" width="150" label="操作时间">
      </el-table-column>
      <el-table-column prop="operationType" header-align="center" align="center" label="操作类型">
        <template slot-scope="scope">
          <el-tag :type="getOperationTypeTagType(scope.row.operationType)">
            {{ getOperationTypeText(scope.row.operationType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="oldSalesmanName" header-align="center" align="center" label="原业务员">
        <template slot-scope="scope">
          <span v-if="scope.row.oldSalesmanName">{{ scope.row.oldSalesmanName }}</span>
          <span v-else style="color: #999;">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="newSalesmanName" header-align="center" align="center" label="新业务员">
        <template slot-scope="scope">
          <span v-if="scope.row.newSalesmanName">{{ scope.row.newSalesmanName }}</span>
          <span v-else style="color: #999;">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="operationReason" header-align="center" align="center" label="操作原因">
        <template slot-scope="scope">
          <span v-if="scope.row.operationReason">{{ scope.row.operationReason }}</span>
          <span v-else style="color: #999;">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="bindingSource" header-align="center" align="center" label="绑定来源">
        <template slot-scope="scope">
          <span v-if="scope.row.bindingSource">{{ scope.row.bindingSource }}</span>
          <span v-else style="color: #999;">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="operatorType" header-align="center" align="center" label="操作人类型">
        <template slot-scope="scope">
          {{ getOperatorTypeText(scope.row.operatorType) }}
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper" style="margin-top: 20px;">
    </el-pagination>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      wxUserId: null,
      historyList: [],
      historyLoading: false,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      searchForm: {
        operationType: ''
      }
    }
  },
  methods: {
    init(wxUserId) {
      this.wxUserId = wxUserId
      this.visible = true
      this.pageIndex = 1
      this.getHistoryList()
    },

    // 获取历史记录
    getHistoryList() {
      this.historyLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/wxuserbinding/history'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'wxUserId': this.wxUserId,
          'operationType': this.searchForm.operationType
        })
      }).then(({ data }) => {
        if (data && data.code ===200) {
          this.historyList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.historyList = []
          this.totalPage = 0
        }
        this.historyLoading = false
      }).catch(() => {
        this.historyLoading = false
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getHistoryList()
    },

    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getHistoryList()
    },

    // 获取操作类型文本
    getOperationTypeText(type) {
      const typeMap = {
        1: '新增绑定',
        2: '更换业务员',
        3: '解除绑定',
        4: '系统自动解绑'
      }
      return typeMap[type] || '未知'
    },

    // 获取操作类型标签类型
    getOperationTypeTagType(type) {
      const typeMap = {
        1: 'success',
        2: 'warning',
        3: 'danger',
        4: 'info'
      }
      return typeMap[type] || ''
    },

    // 获取操作人类型文本
    getOperatorTypeText(type) {
      const typeMap = {
        1: '客户自己',
        2: '业务员',
        3: '管理员',
        4: '系统'
      }
      return typeMap[type] || '未知'
    }
  }
}
</script>

<style scoped>
.history-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
