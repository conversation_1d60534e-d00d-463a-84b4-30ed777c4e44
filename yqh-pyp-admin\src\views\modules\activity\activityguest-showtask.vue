<template>
  <el-dialog
    title="学术任务"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <div v-if="topic.length > 0">
      <h2 style="text-align: center">主题主持任务</h2>
      <el-table :data="topic" border style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="主题名称"
        >
        </el-table-column>
        <el-table-column
          prop="placeName"
          header-align="center"
          align="center"
          label="主题所属场地"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          header-align="center"
          align="center"
          label="开始时间"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          header-align="center"
          align="center"
          label="结束时间"
        >
        </el-table-column>
      </el-table>
    </div>
    <div v-if="topicSpeaker.length > 0">
      <h2 style="text-align: center">主题主席任务</h2>
      <el-table :data="topicSpeaker" border style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="主题名称"
        >
        </el-table-column>
        <el-table-column
          prop="placeName"
          header-align="center"
          align="center"
          label="主题所属场地"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          header-align="center"
          align="center"
          label="开始时间"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          header-align="center"
          align="center"
          label="结束时间"
        >
        </el-table-column>
      </el-table>
    </div>
    <div v-if="scheduleSpeaker.length > 0">
      <h2 style="text-align: center">日程主持任务</h2>
      <el-table :data="scheduleSpeaker" border style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="日程名称"
        >
        </el-table-column>
        <el-table-column
          prop="placeTopicName"
          header-align="center"
          align="center"
          label="日程所属主题"
        >
        </el-table-column>
        <el-table-column
          prop="placeName"
          header-align="center"
          align="center"
          label="日程所属场地"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          header-align="center"
          align="center"
          label="开始时间"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          header-align="center"
          align="center"
          label="结束时间"
        >
        </el-table-column>
      </el-table>
    </div>
    <div v-if="schedule.length > 0">
      <h2 style="text-align: center">日程讲课任务</h2>
      <el-table :data="schedule" border style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="日程名称"
        >
        </el-table-column>
        <el-table-column
          prop="placeTopicName"
          header-align="center"
          align="center"
          label="日程所属主题"
        >
        </el-table-column>
        <el-table-column
          prop="placeName"
          header-align="center"
          align="center"
          label="日程所属场地"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          header-align="center"
          align="center"
          label="开始时间"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          header-align="center"
          align="center"
          label="结束时间"
        >
        </el-table-column>
      </el-table>
    </div>
    <div v-if="scheduleDiscuss.length > 0">
      <h2 style="text-align: center">日程讨论任务</h2>
      <el-table :data="scheduleDiscuss" border style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="日程名称"
        >
        </el-table-column>
        <el-table-column
          prop="placeTopicName"
          header-align="center"
          align="center"
          label="日程所属主题"
        >
        </el-table-column>
        <el-table-column
          prop="placeName"
          header-align="center"
          align="center"
          label="日程所属场地"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          header-align="center"
          align="center"
          label="开始时间"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          header-align="center"
          align="center"
          label="结束时间"
        >
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      id: "",
      topic: [],
      topicSpeaker: [],
      schedule: [],
      scheduleSpeaker: [],
      scheduleDiscuss: [],
    };
  },
  methods: {
    init(id) {
      this.id = id || 0;
      this.visible = true;
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityguest/getTopicAndSchedule/${this.id}`
        ),
        method: "get",
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.topic = data.result.topic;
          this.topicSpeaker = data.result.topicSpeaker;
          this.schedule = data.result.schedule;
          this.scheduleSpeaker = data.result.scheduleSpeaker;
          this.scheduleDiscuss = data.result.scheduleDiscuss;
        }
      });
    },
    tableRowClassName({ row, rowIndex }) {
        if(row.isRepeat) {
          return 'row-row';
        }
        return '';
    }
  },
};
</script>
<style lang="scss">

.el-table .row-row {
    background-color: #FFFF00 !important;
}
</style>