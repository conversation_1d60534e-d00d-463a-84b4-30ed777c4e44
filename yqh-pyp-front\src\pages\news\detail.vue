<template>
  <div>
      <van-card
        style="background: white"
        :thumb="!merchantInfo.picUrl ? 'van-icon' : merchantInfo.picUrl"
      >
        <div slot="title" class="title" style="font-size: 18px">{{ merchantInfo.name }}</div>
        <div
          slot="desc"
          style="padding-top: 10px; font-size: 14px; color: grey"
        >
        <div>{{merchantInfo.brief}}</div>
        </div>
        <template #num>
          <span>{{ merchantInfo.createOn }}</span>
        </template>
      </van-card>
    <div
      class="content"
      v-html="merchantInfo.content"
      @click="showImg($event)"
    ></div>
  </div>
</template>

<script>
import { isURL } from "@/js/validate";

export default {
  components: {},
  data() {
    return {
      openid: undefined,
      merchantId: undefined,
      merchantInfo: {},
    };
  },
  mounted() {
    this.openid = this.$cookie.get("openid");
    this.merchantId = this.$route.query.id;
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/web/news/news/info/${this.merchantId}`).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          document.title = this.merchantInfo.name;
        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  margin-top: 10px;
  background-color: white;
  /deep/ p {
    width: 100%;
  }
  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
.title {
  font-size: 18px;
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
</style>