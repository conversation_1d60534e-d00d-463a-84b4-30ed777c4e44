<template>
  <div class="mod-channel-salesman">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="dataForm.channelId" placeholder="所属渠道" clearable>
          <el-option v-for="channel in channelList" :key="channel.id" :label="channel.name" :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('channel:salesman:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('channel:salesman:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="姓名">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="编号">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="手机号">
      </el-table-column>
      <el-table-column prop="channelName" header-align="center" align="center" label="所属渠道">
      </el-table-column>
      <el-table-column prop="department" header-align="center" align="center" label="部门">
      </el-table-column>
      <el-table-column prop="position" header-align="center" align="center" label="职位">
      </el-table-column>
      <el-table-column prop="parentName" header-align="center" align="center" label="上级业务员">
        <template slot-scope="scope">
          <span>{{ scope.row.parentName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" header-align="center" align="center" label="层级">
      </el-table-column>
      <el-table-column prop="totalOrders" header-align="center" align="center" label="订单数">
      </el-table-column>
      <el-table-column prop="totalAmount" header-align="center" align="center" label="销售额">
        <template slot-scope="scope">
          <span>¥{{ (scope.row.totalAmount || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalCommission" header-align="center" align="center" label="佣金">
        <template slot-scope="scope">
          <span>¥{{ (scope.row.totalCommission || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small" type="success">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="180" label="创建时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('channel:salesman:update')" type="text" size="small"
            @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="isAuth('channel:salesman:delete')" type="text" size="small"
            @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button type="text" size="small" @click="viewDetailsHandle(scope.row)">详情</el-button>
          <el-button type="text" size="small" @click="commissionConfigHandle(scope.row.id, scope.row.name)">佣金配置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <!-- 详情弹窗 -->
    <el-dialog
      title="业务员详情"
      :visible.sync="detailsDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      class="clean-dialog">

      <!-- 简洁的头部信息 -->
      <div class="header-info">
        <div class="left-info">
          <h3 class="name">{{ selectedSalesman.name }}</h3>
          <div class="meta-info">
            <span class="code">{{ selectedSalesman.code }}</span>
            <el-divider direction="vertical"></el-divider>
            <span class="channel">{{ selectedSalesman.channelName || '未分配渠道' }}</span>
            <el-divider direction="vertical"></el-divider>
            <el-tag :type="selectedSalesman.status === 1 ? 'success' : 'danger'" size="mini">
              {{ selectedSalesman.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </div>
        <div class="right-info">
          <el-button size="small" @click="editSalesman(selectedSalesman)">编辑</el-button>
        </div>
      </div>

      <!-- 核心数据展示 -->
      <el-row :gutter="15" class="data-cards">
        <el-col :span="6">
          <div class="data-card">
            <div class="number">{{ selectedSalesman.totalOrders || 0 }}</div>
            <div class="label">总订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card">
            <div class="number">¥{{ (selectedSalesman.totalAmount || 0).toFixed(0) }}</div>
            <div class="label">销售额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card">
            <div class="number">¥{{ (selectedSalesman.totalCommission || 0).toFixed(0) }}</div>
            <div class="label">佣金</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card">
            <div class="number">{{ selectedSalesman.childrenCount || 0 }}</div>
            <div class="label">下级</div>
          </div>
        </el-col>
      </el-row>

      <!-- 详细信息 -->
      <div class="detail-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-group">
              <h4>基本信息</h4>
              <div class="info-item">
                <span class="label">手机号：</span>
                <span class="value">{{ selectedSalesman.mobile }}</span>
              </div>
              <div class="info-item" v-if="selectedSalesman.email">
                <span class="label">邮箱：</span>
                <span class="value">{{ selectedSalesman.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">部门：</span>
                <span class="value">{{ selectedSalesman.department || '未分配' }}</span>
              </div>
              <div class="info-item">
                <span class="label">职位：</span>
                <span class="value">{{ selectedSalesman.position || '业务员' }}</span>
              </div>
              <div class="info-item">
                <span class="label">上级：</span>
                <span class="value">{{ selectedSalesman.parentName || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="label">层级：</span>
                <span class="value">第{{ selectedSalesman.level || 1 }}级</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-group">
              <h4>业绩详情</h4>
              <div class="info-item">
                <span class="label">活动订单：</span>
                <span class="value">{{ selectedSalesman.activityOrders || 0 }}笔</span>
              </div>
              <div class="info-item">
                <span class="label">充值订单：</span>
                <span class="value">{{ selectedSalesman.rechargeOrders || 0 }}笔</span>
              </div>
              <div class="info-item">
                <span class="label">佣金比例：</span>
                <span class="value">
                  {{ selectedSalesman.totalAmount > 0 ?
                      ((selectedSalesman.totalCommission / selectedSalesman.totalAmount) * 100).toFixed(2) + '%' :
                      '0%' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ selectedSalesman.createOn }}</span>
              </div>
              <div class="info-item" v-if="selectedSalesman.tags">
                <span class="label">标签：</span>
                <span class="value">
                  <el-tag
                    v-for="tag in getTagList(selectedSalesman.tags)"
                    :key="tag"
                    size="mini"
                    style="margin-right: 5px;">
                    {{ tag }}
                  </el-tag>
                </span>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="info-group" v-if="selectedSalesman.remarks">
          <h4>备注</h4>
          <div class="remarks">{{ selectedSalesman.remarks }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './channel-salesman-add-or-update'

export default {
  components: {
    AddOrUpdate
  },
  data() {
    return {
      dataForm: {
        name: '',
        mobile: '',
        channelId: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      channelList: [],
      addOrUpdateVisible: false,
      detailsDialogVisible: false,
      selectedSalesman: {},
      activeDetailTab: 'basic'
    }
  },
  activated() {
    this.dataForm.channelId = this.$route.query.channelId
    this.getDataList()
    // this.getChannelList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/channel/salesman/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'channelId': this.dataForm.channelId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取渠道列表
    getChannelList() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.channelList || []
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 查看详情
    viewDetailsHandle(row) {
      this.selectedSalesman = row
      this.activeDetailTab = 'basic'
      this.detailsDialogVisible = true
    },
    // 编辑业务员
    editSalesman(salesman) {
      this.detailsDialogVisible = false
      this.addOrUpdateHandle(salesman.id)
    },
    // 获取层级标签类型
    getLevelTagType(level) {
      if (level <= 1) return 'success'
      if (level <= 3) return 'primary'
      if (level <= 5) return 'warning'
      return 'danger'
    },
    // 获取标签列表
    getTagList(tags) {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/channel/salesman/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 佣金配置
    commissionConfigHandle(salesmanId, salesmanName) {
      this.$router.push({
        path: '/salesman-commission-config',
        query: {
          salesmanId: salesmanId,
          salesmanName: salesmanName
        }
      })
    }
  }
}
</script>

<style scoped>
.clean-dialog >>> .el-dialog__body {
  padding: 24px;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
  margin-bottom: 20px;
}

.left-info .name {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.meta-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.code {
  font-weight: 500;
}

.channel {
  color: #409EFF;
}

.data-cards {
  margin-bottom: 24px;
}

.data-card {
  text-align: center;
  padding: 20px 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.data-card .number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.data-card .label {
  font-size: 13px;
  color: #909399;
}

.detail-section {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
}

.info-group {
  margin-bottom: 20px;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-group h4 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item .label {
  width: 80px;
  color: #606266;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  flex: 1;
}

.remarks {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  min-height: 60px;
}
</style>
