package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionSettlementService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金结算管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/salesman/commission/settlement")
@Api(tags = "业务员佣金结算管理")
public class SalesmanCommissionSettlementController extends AbstractController {
    
    @Autowired
    private SalesmanCommissionSettlementService settlementService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:commission:settlement:list")
    @ApiOperation(value = "结算批次列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<SalesmanCommissionSettlementEntity> page = settlementService.queryPage(params);
        return R.okList( page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:commission:settlement:info")
    @ApiOperation(value = "结算批次信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        SalesmanCommissionSettlementEntity settlement = settlementService.getById(id);
        return R.ok().put("settlement", settlement);
    }

    /**
     * 根据批次号获取信息
     */
    @RequestMapping("/getByBatchNo")
    @RequiresPermissions("salesman:commission:settlement:info")
    @ApiOperation(value = "根据批次号获取结算信息", notes = "")
    public R getByBatchNo(@RequestParam String batchNo, @CookieValue String appid) {
        SalesmanCommissionSettlementEntity settlement = settlementService.getByBatchNo(batchNo, appid);
        return R.ok().put("settlement", settlement);
    }

    /**
     * 结算统计
     */
    @RequestMapping("/stats")
    @RequiresPermissions("salesman:commission:settlement:list")
    @ApiOperation(value = "结算统计", notes = "")
    public R stats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        Map<String, Object> stats = settlementService.getSettlementStats(params);
        return R.ok().put("stats", stats);
    }

    /**
     * 创建结算批次
     */
    @RequestMapping("/createBatch")
    @RequiresPermissions("salesman:commission:settlement:create")
    @SysLog("创建佣金结算批次")
    @ApiOperation(value = "创建结算批次", notes = "")
    public R createBatch(@RequestBody Map<String, Object> params, @CookieValue String appid) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> salesmanIds = (List<Long>) params.get("salesmanIds");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");
            String remarks = (String) params.get("remarks");
            
            SalesmanCommissionSettlementEntity settlement = settlementService.createSettlementBatch(
                    salesmanIds, startTime, endTime, remarks, appid);
            
            return R.ok().put("settlement", settlement);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 执行结算
     */
    @RequestMapping("/execute")
    @RequiresPermissions("salesman:commission:settlement:execute")
    @SysLog("执行佣金结算")
    @ApiOperation(value = "执行结算", notes = "")
    public R execute(@RequestParam String batchNo, @CookieValue String appid) {
        try {
            boolean success = settlementService.executeSettlement(batchNo, appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("执行结算失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 取消结算
     */
    @RequestMapping("/cancel")
    @RequiresPermissions("salesman:commission:settlement:cancel")
    @SysLog("取消佣金结算")
    @ApiOperation(value = "取消结算", notes = "")
    public R cancel(@RequestParam String batchNo, @CookieValue String appid) {
        try {
            boolean success = settlementService.cancelSettlement(batchNo, appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("取消结算失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 生成批次号
     */
    @RequestMapping("/generateBatchNo")
    @RequiresPermissions("salesman:commission:settlement:create")
    @ApiOperation(value = "生成批次号", notes = "")
    public R generateBatchNo() {
        String batchNo = settlementService.generateBatchNo();
        return R.ok().put("batchNo", batchNo);
    }

    /**
     * 删除结算批次
     */
    @RequestMapping("/delete")
    @RequiresPermissions("salesman:commission:settlement:delete")
    @SysLog("删除佣金结算批次")
    @ApiOperation(value = "删除结算批次", notes = "")
    public R delete(@RequestBody Long[] ids) {
        // 只能删除已取消的结算批次
        for (Long id : ids) {
            SalesmanCommissionSettlementEntity settlement = settlementService.getById(id);
            if (settlement != null && settlement.getStatus() != 2) { // 2-已取消
                return R.error("只能删除已取消的结算批次");
            }
        }
        
        settlementService.removeByIds(java.util.Arrays.asList(ids));
        return R.ok();
    }
}
