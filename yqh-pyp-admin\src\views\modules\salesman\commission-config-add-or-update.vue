<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="业务员" prop="salesmanId">
        <el-select
          v-model="dataForm.salesmanId"
          placeholder="请选择业务员"
          filterable
          :disabled="presetSalesmanId !== null">
          <el-option v-for="salesman in salesmanList" :key="salesman.id"
            :label="salesman.name + '(' + salesman.code + ')'" :value="salesman.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="佣金类型" prop="commissionType">
        <el-select v-model="dataForm.commissionType" placeholder="请选择佣金类型" @change="commissionTypeChange">
          <el-option v-for="type in commissionTypes" :key="type.code" :label="type.desc" :value="type.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计算方式" prop="calculationType">
        <el-select v-model="dataForm.calculationType" placeholder="请选择计算方式" @change="calculationTypeChange">
          <el-option v-for="type in availableCalculationTypes" :key="type.code" :label="type.desc" :value="type.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="佣金值" prop="commissionValue">
        <el-input-number v-model="dataForm.commissionValue" :precision="4" :min="0"
          :max="dataForm.calculationType === 2 ? 1 : 999999" placeholder="请输入佣金值">
        </el-input-number>
        <span v-if="dataForm.calculationType === 1" style="margin-left: 10px;">元</span>
        <span v-if="dataForm.calculationType === 2" style="margin-left: 10px;">（比例，如0.1表示10%）</span>
      </el-form-item>
      <el-form-item label="最小金额" prop="minAmount">
        <el-input-number v-model="dataForm.minAmount" :precision="2" :min="0" placeholder="最小佣金金额（可选）">
        </el-input-number>
        <span style="margin-left: 10px;">元</span>
      </el-form-item>
      <el-form-item label="最大金额" prop="maxAmount">
        <el-input-number v-model="dataForm.maxAmount" :precision="2" :min="0" placeholder="最大佣金金额（可选）">
        </el-input-number>
        <span style="margin-left: 10px;">元</span>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="生效时间" prop="effectiveDate">
        <el-date-picker v-model="dataForm.effectiveDate" type="datetime" placeholder="选择生效时间"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="失效时间" prop="expiryDate">
        <el-date-picker v-model="dataForm.expiryDate" type="datetime" placeholder="选择失效时间（可选）"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" placeholder="备注信息"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        salesmanId: '',
        commissionType: '',
        calculationType: '',
        commissionValue: 0,
        minAmount: null,
        maxAmount: null,
        status: 1,
        effectiveDate: '',
        expiryDate: '',
        remarks: ''
      },
      salesmanList: [],
      commissionTypes: [],
      calculationTypes: [],
      presetSalesmanId: null, // 预设的业务员ID
      dataRule: {
        salesmanId: [
          { required: true, message: '业务员不能为空', trigger: 'change' }
        ],
        commissionType: [
          { required: true, message: '佣金类型不能为空', trigger: 'change' }
        ],
        calculationType: [
          { required: true, message: '计算方式不能为空', trigger: 'change' }
        ],
        commissionValue: [
          { required: true, message: '佣金值不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 根据佣金类型过滤可用的计算方式
    availableCalculationTypes() {
      if (this.dataForm.commissionType === 3) {
        // 用户转发佣金只支持固定金额
        return this.calculationTypes.filter(type => type.code === 1)
      }
      return this.calculationTypes
    }
  },
  methods: {
    init(id, presetSalesmanId) {
      this.dataForm.id = id || 0
      this.presetSalesmanId = presetSalesmanId || null
      this.visible = true
      this.getSalesmanList()
      this.getCommissionTypes()
      this.getCalculationTypes()

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()

        // 如果有预设业务员ID，则设置到表单中
        if (this.presetSalesmanId && !this.dataForm.id) {
          this.dataForm.salesmanId = this.presetSalesmanId
        }

        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/salesman/commission/config/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.salesmanId = data.config.salesmanId
              this.dataForm.commissionType = data.config.commissionType
              this.dataForm.calculationType = data.config.calculationType
              this.dataForm.commissionValue = data.config.commissionValue
              this.dataForm.minAmount = data.config.minAmount
              this.dataForm.maxAmount = data.config.maxAmount
              this.dataForm.status = data.config.status
              this.dataForm.effectiveDate = data.config.effectiveDate
              this.dataForm.expiryDate = data.config.expiryDate
              this.dataForm.remarks = data.config.remarks
            }
          })
        }
      })
    },
    // 获取业务员列表
    getSalesmanList() {
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.salesmanList = data.page.list
        }
      })
    },
    // 获取佣金类型选项
    getCommissionTypes() {
      this.$http({
        url: this.$http.adornUrl('/salesman/commission/config/getCommissionTypes'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.commissionTypes = data.commissionTypes
        }
      })
    },
    // 获取计算方式选项
    getCalculationTypes() {
      this.$http({
        url: this.$http.adornUrl('/salesman/commission/config/getCalculationTypes'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.calculationTypes = data.calculationTypes
        }
      })
    },
    // 佣金类型变化
    commissionTypeChange() {
      // 如果选择用户转发佣金，自动设置为固定金额
      if (this.dataForm.commissionType === 3) {
        this.dataForm.calculationType = 1
      }
    },
    // 计算方式变化
    calculationTypeChange() {
      // 重置佣金值
      this.dataForm.commissionValue = 0
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 验证最小最大金额
          if (this.dataForm.minAmount && this.dataForm.maxAmount &&
            this.dataForm.minAmount > this.dataForm.maxAmount) {
            this.$message.error('最小金额不能大于最大金额')
            return
          }

          // 验证百分比范围
          if (this.dataForm.calculationType === 2 &&
            (this.dataForm.commissionValue < 0 || this.dataForm.commissionValue > 1)) {
            this.$message.error('百分比佣金值应在0-1之间')
            return
          }

          this.$http({
            url: this.$http.adornUrl(`/salesman/commission/config/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'salesmanId': this.dataForm.salesmanId,
              'commissionType': this.dataForm.commissionType,
              'calculationType': this.dataForm.calculationType,
              'commissionValue': this.dataForm.commissionValue,
              'minAmount': this.dataForm.minAmount,
              'maxAmount': this.dataForm.maxAmount,
              'status': this.dataForm.status,
              'effectiveDate': this.dataForm.effectiveDate,
              'expiryDate': this.dataForm.expiryDate,
              'remarks': this.dataForm.remarks
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
