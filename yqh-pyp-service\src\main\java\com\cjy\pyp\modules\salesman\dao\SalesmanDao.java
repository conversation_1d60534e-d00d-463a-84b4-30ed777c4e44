package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 业务员数据访问层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@Mapper
public interface SalesmanDao extends BaseMapper<SalesmanEntity> {

    /**
     * 分页查询业务员列表（包含统计信息）
     * @param page 分页参数
     * @param name 业务员姓名
     * @param mobile 手机号
     * @param appid 应用ID
     * @return 分页结果
     */
    List<SalesmanEntity> selectPageWithStats(Map<String,Object> params);

    /**
     * 查询业务员订单总体统计
     * @param params 查询参数
     * @return 统计数据
     */
    Map<String, Object> selectOrderStats(Map<String, Object> params);
}
