<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="考卷名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="考卷名称"></el-input>
    </el-form-item>
    <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="类型" filterable>
          <el-option v-for="item in examType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="时间类型" prop="examType">
        <el-select v-model="dataForm.examType" placeholder="时间类型" filterable>
          <el-option v-for="item in examTimeType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item v-if="dataForm.examType == 0" label="考试时间" prop="times">
      <el-date-picker v-model="dataForm.times" style="width: 100%" @change="dateChange" type="datetimerange" value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
    </el-form-item>
    <!-- v-if="dataForm.examType == 1"  -->
    <el-form-item label="考试时长" prop="examTime">
      <el-input v-model="dataForm.examTime" placeholder="考试时长">
      <template slot="append">分钟</template>
      </el-input>
    </el-form-item>
    <el-form-item label="满分分数" prop="fullMark">
      <el-input v-model="dataForm.fullMark" placeholder="满分分数"></el-input>
    </el-form-item>
    <el-form-item label="及格分数" prop="passMark">
      <el-input v-model="dataForm.passMark" placeholder="及格分数"></el-input>
    </el-form-item>
    <!-- <el-form-item label="可考试次数" prop="examCount">
      <el-input v-model="dataForm.examCount" placeholder="可考试次数"></el-input>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { examType,examTimeType } from "@/data/exam"
  export default {
    data () {
      return {
        examType: examType,
        examTimeType: examTimeType,
        visible: false,
        dataForm: {
          id: 0,
          times: [],
          name: '',
          activityId: '',
          type: 0,
          examType: 0,
          examStartTime: '',
          examTime: '',
          examEndTime: '',
          fullMark: 0,
          passMark: 0,
          examCount: 1,
        },
        dataRule: {
          times: [{
            required: true,
            message: '考试时间不能为空',
            trigger: 'blur'
          }],
          name: [
            { required: true, message: '考卷名称不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '活动表id不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '类型', trigger: 'blur' }
          ],
          examType: [
            { required: true, message: '考试时间类型', trigger: 'blur' }
          ],
          examStartTime: [
            { required: true, message: '考试开始时间', trigger: 'blur' }
          ],
          examTime: [
            { required: true, message: '考试时长', trigger: 'blur' }
          ],
          examEndTime: [
            { required: true, message: '考试结束时间', trigger: 'blur' }
          ],
          fullMark: [
            { required: true, message: '满分分数不能为空', trigger: 'blur' }
          ],
          passMark: [
            { required: true, message: '及格分数不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/exam/exam/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$set(this.dataForm, "times", data.exam.examStartTime ?[data.exam.examStartTime, data.exam.examEndTime] : [])
                this.dataForm.name = data.exam.name
                this.dataForm.activityId = data.exam.activityId
                this.dataForm.type = data.exam.type
                this.dataForm.examType = data.exam.examType
                this.dataForm.examStartTime = data.exam.examStartTime
                this.dataForm.examTime = data.exam.examTime
                this.dataForm.examEndTime = data.exam.examEndTime
                this.dataForm.fullMark = data.exam.fullMark
                this.dataForm.passMark = data.exam.passMark
                this.dataForm.examCount = data.exam.examCount
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/exam/exam/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'activityId': this.dataForm.activityId,
                'type': this.dataForm.type,
                'examType': this.dataForm.examType,
                'examStartTime': this.dataForm.examStartTime,
                'examTime': this.dataForm.examTime,
                'examEndTime': this.dataForm.examEndTime,
                'fullMark': this.dataForm.fullMark,
                'passMark': this.dataForm.passMark,
                'examCount': this.dataForm.examCount,
              })
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      dateChange(v) {
        this.dataForm.examStartTime = v[0];
        this.dataForm.examEndTime = v[1];
        console.log(v)
      }
    }
  }
</script>
