<template>
    <div>
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
            label-width="140px">

            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确定议程任务</div>
                    <el-col :span="8">
                        <el-form-item label="开启议程任务" prop="guestSchedule">
                            <el-select v-model="dataForm.guestSchedule" placeholder="开启议程任务" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="确定议程任务时间" prop="guestScheduleStart">
                            <el-date-picker v-model="times1" @change="guestScheduleStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确定专家基本信息</div>
                    <el-col :span="8">
                        <el-form-item label="开启确定专家信息" prop="guestInfo">
                            <el-select v-model="dataForm.guestInfo" placeholder="开启确定专家信息" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="确定专家信息时间" prop="guestInfoStart">
                            <el-date-picker v-model="times2" @change="guestInfoStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        专家行程</div>
                    <el-col :span="8">
                        <el-form-item label="开启专家行程" prop="guestTrip">
                            <el-select v-model="dataForm.guestTrip" placeholder="开启专家行程" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="专家行程时间" prop="guestTripStart">
                            <el-date-picker v-model="times3" @change="guestTripStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        劳务费信息收集</div>
                    <el-col :span="8">
                        <el-form-item label="劳务费信息收集" prop="guestServiceInfo">
                            <el-select v-model="dataForm.guestServiceInfo" placeholder="劳务费信息收集" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="劳务费信息收集时间" prop="guestServiceInfoStart">
                            <el-date-picker v-model="times5" @change="guestServiceInfoStartdateChange"
                                type="datetimerange" value-format="yyyy/MM/dd HH:mm:ss" range-separator="至"
                                start-placeholder="开始时间" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        劳务费签字确认</div>
                    <el-col :span="8">
                        <el-form-item label="劳务费签字确认" prop="guestService">
                            <el-select v-model="dataForm.guestService" placeholder="劳务费签字确认" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="劳务费签字确认时间" prop="guestServiceStart">
                            <el-date-picker v-model="times4" @change="guestServiceStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="隐藏劳务费" prop="hiddenServicefee">
                            <el-select v-model="dataForm.hiddenServicefee" placeholder="隐藏劳务费" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="12">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        确认是否接送</div>
                    <el-col :span="8">
                        <el-form-item label="确认是否接送" prop="guestLink">
                            <el-select v-model="dataForm.guestLink" placeholder="确认是否接送" filterable>
                                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="确认是否接送时间" prop="guestLinkStart">
                            <el-date-picker v-model="times6" @change="guestLinkStartdateChange" type="datetimerange"
                                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
                                end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>

                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="接默认地点" prop="linkStart">
                            <el-input v-model="dataForm.linkStart" placeholder="接默认地点" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="送默认地点" prop="linkEnd">
                            <el-input v-model="dataForm.linkEnd" placeholder="送默认地点" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        其他配置</div>
                    <el-col :span="12">
                        <el-form-item label="所属协会" prop="association">
                            <el-input v-model="dataForm.association" placeholder="所属协会" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="会议联系人" prop="contact">
                            <el-input v-model="dataForm.contact" placeholder="会议联系人" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="mobile">
                            <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="会议联系人微信" prop="qrcode">
                            <el-upload class="avatar-uploader" list-type="picture-card" :before-upload="checkFileSize"
                                :show-file-list="false" accept=".jpg, .jpeg, .png, .gif"
                                :on-success="qrcodeSuccessHandle" :action="url">
                                <img width="100px" v-if="dataForm.qrcode" :src="dataForm.qrcode" class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card shadow="hover" class="card" style="margin-bottom: 10px;">
                <el-row class="row" :gutter="24">
                    <div style="text-align: center;height: 60px;line-height: 60px;font-size: 24px;font-weight: 600;">
                        专家行程配置</div>
                        
                        <el-col :span="8">
                        <el-form-item label="飞机出票规则" prop="planeTicketType">
                            <el-select v-model="dataForm.planeTicketType" placeholder="飞机出票规则" filterable>
                                <el-option v-for="item in ticketType" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="火车出票规则" prop="trainTicketType">
                            <el-select v-model="dataForm.trainTicketType" placeholder="火车出票规则" filterable>
                                <el-option v-for="item in ticketType" :key="item.key" :label="item.value"
                                    :value="item.key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
        </el-form>
        <div style="text-align: center;">
            <el-button @click="$router.go(-1)">返回</el-button>
            <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
        </div>
    </div>
</template>

<script>
import { yesOrNo } from "@/data/common";
import { ticketType } from "@/data/activity";
export default {
    data() {
        return {
            url: '',
            times1: [],
            times2: [],
            times3: [],
            times4: [],
            times5: [],
            times6: [],
            ticketType,
            yesOrNo,
            dataForm: {
                repeatToken: '',
                id: '',
                activityId: '',
                guestScheduleStart: '',
                guestScheduleEnd: '',
                guestSchedule: 0,
                guestInfoStart: '',
                guestInfoEnd: '',
                guestInfo: 0,
                guestTripStart: '',
                guestTripEnd: '',
                guestTrip: 0,
                guestServiceStart: '',
                guestServiceEnd: '',
                guestService: 0,
                guestServiceInfoStart: '',
                guestServiceInfoEnd: '',
                guestServiceInfo: 0,
                guestLinkStart: '',
                guestLinkEnd: '',
                guestLink: 0,
                planeTicketType: 0,
                trainTicketType: 0,
                contact: '',
                mobile: '',
                qrcode: '',
                association: '',
                linkStart: '',
                linkEnd: '',
                hiddenServicefee: 0,
            },
            dataRule: {
                // startCertDate: [
                //     { required: true, message: "期初建账时间不能为空", trigger: "blur" },
                // ],
            },
        };
    },
    mounted() {
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
        this.dataForm.activityId = this.$route.query.activityId
        this.getToken();
        this.init();
    },
    methods: {
        guestScheduleStartdateChange(v) {
            this.dataForm.guestScheduleStart = v[0];
            this.dataForm.guestScheduleEnd = v[1];
        },
        guestInfoStartdateChange(v) {
            this.dataForm.guestInfoStart = v[0];
            this.dataForm.guestInfoEnd = v[1];
        },
        guestTripStartdateChange(v) {
            this.dataForm.guestTripStart = v[0];
            this.dataForm.guestTripEnd = v[1];
        },
        guestServiceStartdateChange(v) {
            this.dataForm.guestServiceStart = v[0];
            this.dataForm.guestServiceEnd = v[1];
        },
        guestServiceInfoStartdateChange(v) {
            this.dataForm.guestServiceInfoStart = v[0];
            this.dataForm.guestServiceInfoEnd = v[1];
        },
        guestLinkStartdateChange(v) {
            this.dataForm.guestLinkStart = v[0];
            this.dataForm.guestLinkEnd = v[1];
        },
        init() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.$http({
                    url: this.$http.adornUrl(
                        `/activity/activityconfig/findByActivityId/${this.dataForm.activityId}`
                    ),
                    method: "get",
                    params: this.$http.adornParams(),
                }).then(({ data }) => {
                    if (data && data.code === 200) {
                        this.dataForm.id = data.result.id
                        this.dataForm.activityId = data.result.activityId
                        this.dataForm.guestScheduleStart = data.result.guestScheduleStart
                        this.dataForm.guestScheduleEnd = data.result.guestScheduleEnd
                        this.times1 = this.dataForm.guestScheduleStart ? [
                            data.result.guestScheduleStart,
                            data.result.guestScheduleEnd,
                        ] : [];
                        this.dataForm.guestSchedule = data.result.guestSchedule
                        this.dataForm.guestInfoStart = data.result.guestInfoStart
                        this.dataForm.guestInfoEnd = data.result.guestInfoEnd
                        this.times2 = this.dataForm.guestInfoStart ? [
                            data.result.guestInfoStart,
                            data.result.guestInfoEnd,
                        ] : [];
                        this.dataForm.guestInfo = data.result.guestInfo
                        this.dataForm.guestTripStart = data.result.guestTripStart
                        this.dataForm.guestTripEnd = data.result.guestTripEnd
                        this.times3 = this.dataForm.guestTripStart ? [
                            data.result.guestTripStart,
                            data.result.guestTripEnd,
                        ] : [];
                        this.dataForm.guestTrip = data.result.guestTrip
                        this.dataForm.guestServiceStart = data.result.guestServiceStart
                        this.dataForm.guestServiceEnd = data.result.guestServiceEnd
                        this.times4 = this.dataForm.guestServiceStart ? [
                            data.result.guestServiceStart,
                            data.result.guestServiceEnd,
                        ] : [];
                        this.dataForm.guestService = data.result.guestService
                        this.dataForm.guestServiceInfoStart = data.result.guestServiceInfoStart
                        this.dataForm.guestServiceInfoEnd = data.result.guestServiceInfoEnd
                        this.times5 = this.dataForm.guestServiceInfoStart ? [
                            data.result.guestServiceInfoStart,
                            data.result.guestServiceInfoEnd,
                        ] : [];
                        this.dataForm.guestLinkStart = data.result.guestLinkStart
                        this.dataForm.guestLinkEnd = data.result.guestLinkEnd
                        this.times6 = this.dataForm.guestLinkStart ? [
                            data.result.guestLinkStart,
                            data.result.guestLinkEnd,
                        ] : [];
                        this.dataForm.guestLink = data.result.guestLink
                        this.dataForm.association = data.result.association
                        this.dataForm.contact = data.result.contact
                        this.dataForm.mobile = data.result.mobile
                        this.dataForm.qrcode = data.result.qrcode
                        this.dataForm.planeTicketType = data.result.planeTicketType
                        this.dataForm.trainTicketType = data.result.trainTicketType
                        this.dataForm.linkStart = data.result.linkStart
                        this.dataForm.linkEnd = data.result.linkEnd
                        this.dataForm.hiddenServicefee = data.result.hiddenServicefee
                    }
                })
            });
        }, getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                .then(({ data }) => {
                    if (data && data.code === 200) {
                        this.dataForm.repeatToken = data.result;
                    }
                })
        },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 1000) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            },
            error(error) {
                console.log(error)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // 上传成功（背景）
    qrcodeSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.qrcode = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
        // 表单提交
        dataFormSubmit() {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    this.$http({
                        url: this.$http.adornUrl(`/activity/activityconfig/update`),
                        method: 'post',
                        data: this.$http.adornData({
                            'repeatToken': this.dataForm.repeatToken,
                            'id': this.dataForm.id,
                            'activityId': this.dataForm.activityId,
                            'guestScheduleStart': this.dataForm.guestScheduleStart,
                            'guestScheduleEnd': this.dataForm.guestScheduleEnd,
                            'guestSchedule': this.dataForm.guestSchedule,
                            'guestInfoStart': this.dataForm.guestInfoStart,
                            'guestInfoEnd': this.dataForm.guestInfoEnd,
                            'guestInfo': this.dataForm.guestInfo,
                            'guestTripStart': this.dataForm.guestTripStart,
                            'guestTripEnd': this.dataForm.guestTripEnd,
                            'guestTrip': this.dataForm.guestTrip,
                            'guestServiceStart': this.dataForm.guestServiceStart,
                            'guestServiceEnd': this.dataForm.guestServiceEnd,
                            'guestService': this.dataForm.guestService,
                            'guestServiceInfoStart': this.dataForm.guestServiceInfoStart,
                            'guestServiceInfoEnd': this.dataForm.guestServiceInfoEnd,
                            'guestServiceInfo': this.dataForm.guestServiceInfo,
                            'guestLinkStart': this.dataForm.guestLinkStart,
                            'guestLinkEnd': this.dataForm.guestLinkEnd,
                            'guestLink': this.dataForm.guestLink,
                            'association': this.dataForm.association,
                            'contact': this.dataForm.contact,
                            'mobile': this.dataForm.mobile,
                            'qrcode': this.dataForm.qrcode,
                            'planeTicketType': this.dataForm.planeTicketType,
                            'trainTicketType': this.dataForm.trainTicketType,
                            'linkStart': this.dataForm.linkStart,
                            'linkEnd': this.dataForm.linkEnd,
                            'hiddenServicefee': this.dataForm.hiddenServicefee,
                        }),
                    }).then(({ data }) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: "操作成功",
                                type: "success",
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false;
                                    this.$emit("refreshDataList");
                                },
                            });
                        } else {
                            this.$message.error(data.msg);
                            if (data.msg != '不能重复提交') {
                                this.getToken();
                            }
                        }
                        this.loading = false;
                    });
                }
            });
        },
    },
};
</script>
