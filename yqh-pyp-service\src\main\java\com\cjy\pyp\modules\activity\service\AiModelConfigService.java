package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * AI模型配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
public interface AiModelConfigService extends IService<AiModelConfigEntity> {

    PageUtils queryPage(Map<String, Object> params);
    
    /**
     * 根据模型编码查询配置
     */
    AiModelConfigEntity getByModelCode(String modelCode);
    
    /**
     * 查询启用的模型配置列表
     */
    List<AiModelConfigEntity> getEnabledModels();
    
    /**
     * 根据服务提供商查询模型列表
     */
    List<AiModelConfigEntity> getByProvider(String provider);
    
    /**
     * 获取默认模型配置
     */
    AiModelConfigEntity getDefaultModel();
    
    /**
     * 设置默认模型
     */
    void setDefaultModel(String modelCode);
    
    /**
     * 启用/禁用模型
     */
    void updateStatus(Long id, Integer status);
    
    /**
     * 验证模型配置是否有效
     */
    boolean validateConfig(AiModelConfigEntity config);
    
    /**
     * 测试模型连接
     */
    boolean testConnection(String modelCode);
}
