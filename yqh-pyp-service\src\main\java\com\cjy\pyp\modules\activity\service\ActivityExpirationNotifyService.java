package com.cjy.pyp.modules.activity.service;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;

/**
 * 活动过期通知服务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
public interface ActivityExpirationNotifyService {

    /**
     * 发送活动即将过期提醒
     * @param activity 活动实体
     * @param remainingDays 剩余天数
     * @return 操作结果
     */
    R sendExpirationReminder(ActivityEntity activity, Integer remainingDays);

    /**
     * 发送活动已过期通知
     * @param activity 活动实体
     * @return 操作结果
     */
    R sendExpirationNotification(ActivityEntity activity);

    /**
     * 发送活动续费成功通知
     * @param activity 活动实体
     * @param renewalDays 续费天数
     * @param newExpirationTime 新的过期时间
     * @return 操作结果
     */
    R sendRenewalSuccessNotification(ActivityEntity activity, Integer renewalDays, String newExpirationTime);

    /**
     * 批量发送即将过期提醒
     * @param days 提前天数
     * @return 操作结果
     */
    R batchSendExpirationReminders(Integer days);

    /**
     * 批量发送过期通知
     * @return 操作结果
     */
    R batchSendExpirationNotifications();
}
