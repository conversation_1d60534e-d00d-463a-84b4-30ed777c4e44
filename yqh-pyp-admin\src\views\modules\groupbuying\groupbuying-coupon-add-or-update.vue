<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="活动" prop="activityId">
            <el-select v-model="dataForm.activityId" placeholder="请选择活动" style="width: 100%;">
              <el-option
                v-for="activity in activityList"
                :key="activity.id"
                :label="activity.name"
                :value="activity.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="平台类型" prop="platformType">
            <el-select v-model="dataForm.platformType" placeholder="请选择平台类型" style="width: 100%;">
              <el-option label="抖音团购" value="douyin"></el-option>
              <el-option label="美团团购" value="meituan"></el-option>
              <el-option label="大众点评团购" value="dianping"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="团购券名称" prop="couponName">
            <el-input v-model="dataForm.couponName" placeholder="团购券名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="团购券描述" prop="couponDescription">
            <el-input v-model="dataForm.couponDescription" type="textarea" placeholder="团购券描述"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="原价" prop="originalPrice">
            <el-input-number v-model="dataForm.originalPrice" :precision="2" :min="0" placeholder="原价" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="团购价" prop="groupPrice">
            <el-input-number v-model="dataForm.groupPrice" :precision="2" :min="0" placeholder="团购价" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="优惠信息" prop="discountInfo">
            <el-input v-model="dataForm.discountInfo" placeholder="如：5折优惠"></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="团购券链接" prop="couponUrl">
            <el-input v-model="dataForm.couponUrl" placeholder="团购券链接"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="团购券ID" prop="couponId">
            <el-input v-model="dataForm.couponId" placeholder="平台提供的团购券ID"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="封面图片" prop="coverImage">
            <div class="image-upload-container">
              <div v-if="coverImages.length > 0" class="image-preview">
                <img :src="coverImages[0].url" alt="封面图片" class="preview-image" @click="previewImage(coverImages[0].url)">
                <div class="image-actions">
                  <el-button type="text" size="small" @click="openImageModal('coverImage')">更换</el-button>
                  <el-button type="text" size="small" @click="removeCoverImage()">删除</el-button>
                </div>
              </div>
              <div v-else class="upload-placeholder" @click="openImageModal('coverImage')">
                <i class="el-icon-plus"></i>
                <div>点击上传封面图片</div>
              </div>
            </div>
            <div style="color: #909399; font-size: 12px; margin-top: 5px;">建议尺寸：750*400，大小2MB以下</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="二维码链接" prop="qrCodeUrl">
            <el-input v-model="dataForm.qrCodeUrl" placeholder="二维码链接"></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- <el-row>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="dataForm.startTime"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="dataForm.endTime"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="总数量" prop="totalCount">
            <el-input-number v-model="dataForm.totalCount" :min="0" placeholder="总数量（空表示不限量）" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="已售数量" prop="soldCount">
            <el-input-number v-model="dataForm.soldCount" :min="0" placeholder="已售数量" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number v-model="dataForm.sortOrder" placeholder="排序（数字越大越靠前）" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="0">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注信息" prop="remarks">
            <el-input v-model="dataForm.remarks" type="textarea" placeholder="备注信息"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>

    <!-- 图片上传弹窗 -->
    <ImageUploadModal
      :visible.sync="imageModalVisible"
      :multiple="false"
      :max-count="1"
      :default-images="getCurrentImages()"
      @confirm="handleImageConfirm"
    />

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="imgDialogVisible" width="60%">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </el-dialog>
</template>

<script>
  export default {
    components: {
      ImageUploadModal: () => import("@/components/image-upload-modal")
    },
    data () {
      return {
        visible: false,
        activityList: [],
        // 图片上传相关
        imageModalVisible: false,
        currentImageField: '',
        coverImages: [],
        imgDialogVisible: false,
        dialogImageUrl: '',
        dataForm: {
          id: 0,
          activityId: '',
          platformType: '',
          couponName: '',
          couponDescription: '',
          originalPrice: null,
          groupPrice: null,
          discountInfo: '',
          couponUrl: '',
          couponId: '',
          qrCodeUrl: '',
          coverImage: '',
          startTime: null,
          endTime: null,
          totalCount: null,
          soldCount: 0,
          status: 1,
          sortOrder: 0,
          remarks: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '活动不能为空', trigger: 'change' }
          ],
          platformType: [
            { required: true, message: '平台类型不能为空', trigger: 'change' }
          ],
          couponName: [
            { required: true, message: '团购券名称不能为空', trigger: 'blur' }
          ],
          groupPrice: [
            { required: true, message: '团购价不能为空', trigger: 'blur' }
          ],
          // couponUrl: [
          //   { required: true, message: '团购券链接不能为空', trigger: 'blur' }
          // ]
        }
      }
    },
    methods: {
      init (id ,activityId) {
        this.dataForm.id = id || 0
        this.dataForm.activityId = activityId
        this.visible = true
        this.getActivityList()
        this.initImageArrays()
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/groupbuying/coupon/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.coupon.activityId
                this.dataForm.platformType = data.coupon.platformType
                this.dataForm.couponName = data.coupon.couponName
                this.dataForm.couponDescription = data.coupon.couponDescription
                this.dataForm.originalPrice = data.coupon.originalPrice
                this.dataForm.groupPrice = data.coupon.groupPrice
                this.dataForm.discountInfo = data.coupon.discountInfo
                this.dataForm.couponUrl = data.coupon.couponUrl
                this.dataForm.couponId = data.coupon.couponId
                this.dataForm.qrCodeUrl = data.coupon.qrCodeUrl
                this.dataForm.coverImage = data.coupon.coverImage
                this.dataForm.startTime = data.coupon.startTime
                this.dataForm.endTime = data.coupon.endTime
                this.dataForm.totalCount = data.coupon.totalCount
                this.dataForm.soldCount = data.coupon.soldCount
                this.dataForm.status = data.coupon.status
                this.dataForm.sortOrder = data.coupon.sortOrder
                this.dataForm.remarks = data.coupon.remarks

                // 初始化图片数组
                this.initImageArrays()
              }
            })
          }
        })
      },
      // 获取活动列表
      getActivityList () {
        this.$http({
          url: this.$http.adornUrl('/activity/activity/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': 1,
            'limit': 1000
          })
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.activityList = data.page.list
          }
        })
      },
      // 初始化图片数组
      initImageArrays() {
        // 封面图片
        if (this.dataForm.coverImage) {
          this.coverImages = [{
            url: this.dataForm.coverImage,
            name: 'cover.jpg'
          }]
        } else {
          this.coverImages = []
        }
      },

      // 图片上传弹窗相关方法
      openImageModal(field) {
        this.currentImageField = field
        this.imageModalVisible = true
      },

      getCurrentImages() {
        switch (this.currentImageField) {
          case 'coverImage':
            return this.coverImages
          default:
            return []
        }
      },

      handleImageConfirm(images) {
        switch (this.currentImageField) {
          case 'coverImage':
            this.coverImages = images
            this.dataForm.coverImage = images.length > 0 ? images[0].url : ''
            break
        }
        this.imageModalVisible = false
      },

      // 预览图片
      previewImage(url) {
        this.dialogImageUrl = url
        this.imgDialogVisible = true
      },

      // 删除封面图片
      removeCoverImage() {
        this.coverImages = []
        this.dataForm.coverImage = ''
      },

      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/groupbuying/coupon/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'platformType': this.dataForm.platformType,
                'couponName': this.dataForm.couponName,
                'couponDescription': this.dataForm.couponDescription,
                'originalPrice': this.dataForm.originalPrice,
                'groupPrice': this.dataForm.groupPrice,
                'discountInfo': this.dataForm.discountInfo,
                'couponUrl': this.dataForm.couponUrl,
                'couponId': this.dataForm.couponId,
                'qrCodeUrl': this.dataForm.qrCodeUrl,
                'coverImage': this.dataForm.coverImage,
                'startTime': this.dataForm.startTime,
                'endTime': this.dataForm.endTime,
                'totalCount': this.dataForm.totalCount,
                'soldCount': this.dataForm.soldCount,
                'status': this.dataForm.status,
                'sortOrder': this.dataForm.sortOrder,
                'remarks': this.dataForm.remarks
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
.image-upload-container {
  width: 100%;
}

.image-preview {
  position: relative;
  display: inline-block;
  width: 150px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 5px;
  text-align: center;
}

.image-actions .el-button {
  color: white;
  padding: 2px 8px;
  font-size: 12px;
}

.upload-placeholder {
  width: 150px;
  height: 80px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}

.upload-placeholder i {
  font-size: 24px;
  margin-bottom: 5px;
}

.upload-placeholder div {
  font-size: 12px;
}
</style>
