<template>
  <div class="invite-page">
    <!-- 自定义导航栏 -->
    <div class="custom-navbar">
      <van-icon name="arrow-left" @click="$router.go(-1)" class="nav-back" />
      <span class="nav-title">邀请业务员</span>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#ffffff" size="24px" class="loading-center">
      正在加载...
    </van-loading>

    <!-- 邀请功能 -->
    <div v-else class="invite-container">
      <!-- 头部介绍区域 -->
      <div class="hero-section">
        <div class="hero-bg">
          <div class="hero-content">
            <div class="hero-icon">
              <div class="icon-wrapper">
                <van-icon name="friends-o" size="32" />
              </div>
            </div>
            <h1 class="hero-title">邀请业务员</h1>
            <p class="hero-subtitle">分享您的专属邀请码，邀请朋友加入团队</p>

            <!-- 权益卡片 -->
            <div class="benefits-grid">
              <div class="benefit-card">
                <van-icon name="gold-coin-o" size="20" />
                <span>获得佣金</span>
              </div>
              <div class="benefit-card">
                <van-icon name="chart-trending-o" size="20" />
                <span>团队业绩</span>
              </div>
              <div class="benefit-card">
                <van-icon name="gift-o" size="20" />
                <span>持续收益</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 邀请方式卡片 -->
      <div class="content-section">
        <!-- Tab切换 -->
        <div class="tab-switcher">
          <div class="tab-item" :class="{ active: activeTab === 'link' }" @click="activeTab = 'link'">
            <van-icon name="link-o" size="18" />
            <span>邀请链接</span>
          </div>
          <div class="tab-item" :class="{ active: activeTab === 'qrcode' }" @click="activeTab = 'qrcode'">
            <van-icon name="qr" size="18" />
            <span>二维码</span>
          </div>
        </div>

        <!-- 邀请链接内容 -->
        <div v-show="activeTab === 'link'" class="tab-content">
          <div class="invite-card">
            <div class="card-header">
              <div class="header-icon">
                <van-icon name="link-o" size="20" />
              </div>
              <div class="header-text">
                <h3>专属邀请链接</h3>
                <p>复制链接分享给朋友</p>
              </div>
            </div>

            <div class="link-section">
              <div class="link-box">
                <input type="text" :value="inviteLink" readonly class="link-input" @click="selectLink"
                  ref="linkInput" />
                <button class="copy-button" @click="copyLink">
                  <van-icon name="copy" size="16" />
                  复制
                </button>
              </div>
            </div>

            <div class="action-section">
              <button class="share-button" @click="shareLink">
                <van-icon name="share-o" size="18" />
                分享邀请链接
              </button>
            </div>
          </div>
        </div>

        <!-- 二维码内容 -->
        <div v-show="activeTab === 'qrcode'" class="tab-content">
          <div class="invite-card">
            <div class="card-header">
              <div class="header-icon qrcode-icon">
                <van-icon name="qr" size="20" />
              </div>
              <div class="header-text">
                <h3>邀请二维码</h3>
                <p>保存或分享二维码图片</p>
              </div>
            </div>

            <div class="qrcode-section">
              <div class="qrcode-container">
                <div v-if="qrcodeContent" class="qrcode-wrapper">
                  <vue-qrcode :value="qrcodeContent" :options="{ width: 180, margin: 1 }" class="qrcode-canvas" />
                  <div class="qrcode-label">
                    <p>扫码邀请业务员</p>
                    <span>邀请人：{{ inviterName }}</span>
                  </div>
                </div>
                <div v-else class="qrcode-loading">
                  <van-loading type="spinner" size="24px" color="#667eea" />
                  <p>二维码生成中...</p>
                </div>
              </div>
            </div>

            <div class="action-section">
              <div class="qrcode-actions">
                <button class="action-button save-btn" @click="saveQrcode">
                  <van-icon name="photo-o" size="16" />
                  保存图片
                </button>
                <button class="action-button share-btn" @click="shareQrcode">
                  <van-icon name="share-o" size="16" />
                  分享二维码
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 邀请统计 -->
      <!-- <div class="stats-section">
        <div class="stats-card">
          <div class="stats-header">
            <div class="stats-icon">
              <van-icon name="chart-trending-o" size="20" />
            </div>
            <h3>邀请统计</h3>
          </div>

          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ inviteStats.totalInvited || 0 }}</div>
              <div class="stat-label">已邀请人数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ inviteStats.activeInvited || 0 }}</div>
              <div class="stat-label">活跃下级</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ inviteStats.totalReward || 0 }}</div>
              <div class="stat-label">累计奖励</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ inviteStats.monthReward || 0 }}</div>
              <div class="stat-label">本月奖励</div>
            </div>
          </div>
        </div>
      </div> -->

      <!-- 底部帮助 -->
      <div class="help-section">
        <button class="help-button" @click="showInstructions = true">
          <van-icon name="question-o" size="16" />
          查看邀请说明
        </button>
      </div>
    </div>

    <!-- 使用说明弹窗 -->
    <van-dialog v-model="showInstructions" title="邀请说明" :show-cancel-button="false" confirm-button-text="我知道了">
      <div class="instructions">
        <h4>如何邀请业务员？</h4>
        <ol>
          <li>复制邀请链接发送给朋友</li>
          <li>或保存二维码图片分享给朋友</li>
          <li>朋友通过链接或扫码进入注册页面</li>
          <li>填写基本信息完成注册</li>
          <li>注册成功后自动成为您的下级业务员</li>
        </ol>

        <h4>邀请奖励</h4>
        <ul>
          <li>下级业务员的业绩将计入您的团队业绩</li>
          <li>可获得相应的团队奖励和佣金分成</li>
          <li>建立稳定的业务团队，获得持续收益</li>
        </ul>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  isMobilePhone
} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import VueQrcode from '@chenfengyuan/vue-qrcode';

export default {
  components: {
    pcheader,
    VueQrcode,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      loading: false,
      activeTab: 'link',
      inviteLink: '',
      qrcodeContent: '',
      inviterName: '',
      inviterMobile: '',
      inviteStats: {},
      showInstructions: false
    }
  },
  mounted() {
    document.title = "邀请业务员";
    this.generateInviteLink();
  },
  methods: {
    // 生成邀请链接
    async generateInviteLink() {
      try {
        this.loading = true;
        const res = await this.$fly.get('/pyp/web/salesman/generateInviteLink');

        if (res.code === 200) {
          this.inviteLink = res.inviteLink;
          this.qrcodeContent = res.qrcodeContent;
          this.inviterName = res.inviterName;
          this.inviterMobile = res.inviterMobile;

          // 生成二维码
          this.$nextTick(() => {
            this.generateQrcode();
          });
          this.shareLink();
          // 获取邀请统计（模拟数据，实际应该从API获取）
          this.inviteStats = {
            totalInvited: 0,
            activeInvited: 0,
            totalReward: 0,
            monthReward: 0
          };
          
        } else {
          vant.Toast(res.msg || '生成邀请链接失败');
        }
      } catch (error) {
        console.error('生成邀请链接失败:', error);
        vant.Toast('生成邀请链接失败');
      } finally {
        this.loading = false;
      }
    },

    // 生成二维码（使用VueQrcode组件，不需要手动生成）
    generateQrcode() {
      // VueQrcode组件会自动生成二维码，这里不需要额外处理
      console.log('二维码内容:', this.qrcodeContent);
    },

    // 选择链接文本
    selectLink() {
      this.$refs.linkInput.select();
    },

    // 复制链接
    async copyLink() {
      try {
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(this.inviteLink);
          vant.Toast('链接已复制到剪贴板');
        } else {
          // 降级方案
          this.$refs.linkInput.select();
          this.$refs.linkInput.setSelectionRange(0, 99999);
          const successful = document.execCommand('copy');
          if (successful) {
            vant.Toast('链接已复制到剪贴板');
          } else {
            vant.Toast('复制失败，请手动复制');
          }
        }
      } catch (error) {
        console.error('复制失败:', error);
        vant.Toast('复制失败，请手动复制');
      }
    },

    // 分享链接
    shareLink() {
        this.$wxShare(
          '邀请您成为业务员',
          this.inviteLink,
          this.$cookie.get("logo"),
          `${this.inviterName} 邀请您成为业务员，点击链接了解详情`
        );
    },

    // 保存二维码
    saveQrcode() {
      if (!this.qrcodeContent) {
        vant.Toast('二维码还未生成');
        return;
      }

      // 获取二维码canvas元素
      const qrcodeElement = this.$el.querySelector('.qrcode-image canvas');
      if (qrcodeElement) {
        const link = document.createElement('a');
        link.download = '业务员邀请二维码.png';
        link.href = qrcodeElement.toDataURL();
        link.click();
        vant.Toast('二维码已保存');
      } else {
        vant.Toast('保存失败，请重试');
      }
    },

    // 分享二维码
    shareQrcode() {
      if (!this.qrcodeContent) {
        vant.Toast('二维码还未生成');
        return;
      }

      const shareData = {
        title: '邀请您成为业务员',
        desc: `${this.inviterName} 邀请您成为业务员，扫码了解详情`,
        link: this.qrcodeContent,
        imgUrl: '' // 可以添加二维码图片URL
      };

      if (this.$wxShare) {
        this.$wxShare(shareData);
      } else if (navigator.share) {
        navigator.share({
          title: shareData.title,
          text: shareData.desc,
          url: shareData.link
        }).catch(err => {
          console.log('分享失败:', err);
          this.copyLink();
        });
      } else {
        // 降级方案：复制链接
        this.copyLink();
      }
    }
  }
}
</script>

<style lang="less" scoped>
.invite-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  background: transparent;
  position: relative;
  z-index: 10;
}

.nav-back {
  position: absolute;
  left: 20px;
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
  transition: all 0.3s ease;
}

.nav-back:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  background: transparent;
  text-align: center;
}

.loading-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
}

/* 英雄区域 */
.hero-section {
  padding: 15px 0 25px 0;
  position: relative;
}

.hero-bg {
  position: relative;
  z-index: 1;
}

.hero-content {
  text-align: center;
  color: white;
  padding: 0 20px;
}

.hero-icon {
  margin-bottom: 15px;
}

.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  letter-spacing: 0.5px;
}

.hero-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.benefits-grid {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.benefit-card {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.15);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.benefit-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 内容区域 */
.content-section {
  background: #f8f9fa;
  border-radius: 12px;
  margin: 10px;
  margin-top: -15px;
  position: relative;
  z-index: 2;
  min-height: calc(100vh - 180px);
  padding: 20px 16px 30px 16px;
}

/* Tab切换器 */
.tab-switcher {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 邀请卡片 */
.invite-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  margin-right: 12px;
  color: white;
}

.header-icon.qrcode-icon {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
}

.header-text h3 {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.header-text p {
  margin: 0;
  font-size: 12px;
  color: #999;
}

/* 链接区域 */
.link-section {
  margin-bottom: 18px;
}

.link-box {
  display: flex;
  background: #f8f9fa;
  border-radius: 10px;
  padding: 3px;
  border: 1px solid #e9ecef;
}

.link-input {
  flex: 1;
  padding: 10px 14px;
  border: none;
  background: transparent;
  font-size: 13px;
  color: #666;
  outline: none;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 7px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
}

/* 操作区域 */
.action-section {
  margin-top: 16px;
}

.share-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.share-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* 二维码区域 */
.qrcode-section {
  margin-bottom: 18px;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

.qrcode-wrapper {
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  text-align: center;
}

.qrcode-canvas {
  display: block;
  margin: 0 auto 12px auto;
  border-radius: 6px;
}

.qrcode-label p {
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.qrcode-label span {
  font-size: 12px;
  color: #999;
}

.qrcode-loading {
  padding: 50px 20px;
  text-align: center;
  color: #999;
}

.qrcode-loading p {
  margin: 12px 0 0 0;
  font-size: 13px;
}

/* 二维码操作按钮 */
.qrcode-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 10px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  color: white;
}

.save-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(7, 193, 96, 0.3);
}

.share-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.share-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
}

/* 统计区域 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin: 10px;
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ff976a 0%, #ff6b6b 100%);
  border-radius: 10px;
  margin-right: 12px;
  color: white;
}

.stats-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 16px 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 帮助区域 */
.help-section {
  margin-bottom: 25px;
  text-align: center;
}

.help-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.help-button:hover {
  background: white;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.2);
}

/* 使用说明样式 */
.instructions {
  padding: 20px;
  font-size: 14px;
  line-height: 1.6;
}

.instructions h4 {
  margin: 20px 0 15px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.instructions ol,
.instructions ul {
  margin: 15px 0;
  padding-left: 25px;
}

.instructions li {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .benefits-tags {
    gap: 10px;
  }

  .benefit-tag {
    padding: 6px 10px;
    font-size: 11px;
  }

  .stats-grid {
    gap: 10px;
  }

  .stats-item {
    padding: 15px 10px;
  }

  .stats-number {
    font-size: 20px;
  }
}
</style>
