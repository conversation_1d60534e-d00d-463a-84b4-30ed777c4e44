package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

/**
 * 微信用户业务员绑定变更记录DAO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Mapper
public interface WxUserSalesmanBindingLogDao extends BaseMapper<WxUserSalesmanBindingLogEntity> {

    /**
     * 分页查询绑定变更记录
     */
    List<WxUserSalesmanBindingLogEntity> queryPage(Map<String, Object> params);

    /**
     * 根据微信用户ID查询绑定历史
     */
    List<WxUserSalesmanBindingLogEntity> getBindingHistoryByWxUserId(@Param("wxUserId") Long wxUserId,
                                                                     @Param("appid") String appid);

    /**
     * 获取操作统计
     */
    List<Map<String, Object>> getOperationStats(@Param("appid") String appid,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);

    /**
     * 获取最近的绑定变更记录
     */
    List<WxUserSalesmanBindingLogEntity> getRecentBindingChanges(@Param("appid") String appid,
                                                                @Param("limit") Integer limit);

    /**
     * 根据业务员ID查询相关的绑定变更记录
     */
    List<WxUserSalesmanBindingLogEntity> getBindingChangesBySalesmanId(@Param("salesmanId") Long salesmanId,
                                                                       @Param("appid") String appid);
}
