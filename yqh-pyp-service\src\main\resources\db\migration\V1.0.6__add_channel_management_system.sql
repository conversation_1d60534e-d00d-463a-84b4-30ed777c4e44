-- 渠道管理系统数据库变更

-- 1. 创建渠道表
CREATE TABLE `channel` (
  `id` bigint(20) NOT NULL COMMENT '渠道ID',
  `name` varchar(100) NOT NULL COMMENT '渠道名称',
  `code` varchar(50) NOT NULL COMMENT '渠道编号',
  `description` text COMMENT '渠道描述',
  `contact_name` varchar(50) COMMENT '联系人姓名',
  `contact_mobile` varchar(20) COMMENT '联系人手机号',
  `contact_email` varchar(100) COMMENT '联系人邮箱',
  `address` varchar(255) COMMENT '渠道地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `level` int(11) DEFAULT '1' COMMENT '渠道级别',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '上级渠道ID',
  `commission_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '默认佣金比例',
  `remarks` text COMMENT '备注信息',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_code_appid` (`code`, `appid`),
  KEY `idx_channel_appid` (`appid`),
  KEY `idx_channel_parent_id` (`parent_id`),
  KEY `idx_channel_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道管理表';

-- 2. 为sys_user表添加渠道ID字段（用于关联渠道管理员）
ALTER TABLE `sys_user` ADD COLUMN `channel_id` bigint(20) DEFAULT NULL COMMENT '所属渠道ID（仅渠道管理员使用）' AFTER `appid`;
ALTER TABLE `sys_user` ADD KEY `idx_sys_user_channel_id` (`channel_id`);
ALTER TABLE `sys_user` ADD CONSTRAINT `fk_sys_user_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE SET NULL;

-- 3. 创建渠道管理员角色
INSERT INTO `sys_role` (`role_id`, `role_name`, `remark`, `create_user_id`, `create_time`)
VALUES (1000000000000000001, '渠道管理员', '渠道管理员角色，可管理所属渠道的业务员和查看活动数据', 1, NOW());

-- 4. 为业务员表添加渠道ID字段
ALTER TABLE `salesman` ADD COLUMN `channel_id` bigint(20) DEFAULT NULL COMMENT '所属渠道ID' AFTER `appid`;
ALTER TABLE `salesman` ADD KEY `idx_salesman_channel_id` (`channel_id`);
ALTER TABLE `salesman` ADD CONSTRAINT `fk_salesman_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE SET NULL;

-- 5. 创建渠道管理员菜单权限
INSERT INTO `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES
(1000000000000000001, 0, '渠道管理', NULL, NULL, 0, 'fa fa-sitemap', 6),
(1000000000000000002, 1000000000000000001, '渠道列表', 'modules/channel/channel', 'channel:channel:list,channel:channel:info,channel:channel:save,channel:channel:update,channel:channel:delete', 1, 'fa fa-list', 1),
(1000000000000000003, 1000000000000000001, '渠道管理员', 'modules/channel/channel-admin', 'channel:admin:list,channel:admin:info,channel:admin:save,channel:admin:update,channel:admin:delete', 1, 'fa fa-user-circle', 2),
(1000000000000000004, 1000000000000000001, '业务员管理', 'modules/channel/channel-salesman', 'channel:salesman:list,channel:salesman:info,channel:salesman:save,channel:salesman:update,channel:salesman:delete', 1, 'fa fa-users', 3),
(1000000000000000007, 1000000000000000001, '客户管理', 'modules/channel/channel-customer', 'channel:channel:list', 1, 'fa fa-user', 4),
(1000000000000000005, 1000000000000000001, '活动查看', 'modules/channel/channel-activity', 'channel:activity:list,channel:activity:info', 1, 'fa fa-calendar', 5),
(1000000000000000006, 1000000000000000001, '统计报表', 'modules/channel/channel-statistics', 'channel:statistics:view', 1, 'fa fa-bar-chart', 6);

-- 6. 为渠道管理员角色分配菜单权限
INSERT INTO `sys_role_menu` (`id`, `role_id`, `menu_id`) VALUES
(1000000000000000001, 1000000000000000001, 1000000000000000001),
(1000000000000000002, 1000000000000000001, 1000000000000000002),
(1000000000000000003, 1000000000000000001, 1000000000000000003),
(1000000000000000004, 1000000000000000001, 1000000000000000004),
(1000000000000000005, 1000000000000000001, 1000000000000000005),
(1000000000000000006, 1000000000000000001, 1000000000000000006);

-- 7. 创建渠道权限表（用于细粒度权限控制）
CREATE TABLE `channel_permission` (
  `id` bigint(20) NOT NULL COMMENT '权限ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道ID',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型：salesman_manage,activity_view,commission_view,statistics_view',
  `permission_value` text COMMENT '权限值（JSON格式存储具体权限配置）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_channel_permission_channel_id` (`channel_id`),
  KEY `idx_channel_permission_type` (`permission_type`),
  CONSTRAINT `fk_channel_permission_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道权限表';

-- 8. 创建渠道统计表（用于缓存统计数据）
CREATE TABLE `channel_statistics` (
  `id` bigint(20) NOT NULL COMMENT '统计ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `salesman_count` int(11) DEFAULT '0' COMMENT '业务员数量',
  `active_salesman_count` int(11) DEFAULT '0' COMMENT '活跃业务员数量',
  `total_orders` int(11) DEFAULT '0' COMMENT '总订单数',
  `activity_orders` int(11) DEFAULT '0' COMMENT '活动订单数',
  `recharge_orders` int(11) DEFAULT '0' COMMENT '充值订单数',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '总销售额',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '总佣金',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_statistics_channel_date` (`channel_id`, `stat_date`),
  KEY `idx_channel_statistics_appid` (`appid`),
  KEY `idx_channel_statistics_date` (`stat_date`),
  CONSTRAINT `fk_channel_statistics_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道统计表';

-- 9. 插入默认渠道数据（可选）
INSERT INTO `channel` (`id`, `name`, `code`, `description`, `contact_name`, `contact_mobile`, `status`, `level`, `appid`, `create_on`, `create_by`) 
VALUES 
(1, '默认渠道', 'DEFAULT', '系统默认渠道，用于未分配渠道的业务员', '系统管理员', '', 1, 1, 'wx6a7f38e0347e6669', NOW(), 1);

-- 10. 将现有业务员分配到默认渠道（可选，根据实际需求调整）
-- UPDATE `salesman` SET `channel_id` = 1 WHERE `channel_id` IS NULL;
