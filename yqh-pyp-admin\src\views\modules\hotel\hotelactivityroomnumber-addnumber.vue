<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
    <el-form-item label="房号(用“,”隔开)" prop="number">
      <el-input v-model="dataForm.number" placeholder="房号(用“,”隔开)"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          hotelActivityRoomId: '',
          number: '',
        },
        dataRule: {
          number: [
            { required: true, message: '房号不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (hotelActivityRoomId) {
        this.dataForm.hotelActivityRoomId = hotelActivityRoomId
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/save`),
              method: 'post',
              data: this.$http.adornData({
                'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
                'number': this.dataForm.number,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
