package com.cjy.pyp.modules.salesman.enums;

/**
 * 佣金类型枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public enum CommissionTypeEnum {
    
    /**
     * 创建活动佣金 - 对应 activity_recharge_package 表中 package_type=2 的记录
     */
    CREATE_ACTIVITY(1, "创建活动佣金"),
    
    /**
     * 充值次数佣金 - 对应 activity_recharge_package 表中 package_type=1 的记录
     */
    RECHARGE_COUNT(2, "充值次数佣金"),
    
    /**
     * 用户转发佣金 - 对应 activity_recharge_usage 表中 usage_type=3 的记录
     */
    USER_FORWARD(3, "用户转发佣金");
    
    private final Integer code;
    private final String desc;
    
    CommissionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static CommissionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CommissionTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    /**
     * 检查是否支持百分比计算
     */
    public boolean supportPercentage() {
        return this == CREATE_ACTIVITY || this == RECHARGE_COUNT;
    }
    
    /**
     * 获取对应的业务类型标识
     */
    public String getBusinessType() {
        switch (this) {
            case CREATE_ACTIVITY:
                return "CREATE_ACTIVITY";
            case RECHARGE_COUNT:
                return "RECHARGE_COUNT";
            case USER_FORWARD:
                return "USER_FORWARD";
            default:
                return "UNKNOWN";
        }
    }
}
