<template>
  <el-dialog title="系统赠送次数" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="选择活动" prop="activityId">
        <el-select v-model="dataForm.activityId" placeholder="请选择活动" filterable style="width: 100%">
          <el-option
            v-for="item in activityList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="充值方式" prop="rechargeType">
        <el-radio-group v-model="dataForm.rechargeType" @change="onRechargeTypeChange">
          <el-radio :label="1">套餐充值</el-radio>
          <el-radio :label="3">系统赠送</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="dataForm.rechargeType === 1" label="选择套餐" prop="packageId">
        <el-select v-model="dataForm.packageId" placeholder="请选择套餐" style="width: 100%" @change="onPackageChange">
          <el-option
            v-for="item in packageList"
            :key="item.id"
            :label="`${item.name} - ¥${item.price} (${item.packageType === 1 ? item.countValue + '次' : item.activityCount + '个活动'})`"
            :value="item.id">
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">¥{{ item.price }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.rechargeType === 3" label="赠送次数" prop="count">
        <el-input-number v-model="dataForm.count" :min="1" :max="10000" placeholder="赠送次数"></el-input-number>
      </el-form-item>
      <el-form-item v-if="selectedPackage" label="套餐信息">
        <el-card class="package-info">
          <div><strong>套餐名称：</strong>{{ selectedPackage.name }}</div>
          <div><strong>套餐类型：</strong>{{ selectedPackage.type === 1 ? '充值次数套餐' : '创建活动套餐' }}</div>
          <div v-if="selectedPackage.type === 1"><strong>充值次数：</strong>{{ selectedPackage.countValue }}次</div>
          <div v-if="selectedPackage.type === 2"><strong>活动数量：</strong>{{ selectedPackage.activityCount }}个</div>
          <div><strong>价格：</strong><span style="color: #f56c6c; font-weight: bold;">¥{{ selectedPackage.price }}</span></div>
          <div v-if="selectedPackage.originalPrice"><strong>原价：</strong><span style="text-decoration: line-through;">¥{{ selectedPackage.originalPrice }}</span></div>
        </el-card>
      </el-form-item>
      <el-form-item label="有效期(天)" prop="validDays">
        <el-input-number v-model="dataForm.validDays" :min="1" :max="3650" placeholder="有效期天数"></el-input-number>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" :rows="3" placeholder="赠送原因或备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      activityList: [],
      packageList: [],
      selectedPackage: null,
      dataForm: {
        repeatToken: '',
        activityId: '',
        rechargeType: 3, // 1-套餐充值 3-系统赠送
        packageId: '',
        count: 1,
        validDays: 365,
        remarks: ''
      },
      dataRule: {
        activityId: [
          { required: true, message: '活动ID不能为空', trigger: 'blur' }
        ],
        rechargeType: [
          { required: true, message: '充值方式不能为空', trigger: 'change' }
        ],
        packageId: [
          { required: true, message: '套餐不能为空', trigger: 'change' }
        ],
        count: [
          { required: true, message: '赠送次数不能为空', trigger: 'blur' }
        ],
        validDays: [
          { required: true, message: '有效期不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init() {
      this.getToken()
      this.getActivityList()
      this.getPackageList()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.updateValidationRules()
      })
    },
    getActivityList() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityList = data.page.list
        } else {
          this.activityList = []
        }
      })
    },
    getPackageList() {
      this.$http({
        url: this.$http.adornUrl('/activity/rechargepackage/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000,
          'status': 1, // 只获取启用的套餐
          'packageType': 1 // 只获取充值次数套餐，不包括创建活动套餐
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.packageList = data.page.list
        } else {
          this.packageList = []
        }
      })
    },
    onRechargeTypeChange(value) {
      this.selectedPackage = null
      this.dataForm.packageId = ''
      this.dataForm.count = 1
      this.updateValidationRules()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    onPackageChange(packageId) {
      this.selectedPackage = this.packageList.find(pkg => pkg.id === packageId)
      if (this.selectedPackage) {
        if (this.selectedPackage.type === 1) {
          this.dataForm.count = this.selectedPackage.countValue
        } else {
          this.dataForm.count = this.selectedPackage.activityCount
        }
      }
    },
    updateValidationRules() {
      if (this.dataForm.rechargeType === 1) {
        // 套餐充值
        this.$set(this.dataRule, 'packageId', [
          { required: true, message: '套餐不能为空', trigger: 'change' }
        ])
        this.$delete(this.dataRule, 'count')
      } else {
        // 系统赠送
        this.$set(this.dataRule, 'count', [
          { required: true, message: '赠送次数不能为空', trigger: 'blur' }
        ])
        this.$delete(this.dataRule, 'packageId')
      }
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl('/common/createToken'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.updateValidationRules()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let apiUrl = ''
          let submitData = {
            'appid': this.$cookie.get('appid'),
            'repeatToken': this.dataForm.repeatToken,
            'activityId': this.dataForm.activityId,
            'validDays': this.dataForm.validDays,
            'remarks': this.dataForm.remarks
          }

          if (this.dataForm.rechargeType === 1) {
            // 套餐充值
            apiUrl = '/activity/rechargerecord/recharge'
            submitData.rechargeType = 1
            submitData.packageId = this.dataForm.packageId
            submitData.count = this.selectedPackage ? (this.selectedPackage.type === 1 ? this.selectedPackage.countValue : this.selectedPackage.activityCount) : 1
            submitData.amount = this.selectedPackage ? this.selectedPackage.price : 0
          } else {
            // 系统赠送
            apiUrl = '/activity/rechargerecord/gift'
            submitData.count = this.dataForm.count
          }

          this.$http({
            url: this.$http.adornUrl(apiUrl),
            method: this.dataForm.rechargeType === 1 ? 'post' : 'get',
            [this.dataForm.rechargeType === 1 ? 'data' : 'params']: this.dataForm.rechargeType === 1 ? this.$http.adornData(submitData) : this.$http.adornParams(submitData)
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: this.dataForm.rechargeType === 1 ? '充值成功' : '赠送成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken()
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.package-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.package-info div {
  margin-bottom: 8px;
}

.package-info div:last-child {
  margin-bottom: 0;
}
</style>
