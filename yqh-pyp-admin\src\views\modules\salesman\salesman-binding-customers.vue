<template>
  <el-dialog :title="`${salesmanName} - 绑定客户列表`" :close-on-click-modal="false" :visible.sync="visible" width="80%">

    <!-- 搜索条件 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="客户昵称">
        <el-input v-model="searchForm.wxUserName" placeholder="请输入客户昵称" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="searchForm.mobile" placeholder="请输入手机号" clearable></el-input>
      </el-form-item>
      <el-form-item label="绑定状态">
        <el-select v-model="searchForm.status" placeholder="全部" clearable>
          <el-option label="有效" :value="1"></el-option>
          <el-option label="已失效" :value="0"></el-option>
          <el-option label="已解绑" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getCustomerList()">查询</el-button>
        <el-button @click="resetSearch()">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ customerStats.totalCustomers || 0 }}</div>
              <div class="stats-label">总客户数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ customerStats.activeCustomers || 0 }}</div>
              <div class="stats-label">有效绑定</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ customerStats.todayBindings || 0 }}</div>
              <div class="stats-label">今日新增</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ customerStats.totalOrderAmount || 0 }}</div>
              <div class="stats-label">订单总额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 客户列表 -->
    <el-table :data="customerList" border v-loading="listLoading" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="wxUserName" header-align="center" align="center" label="客户信息">
        <template slot-scope="scope">
          <div class="customer-info">
            <div class="customer-name">{{ scope.row.wxUserName }}</div>
            <div class="customer-mobile">{{ scope.row.wxUserMobile || '未绑定手机' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="bindingType" header-align="center" align="center" label="绑定方式">
        <template slot-scope="scope">
          <el-tag :type="getBindingTypeTagType(scope.row.bindingType)">
            {{ getBindingTypeText(scope.row.bindingType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bindingTime" header-align="center" align="center" width="150" label="绑定时间">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="expiryTime" header-align="center" align="center" width="150" label="失效时间">
        <template slot-scope="scope">
          <span v-if="scope.row.expiryTime">{{ scope.row.expiryTime }}</span>
          <span v-else style="color: #67C23A;">永久有效</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="订单数量">
        <template slot-scope="scope">
          <span style="color: #409EFF;">{{ scope.row.orderCount }}</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="订单金额">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.orderAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewOrdersHandle(scope.row)">查看订单</el-button>
          <el-button type="text" size="small" @click="viewHistoryHandle(scope.row)">绑定历史</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper" style="margin-top: 20px;">
    </el-pagination>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="exportCustomers()">导出客户</el-button>
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      salesmanId: null,
      salesmanName: '',
      searchForm: {
        wxUserName: '',
        mobile: '',
        status: ''
      },
      customerList: [],
      customerStats: {},
      listLoading: false,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0
    }
  },
  methods: {
    init(salesmanId, salesmanName) {
      this.salesmanId = salesmanId
      this.salesmanName = salesmanName
      this.visible = true
      this.resetSearch()
      this.getCustomerList()
      this.getCustomerStats()
    },

    // 获取客户列表
    getCustomerList() {
      this.listLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/wxuserbinding/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'salesmanId': this.salesmanId,
          'wxUserName': this.searchForm.wxUserName,
          'mobile': this.searchForm.mobile,
          'status': this.searchForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.customerList = data.page.list || []
          this.totalPage = data.page.totalCount || 0
        } else {
          this.customerList = []
          this.totalPage = 0
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },

    // 获取客户统计
    getCustomerStats() {
      this.$http({
        url: this.$http.adornUrl('/salesman/wxuserbinding/customerStats'),
        method: 'get',
        params: this.$http.adornParams({
          salesmanId: this.salesmanId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.customerStats = data.stats || {}
        }
      })
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        wxUserName: '',
        mobile: '',
        status: ''
      }
      this.pageIndex = 1
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getCustomerList()
    },

    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getCustomerList()
    },

    // 查看订单
    viewOrdersHandle(row) {
      this.visible = false;
      
      // 使用 nextTick 确保弹窗完全关闭后再跳转
      this.$nextTick(() => {
        this.$router.push({
          path: '/salesman-order',
          query: {
            userId: row.wxUserId,
            salesmanId: this.salesmanId
          }
        });
      });
      // this.$nextTick(() => {
      //   this.$router.push({
      //     path: '/salesman-order-association',
      //     query: {
      //       wxUserId: row.wxUserId,
      //       salesmanId: this.salesmanId
      //     }
      //   });
      // });
    },

    // 查看绑定历史
    viewHistoryHandle(row) {
      this.visible = false;
      
      // 同样使用 nextTick 确保弹窗完全关闭后再跳转
      this.$nextTick(() => {
        this.$router.push({
          path: '/salesman-wx-user-binding',
          query: {
            wxUserId: row.wxUserId
          }
        });
      });
    },

    // 导出客户
    exportCustomers() {
      this.$message.info('导出功能开发中...')
    },

    // 获取绑定方式文本
    getBindingTypeText(type) {
      const typeMap = {
        1: '二维码扫描',
        2: '邀请链接',
        3: '手动绑定',
        4: '系统分配'
      }
      return typeMap[type] || '未知'
    },

    // 获取绑定方式标签类型
    getBindingTypeTagType(type) {
      const typeMap = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info'
      }
      return typeMap[type] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '已失效',
        1: '有效',
        2: '已解绑'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'danger',
        1: 'success',
        2: 'info'
      }
      return typeMap[status] || ''
    },

    // 获取订单数量（临时方法，实际应该从后端获取）
    getOrderCount(wxUserId) {
      // 这里应该调用后端接口获取实际的订单数量
      return Math.floor(Math.random() * 20)
    },

    // 获取订单金额（临时方法，实际应该从后端获取）
    getOrderAmount(wxUserId) {
      // 这里应该调用后端接口获取实际的订单金额
      return (Math.random() * 10000).toFixed(2)
    }
  }
}
</script>

<style scoped>
.search-form {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-overview {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-item {
  padding: 15px;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 6px;
}

.stats-label {
  font-size: 13px;
  color: #666;
}

.customer-info {
  text-align: center;
}

.customer-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.customer-mobile {
  font-size: 12px;
  color: #909399;
}
</style>
