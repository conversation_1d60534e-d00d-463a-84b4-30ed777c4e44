<template>
  <div class="mod-config">
    <div style="text-align: center;padding: 20px;font-weight: bold;font-size: 28px">{{ activityInfo.name }}的报名订单</div>
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.contact" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="手机" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
          <el-option label="全部(订单状态)" value=""></el-option>
          <el-option v-for="item in orderStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.verifyStatus" placeholder="审核状态" filterable>
          <el-option label="全部(审核状态)" value=""></el-option>
          <el-option v-for="item in verifyStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.source" placeholder="支付来源" filterable>
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in sources" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:activityuserapplyorder:save')" type="primary"
          @click="addOrUpdateHandle(dataForm.activityId)">新增</el-button>
        <el-button v-if="isAuth('activity:activityuserapplyorder:save')" type="success"
          @click="exportHandle()">导出</el-button>
        <!-- <el-button v-if="isAuth('activity:activityuserapplyorder:save')" type="success" @click="downloadDemoHandle()">下载模板</el-button> -->
        <el-button v-if="isAuth('activity:activityuserapplyorder:save')" type="success"
          @click="downloadDemoSimpleHandle()">下载模板</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList"
            :url="'/activity/activityuserapplyorder/importExcelSimple?activityId=' + dataForm.activityId"
            :name="'报名订单导入'"></Upload>
        </el-button>
        <el-button v-if="isAuth('activity:activityuserapplyorder:sign')" type="warning"
          @click="signQrCode()">电子签到码</el-button>
        <el-button v-if="isAuth('activity:activityuserapplyorder:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" height="800px" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column prop="contact" header-align="center" align="center" label="参会人姓名">
        <div slot-scope="scope" @click="showQrCode(scope.row.id)">{{ scope.row.contact }}</div>
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column show-overflow-tooltip  prop="applyActivityChannelConfigName" header-align="center" align="center" label="报名通道">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="订单状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.status)">{{ scope.row.status |
            statusFilter }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="source" header-align="center" align="center" label="支付来源">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.source)">{{ scope.row.source |
            sourceFilter }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="totalAmount" header-align="center" align="center" label="订单金额">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="payAmount" header-align="center" align="center" label="实付金额">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="name" header-align="center" align="center" label="订单名称">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="orderSn" header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="hours" header-align="center" align="center" label="学时(时)">
        <div slot-scope="scope">{{ (scope.row.hours / 60 / 60).toFixed(1) }}</div>
      </el-table-column>
      <el-table-column prop="signType" header-align="center" align="center" label="签到状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.signType)">{{
            signType[scope.row.signType].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="signTime" header-align="center" align="center" label="签到时间">
      </el-table-column>
      <el-table-column width="120" prop="status" header-align="center" align="center" label="审核状态">
        <div slot-scope="scope">
          <el-tag @click="updateVerifyHandle(scope.row.id)" v-if="scope.row.isVerify" type="primary"
            :class="'tag-color tag-color-' + scope.row.verifyStatus">{{
              verifyStatus[scope.row.verifyStatus].value }}</el-tag>
          <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="审核材料" width="150">
        <div slot-scope="scope">
          <el-image v-if="scope.row.isVerify" class="article-thumb" :src="scope.row.credit"
            :preview-src-list="[scope.row.credit]"></el-image>
          <div v-else>-</div>
          <el-upload :before-upload="checkFileSize" :show-file-list="false" :data="{ id: scope.row.id }"
            accept=".jpg, .jpeg, .png, .gif" :on-success="backgroundSuccessHandle" :action="url">
            <el-button type="primary" size="mini">上传材料</el-button>
          </el-upload>
        </div>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="remarks" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="180" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="scope.row.signType != 1"
            @click="sign(scope.row.activityUserId, 1)">签到</el-button>
          <el-button type="text" style="color: red" size="small" v-else
            @click="sign(scope.row.activityUserId, 2)">签退</el-button>
          <el-button type="text" size="small" @click="applyUserInfo(scope.row.id)">报名信息</el-button>
          <el-button type="text" size="small" @click="updateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <activityuserapplyorder-update v-if="activityuserapplyorderUpdateVisible" ref="activityuserapplyorderUpdate"
      @refreshDataList="getDataList"></activityuserapplyorder-update>
    <activityuserapplyorder-user-info v-if="activityuserapplyorderUserInfoVisible" ref="activityuserapplyorderUserInfo"
      @refreshDataList="getDataList"></activityuserapplyorder-user-info>
    <activityuserapplyorderexportdemo v-if="activityuserapplyorderexportdemoVisible"
      ref="activityuserapplyorderexportdemo" @refreshDataList="getDataList"></activityuserapplyorderexportdemo>
    <activityuserapplyorderqrcode v-if="activityuserapplyorderqrcodeVisible" ref="activityuserapplyorderqrcode"
      @refreshDataList="getDataList"></activityuserapplyorderqrcode>
    <activityuserapplyorderupdateverify v-if="activityuserapplyorderupdateverifyVisible"
      ref="activityuserapplyorderupdateverify" @refreshDataList="getDataList"></activityuserapplyorderupdateverify>
  </div>
</template>

<script>
import AddOrUpdate from './activityuserapplyorder-add-or-update'
import ActivityuserapplyorderUpdate from './activityuserapplyorder-update'
import activityuserapplyorderupdateverify from './activityuserapplyorder-updateverify'
import activityuserapplyorderqrcode from './activityuserapplyorder-qrcode'
import ActivityuserapplyorderUserInfo from './activityuserapplyorder-user-info'
import activityuserapplyorderexportdemo from './activityuserapplyorder-exportdemo'
import { sources, orderStatus } from '@/data/common'
import { signModel, signType, verifyStatus } from '@/data/activity.js';
export default {
  data() {
    return {
      url: "",
      dataForm: {
        contact: '',
        mobile: '',
        status: '',
        verifyStatus: '',
        source: '',
        activityId: undefined
      },
      dataList: [],
      activityInfo: {},
      sources,
      orderStatus,
      verifyStatus,
      signModel,
      signType,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      activityuserapplyorderUpdateVisible: false,
      activityuserapplyorderUserInfoVisible: false,
      activityuserapplyorderexportdemoVisible: false,
      activityuserapplyorderqrcodeVisible: false,
      activityuserapplyorderupdateverifyVisible: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    ActivityuserapplyorderUserInfo,
    ActivityuserapplyorderUpdate,
    activityuserapplyorderexportdemo,
    activityuserapplyorderqrcode,
    activityuserapplyorderupdateverify,
    Upload: () => import('@/components/upload')
  },
  filters: {
    statusFilter(v) {
      let data = orderStatus.filter(item => item.key === v)
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    sourceFilter(v) {
      let data = sources.filter(item => item.key === v)
      if (data.length >= 1) {
        return data[0].value;
      }
    }
  },
  activated() {
    this.url = this.$http.adornUrl(
      `/activity/activityuserapplyorder/uploadCredit?token=${this.$cookie.get("token")}`
    );
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList()
    this.getActivity()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityuserapplyorder/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'mobile': this.dataForm.mobile,
          'contact': this.dataForm.contact,
          'source': this.dataForm.source,
          'status': this.dataForm.status,
          'verifyStatus': this.dataForm.verifyStatus,
          'activityId': this.dataForm.activityId
        })
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 用户信息
    applyUserInfo(id) {
      this.activityuserapplyorderUserInfoVisible = true
      this.$nextTick(() => {
        this.$refs.activityuserapplyorderUserInfo.init(id)
      })
    },
    downloadDemoHandle() {
      this.activityuserapplyorderexportdemoVisible = true
      this.$nextTick(() => {
        this.$refs.activityuserapplyorderexportdemo.init(this.dataForm.activityId)
      })
    },
    showQrCode(id) {
      this.activityuserapplyorderqrcodeVisible = true
      this.$nextTick(() => {
        this.$refs.activityuserapplyorderqrcode.init(id)
      })
    },
    updateVerifyHandle(id) {
      this.activityuserapplyorderupdateverifyVisible = true
      this.$nextTick(() => {
        this.$refs.activityuserapplyorderupdateverify.init(id)
      })
    },
    downloadDemoSimpleHandle() {
      var url = this.$http.adornUrl("/activity/activityuserapplyorder/exportDemoSimple?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    updateHandle(id) {
      this.activityuserapplyorderUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.activityuserapplyorderUpdate.init(id)
      })
    },
    signQrCode() {
      this.$router.push({
        name: 'activityuserapplyordersignqrcode',
        query: {
          activityId: this.dataForm.activityId,
        },
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityuserapplyorder/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 签到
    sign(id, type) {
      this.$confirm(`确定${type == 1 ? '签到' : '签退'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityusersign/sign'),
          method: 'get',
          params: this.$http.adornParams({
            activityUserId: id,
            type: type,
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activityuserapplyorder/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "mobile=" + this.dataForm.mobile,
        "contact=" + this.dataForm.contact,
        "source=" + this.dataForm.source,
        "activityId=" + this.dataForm.activityId,
        "status=" + this.dataForm.status
      ].join('&'));
      window.open(url);
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    // 上传成功（背景）
    backgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.getDataList();
      } else {
        this.$message.error(response.msg);
      }
    },
  }
}
</script>
<style scoped>

.article-thumb {
    height: 50px;
}
</style>