<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="客户" prop="clientId">
        <el-select v-model="dataForm.clientId" filterable>
          <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户类型" prop="clientType">
        <el-select v-model="dataForm.clientType" filterable>
          <el-option v-for="item in clientType" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="新/老客户" prop="oldOrNew">
        <el-select v-model="dataForm.oldOrNew" filterable>
          <el-option v-for="item in oldOrNew" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      client: [],
      clientType: [],
      oldOrNew: [],
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        clientId: '',
        clientType: '',
        oldOrNew: '',

      },
      dataRule: {

      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityconfig/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.clientId = data.activityConfig.clientId
              this.dataForm.clientType = data.activityConfig.clientType
              this.dataForm.oldOrNew = data.activityConfig.oldOrNew
            }
          })
        }
        this.getResult();
        this.findClient();
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    findClient() {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
          }
        })
    },
    getResult() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'clientType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.clientType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'oldOrNew',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.oldOrNew = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityconfig/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'clientId': this.dataForm.clientId,
              'clientType': this.dataForm.clientType,
              'oldOrNew': this.dataForm.oldOrNew,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
