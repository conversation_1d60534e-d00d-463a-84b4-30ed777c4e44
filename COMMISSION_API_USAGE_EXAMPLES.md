# 佣金系统API使用示例

## 1. 获取枚举选项API

### 1.1 获取佣金类型选项

**请求**：
```javascript
// 获取所有佣金类型
this.$http({
  url: this.$http.adornUrl('/salesman/commission/config/getCommissionTypes'),
  method: 'get',
  params: this.$http.adornParams()
}).then(({ data }) => {
  if (data && data.code === 200) {
    this.commissionTypes = data.commissionTypes
    // 返回格式：
    // [
    //   { code: 1, desc: "创建活动佣金" },
    //   { code: 2, desc: "充值次数佣金" },
    //   { code: 3, desc: "用户转发佣金" }
    // ]
  }
})
```

### 1.2 获取计算方式选项

**请求**：
```javascript
// 获取所有计算方式
this.$http({
  url: this.$http.adornUrl('/salesman/commission/config/getCalculationTypes'),
  method: 'get',
  params: this.$http.adornParams()
}).then(({ data }) => {
  if (data && data.code === 200) {
    this.calculationTypes = data.calculationTypes
    // 返回格式：
    // [
    //   { code: 1, desc: "固定金额" },
    //   { code: 2, desc: "百分比" }
    // ]
  }
})

// 根据佣金类型获取支持的计算方式
this.$http({
  url: this.$http.adornUrl('/salesman/commission/config/getCalculationTypes'),
  method: 'get',
  params: this.$http.adornParams({
    commissionType: 3  // 用户转发佣金
  })
}).then(({ data }) => {
  if (data && data.code === 200) {
    this.calculationTypes = data.calculationTypes
    // 用户转发佣金只返回：
    // [
    //   { code: 1, desc: "固定金额" }
    // ]
  }
})
```

### 1.3 获取结算状态选项

**请求**：
```javascript
this.$http({
  url: this.$http.adornUrl('/salesman/commission/record/getSettlementStatuses'),
  method: 'get',
  params: this.$http.adornParams()
}).then(({ data }) => {
  if (data && data.code === 200) {
    this.settlementStatuses = data.settlementStatuses
    // 返回格式：
    // [
    //   { code: 0, desc: "未结算" },
    //   { code: 1, desc: "已结算" },
    //   { code: 2, desc: "已取消" }
    // ]
  }
})
```

## 2. 前端组件使用示例

### 2.1 下拉选择组件

```vue
<template>
  <!-- 佣金类型选择 -->
  <el-select v-model="form.commissionType" placeholder="请选择佣金类型" @change="onCommissionTypeChange">
    <el-option
      v-for="type in commissionTypes"
      :key="type.code"
      :label="type.desc"
      :value="type.code">
    </el-option>
  </el-select>

  <!-- 计算方式选择 -->
  <el-select v-model="form.calculationType" placeholder="请选择计算方式">
    <el-option
      v-for="type in availableCalculationTypes"
      :key="type.code"
      :label="type.desc"
      :value="type.code">
    </el-option>
  </el-select>

  <!-- 结算状态选择 -->
  <el-select v-model="form.settlementStatus" placeholder="请选择结算状态">
    <el-option
      v-for="status in settlementStatuses"
      :key="status.code"
      :label="status.desc"
      :value="status.code">
    </el-option>
  </el-select>
</template>

<script>
export default {
  data() {
    return {
      form: {
        commissionType: '',
        calculationType: '',
        settlementStatus: ''
      },
      commissionTypes: [],
      calculationTypes: [],
      settlementStatuses: [],
      availableCalculationTypes: []
    }
  },
  methods: {
    // 佣金类型变化时，更新可用的计算方式
    onCommissionTypeChange() {
      this.getCalculationTypes(this.form.commissionType)
      // 重置计算方式
      this.form.calculationType = ''
    },
    
    // 获取计算方式（支持按佣金类型过滤）
    getCalculationTypes(commissionType) {
      let params = {}
      if (commissionType) {
        params.commissionType = commissionType
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commission/config/getCalculationTypes'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.availableCalculationTypes = data.calculationTypes
        }
      })
    }
  }
}
</script>
```

### 2.2 表格显示示例

```vue
<template>
  <el-table :data="dataList">
    <!-- 佣金类型列 -->
    <el-table-column prop="commissionType" label="佣金类型">
      <template slot-scope="scope">
        {{ getCommissionTypeDesc(scope.row.commissionType) }}
      </template>
    </el-table-column>
    
    <!-- 计算方式列 -->
    <el-table-column prop="calculationType" label="计算方式">
      <template slot-scope="scope">
        {{ getCalculationTypeDesc(scope.row.calculationType) }}
      </template>
    </el-table-column>
    
    <!-- 结算状态列 -->
    <el-table-column prop="settlementStatus" label="结算状态">
      <template slot-scope="scope">
        <el-tag :type="getStatusTagType(scope.row.settlementStatus)">
          {{ getSettlementStatusDesc(scope.row.settlementStatus) }}
        </el-tag>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  methods: {
    // 根据code获取佣金类型描述
    getCommissionTypeDesc(code) {
      const type = this.commissionTypes.find(t => t.code === code)
      return type ? type.desc : '未知'
    },
    
    // 根据code获取计算方式描述
    getCalculationTypeDesc(code) {
      const type = this.calculationTypes.find(t => t.code === code)
      return type ? type.desc : '未知'
    },
    
    // 根据code获取结算状态描述
    getSettlementStatusDesc(code) {
      const status = this.settlementStatuses.find(s => s.code === code)
      return status ? status.desc : '未知'
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'warning'  // 未结算
        case 1: return 'success'  // 已结算
        case 2: return 'danger'   // 已取消
        default: return 'info'
      }
    }
  }
}
</script>
```

## 3. 工具类使用示例

### 3.1 后端Controller中使用

```java
@RestController
public class ExampleController {
    
    /**
     * 获取所有枚举选项
     */
    @RequestMapping("/getAllEnumOptions")
    public R getAllEnumOptions() {
        Map<String, Object> result = new HashMap<>();
        result.put("commissionTypes", CommissionEnumUtils.getCommissionTypeOptions());
        result.put("calculationTypes", CommissionEnumUtils.getCalculationTypeOptions());
        result.put("settlementStatuses", CommissionEnumUtils.getSettlementStatusOptions());
        return R.ok().put("data", result);
    }
    
    /**
     * 根据佣金类型获取计算方式
     */
    @RequestMapping("/getCalculationTypesByCommissionType")
    public R getCalculationTypesByCommissionType(@RequestParam Integer commissionType) {
        List<Map<String, Object>> options = CommissionEnumUtils
            .getCalculationTypeOptionsByCommissionType(commissionType);
        return R.ok().put("calculationTypes", options);
    }
}
```

### 3.2 通用枚举处理示例

```java
// 使用通用方法处理其他枚举
public enum StatusEnum {
    ACTIVE(1, "激活"),
    INACTIVE(0, "禁用");
    
    private final Integer code;
    private final String desc;
    
    // 构造函数和getter方法...
}

// 在Controller中使用
List<Map<String, Object>> statusOptions = CommissionEnumUtils.buildEnumOptions(
    StatusEnum.values(),
    StatusEnum::getCode,
    StatusEnum::getDesc
);
```

## 4. 数据格式说明

### 4.1 标准返回格式

所有枚举选项API都返回统一格式：

```json
{
  "code": 200,
  "msg": "success",
  "commissionTypes": [
    {
      "code": 1,
      "desc": "创建活动佣金"
    },
    {
      "code": 2,
      "desc": "充值次数佣金"
    },
    {
      "code": 3,
      "desc": "用户转发佣金"
    }
  ]
}
```

### 4.2 前端处理建议

1. **缓存枚举数据**：在应用启动时获取一次，存储在Vuex或全局变量中
2. **统一处理方法**：创建全局的枚举转换方法
3. **类型安全**：使用TypeScript时定义相应的接口类型
4. **错误处理**：对未知的枚举值提供默认显示

这样的设计使得API返回的数据结构更加清晰和规范，便于前端处理和维护。
