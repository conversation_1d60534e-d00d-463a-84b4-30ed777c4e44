<template>
    <van-dialog v-model="show" title="长按二维码关注公众号" @confirm="$emit('close')" confirmButtonText="关闭">
        <div class="text-center padding">
            <van-image width="200" :src="qrcodeImgUrl">
                <template v-slot:error>二维码加载失败
                </template>
            </van-image>
        <div style="color: grey; font-size: 14px">长按二维码关注公众号,了解会议实时动态</div>
        </div>
    </van-dialog>
</template>

<script>
    export default {
        name: 'FollowModal',
        props: {
            show: {
                type: Boolean,
                default: false
            },
            qrcodeImgUrl: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                // qrcodeImgUrl:process.env.VUE_APP_WX_QRCODE
            }
        }
    }
</script>