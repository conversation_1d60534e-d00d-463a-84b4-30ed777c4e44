<template>
  <div class="tinymce-editor">
    <editor
      v-model="myValue"
      :init="init"
      @onExecCommand="onExecCommand"
    ></editor>
  </div>
</template>
<script>
import Editor from "@tinymce/tinymce-vue";

import { ConvertImage, ImageCompressor } from "@/utils/image";
export default {
  name: "tinymce-editor",
  components: {
    Editor,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      init: {
        language_url: "./tinymce/zh_CN.js", //public目录下
        language: "zh_CN",
        height: 500,
        plugins:
          "lists image media table paste link searchreplace anchor code preview pagebreak importcss",
        toolbar:
          "undo redo searchreplace |  formatselect pagebreak | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists link anchor image media table | removeformat code preview", //工具栏展示项
        toolbar_drawer: false,
        image_advtab: true,
        object_resizing: false,
        paste_data_images: true,
        content_css: "./tinymce/article.css",
        images_upload_handler: (blobInfo, success, failure) => {
          this.uploadFile(blobInfo.blob())
            .then((fileUrl) => success(fileUrl))
            .catch((err) => failure(err));
        },
        automatic_uploads: true,
        file_picker_types: "media",
        file_picker_callback: (callback, value, meta) => {
          let that = this;
          if (meta.filetype == "media") {
            let input = document.createElement("input"); //创建一个隐藏的input
            input.setAttribute("type", "file");
            input.onchange = function () {
              let file = this.files[0]; //选取第一个文件
              let fileSize = file.size / 1024 / 1024 <= 20;
              if (!fileSize) {
                alert("上传文件大小不能超过20M");
                return;
              }
              let formData = new FormData();
              formData.append("file", file);
              that
                .$http({
                  url: that.$http.adornUrl("/sys/oss/upload"),
                  method: "post",
                  data: formData,
                })
                .then(({ data }) => {
                  if (data && data.code === 200) {
                    alert("上传成功");
                    callback(data.url);
                  } else {
                    alert(data.msg);
                  }
                });
            };
            //触发点击
            input.click();
          }
        },
      },
      myValue: this.value,
      uploading: false,
      cosConfig: [],
    };
  },
  mounted() {
    // console.log('tinymce-editor mounted:',this.value)
    tinymce.init({});
    this.cosInit();
  },
  methods: {
    cosInit() {
      this.$http({
        url: this.$http.adornUrl("/sys/oss/config"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.cosConfig = data.config;
        } else {
          this.$message.error("请先配置云存储相关信息！");
        }
      });
    },
    onExecCommand(e) {
      //console.log(e)
    },
    uploadFile(file) {
      console.log(file);
      this.uploading = true;

      return new Promise((resolve, reject) => {
        var that = this;
        const formData = new FormData();
        let img = file;
        const fileType = img.name.substring(img.name.indexOf(".") + 1);
        // 判断文件是不是jpeg 不是jpeg的都转成jpeg
        // if (!["jpeg", "jpg"].includes(fileType)) {
        //   ConvertImage(img)
        //     .then((fileUrl) => {
        //       ImageCompressor(fileUrl, "file", 0.2).then((newImg) => {
        //         console.log(newImg);
        //         formData.append("file", newImg);
        //         that
        //           .$http({
        //             url: that.$http.adornUrl("/sys/oss/upload"),
        //             method: "post",
        //             data: formData,
        //           })
        //           .then(({ data }) => {
        //             console.log(data);
        //             if (data && data.code === 200) {
        //               that.$emit("uploaded", data.url);
        //               resolve(data.url);
        //             } else {
        //               that.$message.error("文件上传失败：" + data.msg);
        //               reject(data.msg);
        //             }
        //             that.uploading = false;
        //           })
        //           .catch((err) => reject(err));
        //       });
        //     })
        //     .catch((err) => reject(err));
        // } else {
          // ImageCompressor(file, "file", 0.2).then((newImg) => {
                formData.append("file", file);
                that
                  .$http({
                    url: that.$http.adornUrl("/sys/oss/upload"),
                    method: "post",
                    data: formData,
                  })
                  .then(({ data }) => {
                    console.log(data);
                    if (data && data.code === 200) {
                      that.$emit("uploaded", data.url);
                      resolve(data.url);
                    } else {
                      that.$message.error("文件上传失败：" + data.msg);
                      reject(data.msg);
                    }
                    that.uploading = false;
                  })
                  .catch((err) => reject(err));
              // });
        // }
      });
    },
  },
  watch: {
    value(newValue) {
      this.myValue = newValue;
    },
    myValue(newValue) {
      this.$emit("input", newValue);
    },
  },
};
</script>
<style lang="css">
.tox-tinymce-aux {
  z-index: 9999 !important;
}
</style>