<template>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
            label-width="120px">
            <el-form-item label="议程任务" prop="guestSchedule">
                <el-switch v-model="dataForm.guestSchedule" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
            <el-form-item label="专家信息" prop="guestInfo">
                <el-switch v-model="dataForm.guestInfo" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
            <el-form-item label="专家行程" prop="guestTrip">
                <el-switch v-model="dataForm.guestTrip" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
            <el-form-item label="劳务费签字" prop="guestService">
                <el-switch v-model="dataForm.guestService" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
            <el-form-item label="劳务费信息" prop="guestServiceInfo">
                <el-switch v-model="dataForm.guestServiceInfo" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
            <el-form-item label="行程接送" prop="guestLink">
                <el-switch v-model="dataForm.guestLink" :active-value="1" :inactive-value="0">
                </el-switch>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            dataForm: {
                id: undefined,
                guestId: "",
                guestSchedule: 0,
                guestInfo: 0,
                guestTrip: 0,
                guestService: 0,
                guestServiceInfo: 0,
                guestLink: 0,
            },
            dataRule: {
                guestSchedule: [
                    {
                        required: true,
                        message: "议程任务不能为空",
                        trigger: "blur",
                    },
                ],
                guestInfo: [
                    {
                        required: true,
                        message: "专家信息不能为空",
                        trigger: "blur",
                    },
                ],
                guestTrip: [
                    {
                        required: true,
                        message: "专家行程不能为空",
                        trigger: "blur",
                    },
                ],
                guestService: [
                    {
                        required: true,
                        message: "银行卡信息不能为空",
                        trigger: "blur",
                    },
                ],
                guestServiceInfo: [
                    {
                        required: true,
                        message: "服务信息不能为空",
                        trigger: "blur",
                    },
                ],
                guestLink: [
                    {
                        required: true,
                        message: "行程接送不能为空",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        init(id) {
            this.dataForm.id = id || "";
            this.visible = true;
            this.$nextTick(() => {
                this.$refs["dataForm"].resetFields();
                if (this.dataForm.id) {
                    // 获取信息
                    this.$http({
                        url: this.$http.adornUrl(
                            `/activity/activityguest/info/${this.dataForm.id}`
                        ),
                        method: "get",
                        params: this.$http.adornParams(),
                    }).then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.guestSchedule =
                                data.activityGuest.guestSchedule;
                            this.dataForm.guestInfo =
                                data.activityGuest.guestInfo;
                            this.dataForm.guestTrip =
                                data.activityGuest.guestTrip;
                            this.dataForm.guestService =
                                data.activityGuest.guestService;
                            this.dataForm.guestServiceInfo =
                                data.activityGuest.guestServiceInfo;
                            this.dataForm.guestLink =
                                data.activityGuest.guestLink;
                        }
                    });
                }
            });
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(
                            `/activity/activityguest/${!this.dataForm.id ? "save" : "updateStatus"
                            }`
                        ),
                        method: "post",
                        data: this.$http.adornData(this.dataForm),
                    }).then(({ data }) => {
                        if (data && data.code === 200) {
                            this.$message({
                                message: "操作成功",
                                type: "success",
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false;
                                    this.$emit("refreshDataList");
                                },
                            });
                        } else {
                            this.$message.error(data.msg);
                        }
                    });
                }
            });
        },
    },
};
</script>
