<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.couponName" placeholder="团购券名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.platformType" placeholder="平台类型" clearable>
          <el-option label="抖音团购" value="douyin"></el-option>
          <el-option label="美团团购" value="meituan"></el-option>
          <el-option label="大众点评团购" value="dianping"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="dataForm.activityId" placeholder="选择活动" clearable>
          <el-option
            v-for="activity in activityList"
            :key="activity.id"
            :label="activity.name"
            :value="activity.id">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="状态" clearable>
          <el-option label="上架" value="1"></el-option>
          <el-option label="下架" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('groupbuying:coupon:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('groupbuying:coupon:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button v-if="isAuth('groupbuying:coupon:update')" type="success" @click="updateStatusHandle(1)"
          :disabled="dataListSelections.length <= 0">批量上架</el-button>
        <el-button v-if="isAuth('groupbuying:coupon:update')" type="warning" @click="updateStatusHandle(0)"
          :disabled="dataListSelections.length <= 0">批量下架</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="id" header-align="center" align="center" label="ID">
      </el-table-column>
      <el-table-column prop="couponName" header-align="center" align="center" label="团购券名称">
      </el-table-column>
      <el-table-column prop="platformType" header-align="center" align="center" label="平台类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.platformType === 'douyin'" type="primary">抖音团购</el-tag>
          <el-tag v-else-if="scope.row.platformType === 'meituan'" type="warning">美团团购</el-tag>
          <el-tag v-else-if="scope.row.platformType === 'dianping'" type="success">大众点评团购</el-tag>
          <span v-else>{{ scope.row.platformType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="originalPrice" header-align="center" align="center" label="原价">
        <template slot-scope="scope">
          <span v-if="scope.row.originalPrice">¥{{ scope.row.originalPrice }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="groupPrice" header-align="center" align="center" label="团购价">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.groupPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="soldCount" header-align="center" align="center" label="已售">
        <template slot-scope="scope">
          <span>{{ scope.row.soldCount || 0 }}</span>
          <span v-if="scope.row.totalCount">/ {{ scope.row.totalCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" type="success">上架</el-tag>
          <el-tag v-else type="danger">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sortOrder" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './groupbuying-coupon-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        couponName: '',
        platformType: '',
        activityId: '',
        status: ''
      },
      dataList: [],
      activityList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList()
    // this.getActivityList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/groupbuying/coupon/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'couponName': this.dataForm.couponName,
          'platformType': this.dataForm.platformType,
          'activityId': this.dataForm.activityId,
          'status': this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取活动列表
    getActivityList() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityList = data.page.list
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.dataForm.activityId)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/groupbuying/coupon/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 批量更新状态
    updateStatusHandle(status) {
      var ids = this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对选中的团购券进行[${status === 1 ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/groupbuying/coupon/updateStatus'),
          method: 'post',
          data: this.$http.adornData({
            ids: ids,
            status: status
          }, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
