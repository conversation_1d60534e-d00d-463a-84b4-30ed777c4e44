<template>
  <el-dialog title="日程讨论嘉宾修改" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <!-- <el-form-item  label="添加嘉宾" prop="supplierProducts">
        <el-select v-model="supplierProduct" placeholder="添加嘉宾" filterable @change="selectProduct">
          <el-option v-for="item in supplierProducts" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
    </el-form-item> -->
      <el-table style="margin: 20px 0" v-if="placeActivityTopicGuest.length > 0" :data="placeActivityTopicGuest" border>
        <el-table-column prop="name" header-align="center" align="center" label="嘉宾名称">
        </el-table-column>
        <el-table-column prop="orderBy" show-overflow-tooltip="true" header-align="center" align="center" label="排序">
          <div slot-scope="scope">
            <el-input v-model="scope.row.orderBy" placeholder="排序"></el-input>
          </div>
        </el-table-column>
        <el-table-column prop="confirmStatus" show-overflow-tooltip="true" header-align="center" align="center"
          label="确认状态">
          <div slot-scope="scope">
            <el-select v-model="scope.row.confirmStatus" placeholder="确认状态" filterable>
              <el-option v-for="item in confirmStatus" :key="item.key" :label="item.value"
                :value="item.key"></el-option>
            </el-select>
          </div>
        </el-table-column>
        <el-table-column prop="confirmTime" header-align="center" align="center" label="确认时间">
        </el-table-column>
        <el-table-column prop="confirmReason" header-align="center" align="center" label="拒绝理由">
        </el-table-column>
      </el-table>
      <!-- <el-form-item label="主题名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="主题名称"></el-input>
      </el-form-item>
      <el-form-item label="会议场地" prop="placeId">
        <el-select v-model="dataForm.placeId" placeholder="会议场地" filterable>
          <el-option
            v-for="item in placeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主持人" prop="topicGuestIds">
        <el-select v-model="dataForm.topicGuestIds" multiple placeholder="主持人" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序，数值越小越靠前" prop="orderBy">
        <el-input
          v-model="dataForm.orderBy"
          placeholder="排序，数值越小越靠前"
        ></el-input>
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button :disabled="placeActivityTopicGuest.length == 0" type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { confirmStatus } from "@/data/place";
export default {
  data() {
    return {
      confirmStatus,
      visible: false,
      placeActivityTopicGuest: [],
      guestList: [],
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        placeId: "",
        topicGuestIds: [],
        orderBy: 0,
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "会场名称不能为空", trigger: "blur" },
        ],
        placeId: [{ required: true, message: "场地不能为空", trigger: "blur" }],
        orderBy: [
          {
            required: true,
            message: "排序，数值越小越靠前不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placeactivitytopicschedulediscuss/findBySchedulesId/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.placeActivityTopicGuest = data.result;
            }
          });
          // this.$http({
          //   url: this.$http.adornUrl(
          //     `/place/placeactivitytopic/info/${this.dataForm.id}`
          //   ),
          //   method: "get",
          //   params: this.$http.adornParams(),
          // }).then(({ data }) => {
          //   if (data && data.code === 200) {
          //     this.dataForm.activityId = data.placeActivityTopic.activityId;
          //     this.dataForm.createOn = data.placeActivityTopic.createOn;
          //     this.dataForm.createBy = data.placeActivityTopic.createBy;
          //     this.dataForm.updateOn = data.placeActivityTopic.updateOn;
          //     this.dataForm.updateBy = data.placeActivityTopic.updateBy;
          //     this.dataForm.name = data.placeActivityTopic.name;
          //     this.dataForm.placeId = data.placeActivityTopic.placeId;
          //     this.dataForm.topicGuestIds = data.placeActivityTopic.topicGuestIds;
          //     this.dataForm.orderBy = data.placeActivityTopic.orderBy;
          //   }
          // });
        }
      });
      // this.getPlace();
      // this.getGuest();
    },
    // getPlace() {
    //   this.$http({
    //     url: this.$http.adornUrl(
    //       `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
    //     ),
    //     method: "get",
    //     params: this.$http.adornParams(),
    //   }).then(({ data }) => {
    //     if (data && data.code === 200) {
    //       this.placeList = data.result;
    //     }
    //   });
    // },
    getGuest() {
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityguest/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.guestList = data.result;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      // this.$refs["dataForm"].validate((valid) => {
      //   if (valid) {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivitytopicschedulediscuss/updateBatch`
        ),
        method: "post",
        data: this.placeActivityTopicGuest,
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: "操作成功",
            type: "success",
            duration: 1500,
            onClose: () => {
              this.visible = false;
              this.$emit("refreshDataList");
            },
          });
        } else {
          this.$message.error(data.msg);
        }
      });
    }
    // });
    // },
  },
};
</script>
