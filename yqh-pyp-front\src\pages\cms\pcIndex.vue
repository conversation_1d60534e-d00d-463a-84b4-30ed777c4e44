<template>
  <div class="pc-container">
    <pcheader @changeShowType="changeShowType"/>
    <!-- 导航栏 -->
    <!-- <van-tabs class="nav" v-model="cmsId" @click="onClick">
      <van-tab
        v-for="item in cmsList"
        :key="item.id"
        :title="item.title"
        :name="item.id"
      >
      </van-tab>
    </van-tabs> -->
    <!-- 普通内容 -->
    <div
      class="content"
      v-html="cmsInfo.content || cmsInfo.mobileContent"
    ></div>

    <!-- 九宫格底部-文件下载 -->
    <div
      class="download"
      v-if="
        activityInfo.type &&
        activityInfo.type.includes('2') &&
        activityBottom[2]
      "
      style="margin-top: 10px"
    >
      <van-collapse v-model="downActive">
        <van-collapse-item title="文件下载" name="0">
          <div v-for="item in activityBottom[2]" :key="item.id">
            <van-cell
              :title="item.name"
              v-if="item.type == 2"
              @click="download(item.url)"
              value="点击下载"
            />
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <!-- 九宫格底部-banner广告 -->
    <div
      v-if="
        activityInfo.type &&
        activityInfo.type.includes('1') &&
        activityBottom[1]
      "
      style="margin-top: 10px"
    >
      <van-swipe :autoplay="3000">
        <div v-for="(image, index) in activityBottom[1]" :key="index">
          <van-swipe-item v-if="image.type == 1">
            <van-image
              width="100%"
              height="100px"
              :src="image.name"
              @click="openUrl(item.url)"
            >
            </van-image>
          </van-swipe-item>
        </div>
      </van-swipe>
    </div>
    <!-- 九宫格底部-自定义 -->
    <div
      class="bottomdiy"
      :style="{ background: activityInfo.bottomColor }"
      v-if="
        activityInfo.type &&
        activityInfo.type.includes('0') &&
        activityBottom[0]
      "
    >
      <div v-for="(item, index) in activityBottom[0]" :key="index">
        <a class="item" v-if="item.type == 0" :href="item.url">{{
          item.name
        }}</a>
      </div>
    </div>
    <!-- <div class="copyright"><a href="https://beian.miit.gov.cn/">蒙ICP备2021004073号-1</a></div> -->
  </div>
</template>

<script>
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: {
    pcheader
  },
  data() {
    return {
      baseUrl: '',
      openid: undefined,
      activityId: undefined,
      downActive: ["0"],
      cmsList: [],
      showType: 0, // 0-内容，1-日程，2-嘉宾，3-报名，4-直播
      activityBottom: {},
      bannerIndex: 0,
      activityInfo: {},
      dateCompare: 0,
      cmsId: undefined,
      cmsInfo: {},
    };
  },
  mounted() {
    this.baseUrl = window.location.origin + window.location.pathname;
    this.openid = this.$cookie.get("openid");
    this.activityId = this.$route.query.id;
    this.cmsId = this.$route.query.cmsId;
    // this.getActivityInfo();
    // this.activityLogCount();
  },
  methods: {
    // getActivityInfo() {
    //   this.$fly
    //     .get(`/pyp/activity/activity/info/${this.activityId}`)
    //     .then((res) => {
    //       this.loading = false;
    //       if (res.code == 200) {
    //         this.activityInfo = res.activity;
    //         document.title = this.activityInfo.name;
    //         // 处理倒计时
    //         let t1 = this.activityInfo.startTime;
    //         let dateEnd = new Date(t1.replace(/-/g, "/"));
    //         let dateBegin = new Date(); //当前时间数据
    //         let dateCompare = dateEnd.getTime() - dateBegin.getTime();
    //         this.dateCompare = dateCompare > 0 ? dateCompare : 0;
    //         this.getCmsList();
    //         this.getActivityBottom();
    //       } else {
    //         vant.Toast(res.msg);
    //         this.activityInfo = {};
    //       }
    //     });
    // },
    // 获取底部
    getActivityBottom() {
      this.$fly
        .get(
          `/pyp/web/activity/activitybottom/findByActivity/${this.activityId}`,
          {
            type: this.activityInfo.type,
          }
        )
        .then((res) => {
          if (res.code == 200) {
            this.activityBottom = res.result;
          }
        });
    },
    // 轮播图事件监听
    bannerIndexChange(index) {
      this.bannerIndex = index;
    },
    isJSON(v) {
      try {
        JSON.parse(v);
        return true;
      } catch (error) {
        return false;
      }
    },
    download(v) {
      window.open(v);
    },
    openUrl(v) {
      if (v) {
        window.open(v);
      }
    },
    changeShowType(v) {
      this.cmsId = v;
      this.getCmsInfo();
    },
    getCmsInfo() {
      this.$fly.get(`/pyp/cms/cms/info/${this.cmsId}`).then((res) => {
        if (res.code == 200) {
          this.cmsInfo = res.cms;
          document.title = this.cmsInfo.title;
        } else {
          vant.Toast(res.msg);
          this.cmsInfo = {};
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>

.coutdown {
  .van-count-down {
    line-height: 24px;
  }
  .van-cell__title,
  .van-cell__value {
    text-align: center;
  }
}
.nav /deep/ .van-tabs__wrap {
  height: 60px;
  // border-radius: 12px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  background: white;
  border-radius: 12px;
  /deep/ img {
    width: 100%;
    height: auto;
  }
  .van-cell {
    align-items: center;
  }
}

.copyright {
  height: 46px;
  font-size: 14px;
  font-weight: 400;
  line-height: 46px;
  text-align: center;
  color: black;
}
iframe {
  width: 100%;
  height: 600px;
}
</style>