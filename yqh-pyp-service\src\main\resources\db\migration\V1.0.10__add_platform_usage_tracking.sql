-- 为ActivityImage表添加按平台独立使用计数功能
-- 版本: V1.0.10
-- 作者: cjy
-- 日期: 2025-07-29

-- 创建平台使用记录表，实现按平台独立计数
CREATE TABLE `activity_image_platform_usage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `image_id` bigint(20) NOT NULL COMMENT '图片ID',
  `platform` varchar(50) NOT NULL COMMENT '平台类型（douyin, xiaohongshu, dianping等）',
  `use_count` int(11) DEFAULT 0 COMMENT '该平台的使用次数',
  `first_used_time` datetime DEFAULT NULL COMMENT '首次使用时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_platform` (`image_id`, `platform`),
  KEY `idx_activity_platform` (`activity_id`, `platform`),
  KEY `idx_image_id` (`image_id`),
  KEY `idx_platform_usage` (`platform`, `use_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片平台使用记录表';
