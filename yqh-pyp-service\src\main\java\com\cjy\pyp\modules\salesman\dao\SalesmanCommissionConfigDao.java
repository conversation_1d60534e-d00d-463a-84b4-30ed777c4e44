package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金配置DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Mapper
public interface SalesmanCommissionConfigDao extends BaseMapper<SalesmanCommissionConfigEntity> {

    /**
     * 分页查询佣金配置列表
     * @param params 查询参数
     * @return 佣金配置列表
     */
    List<SalesmanCommissionConfigEntity> queryPage(Map<String, Object> params);

    /**
     * 根据业务员ID和佣金类型查询配置
     * @param salesmanId 业务员ID
     * @param commissionType 佣金类型
     * @param appid 应用ID
     * @return 佣金配置
     */
    SalesmanCommissionConfigEntity getByTypeAndSalesman(@Param("salesmanId") Long salesmanId, 
                                                        @Param("commissionType") Integer commissionType, 
                                                        @Param("appid") String appid);

    /**
     * 根据业务员ID查询所有有效的佣金配置
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 佣金配置列表
     */
    List<SalesmanCommissionConfigEntity> getEffectiveConfigsBySalesman(@Param("salesmanId") Long salesmanId, 
                                                                       @Param("appid") String appid);

    /**
     * 批量查询业务员的佣金配置
     * @param salesmanIds 业务员ID列表
     * @param appid 应用ID
     * @return 佣金配置列表
     */
    List<SalesmanCommissionConfigEntity> getBatchConfigsBySalesmen(@Param("salesmanIds") List<Long> salesmanIds, 
                                                                   @Param("appid") String appid);
}
