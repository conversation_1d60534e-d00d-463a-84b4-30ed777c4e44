<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="确定议程任务开始时间" prop="guestScheduleStart">
        <el-date-picker v-model="dataForm.guestScheduleStart" type="datetime" placeholder="请选择确定议程任务开始时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确定议程任务结束时间" prop="guestScheduleEnd">
        <el-date-picker v-model="dataForm.guestScheduleEnd" type="datetime" placeholder="请选择确定议程任务结束时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开启议程任务" prop="guestSchedule">
        <el-input v-model="dataForm.guestSchedule" placeholder="开启议程任务"></el-input>
      </el-form-item>
      <el-form-item label="确定专家信息开始时间" prop="guestInfoStart">
        <el-date-picker v-model="dataForm.guestInfoStart" type="datetime" placeholder="请选择确定专家信息开始时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确定专家信息结束时间" prop="guestInfoEnd">
        <el-date-picker v-model="dataForm.guestInfoEnd" type="datetime" placeholder="请选择确定专家信息结束时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开启专家信息" prop="guestInfo">
        <el-input v-model="dataForm.guestInfo" placeholder="开启专家信息"></el-input>
      </el-form-item>
      <el-form-item label="确定专家行程开始时间" prop="guestTripStart">
        <el-date-picker v-model="dataForm.guestTripStart" type="datetime" placeholder="请选择确定专家行程开始时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确定专家行程结束时间" prop="guestTripEnd">
        <el-date-picker v-model="dataForm.guestTripEnd" type="datetime" placeholder="请选择确定专家行程结束时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开启专家行程" prop="guestTrip">
        <el-input v-model="dataForm.guestTrip" placeholder="开启专家行程"></el-input>
      </el-form-item>
      <el-form-item label="确定专家银行卡信息开始时间" prop="guestServiceStart">
        <el-date-picker v-model="dataForm.guestServiceStart" type="datetime" placeholder="请选择确定专家银行卡信息开始时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确定专家银行卡信息结束时间" prop="guestServiceEnd">
        <el-date-picker v-model="dataForm.guestServiceEnd" type="datetime" placeholder="请选择确定专家银行卡信息结束时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开启专家银行卡信息" prop="guestService">
        <el-input v-model="dataForm.guestService" placeholder="开启专家银行卡信息"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        activityId: '',
        guestScheduleStart: '',
        guestScheduleEnd: '',
        guestSchedule: '',
        guestInfoStart: '',
        guestInfoEnd: '',
        guestInfo: '',
        guestTripStart: '',
        guestTripEnd: '',
        guestTrip: '',
        guestServiceStart: '',
        guestServiceEnd: '',
        guestService: ''
      },
      dataRule: {
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        guestScheduleStart: [
          { required: true, message: '确定议程任务开始时间不能为空', trigger: 'blur' }
        ],
        guestScheduleEnd: [
          { required: true, message: '确定议程任务结束时间不能为空', trigger: 'blur' }
        ],
        guestSchedule: [
          { required: true, message: '开启议程任务不能为空', trigger: 'blur' }
        ],
        guestInfoStart: [
          { required: true, message: '确定专家信息开始时间不能为空', trigger: 'blur' }
        ],
        guestInfoEnd: [
          { required: true, message: '确定专家信息结束时间不能为空', trigger: 'blur' }
        ],
        guestInfo: [
          { required: true, message: '开启专家信息不能为空', trigger: 'blur' }
        ],
        guestTripStart: [
          { required: true, message: '确定专家行程开始时间不能为空', trigger: 'blur' }
        ],
        guestTripEnd: [
          { required: true, message: '确定专家行程结束时间不能为空', trigger: 'blur' }
        ],
        guestTrip: [
          { required: true, message: '开启专家行程不能为空', trigger: 'blur' }
        ],
        guestServiceStart: [
          { required: true, message: '确定专家银行卡信息开始时间不能为空', trigger: 'blur' }
        ],
        guestServiceEnd: [
          { required: true, message: '确定专家银行卡信息结束时间不能为空', trigger: 'blur' }
        ],
        guestService: [
          { required: true, message: '开启专家银行卡信息不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityconfig/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.activityConfig.activityId
              this.dataForm.guestScheduleStart = data.activityConfig.guestScheduleStart
              this.dataForm.guestScheduleEnd = data.activityConfig.guestScheduleEnd
              this.dataForm.guestSchedule = data.activityConfig.guestSchedule
              this.dataForm.guestInfoStart = data.activityConfig.guestInfoStart
              this.dataForm.guestInfoEnd = data.activityConfig.guestInfoEnd
              this.dataForm.guestInfo = data.activityConfig.guestInfo
              this.dataForm.guestTripStart = data.activityConfig.guestTripStart
              this.dataForm.guestTripEnd = data.activityConfig.guestTripEnd
              this.dataForm.guestTrip = data.activityConfig.guestTrip
              this.dataForm.guestServiceStart = data.activityConfig.guestServiceStart
              this.dataForm.guestServiceEnd = data.activityConfig.guestServiceEnd
              this.dataForm.guestService = data.activityConfig.guestService
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityconfig/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'guestScheduleStart': this.dataForm.guestScheduleStart,
              'guestScheduleEnd': this.dataForm.guestScheduleEnd,
              'guestSchedule': this.dataForm.guestSchedule,
              'guestInfoStart': this.dataForm.guestInfoStart,
              'guestInfoEnd': this.dataForm.guestInfoEnd,
              'guestInfo': this.dataForm.guestInfo,
              'guestTripStart': this.dataForm.guestTripStart,
              'guestTripEnd': this.dataForm.guestTripEnd,
              'guestTrip': this.dataForm.guestTrip,
              'guestServiceStart': this.dataForm.guestServiceStart,
              'guestServiceEnd': this.dataForm.guestServiceEnd,
              'guestService': this.dataForm.guestService
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
