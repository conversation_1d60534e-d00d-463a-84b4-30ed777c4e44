<template>
    <div class="container">
        <div class="background-animation"></div>
        <div class="card-wrapper">
            <div class="card">
                <!-- <transition name="fade" mode="out-in"> -->
                    <div v-if="status === 'preparing'" key="preparing">
                        <div class="pulse-loader">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <h2>准备中</h2>
                        <p class="subtitle">正在准备发布环境，请稍候...</p>
                    </div>
                    <div v-else-if="status === 'publishing'" key="publishing">
                        <div class="pulse-loader">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <h2>正在发布视频</h2>
                        <p class="subtitle">请勿离开或刷新页面...</p>
                    </div>
                    <div v-else-if="status === 'success'" key="success">
                        <div class="icon success-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                        </div>
                        <h2>发布成功！</h2>
                        <p class="subtitle">您的视频已成功发布。</p>
                        <button @click="goBack" class="btn">返回</button>
                    </div>
                    <div v-else-if="status === 'error'" key="error">
                        <div class="icon error-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                        </div>
                        <h2>发布失败</h2>
                        <p class="subtitle">{{ errorMsg }}</p>
                        <div class="button-group">
                            <button @click="uploadVideo" class="btn">重试</button>
                            <button @click="goBack" class="btn btn-secondary">返回</button>
                        </div>
                    </div>
                <!-- </transition> -->
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {},
    data() {
        return {
            activityId: undefined,
            code: undefined,
            status: 'preparing', // 'preparing', 'publishing', 'success', 'error'
            errorMsg: '',
        };
    },
    mounted() {
        document.title = "发布视频";
        this.activityId = this.$route.query.id;
        this.code = this.$route.query.code;
        if (!this.code) {
            // 第一次进入页面可能没有code，停留在准备阶段，不提示错误
            return;
        }
        this.uploadVideo();
    },
    methods: {
        uploadVideo() {
            this.status = 'publishing';
            this.errorMsg = '';
            this.$fly.get('/pyp/web/kuaishou/upload', {
                activityId: this.activityId,
                code: this.code,
            }).then((res) => {
                if (res.code == 200) {
                    this.status = 'success';
                } else {
                    this.status = 'error';
                    this.errorMsg = res.msg || '未知错误';
                }
            }).catch(err => {
                this.status = 'error';
                this.errorMsg = err.message || '网络请求失败';
            });
        },
        goBack() {
            this.$router.replace({
                path: '/cms/index',
                query: {
                    id: this.activityId,
                }
            })
        }
    },
};
</script>

<style lang="less" scoped>
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    padding: 15px;
    position: relative;
    overflow: hidden;
}

.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(125deg, #ffffff11 0%, #ffffff22 100%);
    z-index: 0;
    transform: translateZ(0);
}

.background-animation:before, .background-animation:after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    animation: moveBackground 15s infinite linear;
    transform-origin: center center;
    z-index: -1;
}

.background-animation:after {
    animation-duration: 25s;
    animation-direction: reverse;
    background: radial-gradient(circle, rgba(220,235,255,0.08) 0%, rgba(220,235,255,0) 60%);
}

@keyframes moveBackground {
    0% {
        transform: translate(-20%, -10%) rotate(0deg);
    }
    100% {
        transform: translate(20%, 10%) rotate(360deg);
    }
}

.card-wrapper {
    width: 90%;
    max-width: 450px;
    perspective: 1000px;
    position: relative;
    z-index: 1;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
    text-align: center;
    transform-style: preserve-3d;
    transition: all 0.6s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Fade transition - Vue 2 compatible names */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.5s ease, transform 0.5s ease;
}
.fade-enter, .fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
}

h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 28px;
    font-weight: 600;
    color: #2d3748;
    letter-spacing: -0.5px;
    line-height: 1.3;
}

.subtitle {
    font-size: 17px;
    color: #718096;
    margin-bottom: 32px;
    line-height: 1.6;
    font-weight: 400;
}

.btn {
    padding: 12px 28px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    background-color: #4299e1;
    color: white;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 6px rgba(66, 153, 225, 0.2);
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.4s ease-out;
}

.btn:hover {
    background-color: #3182ce;
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(66, 153, 225, 0.3);
}

.btn:hover:before {
    transform: translateX(0);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.2);
}

.btn-secondary {
    background-color: #718096;
    box-shadow: 0 4px 6px rgba(113, 128, 150, 0.2);
}

.btn-secondary:hover {
    background-color: #4a5568;
    box-shadow: 0 7px 14px rgba(113, 128, 150, 0.3);
}

.button-group {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
}

.pulse-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    margin: 0 auto 20px;
}

.pulse-loader span {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #4299e1;
    margin: 0 5px;
    animation: pulse 1.5s infinite ease-in-out;
}

.pulse-loader span:nth-child(2) {
    animation-delay: 0.3s;
}

.pulse-loader span:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes pulse {
    0%, 80%, 100% { 
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% { 
        transform: scale(1);
        opacity: 1;
    }
}

.icon {
    margin: 0 auto 20px;
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.success-icon {
    color: #38b2ac;
    background-color: rgba(56, 178, 172, 0.1);
}

.success-icon svg {
    width: 40px;
    height: 40px;
}

.error-icon {
    color: #e53e3e;
    background-color: rgba(229, 62, 62, 0.1);
}

.error-icon svg {
    width: 40px;
    height: 40px;
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
    .card {
        padding: 30px 20px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .subtitle {
        font-size: 15px;
        margin-bottom: 25px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 15px;
    }
    
    .button-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .button-group .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .card-wrapper {
        width: 95%;
    }
    
    .card {
        padding: 25px 15px;
    }
    
    .icon {
        width: 70px;
        height: 70px;
    }
}
</style>