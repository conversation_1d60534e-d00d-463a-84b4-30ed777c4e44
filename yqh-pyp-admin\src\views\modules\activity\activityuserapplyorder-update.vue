<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="订单状态" prop="status">
      <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
        <el-option v-for="item in orderStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="支付来源" prop="source">
      <el-select v-model="dataForm.source" placeholder="支付来源" filterable>
        <el-option v-for="item in sources" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" placeholder="备注" clearable></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {sources} from '@/data/common'
import {orderStatus} from '@/data/common'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          activityUserId: '',
          userId: '',
          status: '',
          name: '',
          orderSn: '',
          source: '',
          remarks: '',
        },
        sources,
        orderStatus: orderStatus,
        dataRule: {
          status: [
            { required: true, message: '订单状态不能为空', trigger: 'blur' }
          ],
          source: [
            { required: true, message: '支付来源不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuserapplyorder/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityUserApplyOrder.activityId
                this.dataForm.userId = data.activityUserApplyOrder.userId
                this.dataForm.activityUserId = data.activityUserApplyOrder.activityUserId
                this.dataForm.status = data.activityUserApplyOrder.status
                this.dataForm.name = data.activityUserApplyOrder.name
                this.dataForm.orderSn = data.activityUserApplyOrder.orderSn
                this.dataForm.source = data.activityUserApplyOrder.source
                this.dataForm.remarks = data.activityUserApplyOrder.remarks
              }
            })
          
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuserapplyorder/update`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'userId': this.dataForm.userId,
                'status': this.dataForm.status,
                'name': this.dataForm.name,
                'activityUserId': this.dataForm.activityUserId,
                'orderSn': this.dataForm.orderSn,
                'source': this.dataForm.source,
                'remarks': this.dataForm.remarks,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
