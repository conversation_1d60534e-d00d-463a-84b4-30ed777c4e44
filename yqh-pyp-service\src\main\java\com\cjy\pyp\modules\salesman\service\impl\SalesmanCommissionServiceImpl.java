package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.salesman.dao.SalesmanCommissionRecordDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionConfigService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.wx.service.WxUserService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务员佣金计算服务实现
 */
@Slf4j
@Service
public class SalesmanCommissionServiceImpl implements SalesmanCommissionService {

    @Autowired
    private SalesmanCommissionRecordDao commissionRecordDao;

    @Autowired
    private SalesmanCommissionConfigService commissionConfigService;

    @Autowired
    private WxUserSalesmanBindingService bindingService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private WxUserService wxUserService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderCommission(ActivityRechargeRecordEntity rechargeRecord) {
        try {
            log.info("开始处理订单佣金计算，订单ID: {}, 用户ID: {}, 金额: {}", 
                    rechargeRecord.getId(), rechargeRecord.getUserId(), rechargeRecord.getPayAmount());

            // 检查是否已存在佣金记录
            if (existsCommissionRecord(rechargeRecord.getId(), getOrderType(rechargeRecord))) {
                log.warn("订单 {} 的佣金记录已存在，跳过处理", rechargeRecord.getId());
                return;
            }

            // 获取用户绑定的业务员
            WxUserSalesmanBindingEntity binding = bindingService.getActiveBindingByWxUser(
                    rechargeRecord.getUserId(), rechargeRecord.getAppid());
            
            if (binding == null) {
                log.info("用户 {} 未绑定业务员，跳过佣金计算", rechargeRecord.getUserId());
                return;
            }

            // 确定佣金类型
            Integer commissionType = getCommissionTypeByOrder(rechargeRecord);
            if (commissionType == null) {
                log.warn("无法确定订单 {} 的佣金类型", rechargeRecord.getId());
                return;
            }

            // 获取佣金配置
            List<SalesmanCommissionConfigEntity> configs = commissionConfigService
                    .getEffectiveConfigsBySalesman(binding.getSalesmanId(), rechargeRecord.getAppid());
            
            SalesmanCommissionConfigEntity matchedConfig = null;
            for (SalesmanCommissionConfigEntity config : configs) {
                if (config.getCommissionType().equals(commissionType)) {
                    matchedConfig = config;
                    break;
                }
            }

            if (matchedConfig == null) {
                log.info("业务员 {} 没有匹配的佣金配置，佣金类型: {}", binding.getSalesmanId(), commissionType);
                return;
            }

            // 计算佣金金额
            BigDecimal commissionAmount = calculateCommissionAmount(
                    rechargeRecord.getPayAmount(),
                    matchedConfig.getCommissionType(),
                    matchedConfig.getCalculationType(),
                    matchedConfig.getCommissionValue(), // 作为比例使用
                    matchedConfig.getCommissionValue(), // 作为固定金额使用
                    matchedConfig.getMinAmount(),
                    matchedConfig.getMaxAmount()
            );

            if (commissionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("计算出的佣金金额为0或负数，跳过记录创建");
                return;
            }

            // 创建佣金记录
            createCommissionRecord(
                    binding.getSalesmanId(),
                    rechargeRecord.getUserId(),
                    rechargeRecord.getId(),
                    getOrderType(rechargeRecord),
                    matchedConfig.getId(),
                    matchedConfig.getCommissionType(),
                    matchedConfig.getCalculationType(),
                    rechargeRecord.getPayAmount(),
                    matchedConfig.getCommissionValue(),
                    commissionAmount,
                    commissionAmount,
                    generateDescription(rechargeRecord, matchedConfig),
                    rechargeRecord.getAppid()
            );

            log.info("订单 {} 佣金计算完成，佣金金额: {}", rechargeRecord.getId(), commissionAmount);

        } catch (Exception e) {
            log.error("处理订单佣金计算失败，订单ID: {}", rechargeRecord.getId(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processForwardCommission(Long wxUserId, Long activityId, String appid) {
            log.info("开始处理转发佣金计算，用户ID: {}, 活动ID: {}", wxUserId, activityId);

            // 获取用户绑定的业务员
            Long userId = wxUserId;
            if (userId == null) {
                // 通过activity去找对应的userId
                ActivityEntity activity = activityService.getById(activityId);
                if (activity != null) {
                    WxUser wxUser = wxUserService.getByMobileAndAppId(activity.getMobile(), appid);
                    if (wxUser != null) {
                        userId = wxUser.getId();
                    }
                }
            }

            WxUserSalesmanBindingEntity binding = bindingService.getActiveBindingByWxUser(userId, appid);
            
            if (binding == null) {
                log.info("用户 {} 未绑定业务员，跳过转发佣金计算", userId);
                return;
            }

            // 获取转发佣金配置
            List<SalesmanCommissionConfigEntity> configs = commissionConfigService
                    .getEffectiveConfigsBySalesman(binding.getSalesmanId(), appid);
            
            SalesmanCommissionConfigEntity forwardConfig = null;
            for (SalesmanCommissionConfigEntity config : configs) {
                if (config.getCommissionType().equals(3)) { // 3-转发佣金
                    forwardConfig = config;
                    break;
                }
            }

            if (forwardConfig == null) {
                log.info("业务员 {} 没有转发佣金配置", binding.getSalesmanId());
                return;
            }

            // 转发佣金通常是固定金额
            BigDecimal commissionAmount = forwardConfig.getCommissionValue() != null ?
                    forwardConfig.getCommissionValue() : BigDecimal.ZERO;

            if (commissionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("转发佣金金额为0或负数，跳过记录创建");
                return;
            }

            // 创建转发佣金记录
            createCommissionRecord(
                    binding.getSalesmanId(),
                    userId,
                    activityId, // 使用活动ID作为订单ID
                    3, // 3-转发操作
                    forwardConfig.getId(),
                    3, // 3-转发佣金
                    forwardConfig.getCalculationType(),
                    BigDecimal.ONE, // 转发操作金额设为1
                    null,
                    commissionAmount,
                    commissionAmount,
                    "用户转发操作佣金",
                    appid
            );

            log.info("转发佣金计算完成，用户ID: {}, 佣金金额: {}", userId, commissionAmount);

    }

    @Override
    public BigDecimal calculateCommissionAmount(BigDecimal orderAmount, Integer commissionType, 
                                              Integer calculationType, BigDecimal commissionRate, 
                                              BigDecimal commissionAmount, BigDecimal minAmount, 
                                              BigDecimal maxAmount) {
        if (orderAmount == null || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = BigDecimal.ZERO;

        if (calculationType == 1) {
            // 固定金额
            if (commissionAmount != null && commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                result = commissionAmount;
            }
        } else if (calculationType == 2) {
            // 按比例计算
            if (commissionRate != null && commissionRate.compareTo(BigDecimal.ZERO) > 0) {
                result = orderAmount.multiply(commissionRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
            }
        }

        // 应用最小金额限制
        if (minAmount != null && result.compareTo(minAmount) < 0) {
            result = minAmount;
        }

        // 应用最大金额限制
        if (maxAmount != null && result.compareTo(maxAmount) > 0) {
            result = maxAmount;
        }

        return result.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity createCommissionRecord(Long salesmanId, Long wxUserId,
                                                               Long orderId, Integer orderType,
                                                               Long commissionConfigId, Integer commissionType,
                                                               Integer calculationType, BigDecimal orderAmount,
                                                               BigDecimal commissionRate, BigDecimal commissionAmount,
                                                               BigDecimal actualAmount, String description, String appid) {
        SalesmanCommissionRecordEntity record = new SalesmanCommissionRecordEntity();
        record.setSalesmanId(salesmanId);
        record.setUserId(wxUserId);
        record.setBusinessId(orderId);
        record.setBusinessType(orderType != null ? orderType.toString() : null);
        record.setCommissionType(commissionType);
        record.setCalculationType(calculationType);
        record.setOrderAmount(orderAmount);
        record.setCommissionAmount(commissionAmount);
        record.setSettlementStatus(0); // 0-未结算
        record.setDescription(description);
        record.setBusinessTime(new Date());
        record.setAppid(appid);

        commissionRecordDao.insert(record);

        log.info("创建佣金记录成功，ID: {}, 业务员ID: {}, 佣金金额: {}",
                record.getId(), salesmanId, commissionAmount);

        return record;
    }

    @Override
    public boolean existsCommissionRecord(Long orderId, Integer orderType) {
        QueryWrapper<SalesmanCommissionRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("business_id", orderId)
               .eq("business_type", orderType != null ? orderType.toString() : null);
        return commissionRecordDao.selectCount(wrapper) > 0;
    }

    @Override
    public Map<String, Object> getCommissionStats(Long salesmanId, String appid) {
        // TODO: 实现统计查询
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRecords", 0);
        stats.put("pendingAmount", BigDecimal.ZERO);
        stats.put("settledAmount", BigDecimal.ZERO);
        stats.put("totalAmount", BigDecimal.ZERO);
        return stats;
    }

    @Override
    public List<SalesmanCommissionRecordEntity> getCommissionRecords(Long salesmanId, String appid, Integer page, Integer limit) {
        // TODO: 实现分页查询
        QueryWrapper<SalesmanCommissionRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("salesman_id", salesmanId)
               .eq("appid", appid)
               .orderByDesc("id");
        return commissionRecordDao.selectList(wrapper);
    }

    /**
     * 根据订单确定订单类型
     */
    private Integer getOrderType(ActivityRechargeRecordEntity rechargeRecord) {
        // 根据业务逻辑确定订单类型
        // 这里需要根据实际的订单类型字段来判断
        if (rechargeRecord.getPackageId() != null) {
            return 1; // 充值订单
        }
        return 2; // 活动订单
    }

    /**
     * 根据订单确定佣金类型
     */
    private Integer getCommissionTypeByOrder(ActivityRechargeRecordEntity rechargeRecord) {
        Integer orderType = getOrderType(rechargeRecord);
        if (orderType == 1) {
            return 1; // 充值佣金
        } else if (orderType == 2) {
            return 2; // 活动佣金
        }
        return null;
    }

    /**
     * 生成描述信息
     */
    private String generateDescription(ActivityRechargeRecordEntity rechargeRecord, SalesmanCommissionConfigEntity config) {
        StringBuilder desc = new StringBuilder();
        
        if (config.getCommissionType() == 1) {
            desc.append("充值订单佣金");
        } else if (config.getCommissionType() == 2) {
            desc.append("活动订单佣金");
        }
        
        desc.append("，订单金额：").append(rechargeRecord.getPayAmount());
        
        if (config.getCalculationType() == 1) {
            desc.append("，固定佣金：").append(config.getCommissionValue());
        } else {
            desc.append("，佣金比例：").append(config.getCommissionValue()).append("%");
        }
        
        return desc.toString();
    }
}
