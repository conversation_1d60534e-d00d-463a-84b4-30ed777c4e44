<template>
  <div class="qrcode-generator">
    <canvas ref="qrcodeCanvas" :width="size" :height="size"></canvas>
  </div>
</template>

<script>
export default {
  name: 'QrcodeGenerator',
  props: {
    value: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      default: 200
    }
  },
  watch: {
    value: {
      handler() {
        this.generateQrcode()
      },
      immediate: true
    },
    size() {
      this.generateQrcode()
    }
  },
  mounted() {
    this.generateQrcode()
  },
  methods: {
    generateQrcode() {
      if (!this.value) return
      
      const canvas = this.$refs.qrcodeCanvas
      const ctx = canvas.getContext('2d')
      
      // 清空画布
      ctx.clearRect(0, 0, this.size, this.size)
      
      // 创建图片对象
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      // 使用在线二维码生成API
      const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${this.size}x${this.size}&data=${encodeURIComponent(this.value)}`
      
      img.onload = () => {
        ctx.drawImage(img, 0, 0, this.size, this.size)
      }
      
      img.onerror = () => {
        // 如果API失败，显示文本
        ctx.fillStyle = '#666'
        ctx.font = '14px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('二维码生成中...', this.size / 2, this.size / 2)
      }
      
      img.src = qrcodeUrl
    }
  }
}
</script>

<style scoped>
.qrcode-generator {
  display: inline-block;
}

canvas {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
