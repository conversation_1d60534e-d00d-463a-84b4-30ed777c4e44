package com.cjy.pyp.modules.activity.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 充值使用记录管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("activity/activityrechargeusage")
public class ActivityRechargeUsageController extends AbstractController {
    
    @Autowired
    private ActivityRechargeUsageService activityRechargeUsageService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:rechargeusage:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = activityRechargeUsageService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:rechargeusage:info")
    public R info(@PathVariable("id") Long id) {
        ActivityRechargeUsageEntity rechargeUsage = activityRechargeUsageService.getById(id);
        return R.ok().put("rechargeUsage", rechargeUsage);
    }

    /**
     * 保存
     */
    @SysLog("保存充值使用记录")
    @RequestMapping("/save")
    @RequiresPermissions("activity:rechargeusage:save")
    public R save(@RequestBody ActivityRechargeUsageEntity rechargeUsage) {
        activityRechargeUsageService.save(rechargeUsage);
        return R.ok();
    }

    /**
     * 修改
     */
    @SysLog("修改充值使用记录")
    @RequestMapping("/update")
    @RequiresPermissions("activity:rechargeusage:update")
    public R update(@RequestBody ActivityRechargeUsageEntity rechargeUsage) {
        activityRechargeUsageService.updateById(rechargeUsage);
        return R.ok();
    }

    /**
     * 删除
     */
    @SysLog("删除充值使用记录")
    @RequestMapping("/delete")
    @RequiresPermissions("activity:rechargeusage:delete")
    public R delete(@RequestBody Long[] ids) {
        activityRechargeUsageService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 查询用户使用统计
     */
    @RequestMapping("/userStats")
    public R getUserStats(@RequestParam("userId") Long userId, @RequestParam("activityId") Long activityId) {
        Map<String, Object> stats = activityRechargeUsageService.getUserUsageStats(userId, activityId);
        return R.ok().put("stats", stats);
    }

    /**
     * 查询用户已使用次数
     */
    @RequestMapping("/usedCount")
    public R getUsedCount(@RequestParam("userId") Long userId, @RequestParam("activityId") Long activityId) {
        Integer count = activityRechargeUsageService.getUsedCountByUserAndActivity(userId, activityId);
        return R.ok().put("usedCount", count);
    }
}
