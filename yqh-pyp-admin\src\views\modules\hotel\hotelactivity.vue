<template>
  <div class="mod-config">
    <div style="text-align: center;padding: 20px;font-weight: bold;font-size: 28px">{{activityInfo.name}}的酒店列表</div>
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('hotel:hotelactivity:save')" type="primary" @click="addHandle()">新增</el-button>
        <!-- <el-button v-if="isAuth('hotel:hotelactivity:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="hotelName" header-align="center" align="center" label="酒店名称">
      </el-table-column>
      <el-table-column prop="hotelMobile" header-align="center" align="center" label="酒店联系方式">
      </el-table-column>
      <el-table-column prop="hotelAddress" header-align="center" align="center" label="酒店地址">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showRoom(scope.row.id)">查看房型</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="120" label="销售状态">
        <template slot-scope="scope">
          <el-button type="danger" size="small" v-if="scope.row.status == 1" @click="changeStatus(scope.row.id,0)">关闭售房</el-button>
          <el-button type="success" size="small" v-else @click="changeStatus(scope.row.id,1)">开启售房</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="120" label="排序">
        <template slot-scope="scope">
          <el-button type="danger" size="small" v-if="scope.$index == 0" @click="changeOrderBy(scope.row.id,1)">取消置顶</el-button>
          <el-button type="success" size="small" v-else @click="changeOrderBy(scope.row.id,0)">置顶</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <hotelactivity-add v-if="hotelactivityAddVisible" ref="hotelactivityAdd" @refreshDataList="getDataList"></hotelactivity-add>
  </div>
</template>

<script>
  import AddOrUpdate from './hotelactivity-add-or-update'
  import HotelactivityAdd from './hotelactivity-add'
  export default {
    data() {
      return {
        dataForm: {
          key: '',
          activityId: undefined
        },
        dataList: [],
        activityInfo: {},
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        hotelactivityAddVisible: false
      }
    },
    components: {
      AddOrUpdate,
      HotelactivityAdd
    },
    activated() {
      this.dataForm.activityId = this.$route.query.activityId;
      this.getDataList()
      this.getActivity()
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/hotel/hotelactivity/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'key': this.dataForm.key,
            'activityId': this.dataForm.activityId
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      getActivity() {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.activityInfo = data.activity
            }
          })
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 新增酒店
      addHandle() {
        this.hotelactivityAddVisible = true
        this.$nextTick(() => {
          this.$refs.hotelactivityAdd.init(this.dataForm.activityId)
        })
      },
      // 修改售房状态
      changeStatus(id,status) {
        this.$confirm('确定' + (status == 0 ? '关闭': '开启') + '售房', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/hotel/hotelactivity/update'),
            method: 'post',
            data: this.$http.adornData({
              'id': id,
              'status': status,
            })
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      // 置顶/取消置顶
      changeOrderBy(id,orderBy) {
        this.$confirm('确定' + (orderBy == 0 ? '置顶': '取消置顶'), '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/hotel/hotelactivity/update'),
            method: 'post',
            data: this.$http.adornData({
              'id': id,
              'orderBy': orderBy,
            })
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      // 删除
      deleteHandle(id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/hotel/hotelactivity/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      showRoom(v) {
          this.$router.push({
              name: 'hotelactivityroom',
              query: {
                  activityId: this.dataForm.activityId,
                  hotelActivityId: v
              }
          })
      }
    }
  }
</script>
