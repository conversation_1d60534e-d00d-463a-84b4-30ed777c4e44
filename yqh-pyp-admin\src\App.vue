<template>
    <div id="app">
        <transition name="fade">
            <router-view />
        </transition>
    </div>
</template>

<style>
img.image-sm {
    max-width: 80px;
    max-height: 80px;
}
.el-col .el-select,
.el-col .el-date-editor {
    width: 100%;
}
.demo-table-expand {
    font-size: 0;
}
.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}
.text-warning {
    color: #e6a23c;
}
  .tag-color{
    height:28px;
    border-radius:5px;
    font-size:14px;
    font-family:PingFangSC-Regular,PingFang SC;
    font-weight:400;
    line-height:28px;
    margin-bottom: 3px;
  }
.tag-color-mini {
  height: 20px;
  border-radius: 5px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 3px;
}
  .tag-color-1{
    background:rgba(67,202,73,0.1);
    border:1px solid rgba(67,202,73,1);
    color:rgba(67,202,73,1);
  }
  .tag-color-2{
    background:rgba(255,75,105,0.1);
    border:1px solid rgba(202,67,67,1);
    color:rgba(255,75,105,1);
  }
  .tag-color-3{
    background:rgba(255,171,0,0.1);
    border:1px solid rgba(255,171,0,1);
    color:rgba(255,171,0,1);
  }
  .tag-color-4{
    background:rgba(213,0,255,0.1);
    border:1px solid rgba(213,0,255,1);
    color:rgba(213,0,255,1);
  }
  .tag-color-5{
    background:rgba(0,175,158,0.1);
    border:1px solid rgba(0,175,158,1);
    color:rgba(0,175,158,1);
  }
  .tag-color-6{
    background:rgba(0,156,222,0.1);
    border:1px solid rgba(0,156,222,1);
    color:rgba(0,156,222,1);
  }
  .tag-color-7{
    background:rgba(255,96,0,0.1);
    border:1px solid rgba(255,96,0,1);
    color:rgba(255,96,0,1);
  }
  .tag-color-8{
    background:rgba(255,110,3,0.1);
    border:1px solid rgba(255,147,0,1);
    color:rgba(255,147,0,1);
  }
  .tag-color-9{
    background:rgba(255,0,234,0.1);
    border:1px solid rgba(255,0,234,1);
    color:rgba(255,0,234,1);
  }
  .tag-color-10{
    background:rgba(29,38,212,0.1);
    border:1px solid rgba(29,38,212,1);
    color:rgba(29,38,212,1);
  }
  .tag-color-0{
    background:rgba(9,186,208,0.1);
    border:1px solid rgba(9,186,208,1);
    color:rgba(9,186,208,1);
  }
  
.card-body-title-box {
  width: 42px;
  min-width: 42px;
  flex: 1;
  display: inline-block;
  text-align: center;
  border-right: 1px solid #E5E5E5;
  font-size: 20px;
}
.card-body-title-box:last-child{
  border: 0;
}
.card-body-title-box span {
  padding: 10px;
}


.statistical-data-box-card-date{
  overflow: hidden;
}
.statistical-data-box-card-date:hover{
  overflow-x: scroll;
}
.statistical-data-box-card-body-key{
  font-size: 0.9vmax;
  font-family: DIN Alternate;
  font-weight: bold;
  color: #333333;
}
.statistical-data-box-card-body-name{
  font-size: 0.8vmax;
  font-family: PingFang SC;
  font-weight: 400;
  color: #666666;
}
.statistical-data-box-card-title{
  padding: 17px 0px 0 22px;
}
.statistical-data-card-box {
  display: flex;
}
.statistical-data-box-card{
  display: inline-block;
  flex: 1;
  margin-left: 10px;
}
.statistical-data-box-card:first-child{
  margin-left: 0;
}
.statistical-data-box-card-body {
  display: flex;
  width: auto;
  align-items: center;
}
</style>
