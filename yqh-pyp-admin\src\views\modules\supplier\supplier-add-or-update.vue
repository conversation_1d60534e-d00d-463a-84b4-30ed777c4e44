<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="150px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="供应商名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="供应商名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商类别" prop="supplierTypeId">
            <el-select v-model="dataForm.supplierTypeId">
              <el-option v-for="item in supplierTypes" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="username">
            <el-input v-model="dataForm.username" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="mobile">
            <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'区域'" prop="area">
            <el-cascader style="width: 100%" size="large" :options="options" v-model="dataForm.area"
              @change="handleChange">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input v-model="dataForm.address" placeholder="地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="银行账户信息" prop="isBank">
        <el-switch v-model="dataForm.isBank" active-color="#13ce66" inactive-color="#6f6f6f">
        </el-switch>
      </el-form-item>
      <el-row v-if="dataForm.isBank">
        <el-col :span="12">
          <el-form-item label="银行卡号" prop="bankAccount">
            <el-input v-model="dataForm.bankAccount" placeholder="银行卡号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行" prop="bankAddress">
            <el-input v-model="dataForm.bankAddress" placeholder="开户行"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="发票信息" prop="isInvoice">
        <el-switch v-model="dataForm.isInvoice" active-color="#13ce66" inactive-color="#6f6f6f">
        </el-switch>
      </el-form-item>
      <el-form-item v-if="dataForm.isInvoice" label="统一社会编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="统一社会编码"></el-input>
      </el-form-item>
      <el-row v-if="dataForm.isInvoice">
        <el-col :span="12">
          <el-form-item label="注册地址（专票）" prop="registerAddress">
            <el-input v-model="dataForm.registerAddress" placeholder="注册地址（专票）"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话（专票）" prop="registerTelephone">
            <el-input v-model="dataForm.registerTelephone" placeholder="注册电话（专票）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dataForm.isInvoice">
        <el-col :span="12">
          <el-form-item label="注册银行（专票）" prop="registerBank">
            <el-input v-model="dataForm.registerBank" placeholder="注册银行（专票）"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册账户（专票）" prop="registerAccount">
            <el-input v-model="dataForm.registerAccount" placeholder="注册账户（专票）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { regionData } from 'element-china-area-data'
export default {
  data() {
    return {
      options: regionData,
      loading: false,
      visible: false,
      dataForm: {
        id: 0,
        name: "",
        username: "",
        code: "",
        address: "",
        email: "",
        supplierTypeId: "",
        mobile: "",
        registerAddress: "",
        registerTelephone: "",
        registerBank: "",
        registerAccount: "",
        remarks: "",
        isBank: true,
        isInvoice: false,
        bankAccount: '',
        bankAddress: '',
        repeatToken: '',
        area: '',
      },
      supplierTypes: [],
      dataRule: {
        name: [
          { required: true, message: "供应商名称不能为空", trigger: "blur" },
        ],
        // code: [
        //   {
        //     required: true,
        //     message: "统一社会信用代码不能为空",
        //     trigger: "blur",
        //   },
        // ],
        username: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        // address: [{ required: true, message: "地址不能为空", trigger: "blur" }],
        // supplierTypeId: [
        //   { required: true, message: "供应商类别不能为空", trigger: "blur" },
        // ],
        // mobile: [
        //   { required: true, message: "联系方式不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/supplier/supplier/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.supplier.name;
              this.dataForm.username = data.supplier.username;
              this.dataForm.code = data.supplier.code;
              this.dataForm.address = data.supplier.address;
              this.dataForm.email = data.supplier.email;
              this.dataForm.supplierTypeId = data.supplier.supplierTypeId;
              this.dataForm.mobile = data.supplier.mobile;
              this.dataForm.registerAddress = data.supplier.registerAddress;
              this.dataForm.registerTelephone = data.supplier.registerTelephone;
              this.dataForm.registerBank = data.supplier.registerBank;
              this.dataForm.registerAccount = data.supplier.registerAccount;
              this.dataForm.remarks = data.supplier.remarks;
              this.dataForm.bankAccount = data.supplier.bankAccount
              this.dataForm.bankAddress = data.supplier.bankAddress
              this.dataForm.area = data.supplier.area ? data.supplier.area.split(',') : []
              if (this.dataForm.bankAccount) {
                this.dataForm.isBank = true;
              }
              if (this.dataForm.code) {
                this.dataForm.isInvoice = true;
              }
            }
          });
        }
      });
      this.findSupplierType();
      this.getToken();
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    findSupplierType() {
      this.$http({
        url: this.$http.adornUrl(`/supplier/suppliertype/findAll`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.supplierTypes = data.result;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(
              `/supplier/supplier/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              username: this.dataForm.username,
              code: this.dataForm.code,
              address: this.dataForm.address,
              email: this.dataForm.email,
              supplierTypeId: this.dataForm.supplierTypeId,
              mobile: this.dataForm.mobile,
              registerAddress: this.dataForm.registerAddress,
              registerTelephone: this.dataForm.registerTelephone,
              registerBank: this.dataForm.registerBank,
              appid: this.$cookie.get("appid"),

              registerAccount: this.dataForm.registerAccount,
              remarks: this.dataForm.remarks,
              'bankAddress': this.dataForm.bankAddress,
              'bankAccount': this.dataForm.bankAccount,
              'area': this.dataForm.area ? this.dataForm.area.join(',') : '',
              'repeatToken': this.dataForm.repeatToken,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit('refreshDataList', data.result)
                },
              });
            } else {
              this.$message.error(data.msg);
            }
            this.loading = false;
          });
        }
      });
    },
  },
};
</script>
