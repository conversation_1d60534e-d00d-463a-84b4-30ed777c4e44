<template>
    <div class="list-container">
      <ul>
        <li v-for="(item, index) in items" :key="index" @click="selectItem(index)" :class="{selected: value === index}">
          <div class="item-content">
            <span class="title">{{ item.filename }}</span>
            <span class="duration">{{ item.duration | formatDuration }}</span>
            <!-- <span class="time">{{ item.createOn }}</span> -->
          </div>
        </li>
      </ul>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ListPage',
    props: {
      items: {
        type: Array,
        required: true
      },
      value: {
        type: Number,
        default: 0
      }
    },
    filters: {
        formatDuration(seconds) {
            const h = Math.floor(seconds / 3600);
            const m = Math.floor((seconds % 3600) / 60);
            const s = seconds % 60;

            const hDisplay = h > 0 ? h + '小时 ' : '';
            const mDisplay = m > 0 ? m + '分钟 ' : '';
            const sDisplay = s > 0 ? s + '秒' : '';
            return hDisplay + mDisplay + sDisplay || '0秒';
        }
    },
    methods: {
      selectItem(index) {
      if (this.value === index) return; // 避免重复选择相同项
        this.$emit('input', index);
        this.$emit('item-selected', index);
      }
    }
  };
  </script>
  
  <style scoped>
  .list-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }
  
  h1 {
    text-align: center;
    color: #333;
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  ul {
    list-style: none;
    padding: 0;
  }
  
  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s, box-shadow 0.3s;
    background-color: #fff;
  }
  
  li:hover {
    background-color: #f0f0f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  li.selected {
    background-color: #d0e7ff;
    border-color: #007bff;
  }
  
  .item-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .duration, .time {
    font-size: 14px;
    color: #666;
  }
  
  .duration {
    margin-bottom: 5px;
  }
  </style>
  