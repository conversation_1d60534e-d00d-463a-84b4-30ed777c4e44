package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务员佣金记录实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Data
@TableName("salesman_commission_record")
@Accessors(chain = true)
public class SalesmanCommissionRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 佣金类型：1-创建活动佣金，2-充值次数佣金，3-用户转发佣金
     */
    private Integer commissionType;

    /**
     * 业务类型标识
     */
    private String businessType;

    /**
     * 业务ID（充值记录ID或使用记录ID）
     */
    private Long businessId;

    /**
     * 订单金额（仅类型1和2有值）
     */
    private BigDecimal orderAmount;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 佣金金额
     */
    private BigDecimal commissionAmount;

    /**
     * 计算方式：1-固定金额，2-百分比
     */
    private Integer calculationType;

    /**
     * 结算状态：0-未结算，1-已结算，2-已取消
     */
    private Integer settlementStatus;

    /**
     * 结算时间
     */
    private Date settlementTime;

    /**
     * 结算批次号
     */
    private String settlementBatchNo;

    /**
     * 结算备注
     */
    private String settlementRemarks;

    /**
     * 业务发生时间
     */
    private Date businessTime;

    /**
     * 佣金描述
     */
    private String description;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    // 关联查询字段
    /**
     * 业务员姓名
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 业务员编号
     */
    @TableField(exist = false)
    private String salesmanCode;

    /**
     * 用户昵称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 活动名称
     */
    @TableField(exist = false)
    private String activityName;

    /**
     * 佣金类型描述
     */
    @TableField(exist = false)
    private String commissionTypeDesc;

    /**
     * 计算方式描述
     */
    @TableField(exist = false)
    private String calculationTypeDesc;

    /**
     * 结算状态描述
     */
    @TableField(exist = false)
    private String settlementStatusDesc;
}
