<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">
      <el-row>
        <!-- <el-col :span="12">
          <el-form-item label="往来方式" prop="transformType">
            <el-select v-model="dataForm.transformType" placeholder="往来方式" filterable @change="transformTypeChange">
              <el-option v-for="item in transformType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12" v-if="!dataForm.id">
          <el-form-item label="类型" prop="type">
            <el-select :disabled="dataForm.transformType == 1" v-model="dataForm.type" @change="typeChange" placeholder="收入or支出" filterable>
              <el-option v-for="item in transformPayType" :key="item.key" :label="item.value"
                :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item :label="((dataForm.type == 0 ? '付' : '收') + '款金额')" prop="price">
            <el-input v-model="dataForm.price" :placeholder="((dataForm.type == 0 ? '付' : '收') + '款金额')"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item :label="'已' + (dataForm.type == 0 ? '付' : '收') + '金额'" prop="arrivePrice">
            <el-input disabled v-model="dataForm.arrivePrice"
              :placeholder="'已' + (dataForm.type == 0 ? '付' : '收') + '金额'"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="往来单位" prop="transformUnitId">
            <div style="display: flex">
            <el-select v-model="dataForm.transformUnitId" placeholder="往来单位" filterable>
              <el-option v-for="item in contractTransformUnit" :key="item.id" :label="item.name"
                :value="item.id"></el-option>
            </el-select>
              <el-button type="text" @click="contracttransformunitaddhandle">快速新增</el-button>
            </div>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item :label="(dataForm.type == 0 ? '付' : '收') + '款账户'" prop="priceBankId">
            <el-select v-model="dataForm.priceBankId" :placeholder="(dataForm.type == 0 ? '付' : '收') + '款账户'" filterable>
              <el-option v-for="item in priceBank" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item :label="'所属会议'" prop="activityId">
            <el-select v-model="dataForm.activityId" :placeholder="'所属会议'" filterable>
              <el-option v-for="item in activity" :key="item.id" :label="item.code +'-'+item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="计划回收日期" prop="transformDate">
            <el-date-picker style="width: 100%" v-model="dataForm.transformDate" type="date"
              placeholder="请选择计划日期" value-format="yyyy/MM/dd">
            </el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="备注" prop="remarks">
            <el-input  v-model="dataForm.remarks" ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item v-if="false" label="收付款状态" prop="priceStatus">
            <el-input disabled v-model="dataForm.priceStatus" ></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
    <pricetransformunitadd v-if="pricetransformunitaddVisible" ref="pricetransformunitadd"
      @refreshDataList="findContractTransformUnit"></pricetransformunitadd>
  </div>
</template>

<script>
import pricetransformunitadd from './pricetransformunit-add-or-update'
import {
  contractPriceStatus,
  contractTypeSimple,
} from "@/data/price";
export default {
  components: {
    pricetransformunitadd
  },
  data() {
    return {
      pricetransformunitaddVisible: false,
      contractTransformUnit: [],
      priceBank: [],
      activity: [],
      contractPriceStatus,
      contractTypeSimple,
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        name: '',
        price: '',
        priceStatus: 0,
        arrivePrice: '',
        transformUnitId: '',
        type: 0,
        remarks: '',
        transformDate: '',
        priceBankId: '',
        activityId: '',
        appid: ''
      },
      dataRule: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        priceBankId: [
          { required: true, message: '付款账号不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        priceStatus: [
          { required: true, message: '付款状态不能为空', trigger: 'blur' }
        ],
        arrivePrice: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        transformUnitId: [
          { required: true, message: '往来单位ID不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '类型不能为空', trigger: 'blur' }
        ],
        transformDate: [
          { required: true, message: '预计时间不能为空', trigger: 'blur' }
        ],
        appid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricetransform/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.priceTransform.name
              this.dataForm.price = data.priceTransform.price
              this.dataForm.priceStatus = data.priceTransform.priceStatus
              this.dataForm.arrivePrice = data.priceTransform.arrivePrice
              this.dataForm.transformUnitId = data.priceTransform.transformUnitId
              this.dataForm.type = data.priceTransform.type
              this.dataForm.remarks = data.priceTransform.remarks
              this.dataForm.transformDate = data.priceTransform.transformDate
              this.dataForm.priceBankId = data.priceTransform.priceBankId
              this.dataForm.activityId = data.priceTransform.activityId
              this.dataForm.appid = data.priceTransform.appid
            }
          })
        }
      })
      this.findContractTransformUnit();
      this.findPriceBank();
      this.findActivity();
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    findContractTransformUnit(v) {
      this.$http({
        url: this.$http.adornUrl('/price/pricetransformunit/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.contractTransformUnit = data.result
          if (v) {
            this.dataForm.transformUnitId = v;
            this.getToken();
          }
        }
      })
    },
    findPriceBank() {
      this.$http({
        url: this.$http.adornUrl('/price/pricebank/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.priceBank = data.result
        }
      })
    },
    findActivity() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/findByAppid'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activity = data.result
        }
      })
    },
    contracttransformunitaddhandle() {
      this.pricetransformunitaddVisible = true
      this.$nextTick(() => {
        this.$refs.pricetransformunitadd.init()
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricetransform/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'price': this.dataForm.price,
              'priceStatus': this.dataForm.priceStatus,
              'arrivePrice': this.dataForm.arrivePrice,
              'transformUnitId': this.dataForm.transformUnitId,
              'type': this.dataForm.type,
              'remarks': this.dataForm.remarks,
              'transformDate': this.dataForm.transformDate,
              'priceBankId': this.dataForm.priceBankId,
              'activityId': this.dataForm.activityId,
              appid: this.$cookie.get("appid"),
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
