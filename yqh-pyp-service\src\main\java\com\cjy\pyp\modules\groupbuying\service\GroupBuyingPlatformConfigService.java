package com.cjy.pyp.modules.groupbuying.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingPlatformConfigEntity;

import java.util.List;

/**
 * 团购券平台配置服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
public interface GroupBuyingPlatformConfigService extends IService<GroupBuyingPlatformConfigEntity> {

    /**
     * 获取所有启用的平台配置
     *
     * @return 平台配置列表
     */
    List<GroupBuyingPlatformConfigEntity> getEnabledPlatforms();

    /**
     * 根据平台类型获取配置
     *
     * @param platformType 平台类型
     * @return 平台配置
     */
    GroupBuyingPlatformConfigEntity getByPlatformType(String platformType);

    /**
     * 生成跳转URL
     *
     * @param platformType 平台类型
     * @param couponId 团购券ID
     * @param isApp 是否为APP跳转
     * @return 跳转URL
     */
    String generateJumpUrl(String platformType, String couponId, boolean isApp);
}
