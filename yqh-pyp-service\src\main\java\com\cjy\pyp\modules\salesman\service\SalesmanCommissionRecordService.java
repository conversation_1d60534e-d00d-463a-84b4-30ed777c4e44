package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 业务员佣金记录服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public interface SalesmanCommissionRecordService extends IService<SalesmanCommissionRecordEntity> {

    /**
     * 分页查询佣金记录列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<SalesmanCommissionRecordEntity> queryPage(Map<String, Object> params);

    /**
     * 查询业务员佣金统计
     * @param params 查询参数
     * @return 统计结果
     */
    Map<String, Object> getCommissionStats(Map<String, Object> params);

    /**
     * 查询待结算的佣金记录
     * @param params 查询参数
     * @return 佣金记录列表
     */
    List<SalesmanCommissionRecordEntity> getUnsettledRecords(Map<String, Object> params);

    /**
     * 批量更新结算状态
     * @param recordIds 记录ID列表
     * @param settlementStatus 结算状态
     * @param settlementBatchNo 结算批次号
     * @param settlementRemarks 结算备注
     * @return 更新数量
     */
    int batchUpdateSettlementStatus(List<Long> recordIds, Integer settlementStatus, 
                                   String settlementBatchNo, String settlementRemarks);

    /**
     * 检查业务记录是否已生成佣金
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param appid 应用ID
     * @return 是否存在
     */
    boolean existsByBusiness(String businessType, Long businessId, String appid);

    /**
     * 查询业务员佣金汇总
     * @param params 查询参数
     * @return 汇总结果列表
     */
    List<Map<String, Object>> getCommissionSummary(Map<String, Object> params);

    /**
     * 查询佣金趋势数据
     * @param params 查询参数
     * @return 趋势数据列表
     */
    List<Map<String, Object>> getCommissionTrend(Map<String, Object> params);

    /**
     * 生成创建活动佣金记录
     * @param salesmanId 业务员ID
     * @param rechargeRecordId 充值记录ID
     * @param orderAmount 订单金额
     * @param appid 应用ID
     * @return 佣金记录
     */
    SalesmanCommissionRecordEntity generateCreateActivityCommission(Long salesmanId, Long rechargeRecordId, 
                                                                   BigDecimal orderAmount, String appid);

    /**
     * 生成充值次数佣金记录
     * @param salesmanId 业务员ID
     * @param rechargeRecordId 充值记录ID
     * @param orderAmount 订单金额
     * @param appid 应用ID
     * @return 佣金记录
     */
    SalesmanCommissionRecordEntity generateRechargeCountCommission(Long salesmanId, Long rechargeRecordId, 
                                                                  BigDecimal orderAmount, String appid);

    /**
     * 生成用户转发佣金记录
     * @param salesmanId 业务员ID
     * @param usageRecordId 使用记录ID
     * @param appid 应用ID
     * @return 佣金记录
     */
    SalesmanCommissionRecordEntity generateUserForwardCommission(Long salesmanId, Long usageRecordId, String appid);

    /**
     * 计算佣金金额
     * @param commissionType 佣金类型
     * @param calculationType 计算方式
     * @param commissionValue 佣金值
     * @param orderAmount 订单金额
     * @param minAmount 最小金额
     * @param maxAmount 最大金额
     * @return 佣金金额
     */
    BigDecimal calculateCommissionAmount(Integer commissionType, Integer calculationType, 
                                        BigDecimal commissionValue, BigDecimal orderAmount, 
                                        BigDecimal minAmount, BigDecimal maxAmount);
}
