package com.cjy.pyp.modules.groupbuying.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingCouponEntity;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingPlatformConfigEntity;
import com.cjy.pyp.modules.groupbuying.service.GroupBuyingCouponService;
import com.cjy.pyp.modules.groupbuying.service.GroupBuyingPlatformConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 团购券前端API控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("web/groupbuying")
@Api(tags = "团购券前端API")
public class WebGroupBuyingController {
    @Autowired
    private GroupBuyingCouponService groupBuyingCouponService;
    
    @Autowired
    private GroupBuyingPlatformConfigService platformConfigService;

    /**
     * 获取活动的团购券列表
     */
    @GetMapping("/coupons")
    @ApiOperation(value = "获取活动团购券列表", notes = "")
    public R getCoupons(@RequestParam("activityId") Long activityId,
                       @RequestParam(value = "platformType", required = false) String platformType) {
        List<GroupBuyingCouponEntity> coupons;
        
        if (platformType != null) {
            coupons = groupBuyingCouponService.getByActivityIdAndPlatform(activityId, platformType);
        } else {
            coupons = groupBuyingCouponService.getValidCoupons(activityId);
        }
        
        return R.ok().put("coupons", coupons);
    }

    /**
     * 获取团购券详情
     */
    @GetMapping("/coupon/{id}")
    @ApiOperation(value = "获取团购券详情", notes = "")
    public R getCouponDetail(@PathVariable("id") Long id) {
        GroupBuyingCouponEntity coupon = groupBuyingCouponService.getById(id);
        if (coupon == null || coupon.getStatus() != 1) {
            return R.error("团购券不存在或已下架");
        }
        return R.ok().put("coupon", coupon);
    }

    /**
     * 获取平台配置列表
     */
    @GetMapping("/platforms")
    @ApiOperation(value = "获取平台配置列表", notes = "")
    public R getPlatforms() {
        List<GroupBuyingPlatformConfigEntity> platforms = platformConfigService.getEnabledPlatforms();
        return R.ok().put("platforms", platforms);
    }

    /**
     * 生成跳转链接
     */
    @GetMapping("/jumpUrl")
    @ApiOperation(value = "生成跳转链接", notes = "")
    public R generateJumpUrl(@RequestParam("couponId") Long couponId,
                            @RequestParam(value = "isApp", defaultValue = "false") boolean isApp) {
        GroupBuyingCouponEntity coupon = groupBuyingCouponService.getById(couponId);
        if (coupon == null || coupon.getStatus() != 1) {
            return R.error("团购券不存在或已下架");
        }

        // 如果有直接配置的URL，优先使用
        if (coupon.getCouponUrl() != null && !coupon.getCouponUrl().isEmpty()) {
            return R.ok().put("jumpUrl", coupon.getCouponUrl());
        }

        // 否则根据平台配置生成URL
        String jumpUrl = platformConfigService.generateJumpUrl(
                coupon.getPlatformType(), 
                coupon.getCouponId(), 
                isApp
        );
        
        if (jumpUrl == null) {
            return R.error("无法生成跳转链接");
        }
        
        return R.ok().put("jumpUrl", jumpUrl);
    }

    /**
     * 记录团购券点击
     */
    @RequestMapping("/click")
    @ApiOperation(value = "记录团购券点击", notes = "")
    public R recordClick(@RequestParam("couponId") Long couponId) {
        // 这里可以记录点击统计
        // 暂时只返回成功
        return R.ok();
    }

    /**
     * 获取活动的团购券统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取团购券统计信息", notes = "")
    public R getStatistics(@RequestParam("activityId") Long activityId) {
        List<GroupBuyingCouponEntity> allCoupons = groupBuyingCouponService.getByActivityId(activityId);
        List<GroupBuyingCouponEntity> validCoupons = groupBuyingCouponService.getValidCoupons(activityId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCount", allCoupons.size());
        statistics.put("validCount", validCoupons.size());
        
        // 按平台统计
        Map<String, Integer> platformCount = new HashMap<>();
        for (GroupBuyingCouponEntity coupon : validCoupons) {
            platformCount.put(coupon.getPlatformType(), 
                    platformCount.getOrDefault(coupon.getPlatformType(), 0) + 1);
        }
        statistics.put("platformCount", platformCount);
        
        return R.ok().put("statistics", statistics);
    }
}
