<template>
  <div class="mod-channel-activity">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="活动名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="dataForm.configActivityTypeId" placeholder="活动类型" clearable>
          <el-option label="全部" value=""></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="getOverview()">统计概览</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;" v-if="showOverview">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.totalActivities || 0 }}</div>
            <div class="stats-label">总活动数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.activeActivities || 0 }}</div>
            <div class="stats-label">进行中活动</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.totalParticipants || 0 }}</div>
            <div class="stats-label">总参与人数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (overview.totalRevenue || 0).toFixed(2) }}</div>
            <div class="stats-label">总收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="name" header-align="center" align="center" label="活动名称">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="活动编号">
      </el-table-column>
      <el-table-column prop="startTime" header-align="center" align="center" width="180" label="开始时间">
      </el-table-column>
      <el-table-column prop="endTime" header-align="center" align="center" width="180" label="结束时间">
      </el-table-column>
      <el-table-column prop="participantCount" header-align="center" align="center" label="参与人数">
        <template slot-scope="scope">
          <span>{{ scope.row.participantCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="revenue" header-align="center" align="center" label="收入">
        <template slot-scope="scope">
          <span>¥{{ (scope.row.revenue || 0).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetails(scope.row)">查看详情</el-button>
          <el-button type="text" size="small" @click="viewParticipants(scope.row)">参与者</el-button>
          <el-button type="text" size="small" @click="viewStats(scope.row)">统计</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 活动详情弹窗 -->
    <el-dialog title="活动详情" :visible.sync="detailsDialogVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="活动名称">{{ selectedActivity.name }}</el-descriptions-item>
        <el-descriptions-item label="活动编号">{{ selectedActivity.code }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ selectedActivity.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ selectedActivity.endTime }}</el-descriptions-item>
        <el-descriptions-item label="活动地点" :span="2">{{ selectedActivity.address }}</el-descriptions-item>
        <el-descriptions-item label="活动描述" :span="2">{{ selectedActivity.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 参与者列表弹窗 -->
    <el-dialog title="活动参与者" :visible.sync="participantsDialogVisible" width="80%">
      <el-table :data="participantsList" border style="width: 100%;">
        <el-table-column prop="userName" label="用户名"></el-table-column>
        <el-table-column prop="mobile" label="手机号"></el-table-column>
        <el-table-column prop="joinTime" label="参与时间"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '已参与' : '已取消' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 统计信息弹窗 -->
    <el-dialog title="活动统计" :visible.sync="statsDialogVisible" width="60%">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">参与统计</div>
            <p>总参与人数: {{ activityStats.totalParticipants || 0 }}</p>
            <p>活跃参与者: {{ activityStats.activeParticipants || 0 }}</p>
            <p>完成任务数: {{ activityStats.completedTasks || 0 }}</p>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div slot="header">收入统计</div>
            <p>总收入: ¥{{ (activityStats.totalRevenue || 0).toFixed(2) }}</p>
            <p>平均收入: ¥{{ (activityStats.averageRevenue || 0).toFixed(2) }}</p>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        name: '',
        configActivityTypeId: ''
      },
      dateRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      showOverview: false,
      overview: {},
      detailsDialogVisible: false,
      participantsDialogVisible: false,
      statsDialogVisible: false,
      selectedActivity: {},
      participantsList: [],
      activityStats: {}
    }
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'name': this.dataForm.name,
        'configActivityTypeId': this.dataForm.configActivityTypeId
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.start = this.dateRange[0]
        params.end = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/channel/activity/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取统计概览
    getOverview() {
      this.$http({
        url: this.$http.adornUrl('/channel/activity/overview'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.overview = data.overview || {}
          this.showOverview = true
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 查看详情
    viewDetails(row) {
      this.selectedActivity = row
      this.detailsDialogVisible = true
    },
    // 查看参与者
    viewParticipants(row) {
      this.selectedActivity = row
      this.getParticipants(row.id)
      this.participantsDialogVisible = true
    },
    // 获取参与者列表
    getParticipants(activityId) {
      this.$http({
        url: this.$http.adornUrl(`/channel/activity/participants/${activityId}`),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.participantsList = data.page.list || []
        }
      })
    },
    // 查看统计
    viewStats(row) {
      this.selectedActivity = row
      this.getActivityStats(row.id)
      this.statsDialogVisible = true
    },
    // 获取活动统计
    getActivityStats(activityId) {
      this.$http({
        url: this.$http.adornUrl(`/channel/activity/stats/${activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityStats = data.stats || {}
        }
      })
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}

.stats-item {
  padding: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>
