package com.cjy.pyp.modules.activity.service;

import java.util.List;
import java.util.Map;

/**
 * 平台媒体配置服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
public interface PlatformMediaConfigService {
    
    /**
     * 获取所有平台配置
     * @return 平台配置列表
     */
    List<Map<String, Object>> getAllPlatformConfigs();
    
    /**
     * 获取平台支持的媒体类型
     * @param platform 平台编码
     * @return 媒体类型列表
     */
    List<Map<String, Object>> getPlatformMediaTypes(String platform);
    
    /**
     * 获取平台的默认图片数量
     * @param platform 平台编码
     * @return 默认图片数量
     */
    Integer getPlatformDefaultImageCount(String platform);
    
    /**
     * 验证平台和媒体类型组合是否有效
     * @param platform 平台编码
     * @param mediaType 媒体类型
     * @return 是否有效
     */
    boolean isValidPlatformMediaType(String platform, String mediaType);
}
