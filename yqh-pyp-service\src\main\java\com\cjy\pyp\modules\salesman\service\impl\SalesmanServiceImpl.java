package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.salesman.dao.SalesmanDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.github.pagehelper.PageHelper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 业务员服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@Service("salesmanService")
public class SalesmanServiceImpl extends ServiceImpl<SalesmanDao, SalesmanEntity> implements SalesmanService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String name = (String) params.get("name");
        String mobile = (String) params.get("mobile");
        String code = (String) params.get("code");
        String appid = (String) params.get("appid");
        String status = (String) params.get("status");

        IPage<SalesmanEntity> page = this.page(
            new Query<SalesmanEntity>().getPage(params),
            new QueryWrapper<SalesmanEntity>()
                .like(StringUtils.isNotBlank(name), "name", name)
                .like(StringUtils.isNotBlank(mobile), "mobile", mobile)
                .like(StringUtils.isNotBlank(code), "code", code)
                .eq(StringUtils.isNotBlank(appid), "appid", appid)
                .eq(StringUtils.isNotBlank(status), "status", status)
                .orderByDesc("create_on")
        );

        return new PageUtils(page);
    }

    @Override
    public List<SalesmanEntity> findByAppid(String appid) {
        return this.list(new QueryWrapper<SalesmanEntity>()
            .eq("appid", appid)
            .eq("status", 1)
            .orderByDesc("create_on"));
    }

    @Override
    public SalesmanEntity findByCode(String code, String appid) {
        return this.getOne(new QueryWrapper<SalesmanEntity>()
            .eq("code", code)
            .eq("appid", appid));
    }

    @Override
    public SalesmanEntity findByMobile(String mobile, String appid) {
        return this.getOne(new QueryWrapper<SalesmanEntity>()
            .eq("mobile", mobile)
            .eq("appid", appid));
    }

    @Override
    public boolean existsByCode(String code, String appid, Long excludeId) {
        QueryWrapper<SalesmanEntity> wrapper = new QueryWrapper<SalesmanEntity>()
            .eq("code", code)
            .eq("appid", appid);
        
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean existsByMobile(String mobile, String appid, Long excludeId) {
        QueryWrapper<SalesmanEntity> wrapper = new QueryWrapper<SalesmanEntity>()
            .eq("mobile", mobile)
            .eq("appid", appid);
        
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    @Override
    public List<SalesmanEntity> queryPageWithStats(Map<String, Object> params) {
        

        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page,limit);
        // 使用自定义查询方法，关联订单统计信息
        List<SalesmanEntity> list = this.baseMapper.selectPageWithStats(params);

        return list;
    }

    @Override
    public Integer calculateLevel(Long salesmanId) {
        if (salesmanId == null) {
            return 1;
        }

        SalesmanEntity salesman = this.getById(salesmanId);
        if (salesman == null || salesman.getParentId() == null) {
            return 1;
        }

        // 递归计算层级
        return calculateLevel(salesman.getParentId()) + 1;
    }

    @Override
    public void updateLevel(Long salesmanId) {
        if (salesmanId == null) {
            return;
        }

        Integer level = calculateLevel(salesmanId);
        SalesmanEntity salesman = new SalesmanEntity();
        salesman.setId(salesmanId);
        salesman.setLevel(level);
        this.updateById(salesman);

        // 更新所有下级的层级
        List<SalesmanEntity> children = this.list(
            new QueryWrapper<SalesmanEntity>().eq("parent_id", salesmanId)
        );
        for (SalesmanEntity child : children) {
            updateLevel(child.getId());
        }
    }

    @Override
    public List<SalesmanEntity> searchSalesmen(String keyword, Integer limit, String appid) {
        QueryWrapper<SalesmanEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("appid", appid);
        wrapper.eq("status", 1); // 只查询有效的业务员

        if (keyword != null && !keyword.trim().isEmpty()) {
            wrapper.and(w -> w.like("name", keyword)
                           .or().like("code", keyword)
                           .or().like("mobile", keyword));
        }

        wrapper.orderByDesc("create_on");
        wrapper.last("LIMIT " + (limit != null ? limit : 20));

        return this.list(wrapper);
    }

    @Override
    public Map<String, Object> getOrderStats(Map<String, Object> params) {
        // 调用 DAO 层的统计查询方法
        return this.baseMapper.selectOrderStats(params);
    }
}
