# 携程点评功能升级文档

## 升级概述

将 `goCtrip()` 方法升级为与 `goDazhongdianping()` 相同的实现模式，提供更好的用户体验和内容预览功能。

## 主要改进

### 1. 统一交互模式

**原实现**：
- 直接跳转，无内容预览
- 简单的配置解析和错误处理

**新实现**：
- 使用 `showReviewContent('ctrip', callback)` 模式
- 显示点评内容预览弹窗
- 用户确认后再执行跳转

### 2. 内容预览功能

- **文案预览**：显示AI生成的携程点评文案
- **图片展示**：展示相关的活动图片（最多3张）
- **一键复制**：支持复制点评内容到剪贴板
- **话题标签**：显示相关的话题关键词

### 3. 智能跳转逻辑

支持多种配置格式：

```javascript
// JSON配置格式
{
  "url": "https://hotels.ctrip.com/hotel/123456.html",
  "id": "123456"
}

// 直接URL格式
"https://hotels.ctrip.com/hotel/123456.html"

// 店铺ID格式（用于APP跳转）
"123456"
```

跳转优先级：
1. 如果配置了 `url`，优先使用网页跳转
2. 如果配置了 `id`，使用携程APP的URL Scheme
3. 如果是HTTP链接，直接打开网页
4. 其他情况显示错误提示

## 技术实现

### 前端修改

**文件**：`yqh-pyp-front/src/pages/cms/Index.vue`

1. **goCtrip方法重构**：
```javascript
goCtrip() {
  this.showReviewContent('ctrip', () => {
    if (!this.activityInfo.ctripConfig) {
      vant.Toast("暂未设置携程点评");
      return;
    }
    // 智能跳转逻辑
    try {
      const config = JSON.parse(this.activityInfo.ctripConfig);
      if (config.url) {
        window.open(config.url, "_blank");
      } else if (config.id) {
        window.open('ctrip://wireless/hotel/' + config.id, "_self");
      } else {
        vant.Toast("携程配置格式错误");
      }
    } catch (e) {
      // 非JSON格式处理
      if (this.activityInfo.ctripConfig.startsWith('http')) {
        window.open(this.activityInfo.ctripConfig, "_blank");
      } else {
        vant.Toast("携程配置解析失败");
      }
    }
  });
}
```

2. **showReviewContent方法扩展**：
```javascript
// 添加携程平台支持
case 'ctrip':
  apiUrl = '/pyp/web/activity/review/ctrip';
  break;
```

### 后端修改

**文件**：`yqh-pyp-service/src/main/java/com/cjy/pyp/modules/activity/web/WebActivityReviewController.java`

1. **新增携程点评API**：
```java
@GetMapping("/ctrip")
public R getCtripReview(@RequestParam("activityId") Long activityId) {
    try {
        return getReviewContent(activityId, "ctrip", "携程点评");
    } catch (Exception e) {
        return R.error("获取携程点评内容失败: " + e.getMessage());
    }
}
```

### 数据库配置

**文件**：`V1.0.6__add_my_shop_to_activity.sql`

添加携程点评的广告类型配置：
```sql
INSERT INTO `ad_type_config` (...) VALUES
('ctrip', '携程点评', '携程', '旅游点评', '25字以内，突出体验', '100-200字，详细评价', 4, '不带#号，用逗号分隔', '...', '专业、详细、实用、有参考价值', '...', 9, 1);
```

## 用户体验提升

### 升级前
1. 点击携程点评按钮
2. 直接跳转（可能失败）
3. 无内容预览

### 升级后
1. 点击携程点评按钮
2. 显示点评内容预览弹窗
3. 查看AI生成的文案和图片
4. 可选择复制内容
5. 确认后跳转到携程

## 配置说明

### 管理员配置

在活动管理后台的"携程配置"字段中，支持以下格式：

1. **完整JSON配置**（推荐）：
```json
{
  "url": "https://hotels.ctrip.com/hotel/123456.html",
  "id": "123456"
}
```

2. **简单URL配置**：
```
https://hotels.ctrip.com/hotel/123456.html
```

3. **店铺ID配置**：
```
123456
```

### 跳转行为

- **网页跳转**：在新窗口打开携程网页
- **APP跳转**：使用 `ctrip://wireless/hotel/ID` 格式调用携程APP
- **错误处理**：配置错误时显示友好提示

## 兼容性说明

- **向后兼容**：现有的配置格式仍然支持
- **渐进增强**：新功能不影响现有用户体验
- **错误降级**：配置解析失败时有合理的降级处理

## 测试建议

1. **配置测试**：测试各种配置格式的解析和跳转
2. **内容生成**：验证携程点评文案的生成质量
3. **跳转测试**：测试网页和APP跳转的成功率
4. **错误处理**：测试各种异常情况的处理

## 后续优化

1. **统计功能**：记录携程点评的使用情况
2. **模板优化**：根据使用反馈优化文案生成模板
3. **配置验证**：添加配置格式的前端验证
4. **批量操作**：支持批量配置多个活动的携程信息
