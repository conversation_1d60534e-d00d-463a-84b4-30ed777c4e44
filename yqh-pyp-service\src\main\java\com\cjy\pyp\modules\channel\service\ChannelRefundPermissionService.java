package com.cjy.pyp.modules.channel.service;

import com.cjy.pyp.common.utils.R;

import java.util.Map;

/**
 * 渠道退款权限服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
public interface ChannelRefundPermissionService {

    /**
     * 检查订单是否具有退款权限
     * @param orderId 订单ID
     * @return 是否具有退款权限
     */
    boolean hasRefundPermission(Long orderId);

    /**
     * 检查订单退款权限并返回详细信息
     * @param orderId 订单ID
     * @return 权限检查结果，包含是否有权限、拒绝原因等信息
     */
    R checkRefundPermissionWithDetails(Long orderId);

    /**
     * 为订单分配退款权限
     * @param orderId 订单ID
     * @return 分配结果
     */
    R assignRefundPermission(Long orderId);

    /**
     * 释放订单的退款权限（退款完成后调用）
     * @param orderId 订单ID
     * @return 释放结果
     */
    R releaseRefundPermission(Long orderId);

    /**
     * 获取渠道退款名额使用情况
     * @param channelId 渠道ID
     * @return 名额使用情况统计
     */
    Map<String, Object> getChannelRefundQuotaUsage(Long channelId);

    /**
     * 批量更新渠道内订单的退款权限
     * @param channelId 渠道ID
     * @return 更新结果
     */
    R batchUpdateRefundPermissions(Long channelId);

    /**
     * 根据订单获取关联的渠道ID
     * @param orderId 订单ID
     * @return 渠道ID，如果没有关联则返回null
     */
    Long getChannelIdByOrderId(Long orderId);

    /**
     * 获取订单在渠道内的退款权限排序
     * @param orderId 订单ID
     * @return 排序位置，如果没有权限则返回null
     */
    Integer getOrderRefundQuotaSequence(Long orderId);

    /**
     * 检查渠道是否启用退款名额控制
     * @param channelId 渠道ID
     * @return 是否启用
     */
    boolean isRefundQuotaEnabled(Long channelId);

    /**
     * 更新渠道退款名额设置
     * @param channelId 渠道ID
     * @param refundQuota 新的退款名额
     * @param enabled 是否启用退款名额控制
     * @return 更新结果
     */
    R updateChannelRefundQuota(Long channelId, Integer refundQuota, Boolean enabled);

    /**
     * 获取订单退款权限被拒绝的原因
     * @param orderId 订单ID
     * @return 拒绝原因描述
     */
    String getRefundPermissionDeniedReason(Long orderId);
}
