package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.AiModelConfigDao;
import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;
import com.cjy.pyp.modules.activity.service.AiModelConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * AI模型配置服务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@Slf4j
@Service("aiModelConfigService")
public class AiModelConfigServiceImpl extends ServiceImpl<AiModelConfigDao, AiModelConfigEntity> implements AiModelConfigService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String modelName = (String) params.get("modelName");
        String provider = (String) params.get("provider");
        String status = (String) params.get("status");

        IPage<AiModelConfigEntity> page = this.page(
                new Query<AiModelConfigEntity>().getPage(params),
                new QueryWrapper<AiModelConfigEntity>()
                        .like(StringUtils.isNotBlank(modelName), "model_name", modelName)
                        .eq(StringUtils.isNotBlank(provider), "provider", provider)
                        .eq(StringUtils.isNotBlank(status), "status", status)
                        .orderByAsc("sort_order")
                        .orderByAsc("id")
        );

        return new PageUtils(page);
    }

    @Override
    public AiModelConfigEntity getByModelCode(String modelCode) {
        return baseMapper.selectByModelCode(modelCode);
    }

    @Override
    public List<AiModelConfigEntity> getEnabledModels() {
        return baseMapper.selectEnabledModels();
    }

    @Override
    public List<AiModelConfigEntity> getByProvider(String provider) {
        return baseMapper.selectByProvider(provider);
    }

    @Override
    public AiModelConfigEntity getDefaultModel() {
        return baseMapper.selectDefaultModel();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultModel(String modelCode) {
        // 先清除所有默认标记，再设置新的默认模型
        baseMapper.updateDefaultModel(modelCode);
        log.info("设置默认AI模型: {}", modelCode);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        AiModelConfigEntity entity = new AiModelConfigEntity();
        entity.setId(id);
        entity.setStatus(status);
        this.updateById(entity);
        log.info("更新AI模型状态: id={}, status={}", id, status);
    }

    @Override
    public boolean validateConfig(AiModelConfigEntity config) {
        if (config == null) {
            return false;
        }
        
        // 验证必填字段
        if (StringUtils.isBlank(config.getModelCode()) ||
            StringUtils.isBlank(config.getModelName()) ||
            StringUtils.isBlank(config.getProvider()) ||
            StringUtils.isBlank(config.getApiUrl())) {
            return false;
        }
        
        // 验证数值范围
        if (config.getMaxTokens() != null && config.getMaxTokens() <= 0) {
            return false;
        }
        
        if (config.getTimeout() != null && config.getTimeout() <= 0) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean testConnection(String modelCode) {
        try {
            AiModelConfigEntity config = getByModelCode(modelCode);
            if (config == null || config.getStatus() != 1) {
                return false;
            }
            
            // TODO: 实现具体的连接测试逻辑
            // 这里可以发送一个简单的测试请求到对应的API
            log.info("测试AI模型连接: {}", modelCode);
            return true;
            
        } catch (Exception e) {
            log.error("测试AI模型连接失败: {}", modelCode, e);
            return false;
        }
    }
}
