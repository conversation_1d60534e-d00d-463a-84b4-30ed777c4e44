<template>
  <div>
    <!-- <div style="padding-bottom: 70px;" class="content" v-html="merchantInfo.paramValue" @click="showImg($event)"></div> -->
    <img :src="merchantInfo.paramValue" alt="" style="padding-bottom: 70px;" class="content"  @click="showImg($event)">

    <div style="position: fixed;width: 100%;bottom: 0;height: 70px;line-height: 70px;background-color: white;">
      <van-button color="#DD5C5F" @click="turnUrlTo" style="width: 94%;margin-top: 10px;margin-left: 3%;" round block
        type="info" >申请试用/商务合作</van-button>
    </div>
  </div>
</template>

<script>

export default {
  components: {
  },
  data() {
    return {
      inviteUserId: '',
      openid: undefined,
      userInfo: {},
      merchantInfo: {},
      turnUrl: {},
    };
  },
  mounted() {
    document.title = "易企化-AI数字营销";
    this.inviteUserId = this.$route.query.userId;
    this.getCmsInfo();
  },
  methods: {
    bindInviteUser() {
      // 先做无感邀请
      this.$fly.get("/finance/wxUser/bindInviteUser", {
        inviteUserId: this.inviteUserId
      })
        .then((res) => {
        });
    },
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
          this.$wxShare(
            "易企化-AI数字营销",
            this.$cookie.get("logo"),
            "帮助商家快速增量获客\n商务合作：13860473168",
            (window.location.href + '?userId=' + this.userInfo.id)
          ); //加载微信分享
        } else {
          vant.Toast(res.msg);
        }
      });
    },
    getCmsInfo() {
      this.$fly.get(`/pyp/sys/config/findOnlyParamKey`, {
        paramKey: 'yingxiao'
      }).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          this.getUserInfo();
          this.getConfig();
          // if (this.inviteUserId) {
          //   this.bindInviteUser();
          // }
        } else {
          this.merchantInfo = {};
        }
      });
    },
    getConfig() {
      this.$fly.get(`/pyp/sys/config/findOnlyParamKey`, {
        paramKey: 'turnUrl'
      }).then((res) => {
        if (res.code == 200) {
          this.turnUrl = res.result;
        } else {
          this.turnUrl = {};
        }
      });
    },
    turnUrlTo() {
      if (this.turnUrl.paramValue) {
        window.location.href = this.turnUrl.paramValue;
      } else {
        vant.Toast("暂未配置跳转链接");
      }
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
img {
    width: 100%;
    height: auto;
  }

.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.prism-player {
  background-color: white;
}

/deep/ .prism-info-display {
  padding: 0 !important;
}
</style>