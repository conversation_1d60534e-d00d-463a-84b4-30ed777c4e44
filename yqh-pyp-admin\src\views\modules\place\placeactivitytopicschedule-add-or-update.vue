<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    
    <el-form-item label="日程名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="日程名称"></el-input>
    </el-form-item>
      <el-form-item label="会议日期" prop="times">
        <el-date-picker :picker-options="pickerOptions" v-model="dataForm.times" style="windth: 100%" @change="dateChange" :default-value="activityInfo.startTime" type="datetimerange" value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="会议主题" prop="placeActivityTopicId">
        <el-select v-model="dataForm.placeActivityTopicId" placeholder="会议主题" filterable>
          <el-option
            v-for="item in placeTopicList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="讲者" prop="scheduleGuestIds">
        <el-select v-model="dataForm.scheduleGuestIds" multiple placeholder="讲者" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主持" prop="scheduleSpeakerIds">
        <el-select v-model="dataForm.scheduleSpeakerIds" multiple placeholder="主持" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="讨论" prop="scheduleDiscussIds">
        <el-select v-model="dataForm.scheduleDiscussIds" multiple placeholder="讨论" filterable>
          <el-option
            v-for="item in guestList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    <el-form-item label="讲者别名" prop="aliasGuestName">
      <el-input v-model="dataForm.aliasGuestName" placeholder="讲者别名"></el-input>
    </el-form-item>
    <el-form-item label="主持别名" prop="aliasSpeakerName">
      <el-input v-model="dataForm.aliasSpeakerName" placeholder="主持别名"></el-input>
    </el-form-item>
    <el-form-item label="讨论别名" prop="aliasDiscussName">
      <el-input v-model="dataForm.aliasDiscussName" placeholder="讨论别名"></el-input>
    </el-form-item>
    <!-- <el-form-item label="排序，数值越小越靠前" prop="orderBy">
      <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
      placeTopicList: [],
      guestList: [],
      activityInfo: {},
        dataForm: {
          id: 0,
          activityId: '',
          orderBy: 0,
          placeActivityTopicId: '',
          name: '',
          startTime: '',
          endTime: '',
          aliasGuestName: '',
          aliasSpeakerName: '',
          aliasDiscussName: '',
          scheduleGuestIds: [],
          scheduleSpeakerIds: [],
          scheduleDiscussIds: [],
          times: [],
          videoUrl: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          times: [{
            required: true,
            message: '会议时间不能为空',
            trigger: 'blur'
          }],
          orderBy: [
            { required: true, message: '排序，数值越小越靠前不能为空', trigger: 'blur' }
          ],
          placeActivityTopicId: [
            { required: true, message: '主题ID不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '日程名称不能为空', trigger: 'blur' }
          ],
          startTime: [
            { required: true, message: '开始时间不能为空', trigger: 'blur' }
          ],
          endTime: [
            { required: true, message: '结束时间不能为空', trigger: 'blur' }
          ],
        },
        pickerOptions:{
           disabledDate: (time) => {
              // 如果函数里处理的数据比较麻烦,也可以单独放在一个函数里,避免data数据太臃肿
              return this.dealDisabledDate(time);
            }
        },
      }
    },
    methods: {
      init (activityId,placeActivityTopicId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.placeActivityTopicId = placeActivityTopicId || undefined;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicschedule/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$set(this.dataForm, "times", [data.placeActivityTopicSchedule.startTime, data.placeActivityTopicSchedule.endTime])
                this.dataForm.activityId = data.placeActivityTopicSchedule.activityId
                this.dataForm.placeId = data.placeActivityTopicSchedule.placeId
                this.dataForm.orderBy = data.placeActivityTopicSchedule.orderBy
                this.dataForm.placeActivityTopicId = data.placeActivityTopicSchedule.placeActivityTopicId
                this.dataForm.name = data.placeActivityTopicSchedule.name
                this.dataForm.scheduleGuestIds = data.placeActivityTopicSchedule.scheduleGuestIds
                this.dataForm.scheduleSpeakerIds = data.placeActivityTopicSchedule.scheduleSpeakerIds
                this.dataForm.scheduleDiscussIds = data.placeActivityTopicSchedule.scheduleDiscussIds
                this.dataForm.startTime = data.placeActivityTopicSchedule.startTime
                this.dataForm.endTime = data.placeActivityTopicSchedule.endTime
                this.dataForm.videoUrl = data.placeActivityTopicSchedule.videoUrl
                this.dataForm.aliasGuestName = data.placeActivityTopicSchedule.aliasGuestName
                this.dataForm.aliasSpeakerName = data.placeActivityTopicSchedule.aliasSpeakerName
                this.dataForm.aliasDiscussName = data.placeActivityTopicSchedule.aliasDiscussName
              }
            })
          }
        })
        this.getPlaceTopic();
        this.getGuest();
        this.getActivity();
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicschedule/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'placeId': this.dataForm.placeId,
                'orderBy': this.dataForm.orderBy,
                'placeActivityTopicId': this.dataForm.placeActivityTopicId,
                'name': this.dataForm.name,
                'startTime': this.dataForm.startTime,
                'endTime': this.dataForm.endTime,
                'scheduleGuestIds': this.dataForm.scheduleGuestIds,
                'scheduleSpeakerIds': this.dataForm.scheduleSpeakerIds,
                'scheduleDiscussIds': this.dataForm.scheduleDiscussIds,
                'aliasGuestName': this.dataForm.aliasGuestName,
                'aliasSpeakerName': this.dataForm.aliasSpeakerName,
                'aliasDiscussName': this.dataForm.aliasDiscussName,
                'videoUrl': this.dataForm.videoUrl
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
    getPlaceTopic() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivitytopic/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeTopicList = data.result;
        }
      });
    },
    getGuest() {
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityguest/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.guestList = data.result;
        }
      });
    },
      getActivity() {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.activityInfo = data.activity
            }
          })
      },
      dealDisabledDate (time) {
        // time 是一个new Date数据
        if(this.activityInfo.endTime != null && this.activityInfo.startTime != null){
          return (time.getTime() + 24*60*60*1000) < (new Date(this.activityInfo.startTime).getTime()) || time.getTime() >= (new Date(this.activityInfo.endTime).getTime()) ;//时间范围必须是时间戳
        }
      },
      dateChange(v) {
        this.dataForm.startTime = v[0];
        this.dataForm.endTime = v[1];
        console.log(v)
      }
    }
  }
</script>
