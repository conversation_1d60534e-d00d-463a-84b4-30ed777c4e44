package com.cjy.pyp.modules.channel.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.wx.entity.WxUser;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 渠道数据访问层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@Mapper
public interface ChannelDao extends BaseMapper<ChannelEntity> {

    /**
     * 分页查询渠道列表（包含统计信息）
     * @param params 查询参数
     * @return 分页结果
     */
    List<ChannelEntity> selectPageWithStats(Map<String, Object> params);

    /**
     * 根据渠道ID查询统计信息
     * @param channelId 渠道ID
     * @param appid 应用ID
     * @return 统计信息
     */
    Map<String, Object> selectStatsByChannelId(@Param("channelId") Long channelId, @Param("appid") String appid);
    Map<String, Object> selectStatsByChannelId(@Param("channelIds")  List<Long> channelId, @Param("appid") String appid);

    /**
     * 查询渠道的直接子渠道ID
     * @param channelId 渠道ID
     * @return 直接子渠道ID列表
     */
    List<Long> selectAllChildChannelIds(@Param("channelId") Long channelId);

    /**
     * 根据应用ID查询渠道总体统计
     * @param appid 应用ID
     * @return 统计信息
     */
    Map<String, Object> selectOverallStatsByAppid(@Param("appid") String appid);

    /**
     * 检查订单是否属于指定的业务员列表
     * @param orderId 订单ID
     * @param salesmanIds 业务员ID列表
     * @return 是否属于
     */
    boolean checkOrderBelongsToSalesmen(@Param("orderId") Long orderId, @Param("salesmanIds") List<Long> salesmanIds);

    /**
     * 检查客户是否绑定了指定的业务员列表
     * @param wxUserId 微信用户ID
     * @param salesmanIds 业务员ID列表
     * @return 是否绑定
     */
    boolean checkCustomerBelongsToSalesmen(@Param("wxUserId") Long wxUserId, @Param("salesmanIds") List<Long> salesmanIds);

    /**
     * 分页查询渠道客户列表
     * @param params 查询参数
     * @return 客户列表
     */
    List<WxUser> selectCustomerPage(Map<String, Object> params);

    /**
     * 查询渠道客户统计
     * @param params 查询参数
     * @return 统计数据
     */
    Map<String, Object> selectCustomerStats(Map<String, Object> params);
}
