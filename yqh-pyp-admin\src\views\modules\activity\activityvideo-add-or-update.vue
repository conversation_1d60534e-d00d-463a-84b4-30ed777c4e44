<template>
  <el-dialog
    :title="!dataForm.id ? '新增活动视频' : '修改活动视频'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">

      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="视频名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="请输入视频名称"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="排序" prop="paixu">
            <el-input-number
              v-model="dataForm.paixu"
              :min="0"
              :max="9999"
              placeholder="排序值"
              style="width: 100%">
            </el-input-number>
          </el-form-item>
        </el-col> -->
      </el-row>

      <!-- 视频文件 -->
      <el-form-item label="视频文件" prop="mediaUrl">
        <div class="video-upload-section">
          <el-button type="primary" @click="openVideoModal" :disabled="uploading">
            <i class="el-icon-video-camera"></i> {{ selectedVideo ? '重新选择视频' : '选择视频' }}
          </el-button>

          <div v-if="selectedVideo" class="selected-video-preview">
            <div class="video-item">
              <video :src="selectedVideo.url" width="200" height="150" controls preload="metadata"></video>
              <div class="video-info">
                <p class="video-name">{{ dataForm.name || '未命名视频' }}</p>
                <p class="video-url">{{ selectedVideo.url }}</p>
                <p class="video-size" v-if="dataForm.fileSize">文件大小: {{ formatFileSize(dataForm.fileSize) }}</p>
                <p class="video-duration" v-if="dataForm.duration">时长: {{ formatDuration(dataForm.duration) }}</p>
              </div>
              <div class="video-actions">
                <el-button size="mini" @click="previewVideo(selectedVideo.url)">
                  <i class="el-icon-zoom-in"></i> 预览
                </el-button>
                <el-button size="mini" type="danger" @click="removeVideo">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </div>
            </div>
          </div>

          <div v-if="!selectedVideo && !dataForm.mediaUrl" class="upload-tip">
            <i class="el-icon-video-camera-solid"></i>
            <p>请选择视频文件</p>
          </div>
        </div>
      </el-form-item>

      <!-- 视频信息 -->
      <!-- <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="文件ID" prop="fileId">
            <el-input v-model="dataForm.fileId" placeholder="视频文件ID" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="帧率" prop="frameRate">
            <el-input-number
              v-model="dataForm.frameRate"
              :min="1"
              :max="120"
              placeholder="帧率"
              style="width: 100%">
              <template slot="append">fps</template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过期时间" prop="expireTime">
            <el-date-picker
              v-model="dataForm.expireTime"
              type="datetime"
              placeholder="请选择过期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row> -->

      <!-- 类型选择 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="视频类型" prop="type">
            <el-radio-group v-model="dataForm.type">
              <el-radio :label="0">
                <span class="radio-label">
                  <i class="el-icon-files"></i> 素材
                </span>
              </el-radio>
              <el-radio :label="1">
                <span class="radio-label">
                  <i class="el-icon-video-camera"></i> 成品
                </span>
              </el-radio>
            </el-radio-group>
            <div class="type-description">
              <p v-if="dataForm.type === 0" class="type-desc material">素材：原始视频文件，可用于后期编辑制作</p>
              <p v-if="dataForm.type === 1" class="type-desc product">成品：最终制作完成的视频，可直接使用</p>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dataForm.id">
          <el-form-item label="关联文案" prop="activityTextId">
            <el-select
              v-model="dataForm.activityTextId"
              placeholder="选择关联文案"
              clearable
              filterable
              style="width: 100%">
              <el-option
                v-for="text in activityTexts"
                :key="text.id"
                :label="text.title || text.content"
                :value="text.id">
                <span style="float: left">{{ text.title || text.content }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ text.id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dataForm.id">
          <el-form-item label="使用次数">
            <el-input v-model="dataForm.useCount" readonly>
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="submitting">
        {{ submitting ? '保存中...' : '确定' }}
      </el-button>
    </span>

    <!-- 视频上传弹窗 -->
    <VideoUploadModal
      :visible.sync="videoModalVisible"
      :default-video="selectedVideo"
      @confirm="handleVideoConfirm"
    />

    <!-- 视频预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      width="70%"
      append-to-body
      :z-index="3100"
    >
      <video width="100%" :src="previewVideoUrl" controls autoplay>
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  components: {
    VideoUploadModal: () => import("@/components/video-upload-modal")
  },
  data() {
    return {
      visible: false,
      submitting: false,
      uploading: false,
      videoModalVisible: false,
      previewVisible: false,
      previewVideoUrl: '',
      selectedVideo: null,
      activityTexts: [],
      dataForm: {
        repeatToken: '',
        id: 0,
        fileId: '',
        name: '',
        fileSize: 0,
        duration: 0,
        mediaUrl: '',
        expireTime: '',
        frameRate: 25,
        activityId: '',
        paixu: 0,
        type: 0,
        useCount: 0,
        activityTextId: ''
      },
      dataRule: {
        name: [
          { required: true, message: '视频名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '视频名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        mediaUrl: [
          { required: true, message: '请选择视频文件', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择视频类型', trigger: 'change' }
        ],
        paixu: [
          { type: 'number', message: '排序必须为数字值', trigger: 'blur' }
        ],
        frameRate: [
          { type: 'number', message: '帧率必须为数字值', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id, activityId, defaultType = 0) {
      this.getToken()
      this.loadActivityTexts(activityId)
      this.dataForm.id = id || 0
      this.dataForm.activityId = activityId
      this.visible = true
      this.selectedVideo = null

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.loadActivityVideoInfo()
        } else {
          // 新增时的默认值
          this.dataForm.type = defaultType // 使用传入的默认类型
          this.dataForm.paixu = 0
          this.dataForm.frameRate = 25
          this.dataForm.useCount = 0
        }
      })
    },

    loadActivityVideoInfo() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activityvideo/info/${this.dataForm.id}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          const info = data.activityVideo
          this.dataForm.fileId = info.fileId || ''
          this.dataForm.name = info.name
          this.dataForm.fileSize = info.fileSize || 0
          this.dataForm.duration = info.duration || 0
          this.dataForm.mediaUrl = info.mediaUrl
          this.dataForm.expireTime = info.expireTime || ''
          this.dataForm.frameRate = info.frameRate || 25
          this.dataForm.activityId = info.activityId
          this.dataForm.paixu = info.paixu || 0
          this.dataForm.type = info.type || 0
          this.dataForm.useCount = info.useCount || 0
          this.dataForm.activityTextId = info.activityTextId || ''

          // 初始化已选择的视频
          if (info.mediaUrl) {
            this.selectedVideo = {
              id: `existing_${this.dataForm.id}`,
              url: info.mediaUrl,
              name: info.name,
              fileSize: info.fileSize,
              duration: info.duration,
              fileId: info.fileId,
              frameRate: info.frameRate,
              createDate: new Date().toISOString()
            }
          }
        }
      }).catch(err => {
        this.$message.error('加载视频信息失败')
        console.error(err)
      })
    },

    loadActivityTexts(activityId) {
      // 加载活动相关的文案列表
      this.$http({
        url: this.$http.adornUrl('/activity/activitytext/list'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: activityId,
          page: 1,
          limit: 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityTexts = data.page?.list || []
        }
      }).catch(err => {
        console.error('加载文案列表失败:', err)
        this.activityTexts = []
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result
        }
      }).catch(err => {
        console.error('获取token失败:', err)
      })
    },

    // 视频上传相关方法
    openVideoModal() {
      this.videoModalVisible = true
    },

    handleVideoConfirm(video) {
      if (video) {
        this.selectedVideo = video
        this.dataForm.mediaUrl = video.url
        this.dataForm.fileId = video.fileId || ''
        this.dataForm.fileSize = video.fileSize || 0
        this.dataForm.duration = video.duration || 0
        this.dataForm.frameRate = video.frameRate || 25

        // 如果视频名称为空，使用文件名
        if (!this.dataForm.name && video.name) {
          this.dataForm.name = video.name.split('.')[0]
        }
      }
    },

    removeVideo() {
      this.selectedVideo = null
      this.dataForm.mediaUrl = ''
      this.dataForm.fileId = ''
      this.dataForm.fileSize = 0
      this.dataForm.duration = 0
    },

    previewVideo(url) {
      this.previewVideoUrl = url
      this.previewVisible = true
    },

    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },

    formatDuration(seconds) {
      if (!seconds || seconds === 0) return '0秒'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else if (minutes > 0) {
        return `${minutes}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${secs}秒`
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.mediaUrl) {
            this.$message.error('请选择视频文件')
            return
          }

          this.submitting = true

          const submitData = {
            'repeatToken': this.dataForm.repeatToken,
            'id': this.dataForm.id || undefined,
            'fileId': this.dataForm.fileId || '',
            'name': this.dataForm.name,
            'fileSize': this.dataForm.fileSize || 0,
            'duration': this.dataForm.duration || 0,
            'mediaUrl': this.dataForm.mediaUrl,
            'expireTime': this.dataForm.expireTime || '',
            'frameRate': this.dataForm.frameRate || 25,
            'activityId': this.dataForm.activityId,
            'paixu': this.dataForm.paixu || 0,
            'type': this.dataForm.type,
            'useCount': this.dataForm.useCount || 0,
            'activityTextId': this.dataForm.activityTextId || ''
          }

          this.$http({
            url: this.$http.adornUrl(`/activity/activityvideo/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(submitData)
          }).then(({ data }) => {
            this.submitting = false
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg || '操作失败')
              if (data.msg !== '不能重复提交') {
                this.getToken()
              }
            }
          }).catch(err => {
            this.submitting = false
            this.$message.error('提交失败，请重试')
            console.error('提交失败:', err)
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.video-upload-section {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.video-upload-section:hover {
  border-color: #409EFF;
}

.selected-video-preview {
  margin-top: 20px;
}

.video-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
  text-align: left;
}

.video-info {
  flex: 1;
}

.video-name {
  font-weight: 500;
  color: #303133;
  margin: 0 0 5px 0;
}

.video-url {
  color: #909399;
  font-size: 12px;
  margin: 0 0 5px 0;
  word-break: break-all;
}

.video-size,
.video-duration {
  color: #67C23A;
  font-size: 12px;
  margin: 0;
}

.video-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-tip {
  color: #909399;
  padding: 40px 0;
}

.upload-tip i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.upload-tip p {
  margin: 0;
  font-size: 14px;
}

.radio-label {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.radio-label i {
  font-size: 16px;
}

.type-description {
  margin-top: 8px;
}

.type-desc {
  margin: 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.type-desc.material {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #d6f7ff;
}

.type-desc.product {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #d9f7be;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-item {
    flex-direction: column;
    text-align: center;
  }

  .video-actions {
    flex-direction: row;
    justify-content: center;
  }
}
</style>
