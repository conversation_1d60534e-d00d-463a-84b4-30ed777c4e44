<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <!-- <el-form-item>
        <el-input v-model="dataForm.name" placeholder="参数名" clearable></el-input>
      </el-form-item> -->
    <el-form-item >
      <el-select v-model="dataForm.transformUnitId" filterable>
          <el-option label="全部(往来单位)" value=""></el-option>
        <el-option v-for="item in contractTransformUnit" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item >
      <el-select v-model="dataForm.priceBankId" filterable>
          <el-option label="全部(银行账户)" value=""></el-option>
        <el-option v-for="item in priceBank" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.priceStatus" placeholder="收/付款状态" filterable>
          <el-option label="全部(收/付款状态)" value=""></el-option>
          <el-option v-for="item in contractPriceStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('price:pricetransform:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('price:pricetransform:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <!-- <el-table-column prop="name" header-align="center" align="center" label="名称">
      </el-table-column> -->
      <el-table-column prop="transformUnitName" header-align="center" align="center" label="往来单位">
      </el-table-column>
      <el-table-column prop="activityCode" header-align="center" align="center" label="会议编号">
      </el-table-column>
      <el-table-column prop="activityName" width="150"  :show-overflow-tooltip="true" header-align="center" align="center" label="会议名称">
      </el-table-column>
      <el-table-column prop="priceBankName" header-align="center" align="center" label="银行账户">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="往来金额">
      </el-table-column>
      <el-table-column prop="buyPriceStatus" header-align="center" align="center" label="付款状态">
        <div slot-scope="scope">
          <el-tag  
            type="primary"
            :class="'tag-color-mini tag-color-' + scope.row.buyPriceStatus"
            >{{ scope.row.buyPriceStatus == null ? '空' : ( contractPriceStatus[scope.row.buyPriceStatus].value1) }}</el-tag
          >
        </div>
      </el-table-column>
      <el-table-column prop="buyArrivePrice" header-align="center" align="center" label="已付金额">
      </el-table-column>
      <el-table-column prop="priceStatus" header-align="center" align="center" label="收款状态">
        <div slot-scope="scope">
          <el-tag  
            type="primary"
            :class="'tag-color-mini tag-color-' + scope.row.priceStatus"
            >{{ scope.row.priceStatus == null ? '空' : (scope.row.type == 0 ? 
            contractPriceStatus[scope.row.priceStatus].value : contractPriceStatus[scope.row.priceStatus].value1) }}</el-tag
          >
        </div>
      </el-table-column>
      <el-table-column prop="arrivePrice" header-align="center" align="center" label="已收金额">
      </el-table-column>
      <!-- <el-table-column prop="type" header-align="center" align="center" label="类型">
        <div slot-scope="scope">
          <el-tag  type="primary"
            :class="'tag-color-mini tag-color-' + scope.row.type">{{ scope.row.type == null ? '空' :
              contractTypeSimple[scope.row.type].value }}</el-tag>
        </div>
      </el-table-column> -->
      <el-table-column prop="remarks" header-align="center" align="center" label="备注">
      </el-table-column>
      <!-- <el-table-column prop="transformDate" header-align="center" align="center" label="预计回收时间">
      </el-table-column> -->
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="scope.row.buyPriceStatus == 1 || scope.row.buyPriceStatus == 0"  @click="pay(scope.row.id,0)">{{'付款'}}</el-button>
          <el-button type="text" size="small" v-if="scope.row.priceStatus == 1 || scope.row.priceStatus == 0"  @click="pay(scope.row.id,1)">{{'收款'}}</el-button>
          <el-button v-if="(scope.row.priceStatus != 0 && scope.row.priceStatus != 3) || (scope.row.buyPriceStatus != 0 && scope.row.buyPriceStatus != 3)" type="text" size="small"
            style="color: green" @click="priceDetailHandle(scope.row.id)">{{scope.row.type == 0 ? '流水明细' : '流水明细'}}</el-button>
          <el-button type="text" size="small" v-if="(scope.row.priceStatus == 0 || scope.row.priceStatus == 3) && (scope.row.buyPriceStatus== 0 ||scope.row.buyPriceStatus ==3)"  @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" v-if="(scope.row.priceStatus == 0 || scope.row.priceStatus == 3) && (scope.row.buyPriceStatus == 0 || scope.row.buyPriceStatus == 3)" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <pricetransformpay v-if="pricetransformpayVisible" ref="pricetransformpay" @refreshDataList="getDataList"></pricetransformpay>
    <pricetransformdetail v-if="pricetransformdetailVisible" ref="pricetransformdetail" @refreshDataList="getDataList"></pricetransformdetail>
  </div>
</template>

<script>
import {
  contractPriceStatus,
  contractTypeSimple,
} from "@/data/price";
import AddOrUpdate from './pricetransform-add-or-update'
import pricetransformpay from './pricetransform-pay'
import pricetransformdetail from './pricetransform-detail'
export default {
  data() {
    return {
  contractPriceStatus,
  contractTypeSimple,
      dataForm: {
        name: '',
        transformUnitId: '',
        priceBankId: '',
        priceStatus: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      contractTransformUnit: [],
      priceBank: [],
      addOrUpdateVisible: false,
      pricetransformpayVisible: false,
      pricetransformdetailVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    pricetransformpay,
    pricetransformdetail,
  },
  activated() {
    this.getDataList()
    this.findContractTransformUnit()
    this.findPriceBank()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/price/pricetransform/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          appid: this.$cookie.get("appid"),
          'name': this.dataForm.name,
          'transformUnitId': this.dataForm.transformUnitId,
          'priceBankId': this.dataForm.priceBankId,
          'priceStatus': this.dataForm.priceStatus,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    findContractTransformUnit() {
      this.$http({
        url: this.$http.adornUrl('/price/pricetransformunit/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.contractTransformUnit = data.result
        }
      })
    },
    findPriceBank() {
      this.$http({
        url: this.$http.adornUrl('/price/pricebank/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.priceBank = data.result
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/price/pricetransform/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    
    pay(id,type) {
      this.pricetransformpayVisible = true
      this.$nextTick(() => {
        this.$refs.pricetransformpay.init(id,type)
      })
    },
    priceDetailHandle(id) {
      this.pricetransformdetailVisible = true
      this.$nextTick(() => {
        this.$refs.pricetransformdetail.init(id)
      })
    },
  }
}
</script>
