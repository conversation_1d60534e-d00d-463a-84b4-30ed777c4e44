<template>
  <div>
    <div>
      <vue-aliplayer-v2 class="aliplayer" :source="source" ref="VueAliplayerV2" :options="options"></vue-aliplayer-v2>
    </div><van-tabs animated>
      <van-tab title="功能介绍">
      </van-tab>
    </van-tabs>
    <div style="padding-bottom: 70px;" class="content" v-html="merchantInfo.paramValue" @click="showImg($event)"></div>

    <div style="position: fixed;width: 100%;bottom: 0;height: 70px;line-height: 70px;background-color: white;">
      <van-button color="#DD5C5F" @click="$router.push({
        name: 'proxyApply',
        query: {
          type: 0
        }
      })" style="    width: 94%;margin-top: 10px;margin-left: 3%;" round block type="info" :loading="loading"
        loading-text="提交中">申请试用/商务合作</van-button>
    </div>
  </div>
</template>

<script>

import VueAliplayerV2 from "vue-aliplayer-v2";
export default {
  components: {
    VueAliplayerV2,
  },
  data() {
    return {
      inviteUserId: '',
      openid: undefined,
      userInfo: {},
      merchantInfo: {},
      options: {
        source: "http://video.fjmeeting.com/sv/282737e3-1900b5dfee1/282737e3-1900b5dfee1.mp4",
        height: "230px",
        width: "100%",
        cover: "https://mpjoy.oss-cn-beijing.aliyuncs.com/20240612/65aba6aa2df94321926f5efa2cbf0761.jpg",
        isLive: false,
        autoplay: false,
        skinLayout: [{
          name: "bigPlayButton",
          align: "blabs",
          x: 30,
          y: 50
        },
        {
          name: "H5Loading",
          align: "cc",
        },
        {
          name: "errorDisplay",
          align: "tlabs",
          x: 0,
          y: 0
        },
        {
          name: "infoDisplay"
        },
        {
          name: "tooltip",
          align: "blabs",
          x: 0,
          y: 56
        },
        {
          name: "thumbnail"
        },
        {
          name: "controlBar",
          align: "blabs",
          x: 0,
          y: 0,
          children: [{
            name: "progress",
            align: "blabs",
            x: 0,
            y: 44
          },
          {
            name: "playButton",
            align: "tl",
            x: 15,
            y: 12
          },
          {
            name: "timeDisplay",
            align: "tl",
            x: 40,
            y: 7
          },
          {
            name: "nextButton",
            align: "tl",
            x: 10,
            y: 26
          },
          {
            name: "fullScreenButton",
            align: "tr",
            x: 10,
            y: 12
          },
          {
            name: "setting",
            align: "tr",
            x: 15,
            y: 12
          },
          {
            name: "volume",
            align: "tr",
            x: 5,
            y: 10
          },
          ],
        }]

      }
    };
  },
  mounted() {
    document.title = "易企化";
    this.inviteUserId = this.$route.query.userId;
    this.getCmsInfo();
  },
  methods: {
    bindInviteUser() {
      // 先做无感邀请
      this.$fly.get("/finance/wxUser/bindInviteUser", {
        inviteUserId: this.inviteUserId
      })
        .then((res) => {
          if (res.code == 200) {
            // 
          } else {
            // uni.showToast({
            //     title: res.msg,
            //     icon: "none",
            // });
          }
        });
    },
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
          this.$wxShare(
            "易企化-业财一体数字化解决方案",
            this.$cookie.get("logo"),
            "提供业财一体化财务软件\n商务合作：15880293295",
            (window.location.href + '?userId=' + this.userInfo.id)
          ); //加载微信分享
        } else {
          vant.Toast(res.msg);
        }
      });
    },
    getCmsInfo() {
      this.$fly.get(`/pyp/web/config/findParamKey`, {
        paramKey: 'yqh'
      }).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          this.getUserInfo();
          if (this.inviteUserId) {
            // this.bindInviteUser();
          }
        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  background-color: white;

  /deep/ p {
    width: 100%;
  }

  /deep/ img {
    width: 100%;
    height: auto;
  }
}

.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.prism-player {
  background-color: white;
}

/deep/ .prism-info-display {
  padding: 0 !important;
}
</style>