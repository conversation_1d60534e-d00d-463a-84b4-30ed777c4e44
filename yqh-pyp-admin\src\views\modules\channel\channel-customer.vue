<template>
  <div class="mod-channel-customer">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.nickname" placeholder="客户昵称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.channelId" placeholder="所属渠道" clearable>
          <el-option
            v-for="channel in channelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.bindingStatus" placeholder="绑定状态" clearable>
          <el-option label="已绑定" value="1"></el-option>
          <el-option label="未绑定" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="success" @click=" $router.push({ name: 'salesman-wx-user-binding' })">业务员客户绑定</el-button>
        <!-- <el-button @click="updateCustomerChannel()" type="warning">批量更新渠道归属</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;" >
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">{{ stats.totalCustomers || 0 }}</div>
          <div class="stats-label">总客户数</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">{{ stats.activeCustomers || 0 }}</div>
          <div class="stats-label">活跃客户</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">{{ stats.boundCustomers || 0 }}</div>
          <div class="stats-label">已绑定客户</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">{{ stats.payingCustomers || 0 }}</div>
          <div class="stats-label">付费客户</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">{{ stats.channelCustomers || 0 }}</div>
          <div class="stats-label">渠道客户</div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <div class="stats-number">¥{{ stats.totalRevenue || 0 }}</div>
          <div class="stats-label">总收入</div>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <!-- <el-table-column prop="id" header-align="center" align="center" width="80" label="ID"></el-table-column> -->
      <el-table-column prop="nickname" header-align="center" align="center" width="200" label="客户昵称">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center;justify-content: center;">
            <el-avatar :src="scope.row.headimgurl" size="small" style="margin-right: 8px;" v-if="scope.row.headimgurl"></el-avatar>
            <span>{{ scope.row.nickname || '未设置' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="手机号"></el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="绑定业务员">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.salesmanName" type="success">{{ scope.row.salesmanName }}</el-tag>
          <el-tag v-else type="info">未绑定</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="channelName" header-align="center" align="center" label="所属渠道">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.channelName" type="primary">{{ scope.row.channelName }}</el-tag>
          <el-tag v-else type="warning">未分配</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" width="80" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bindingTime" header-align="center" align="center" width="150" label="绑定时间">
        <template slot-scope="scope">
          {{ scope.row.bindingTime | formatDate }}
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="150" label="注册时间">
        <template slot-scope="scope">
          {{ scope.row.createOn | formatDate }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        nickname: '',
        mobile: '',
        salesmanName: '',
        channelId: '',
        bindingStatus: ''
      },
      dataList: [],
      channelList: [],
      stats: {},
      statsVisible: false,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: []
    }
  },
  activated () {
    this.initFromQuery()
    this.getDataList()
    this.getChannelList()
    this.getStats()
  },
  methods: {
    // 从查询参数初始化
    initFromQuery () {
      if (this.$route.query.channelId) {
        this.dataForm.channelId = this.$route.query.channelId
      }
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/channel/channel/customerList'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'nickname': this.dataForm.nickname,
          'mobile': this.dataForm.mobile,
          'salesmanName': this.dataForm.salesmanName,
          'channelId': this.dataForm.channelId,
          'bindingStatus': this.dataForm.bindingStatus
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取渠道列表
    getChannelList () {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.page.list
        }
      })
    },
    // 获取统计信息
    getStats () {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/customerStats'),
        method: 'get',
        params: this.$http.adornParams({
          'nickname': this.dataForm.nickname,
          'mobile': this.dataForm.mobile,
          'salesmanName': this.dataForm.salesmanName,
          'channelId': this.dataForm.channelId,
          'bindingStatus': this.dataForm.bindingStatus
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.stats = data.stats
          this.statsVisible = true
        }
      })
    },
    // 批量更新客户渠道归属
    updateCustomerChannel () {
      this.$confirm('确定要批量更新客户渠道归属吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/channel/channel/updateCustomerChannel'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '批量更新成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    }
  },
  filters: {
    formatDate (value) {
      if (!value) return ''
      return new Date(value).toLocaleString()
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
  cursor: pointer;
}
.stats-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}
.stats-label {
  font-size: 14px;
  color: #666;
}
</style>
