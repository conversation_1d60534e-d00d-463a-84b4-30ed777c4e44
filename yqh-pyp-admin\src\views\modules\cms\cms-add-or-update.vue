<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题"></el-input>
      </el-form-item>
      <el-form-item label="父id" prop="pid">
        <el-select v-model="dataForm.pid" placeholder="父id" filterable>
          <el-option label="无" value="0"></el-option>
          <el-option v-for="item in cmsList" :key="item.id" :label="item.title" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="移动端图标" prop="mobileIcon">

        <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card" :show-file-list="false"
          accept=".jpg, .jpeg, .png, .gif" :on-success="appSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.mobileIcon" :src="dataForm.mobileIcon" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <!-- <div style="color: red">建议尺寸：300*300，大小100kb以下</div> -->
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" :max="100" label="排序"></el-input-number>
      </el-form-item>
      <div style="color: red">配置外部链接后，“模块跳转”和“内容”不生效</div>
      <el-form-item label="外部链接" prop="url">
        <el-input v-model="dataForm.url" placeholder="外部链接(如果配置内容失效)"></el-input>
      </el-form-item>
      <el-form-item label="背景颜色" prop="color">
        <el-color-picker v-model="dataForm.color"></el-color-picker>
        <!-- <el-input v-model="dataForm.color" placeholder="背景颜色(6位颜色代码)">
          <template slot="prepend">#</template>
        </el-input> -->
      </el-form-item>
      <el-form-item label="模块跳转" prop="model">
        <el-select v-model="dataForm.model" placeholder="模块跳转" filterable>
          <el-option label="无" value=""></el-option>
          <el-option v-for="item in modelList" :key="item.name" :label="item.name" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
      </el-form-item>
      <el-form-item label="动画效果" prop="animate">
        <el-select v-model="dataForm.animate" placeholder="动画效果" filterable>
          <el-option label="无" value=""></el-option>
          <el-option v-for="item in animate" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
          </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input v-model="dataForm.longitude" placeholder="经度"></el-input>
          </el-form-item></el-col>
        <el-col :span="12"><el-form-item label="纬度" prop="latitude">
            <el-input v-model="dataForm.latitude" placeholder="纬度"></el-input>
          </el-form-item></el-col>
      </el-row>
      <a style="color:red;margin-left:50px" target="_blank"
        href="https://lbs.qq.com/tool/getpoint/index.html">腾讯地图坐标拾取工具</a>

      <!-- <el-form-item label="移动端内容" prop="mobileContent">
        <tinymce-editor ref="editor" v-model="dataForm.mobileContent"></tinymce-editor>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {animate,gradient} from "@/data/common";
import Compressor from 'compressorjs';
export default {
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor"),
    OssUploader: () =>
      import('../oss/oss-uploader')
  },
  data() {
    return {
      animate,
      gradient,
      visible: false,
      imgAppDialogVisible: false,
      dialogAppImageUrl: '',
      url: '',
      cmsList: [],
      modelList: [
        { name: '个人中心', value: '{"name": "meMine","query": {"id": "${activityId}"}}' },
        { name: '会议议程', value: '{"name": "schedulesIndex","query": {"id": "${activityId}"}}' },
        { name: '会议议程(新)', value: '{"name": "schedulesIndexNew","query": {"id": "${activityId}"}}' },
        { name: '嘉宾列表', value: '{"name": "schedulesExperts","query": {"id": "${activityId}"}}' },
        { name: '参会报名', value: '{"name": "applyIndex","query": {"id": "${activityId}"}}' },
        { name: '酒店预订', value: '{"name": "hotelIndex","query": {"id": "${activityId}"}}' },
        { name: '会议直播', value: '{"name": "livesIndex","query": {"id": "${activityId}"}}' },
        { name: '考试&问卷', value: '{"name": "examIndex","query": {"id": "${activityId}"}}' },
        { name: '展商列表', value: '{"name": "merchantIndex","query": {"id": "${activityId}"}}' },
        { name: '导航列表', value: '{"name": "divNav","query": {"id": "${activityId}"}}' },
        { name: '座位查询', value: '{"name": "divZuowei","query": {"id": "${activityId}"}}' },
        { name: '专家信息确认', value: '{"name": "expertIndexCheck","query": {"id": "${activityId}"}}' },
      ],
      dataForm: {
        id: 0,
        title: '',
        pid: '0',
        activityId: '',
        mobileIcon: '',
        content: '',
        mobileContent: '',
        url: '',
        model: '',
        color: '',
        longitude: '',
        latitude: '',
        animate: '',
        paixu: 0
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        pid: [
          { required: true, message: '父id不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '活动表id不能为空', trigger: 'blur' }
        ],
        mobileIcon: [
          { required: true, message: '移动端图标不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.id = id || 0
      this.visible = true
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/cms/cms/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.title = data.cms.title
              this.dataForm.pid = data.cms.pid.toString();
              this.dataForm.activityId = data.cms.activityId
              this.dataForm.mobileIcon = data.cms.mobileIcon
              this.dataForm.content = data.cms.content
              this.dataForm.mobileContent = data.cms.mobileContent
              this.dataForm.url = data.cms.url
              this.dataForm.paixu = data.cms.paixu
              this.dataForm.model = data.cms.model
              this.dataForm.color = data.cms.color
              this.dataForm.longitude = data.cms.longitude
              this.dataForm.latitude = data.cms.latitude
              this.dataForm.animate = data.cms.animate
            }
          })
        }
      })
      this.getCmsByActivityId();
    },
    getCmsByActivityId() {
      this.$http({
        url: this.$http.adornUrl(`/cms/cms/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.cmsList = data.result;
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/cms/cms/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'title': this.dataForm.title,
              'pid': this.dataForm.pid,
              'activityId': this.dataForm.activityId,
              'mobileIcon': this.dataForm.mobileIcon,
              'content': this.dataForm.content,
              'mobileContent': this.dataForm.mobileContent,
              'url': this.dataForm.url,
              'model': this.dataForm.model,
              'color': this.dataForm.color,
              'longitude': this.dataForm.longitude,
              'latitude': this.dataForm.latitude,
              'animate': this.dataForm.animate,
              'paixu': this.dataForm.paixu
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 > 110) {
        this.$message.error(`${file.name}文件大于100KB，请选择小于100KB大小的icon`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.mobileIcon = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
  }
}
</script>
