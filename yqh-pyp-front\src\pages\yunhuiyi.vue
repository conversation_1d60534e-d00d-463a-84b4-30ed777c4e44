<template>
  <div>
    <div>
      <vue-aliplayer-v2 class="aliplayer" :source="source" ref="VueAliplayerV2" :options="options"></vue-aliplayer-v2>
    </div><van-tabs animated>
      <van-tab title="功能介绍">
      </van-tab>
    </van-tabs>
    <div style="padding-bottom: 70px;" class="content" v-html="merchantInfo.paramValue" @click="showImg($event)"></div>

    <div style="position: fixed;width: 100%;bottom: 0;height: 70px;line-height: 70px;background-color: white;">
      <van-button
      @click="$router.push({
        name: 'proxyApply',
        query: {
          type: 1
        }
      })"
      style="    width: 94%;margin-top: 10px;margin-left: 3%;"
        round
        block
        type="info"
        :loading="loading"
        loading-text="提交中"
        >申请试用/商务合作</van-button
      >
    </div>
  </div>
</template>

<script>

import VueAliplayerV2 from "vue-aliplayer-v2";
export default {
  components: {
    VueAliplayerV2,
  },
  data() {
    return {
      openid: undefined,
      merchantInfo: {},
      options: {
        source: "http://video.fjmeeting.com/sv/28c723a0-18cd38b751c/28c723a0-18cd38b751c.mp4",
        height: "230px",
        width: "100%",
        cover: "http://mpjoy.oss-cn-beijing.aliyuncs.com/20230420/c311301d5df24fad86aaa9dfefd43b82.png",
        isLive: false,
        autoplay: false,
        skinLayout: [{
          name: "bigPlayButton",
          align: "blabs",
          x: 30,
          y: 50
        },
        {
          name: "H5Loading",
          align: "cc",
        },
        {
          name: "errorDisplay",
          align: "tlabs",
          x: 0,
          y: 0
        },
        {
          name: "infoDisplay"
        },
        {
          name: "tooltip",
          align: "blabs",
          x: 0,
          y: 56
        },
        {
          name: "thumbnail"
        },
        {
          name: "controlBar",
          align: "blabs",
          x: 0,
          y: 0,
          children: [{
            name: "progress",
            align: "blabs",
            x: 0,
            y: 44
          },
          {
            name: "playButton",
            align: "tl",
            x: 15,
            y: 12
          },
          {
            name: "timeDisplay",
            align: "tl",
            x: 40,
            y: 7
          },
          {
            name: "nextButton",
            align: "tl",
            x: 10,
            y: 26
          },
          {
            name: "fullScreenButton",
            align: "tr",
            x: 10,
            y: 12
          },
          {
            name: "setting",
            align: "tr",
            x: 15,
            y: 12
          },
          {
            name: "volume",
            align: "tr",
            x: 5,
            y: 10
          },
          ],
        }]

      }
    };
  },
  mounted() {
    document.title = "易企化-云会易";
    this.$wxShare(
      "易企化-云会易",
      this.$cookie.get("logo"),
      this.$cookie.get("slog"),
    ); //加载微信分享
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/web/config/findParamKey`, {
        paramKey: 'yunhuiyi'
      }).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;

        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  margin-top: 10px;
  background-color: white;

  /deep/ p {
    width: 100%;
  }

  /deep/ img {
    width: 100%;
    height: auto;
  }
}

.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.prism-player {
  background-color: white;
}
 /deep/ .prism-info-display{
  padding: 0 !important;
}
</style>