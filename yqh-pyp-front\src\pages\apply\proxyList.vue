<template>
  <div>

    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">代报名列表</div>
    </div>
    <van-card style="background: white" v-for="item in dataList" :key="item.id"
      :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner">
      <div @click="$router.push({ name: 'cmsIndex', query: { id: item.activityId } })" slot="title"
        style="font-size: 18px">{{ item.activityName }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        <div>联系人：{{ item.contact }}</div>
        <div>联系方式：{{ item.mobile }}</div>
        <div>创建时间：{{ item.createOn }}</div>
      </div>
      <template #tags>
        <div style="display: flex; justify-content: space-between">
          <van-tag style="margin: 5px 10px 5px 0px" size="medium" plain :type="item.status | statusTypeFilter">{{
            item.status | statusFilter }}</van-tag>
          <div style="display: flex">
            <van-button size="small" style="width: 60px" v-if="item.status == 0" round block
              @click="$router.push({ name: 'applySuccess', query: { orderId: item.id, id: activityId } })"
              type="primary">去支付</van-button>
            <van-button size="small" @click="cancelApply(item.activityUserId)" style="width: 60px; margin-left: 10px"
              v-if="item.status == 0 || item.status == 1" round block type="danger">取消</van-button>
          </div>
        </div>
      </template>
    </van-card>
      <div class="bottom-button">
        <van-button  round block type="info" @click="goApplyProxy"   >添加报名人</van-button>
      </div>
  </div>
</template>

<script>
import orderStatus from '@/data/orderStatus.json'
export default {
  data() {
    return {
      activityId: '',
      loading: false,
      finished: false,
      flag: false,
      orderStatus: orderStatus,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
    };
  },
  filters: {
    statusFilter(v) {
      let data = orderStatus.filter(item => item.key === v)
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    statusTypeFilter(v) {
      let data = orderStatus.filter(item => item.key === v)
      if (data.length >= 1) {
        return data[0].type;
      }
    },
  },
  mounted() {
    document.title = "代报名列表";
    this.activityId = this.$route.query.id;
    this.getActivityList();
  },
  methods: {
    goApplyProxy() {
      this.$router.push({
          name: "applyProxy",
          query: { id: this.activityId },
        });
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityList();
      }
    },
    getActivityList() {
      this.$fly
        .get("/pyp/web/activity/activityuserapplyorder/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          isProxy: 1,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    // 取消报名
    cancelApply(v) {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认取消报名?",
      })
        .then(() => {
          this.$fly
            .post("/pyp/web/activity/activityuserapplyorder/cancelOrder", {
              id: v,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("取消成功");
                this.dataList = [],
                  this.pageIndex = 1,
                  this.pageSize = 10,
                  this.totalPage = 0,
                  this.getActivityList();
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => { });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .van-card__num {
  display: flex;
}
.bottom-button {
  margin-left: 3%;
  margin-bottom: 20px;
  position: fixed;
  bottom: 0;
  width: 94%;
}
</style>