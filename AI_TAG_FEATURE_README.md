# AI标签功能使用说明

## 功能概述

AI标签功能允许用户针对特定场景生成特定的文案，比如理发店可以设置"男,女"标签，用户在"换一篇"时可以选择不同标签生成对应的文案。

## 主要特性

1. **活动级别配置**: 在活动管理中配置AI标签
2. **智能文案生成**: 根据选择的标签生成针对性文案
3. **用户端选择**: 用户可以在换一篇时选择不同标签
4. **提示词优化**: 提示词模板已优化，支持根据标签调整内容风格

## 使用流程

### 1. 管理端配置

#### 步骤1：配置活动AI标签
1. 登录管理后台
2. 进入"活动管理"页面
3. 编辑或新建活动
4. 在"AI标签"字段中输入标签，多个标签用逗号分隔
   - 示例：`男,女,儿童,老人`
   - 示例：`学生,白领,家庭主妇`
5. 保存活动配置

#### 步骤2：验证配置
- 配置完成后，系统会自动将AI标签应用到文案生成中
- 可以在文案管理页面测试生成效果

### 2. 用户端使用

#### 步骤1：查看文案
1. 用户访问活动页面
2. 点击任意平台按钮（如抖音、大众点评等）
3. 查看生成的文案内容

#### 步骤2：选择AI标签换一篇
1. 在文案预览弹窗中，如果活动配置了AI标签，会显示"选择标签"按钮
2. 点击"选择标签"按钮
3. 从弹出的选择器中选择目标受众标签
4. 系统会自动根据选择的标签重新生成文案
5. 新文案会针对选择的受众群体进行优化

## 配置示例

### 理发店示例
```
AI标签配置：男,女,儿童
```

**效果对比：**
- 选择"男"：生成偏向男性关注点的文案（如造型、商务形象等）
- 选择"女"：生成偏向女性关注点的文案（如发型设计、护理等）
- 选择"儿童"：生成适合儿童的文案（如安全、有趣等）

### 餐厅示例
```
AI标签配置：情侣,家庭,商务,朋友聚会
```

**效果对比：**
- 选择"情侣"：突出浪漫氛围、私密空间
- 选择"家庭"：强调亲子友好、营养健康
- 选择"商务"：重点介绍环境优雅、适合谈事
- 选择"朋友聚会"：突出热闹氛围、分享美食

### 健身房示例
```
AI标签配置：减肥,增肌,塑形,康复训练
```

**效果对比：**
- 选择"减肥"：重点介绍有氧运动、卡路里消耗
- 选择"增肌"：突出力量训练、蛋白质补充
- 选择"塑形"：强调体型雕塑、线条美化
- 选择"康复训练"：重点介绍专业指导、安全保障

## 技术实现

### 数据库结构
```sql
-- 活动表增加AI标签字段
ALTER TABLE `tb_activity` ADD COLUMN `ai_tag` varchar(100) DEFAULT NULL;

-- 文案表增加AI标签字段
ALTER TABLE `activity_text` ADD COLUMN `ai_tag` varchar(100) DEFAULT NULL;
```

### API接口

#### 获取活动AI标签
```
GET /web/activity/activitytext/getAiTags?activityId={activityId}
```

#### 根据标签获取文案
```
GET /web/activity/activitytext/getByTag?activityId={activityId}&adType={adType}&aiTag={aiTag}
```

#### 重新生成文案（支持AI标签）
```
GET /web/activity/review/regenerate/{platform}?activityId={activityId}&aiTag={aiTag}
```

### 前端组件
- AI标签选择器：`van-picker`组件
- 标签按钮：显示当前选择的标签
- 换一篇功能：支持带标签的重新生成

## 提示词优化

系统已对所有平台的提示词模板进行优化，增加了以下特性：

1. **受众导向**: 根据AI标签调整语言风格和内容重点
2. **个性化表达**: 针对不同受众群体使用不同的表达方式
3. **关注点调整**: 根据标签突出不同的产品/服务特点
4. **语言风格**: 自动适配目标受众的语言习惯

### 优化示例
原始提示词：
```
请为抖音平台生成短视频文案，关键词：理发店
```

优化后提示词：
```
你是一位专业的抖音内容创作者，请为抖音平台生成短视频文案。
关键词：理发店
目标受众标签：男，请根据此标签特点调整文案内容和语言风格
```

## 注意事项

1. **标签设置**: AI标签应该简洁明了，避免过于复杂的描述
2. **数量限制**: 建议每个活动的AI标签不超过8个，以免选择过于复杂
3. **标签命名**: 使用用户容易理解的词汇，如"男女老少"而不是"M1F1O1Y1"
4. **内容一致性**: 确保不同标签生成的内容都符合活动的基本信息
5. **测试验证**: 配置完成后建议测试各个标签的生成效果

## 故障排除

### 常见问题

1. **标签按钮不显示**
   - 检查活动是否配置了AI标签
   - 确认AI标签字段不为空

2. **选择标签后没有重新生成**
   - 检查网络连接
   - 查看浏览器控制台是否有错误信息

3. **生成的文案没有差异**
   - 检查AI标签是否正确传递到后端
   - 确认提示词模板是否已更新

4. **标签选择器显示异常**
   - 检查标签格式是否正确（逗号分隔）
   - 确认没有多余的空格或特殊字符

### 调试方法

1. **查看网络请求**: 使用浏览器开发者工具查看API调用
2. **检查参数传递**: 确认aiTag参数是否正确传递
3. **查看后端日志**: 检查服务器日志中的AI模型调用记录
4. **测试API**: 直接调用API接口测试功能

## 版本历史

- **v1.0.12**: 初始版本，支持基础AI标签功能
  - 数据库表结构更新
  - 后端API实现
  - 前端界面集成
  - 提示词模板优化

## 扩展计划

1. **标签模板**: 预设常用行业的标签模板
2. **智能推荐**: 根据活动类型自动推荐合适的标签
3. **效果分析**: 统计不同标签的使用频率和效果
4. **批量配置**: 支持批量为多个活动配置相同的标签
