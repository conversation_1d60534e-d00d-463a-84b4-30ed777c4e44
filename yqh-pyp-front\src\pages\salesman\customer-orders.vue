<template>
  <div class="customer-orders-page">
    <!-- 导航栏 -->
    <van-nav-bar :title="`${customerName}的订单`" left-text="返回" left-arrow @click-left="$router.go(-1)" />

    <!-- 订单筛选 -->
    <div class="filter-section">
      <van-tabs v-model="statusFilter" @change="onFilterChange" 
                color="#1989fa" title-active-color="#1989fa">
        <van-tab title="全部" name="all"></van-tab>
        <van-tab title="待支付" name="pending"></van-tab>
        <van-tab title="已支付" name="paid"></van-tab>
        <van-tab title="已取消" name="cancelled"></van-tab>
      </van-tabs>
    </div>

    <!-- 订单列表 -->
    <div class="orders-list">
      <van-list v-model="loading" :finished="finished" 
                finished-text="没有更多订单了" @load="loadOrders">
        <div v-for="order in ordersList" :key="order.id" class="order-item">
          <div class="order-header">
            <div class="order-info">
              <div class="order-number">订单号：{{ order.orderSn }}</div>
              <div class="order-time">{{ formatTime(order.createTime) }}</div>
            </div>
            <div class="order-status">
              <van-tag :type="getOrderStatusTagType(order.status)" size="small" round>
                {{ getOrderStatusText(order.status) }}
              </van-tag>
            </div>
          </div>

          <div class="order-content">
            <div class="order-detail">
              <div class="detail-row">
                <span class="label">订单类型：</span>
                <span class="value">{{ getOrderTypeText(order.rechargeType) }}</span>
              </div>
              <div class="detail-row" v-if="order.countValue">
                <span class="label">充值次数：</span>
                <span class="value">{{ order.countValue }}次</span>
              </div>
              <div class="detail-row">
                <span class="label">订单金额：</span>
                <span class="value amount">¥{{ order.amount || 0 }}</span>
              </div>
              <div class="detail-row" v-if="order.payAmount && order.status === 1">
                <span class="label">实付金额：</span>
                <span class="value paid">¥{{ order.payAmount || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>

      <van-empty v-if="ordersList.length === 0 && !loading" 
                 description="暂无订单数据" 
                 image="https://img.yzcdn.cn/vant/custom-empty-image.png" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomerOrders',
  data() {
    return {
      customerId: null,
      customerName: '客户',
      statusFilter: 'all',
      ordersList: [],
      loading: false,
      finished: false,
      page: 1
    }
  },
  created() {
    this.customerId = this.$route.query.customerId
    this.customerName = this.$route.query.customerName || '客户'
    this.loadOrders()
  },
  methods: {
    // 加载订单列表
    async loadOrders() {
      if (this.loading) return

      this.loading = true

      try {
        const res = await this.$fly.get('/pyp/web/salesman/getCustomerOrders', {
          customerId: this.customerId,
          page: this.page,
          limit: 10,
          status: this.statusFilter === 'all' ? null : this.getOrderStatusValue(this.statusFilter)
        })

        if (res.code === 200) {
          const newOrders = (res.result || []).map(item => ({
            id: item.id,
            orderSn: item.orderSn,
            rechargeType: item.rechargeType,
            countValue: item.countValue,
            amount: item.amount,
            payAmount: item.payAmount,
            status: item.status,
            createTime: item.createTime || item.createOn,
            payTime: item.payTime
          }))

          if (this.page === 1) {
            this.ordersList = newOrders
          } else {
            this.ordersList.push(...newOrders)
          }

          this.page++
          this.finished = newOrders.length < 10
        } else {
          this.$toast(res.msg || '加载订单失败')
          this.finished = true
        }
      } catch (error) {
        console.error('加载订单失败:', error)
        this.finished = true
      } finally {
        this.loading = false
      }
    },

    // 筛选变化
    onFilterChange() {
      this.ordersList = []
      this.page = 1
      this.finished = false
      this.loadOrders()
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '未知时间'
      
      try {
        const date = new Date(time)
        return date.toLocaleString()
      } catch (error) {
        return time
      }
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已取消',
        3: '已退款'
      }
      return statusMap[status] || '未知'
    },

    // 获取订单状态标签类型
    getOrderStatusTagType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger',
        3: 'default'
      }
      return typeMap[status] || 'default'
    },

    // 获取订单类型文本
    getOrderTypeText(type) {
      const typeMap = {
        1: '套餐充值',
        2: '自定义充值',
        3: '系统赠送',
        4: '创建活动套餐'
      }
      return typeMap[type] || '未知类型'
    },

    // 获取订单状态值
    getOrderStatusValue(filterValue) {
      const valueMap = {
        'pending': 0,
        'paid': 1,
        'cancelled': 2
      }
      return valueMap[filterValue]
    }
  }
}
</script>

<style scoped>
.customer-orders-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.orders-list {
  padding: 16px;
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #ebedf0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
  color: #969799;
}

.order-content {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  font-size: 13px;
  color: #646566;
}

.detail-row .value {
  font-size: 13px;
  color: #323233;
  font-weight: 500;
}

.detail-row .value.amount {
  color: #ff976a;
}

.detail-row .value.paid {
  color: #07c160;
}
</style>
