package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.ActivityRefundRecordEntity;

import java.util.Map;

/**
 * 退款记录表
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface ActivityRefundRecordService extends IService<ActivityRefundRecordEntity> {

    PageUtils queryPage(Map<String, Object> params);

    /**
     * 创建退款记录
     */
    void createRefundRecord(ActivityRefundRecordEntity refundRecord);

    /**
     * 更新退款记录状态
     */
    void updateRefundStatus(Long refundRecordId, Integer status, String remarks);

    /**
     * 根据充值记录ID获取退款记录
     */
    ActivityRefundRecordEntity getByRechargeRecordId(Long rechargeRecordId);
}
