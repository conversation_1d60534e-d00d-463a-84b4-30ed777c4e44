<!-- FlightSelectorDialog.vue -->
<template>
    <el-dialog title="选择火车票" :visible.sync="visible" width="800px" @close="handleClose">
        <div class="flight-container">
            <!-- 日期导航 -->
            <el-row type="flex" justify="space-between" align="middle" class="date-nav">
                <el-col :span="4">
                    <el-button plain @click="switchDay(-1)" :disabled="isLoading">
                        前一天
                    </el-button>
                </el-col>
                <el-col :span="16" class="current-date">
                    {{ inDate }}
                </el-col>
                <el-col :span="4" class="text-right">
                    <el-button plain @click="switchDay(1)" :disabled="isLoading">
                        后一天
                    </el-button>
                </el-col>
            </el-row>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-overlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">火车票信息加载中...</div>
            </div>
            <!-- 火车票列表 -->
            <template v-if="!isLoading">
                <div v-if="flights.length > 0" class="flight-list">
                    <div v-for="(flight,index) in flights" :key="flight.trainCode" class="flight-item"
                        :class="{ 'selected': selectedFlight === flight.trainCode }"
                        @click="selectFlight(flight.trainCode)">
                        <div class="time-row">
                            <span class="departure-time">{{ flight.fromDateTime }}</span>
                            <span class="duration">{{ flight.runTime }}时</span>
                            <span class="arrival-time">{{ flight.toDateTime }}</span>
                        </div>

                        <div class="airport-row">
                            <span class="departure-airport">{{ flight.fromStation }}</span>
                            <div>
                                <span class="flight-number">{{ flight.trainCode }}</span>
                                <!-- <span class="flight-number">{{ flight.airlineCompany }}</span>
                                <span class="flight-number">{{ flight.meals }}</span> -->
                            </div>
                            <span class="arrival-airport">{{ flight.toStation }}</span>
                        </div>
                        <div v-for="item in flight.Seats" :key="item.seatTypeName" class="flight-item"
                            :class="{ 'selected-item': (selectedFlightItem.seatType === item.seatType && index === selectIndex) }"
                            @click="selectFlightItem(item,index)">
                            <div class="time-row">
                                <span class="duration">{{ item.seatTypeName }}</span>
                                <span>剩余座位：{{ item.leftTicketNum }}</span>
                                <span>售价：{{ item.ticketPrice }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <el-empty v-else description="暂无火车票信息" />
            </template>
        </div>

        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="confirmSelection">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>

import { format } from "@/utils/date.js";
export default {
    name: 'FlightSelectorDialog',
    data() {
        return {
            isLoading: false,
            visible: false,
            inDate: '',
            startCityCode: '',
            endCityCode: '',
            selectedFlight: null,
            selectIndex: 0,
            selectedFlightItem: {},
            flights: [],
            callback: null // 添加回调函数属性
            // minDate: new Date('2024-01-17'),  // 限制最早可选日期
            // maxDate: new Date('2024-01-19')   // 限制最晚可选日期
        }
    },
    computed: {
        // formattedDate() {
        //     const month = (this.currentDate.getMonth() + 1).toString().padStart(2, '0')
        //     const day = this.currentDate.getDate().toString().padStart(2, '0')
        //     const weekdays = ['日', '一', '二', '三', '四', '五', '六']
        //     return `${month}月${day}日 周${weekdays[this.currentDate.getDay()]}`
        // },
        // isPrevDisabled() {
        //     return this.currentDate <= this.minDate
        // },
        // isNextDisabled() {
        //     return this.currentDate >= this.maxDate
        // }
    },
    methods: {
        init(startCityCode, endCityCode, inDate, callback) {
            this.callback = callback || null;
            this.startCityCode = startCityCode;
            this.endCityCode = endCityCode;
            this.inDate = inDate;
            this.visible = true;
            this.flights = [];
            this.selectedFlight = null;
            this.selectIndex = 0;
            this.selectedFlightItem = {}
            this.loadFlights();

        },
        loadFlights() {
            this.isLoading = true
            this.$http({
                url: this.$http.adornUrl("/panhe/searchTrain"),
                method: "get",
                params: this.$http.adornParams({
                    fromCity: this.startCityCode,
                    toCity: this.endCityCode,
                    fromDate: this.inDate,
                }),
            }).then(({ data }) => {
                this.isLoading = false;
                if (data && data.code === 200) {
                    this.flights = data.result;
                } else {
                    this.$message.error(data.msg)
                }
            });
        },
        switchDay(offset) {
            const newDate = new Date(this.inDate)
            newDate.setDate(newDate.getDate() + offset)
            this.inDate = format(newDate, "yyyy/MM/dd");
            this.flights = [];
            this.selectedFlight = null;
            this.selectIndex = 0;
            this.selectedFlightItem = {}
            this.loadFlights()
        },
        selectFlight(number) {
            this.selectedFlight = number
        },
        selectFlightItem(number,index) {
            this.selectedFlightItem = number
            this.selectIndex = index;
            console.log(this.selectedFlightItem)
        },
        confirmSelection() {
            if (this.selectedFlight && this.selectFlightItem) {
                const flight = this.flights.find(f => f.trainCode === this.selectedFlight)
                console.log(flight)
                flight.cabinBookPara = this.selectedFlightItem.seatType;
                flight.cabinCode = this.selectedFlightItem.seatTypeName;
                flight.price = this.selectedFlightItem.ticketPrice;
                // 如果有回调函数，则调用回调
                if (this.callback) {
                    this.callback(flight);
                } else {
                    // 原有的emit逻辑
                    this.$emit('select', flight);
                }
                this.visible = false
            } else {
                this.$message.warning('请先选择火车票和仓位')
            }
        },
        handleClose() {
            this.selectedFlight = null
        }
    },
}
</script>

<style lang="scss" scoped>
.flight-container {
    padding: 10px;
}

.date-nav {
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;

    .current-date {
        text-align: center;
        font-size: 16px;
        color: #303133;
        font-weight: 500;
    }
}

.flight-list {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    height: 600px;
    overflow-y: scroll;
}

.flight-item {
    padding: 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: all 0.2s;
}

.flight-item:hover {
    background: #f5f7fa;
}

.flight-item.selected {
    background: #ecf5ff;
    border-left: 3px solid #409eff;
}

.flight-item.selected-item {
    background: #ffdbdb;
    border-left: 3px solid #ff4040;
}

.time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.departure-time,
.arrival-time {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.duration {
    font-size: 14px;
    color: #666;
}

.airport-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.departure-airport,
.arrival-airport {
    font-size: 14px;
    color: #666;
    max-width: 40%;
}

.flight-number {
    font-size: 13px;
    color: #409eff;
    padding: 4px 8px;
    background: #e8f4ff;
    border-radius: 4px;
    margin-left: 10px;
}

::v-deep .el-dialog__body {
    padding: 20px;
}

.el-empty {
    padding: 40px 0;
}

.loading-overlay {
    position: relative;
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>