<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-select v-model="dataForm.supplierId" placeholder="供应商">
          <el-option label="全部(供应商)" value=""></el-option>
          <el-option v-for="item in suppliers" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.productTypeId" placeholder="产品类别">
          <el-option label="全部(产品类别)" value=""></el-option>
          <el-option v-for="item in productTypes" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.productType" placeholder="产品类型">
          <el-option label="全部(产品类型)" value=""></el-option>
          <el-option v-for="item in productType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="产品名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('supplier:supplierproduct:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('supplier:supplierproduct:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="success" @click="downloadExampleHandle()">模板下载</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList"
            :url="'/supplier/supplierproductstockconfirm/importExcel?appid=' + $cookie.get('appid')"
            :name="'入库单数据导入'"></Upload>
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border size="mini" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="产品名称">
      </el-table-column>
      <el-table-column prop="brief" :show-overflow-tooltip="true" header-align="center" align="center" label="描述">
      </el-table-column>
      <el-table-column prop="picUrl" header-align="center" align="center" label="图片">
        <img class="headimg" slot-scope="scope" v-if="scope.row.picUrl" :src="scope.row.picUrl" />
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="单位">
      </el-table-column>
      <el-table-column prop="supplierName" header-align="center" align="center" label="供应商">
      </el-table-column>
      <el-table-column prop="productTypeName" header-align="center" align="center" label="产品类别">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="成本价格">
      </el-table-column>
      <el-table-column prop="sellPrice" header-align="center" align="center" label="对外售价">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './supplierproduct-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        supplierId: '',
        productTypeId: '',
        productType: '',
        name: '',
      },
      dataList: [],
      suppliers: [],
      productTypes: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,

      dataListSelections: [],
      addOrUpdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    Upload: () => import('@/components/upload'),
  },
  activated() {
    this.getDataList()
    this.findSupplier();
    this.findProductType();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {

      this.$http({
        url: this.$http.adornUrl('/supplier/supplierproduct/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'supplierId': this.dataForm.supplierId,
          'productTypeId': this.dataForm.productTypeId,
          'productType': this.dataForm.productType,
          appid: this.$cookie.get("appid"),
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }

      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    findSupplier() {
      this.$http({
        url: this.$http.adornUrl(`/supplier/supplier/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.suppliers = data.result;
        }
      })
    },
    findProductType() {
      this.$http({
        url: this.$http.adornUrl(`/supplier/producttype/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.productTypes = data.result;
        }
      })
    },
    downloadExampleHandle() {
      var url = this.$http.adornUrl("/supplier/supplierproductstockconfirm/exportExcelExample?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/supplier/supplierproduct/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
