<template>
  <div>
    <div v-if="merchantInfo.content"
      class="content"
      v-html="merchantInfo.content"
      @click="showImg($event)"
    ></div>
    <van-empty v-else  description="暂无信息" />
    <!-- 返回按钮 -->
    <img class="back" @click="cmsTurnBack" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png" alt="" />
  </div>
</template>

<script>

export default {
  components: {},
  data() {
    return {
      openid: undefined,
      hotelActivityId: undefined,
      activityId: undefined,
      merchantInfo: {},
    };
  },
  mounted() {
    document.title = "酒店详情";
    this.hotelActivityId = this.$route.query.id;
    this.activityId = this.$route.query.activityId;
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/web/hotel/hotelactivity/info/${this.hotelActivityId}`).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          
        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
    cmsTurnBack() {
      this.$router.go(-1);
    }
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  margin-top: 10px;
  background-color: white;
  /deep/ p {
    width: 100%;
  }
  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
</style>