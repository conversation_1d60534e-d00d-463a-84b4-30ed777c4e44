<template>
  <el-dialog
    title="答题情况"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
      <el-table :data="examActivityUserOptionEntities" border style="width: 100%">
        <el-table-column
          prop="questionName"
           show-overflow-tooltip
          header-align="center"
          align="center"
          label="题目名称"
        >
        </el-table-column>
        <el-table-column
          prop="optionName"
          header-align="center"
          align="center"
          label="作答结果"
        >
          <div slot-scope="scope"  :style="scope.row.optionName != scope.row.realOptionName ? 'color: red' : 'color: green'">
            {{scope.row.optionName}}
          </div>
        </el-table-column>
        <el-table-column
          prop="realOptionName"
          header-align="center"
          align="center"
          label="正确答案"
        >
          <div slot-scope="scope" :style="scope.row.optionName != scope.row.realOptionName ? 'color: red' : 'color: green'">
            {{scope.row.realOptionName}}
          </div>
        </el-table-column>
      </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      id: "",
      examActivityUserOptionEntities: [],
    };
  },
  methods: {
    init(id) {
      this.id = id || 0;
      this.visible = true;
      this.$http({
        url: this.$http.adornUrl(
          `/exam/examactivityuseroption/findByExamActivityUserId/${this.id}`
        ),
        method: "get",
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.examActivityUserOptionEntities = data.result;
        }
      });
    },
  },
};
</script>
