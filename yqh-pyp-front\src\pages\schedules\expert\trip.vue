<template>
    <div :class="isMobilePhone ? '' : 'pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <van-card style="background: white"
            :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
            <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
            <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary"
                    plain>{{
        guestInfo.unit }}</van-tag>
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning"
                    plain>{{
        guestInfo.duties }}</van-tag>
            </div>
        </van-card>
        <div style="margin-top: 8px" class="nav-title">
            <div class="color"></div>
            <div class="text">行程信息</div>
        </div>
        <div v-if="trip && trip.length > 0">
            <div class='flight-card' v-for="(item, index) in trip" :key="index" @click="viewTripDetail(item)">
                <div class='flight-top'>
                    <div class='flight-top-name'>
                        <img class='flight-icon' :src="item.inType == 0
        ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/ee05423e609347e39d0cb38f9424e0ac.png'
        : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/4da10defd13b4f9ea1716a38d43b9e6b.png'">
                        <span class='flight-no'> {{ item.inNumber }}-{{ item.inDate }}</span>
                    </div>
                    <div class="flight-status-tags">
                        <!-- 出票状态标签 -->
                        <van-tag :type="item.isBuy == 1 ? 'success' : 'warning'" round size="medium"
                            style="margin-right: 8px">
                            {{ isBuy[item.isBuy] ? isBuy[item.isBuy].name : '未出票' }}
                        </van-tag>
                        <img class='flight-top-arow'
                            src='http://mpjoy.oss-cn-beijing.aliyuncs.com/20241022/54aa75f6bee64249b317e1f2796f354f.png'>
                    </div>
                </div>
                <div class='flight-middle'>
                    <div class='flight-start'>
                        <div class='flight-start-time'> {{ item.inStartDate | timeFilter }} </div>
                        <div class='flight-start-a'> {{ item.inStartPlace + (item.inType == 0 ? ('-'
        + item.inStartTerminal) : '') }} </div>
                    </div>
                    <div class='flight-status'>
                        <div v-if="item.inType == 0" class='flight-status-show'
                            :class="{ 'plane-type': item.inType == 0, 'train-type': item.inType == 1 }">
                            {{ item.orderStatus | tripPlaneStatusFilter }}
                        </div>
                        <div v-else class='flight-status-show'
                            :class="{ 'plane-type': item.inType == 0, 'train-type': item.inType == 1 }">
                            {{ item.orderStatus | tripTrainStatusFilter }}
                        </div>
                        <div class='flight-status-arow1'>
                            <div class='flight-status-arow2'></div>
                        </div>
                    </div>
                    <div class='flight-end'>
                        <div class='flight-end-time'> {{ item.inEndDate | timeFilter }} </div>
                        <div class='flight-end-a'> {{ item.inEndPlace + (item.inType == 0 ? ('-' + item.inEndTerminal) :
        '') }} </div>
                    </div>
                </div>
                <!-- 添加改签按钮 -->
                <div class="flight-actions" v-if="item.inType == 0 && item.orderStatus == 4 && item.isCha == 0">
                    <van-button type="primary" size="small" @click.stop="handleChangeTicket(item)">申请改签</van-button>
                </div>
            </div>
        </div>
        <div v-else class="empty-trip">
            <van-empty description="暂无行程信息" />
        </div>
        <div style="margin: 16px">
            <van-button round block type="info" @click="addTrip">新增行程</van-button>
        </div>
        <div style="margin: 16px">
            <van-button round block type="primary" @click="
        $router.replace({
            path: '/schedules/expertIndex',
            query: { detailId: id },
        });">返回上一页面</van-button>
        </div>
    </div>
</template>
<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
// 移除未使用的 SignCanvas 导入
import { guestGoType, tripType, doType, tripPlaneStatus, tripTrainStatus, isBuy } from "@/data/common";
export default {
    components: { pcheader },  // 移除未使用的 SignCanvas 组件
    data() {
        return {
            isBuy,
            tripPlaneStatus,
            tripTrainStatus,
            areaCode: '',
            guestGoType,
            doType,
            tripType,
            loading: false,
            tripTypeShow: false,
            areaShow: false,
            dateShow: false,
            guestGoTypeShow: false,
            // 移除未使用的 signVisible
            isMobilePhone: isMobilePhone(),
            openid: undefined,
            activityId: undefined,
            id: undefined,
            guestInfo: {},
            trip: [],
            activityInfo: {},
            minDate: new Date(2024, 0, 1),
            maxDate: new Date(2030, 10, 1),
            currentDate: new Date(),
            startShow: false,
            start: '',
            endShow: false,
            end: '',
            minHour: '',
            tripForm: {
                id: '',
                inType: 0,
                inTypeName: '',
                type: 0,
                typeName: '',
                inDate: '',
                inNumber: '',
                inEndPlace: '',
                inStartPlace: '',
                inEndDate: '',
                inStartDate: '',
                activityGuestId: '',
                activityId: '',
                isBuy: 0,
                price: 0,
                doType: 0,
                image: '',
            },
        };
    },
    // ... 保留 filters 部分
    filters: {
        timeFilter(v) {
            return v ? date.formatDate.format(
                new Date(v),
                "hh:mm"
            ) : '';
        },

        tripPlaneStatusFilter(val) {
            let data = tripPlaneStatus.filter(item => item.key === val)
            if (data.length >= 1) {
                return data[0].value;
            }
        },
        tripTrainStatusFilter(val) {
            let data = tripTrainStatus.filter(item => item.key === val)
            if (data.length >= 1) {
                return data[0].value;
            }
        },
    },
    mounted() {
        // this.activityId = this.$route.query.id;
        this.id = this.$route.query.detailId;
        this.tripForm.activityGuestId = this.$route.query.detailId;
        this.openid = this.$cookie.get("openid");
        this.getActivityList();
        this.getTopicAndSchedule();
    },
    methods: {
        
        // 添加查看行程详情方法
        viewTripDetail(item) {
            this.$router.push({
                path: '/schedules/expertTripDetail',
                query: {
                    detailId: this.id,
                    tripId: item.id
                }
            });
        },
        // 添加改签处理函数
        handleChangeTicket(item) {
            // 阻止事件冒泡，防止触发editTrip
            event.stopPropagation();

            // 跳转到改签页面或显示改签弹窗
            this.$router.push({
                path: '/schedules/expertTripChange',
                query: {
                    detailId: this.id,
                    tripId: item.id
                }
            });
        },

        isImage(fileName) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            const fileExtension = fileName.split('.').pop().toLowerCase();
            return imageExtensions.includes(fileExtension);
        },
        preImage(v) {
            vant.ImagePreview({
                images: [v], // 图片集合
                closeable: true, // 关闭按钮
            });
        },
        // 获取状态标签类型
        getStatusTagType(item) {
            const status = item.orderStatus;
            if (status === undefined) return 'default';

            // 根据状态返回不同的标签类型
            if (status === 0) return 'primary'; // 待支付
            if (status === 1) return 'success'; // 已支付/已出票
            if (status === 2) return 'danger';  // 已取消/已退票
            return 'default';
        },
        editTrip(item) {
            // 跳转到编辑页面，并传递行程ID
            this.$router.push({
                path: '/schedules/expertTripEdit',
                query: {
                    detailId: this.id,
                    tripId: item.id
                }
            });
        },
        editTrip(item) {
            // 跳转到编辑页面，并传递行程ID
            this.$router.push({
                path: '/schedules/expertTripEdit',
                query: {
                    detailId: this.id,
                    tripId: item.id
                }
            });
        },

        getTopicAndSchedule() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getTrip/${this.id}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.trip = res.result;
                    } else {
                        vant.Toast(res.msg);
                        this.trip = [];
                    }
                });
        },
        getActivityList() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
                .then((res) => {
                    if (res.code == 200) {
                        this.guestInfo = res.result;
                        this.activityId = res.result.activityId;
                        this.tripForm.activityId = res.result.activityId;
                    } else {
                        vant.Toast(res.msg);
                        this.guestInfo = {};
                    }
                });
        },
        addTrip() {
            // 跳转到新增页面
            this.$router.push({
                path: '/schedules/expertTripEdit',
                query: {
                    detailId: this.id
                }
            });
        },
    },
};
</script>
<style lang="less" scoped>
.transparent {
    /deep/.van-collapse-item__content {
        background: transparent;
    }
}

.content {
    /deep/ img {
        max-width: 100%;
        height: auto;
    }
}

.van-card__thumb /deep/ img {
    object-fit: contain !important;
}

.van-cell__title {
    flex: none;
    box-sizing: border-box;
    width: 6.2em;
    margin-right: 12px;
    color: #646566;
    text-align: left;
    word-wrap: break-word;
    line-height: 33px;
}

.van-cell__value {
    text-align: left;
    display: flex;
    align-items: center;
}

.sign {
    position: fixed;
    top: 0;
    background: white;
    height: 100%;

    width: 100%;

    .button {
        text-align: center;
        width: 30%;
        height: 40px;
        line-height: 40px;
        background: #4485ff;
        border-radius: 20px;
        text-align: center;
        color: white;
    }
}

.sign-btns {
    display: flex;
    justify-content: space-between;

    #clear,
    #clear1,
    #save {
        display: inline-block;
        padding: 5px 10px;
        width: 76px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #eee;
        background: #e1e1e1;
        border-radius: 10px;
        text-align: center;
        margin: 20px auto;
        cursor: pointer;
    }
}

.flight {
    margin: 10px 13px;
    background-color: white;
    padding: 10px;
    border-radius: 8px;
}

.flight-top {
    position: relative;
    border-bottom: 1px dashed #9f9f9f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flight-top-name {
    height: 33px;
    display: flex;
    align-items: center;
}

.flight-icon {
    width: 29px !important;
    height: 19px;
    vertical-align: middle;
}

.flight-no {
    font-size: 14px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    margin-left: 8px;
}

.flight-top-arow {
    width: 5px;
    height: 9px;
}

.flight-middle {
    height: 67px;
    display: flex;
    /*	border-bottom: 1px solid #F6F6F6;*/
}

.flight-start,
.flight-status,
.flight-end {
    flex: 1;
}

.flight-start-time,
.flight-end-time {
    margin-top: 15px;
    font-size: 25px;
    font-family: Roboto-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 17px;
}

.flight-top-arow,
.flight-end-time,
.flight-end-a,
.flight-num {
    float: right;
}

.flight-status {
    margin-top: 15px;
    position: relative;
}


.flight-start-a,
.flight-end-a {
    margin-top: 6px;
    font-size: 13px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    color: rgb(66, 66, 66);
    line-height: 17px;
}

.flight-end {
    position: relative;
}

.flight-end-a {
    position: absolute;
    right: 0;
    /*bottom: 7px;*/
    top: 31px;
}

.flight-status-arow2 {
    position: absolute;
    width: 8px;
    height: 0;
    margin: 0 auto;
    right: -2px;
    top: -4px;
    border: 1px solid #666;
    transform: rotate(45deg);
}

.flight-status-arow1 {
    position: absolute;
    width: 115px;
    height: 0;
    margin: 0 auto;
    margin-top: 14px;
    border: 1px solid #666;
    left: 50%;
    top: 8%;
    margin-left: -57.5px;
}

.flight-bottom,
.flight-info {
    display: flex;
}

.flight-baggage,
.bottom-baggage {
    text-align: right;
}

.bottom-num,
.bottom-exit,
.flight-num,
.flight-exit {
    text-align: center;
}

.flight-date,
.flight-num,
.flight-exit,
.flight-baggage {
    flex: 1;
    font-size: 12px;
    font-family: Roboto-Regular;
    font-weight: 400;
    color: #333;
    line-height: 12px;
}

.file-info {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    margin-top: 10px;
}

.file-text {
    margin-left: 5px;
    flex: 1;
}

.remove-icon {
    margin-left: 10px;
    cursor: pointer;
    transition: color 0.3s;
}

.remove-icon:hover {
    color: #ff4d4f;
}


.flight-card {
    margin: 16px;
    background-color: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }
}

.flight-top {
    position: relative;
    border-bottom: 1px dashed #9f9f9f;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 12px;
    margin-bottom: 12px;
}

.flight-status-tags {
    display: flex;
    align-items: center;
}

.flight-top-name {
    height: 33px;
    display: flex;
    align-items: center;
}

.flight-icon {
    width: 29px !important;
    height: 19px;
    vertical-align: middle;
}

.flight-no {
    font-size: 14px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    margin-left: 8px;
}

.flight-top-arow {
    width: 5px;
    height: 9px;
}

.flight-middle {
    height: 67px;
    display: flex;
    padding: 8px 0;
}

.flight-start,
.flight-status,
.flight-end {
    flex: 1;
}

.flight-start-time,
.flight-end-time {
    margin-top: 15px;
    font-size: 25px;
    font-family: Roboto-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 17px;
}

.flight-top-arow,
.flight-end-time,
.flight-end-a,
.flight-num {
    float: right;
}

.flight-status {
    margin-top: 15px;
    position: relative;
}

.flight-status-show {
    width: 88px;
    height: 25px;
    text-align: center;
    border-radius: 16px;
    font-size: 11px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    line-height: 25px;
    position: absolute;
    top: -5px;
    left: 50%;
    margin-left: -43.5px;
    z-index: 2;
    background-color: #e8f3ff;
    color: #1989fa;
}

.plane-type {
    background-color: #e8f3ff;
    color: #1989fa;
}

.train-type {
    background-color: #f0f9eb;
    color: #67c23a;
}

.flight-start-a,
.flight-end-a {
    margin-top: 6px;
    font-size: 13px;
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    color: rgb(66, 66, 66);
    line-height: 17px;
}

.flight-end {
    position: relative;
}

.flight-end-a {
    position: absolute;
    right: 0;
    top: 31px;
}

.flight-status-arow2 {
    position: absolute;
    width: 8px;
    height: 0;
    margin: 0 auto;
    right: -2px;
    top: -4px;
    border: 1px solid #666;
    transform: rotate(45deg);
}

.flight-status-arow1 {
    position: absolute;
    width: 115px;
    height: 0;
    margin: 0 auto;
    margin-top: 14px;
    border: 1px solid #666;
    left: 50%;
    top: 8%;
    margin-left: -57.5px;
}

.empty-trip {
    margin: 40px 0;
    text-align: center;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 8px;
}

/* 添加改签按钮样式 */
.flight-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #f5f5f5;
}
</style>