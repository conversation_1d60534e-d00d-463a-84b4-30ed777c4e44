package com.cjy.pyp.modules.channel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道退款名额使用记录实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
@Data
@TableName("channel_refund_quota_record")
@Accessors(chain = true)
public class ChannelRefundQuotaRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 操作类型：1-分配权限，2-释放权限
     */
    private Integer actionType;

    /**
     * 名额序号（在该渠道内的排序）
     */
    private Integer quotaSequence;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remarks;

    // 非数据库字段，用于关联查询
    /**
     * 渠道名称
     */
    @TableField(exist = false)
    private String channelName;

    /**
     * 渠道编号
     */
    @TableField(exist = false)
    private String channelCode;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户手机号
     */
    @TableField(exist = false)
    private String userMobile;

    /**
     * 业务员名称
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 订单金额
     */
    @TableField(exist = false)
    private java.math.BigDecimal orderAmount;

    /**
     * 订单状态
     */
    @TableField(exist = false)
    private Integer orderStatus;

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        ASSIGN(1, "分配权限"),
        RELEASE(2, "释放权限");

        private final Integer code;
        private final String desc;

        ActionType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ActionType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (ActionType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 获取操作类型描述
     */
    public String getActionTypeDesc() {
        ActionType type = ActionType.getByCode(this.actionType);
        return type != null ? type.getDesc() : "未知";
    }
}
