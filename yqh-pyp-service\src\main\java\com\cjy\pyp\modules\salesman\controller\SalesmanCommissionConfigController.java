package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.enums.CalculationTypeEnum;
import com.cjy.pyp.modules.salesman.enums.CommissionTypeEnum;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionConfigService;
import com.cjy.pyp.modules.salesman.utils.CommissionEnumUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 业务员佣金配置管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/salesman/commission/config")
@Api(tags = "业务员佣金配置管理")
public class SalesmanCommissionConfigController extends AbstractController {
    
    @Autowired
    private SalesmanCommissionConfigService commissionConfigService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:commission:config:list")
    @ApiOperation(value = "佣金配置列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<SalesmanCommissionConfigEntity> page = commissionConfigService.queryPage(params);
        return R.okList( page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:commission:config:info")
    @ApiOperation(value = "佣金配置信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        SalesmanCommissionConfigEntity config = commissionConfigService.getById(id);
        return R.ok().put("config", config);
    }

    /**
     * 根据业务员ID和佣金类型获取配置
     */
    @RequestMapping("/getByTypeAndSalesman")
    @RequiresPermissions("salesman:commission:config:info")
    @ApiOperation(value = "获取业务员佣金配置", notes = "")
    public R getByTypeAndSalesman(@RequestParam Long salesmanId, 
                                  @RequestParam Integer commissionType,
                                  @CookieValue String appid) {
        SalesmanCommissionConfigEntity config = commissionConfigService.getByTypeAndSalesman(
                salesmanId, commissionType, appid);
        return R.ok().put("config", config);
    }

    /**
     * 获取业务员的所有有效配置
     */
    @RequestMapping("/getEffectiveConfigs/{salesmanId}")
    @RequiresPermissions("salesman:commission:config:info")
    @ApiOperation(value = "获取业务员有效配置", notes = "")
    public R getEffectiveConfigs(@PathVariable("salesmanId") Long salesmanId, @CookieValue String appid) {
        List<SalesmanCommissionConfigEntity> configs = commissionConfigService.getEffectiveConfigsBySalesman(
                salesmanId, appid);
        return R.ok().put("configs", configs);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("salesman:commission:config:save")
    @SysLog("保存佣金配置")
    @ApiOperation(value = "保存佣金配置", notes = "")
    public R save(@RequestBody SalesmanCommissionConfigEntity config, @CookieValue String appid) {
        ValidatorUtils.validateEntity(config);

        // 验证业务规则
        String validationResult = validateConfig(config);
        if (validationResult != null) {
            return R.error(validationResult);
        }

        config.setAppid(appid);
        config.setId(null); // 确保是新增操作

        try {
            commissionConfigService.saveOrUpdateConfig(config);
            return R.ok();
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("salesman:commission:config:update")
    @SysLog("修改佣金配置")
    @ApiOperation(value = "修改佣金配置", notes = "")
    public R update(@RequestBody SalesmanCommissionConfigEntity config, @CookieValue String appid) {
        ValidatorUtils.validateEntity(config);

        // 验证ID是否存在
        if (config.getId() == null) {
            return R.error("配置ID不能为空");
        }

        // 验证业务规则
        String validationResult = validateConfig(config);
        if (validationResult != null) {
            return R.error(validationResult);
        }

        config.setAppid(appid);

        try {
            commissionConfigService.saveOrUpdateConfig(config);
            return R.ok();
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 验证佣金配置的业务规则
     */
    private String validateConfig(SalesmanCommissionConfigEntity config) {
        // 验证佣金类型
        CommissionTypeEnum commissionType = CommissionTypeEnum.getByCode(config.getCommissionType());
        if (commissionType == null) {
            return "无效的佣金类型";
        }

        // 验证计算方式
        CalculationTypeEnum calculationType = CalculationTypeEnum.getByCode(config.getCalculationType());
        if (calculationType == null) {
            return "无效的计算方式";
        }

        // 用户转发佣金只支持固定金额
        if (commissionType == CommissionTypeEnum.USER_FORWARD &&
            calculationType != CalculationTypeEnum.FIXED_AMOUNT) {
            return "用户转发佣金只支持固定金额计算方式";
        }

        // 验证佣金值
        if (config.getCommissionValue() == null || config.getCommissionValue().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return "佣金值必须大于0";
        }

        // 验证百分比范围
        if (calculationType == CalculationTypeEnum.PERCENTAGE &&
            (config.getCommissionValue().compareTo(java.math.BigDecimal.ZERO) <= 0 ||
             config.getCommissionValue().compareTo(java.math.BigDecimal.ONE) > 0)) {
            return "百分比佣金值应在0-1之间";
        }

        // 验证最小最大金额
        if (config.getMinAmount() != null && config.getMaxAmount() != null &&
            config.getMinAmount().compareTo(config.getMaxAmount()) > 0) {
            return "最小金额不能大于最大金额";
        }

        return null; // 验证通过
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("salesman:commission:config:delete")
    @SysLog("删除佣金配置")
    @ApiOperation(value = "删除佣金配置", notes = "")
    public R delete(@RequestBody Long[] ids) {
        commissionConfigService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 批量保存配置
     */
    @RequestMapping("/batchSave")
    @RequiresPermissions("salesman:commission:config:save")
    @SysLog("批量保存佣金配置")
    @ApiOperation(value = "批量保存佣金配置", notes = "")
    public R batchSave(@RequestBody List<SalesmanCommissionConfigEntity> configs, @CookieValue String appid) {
        for (SalesmanCommissionConfigEntity config : configs) {
            config.setAppid(appid);
        }
        
        boolean success = commissionConfigService.batchSaveConfigs(configs);
        if (success) {
            return R.ok();
        } else {
            return R.error("批量保存失败");
        }
    }

    /**
     * 复制配置
     */
    @RequestMapping("/copyConfigs")
    @RequiresPermissions("salesman:commission:config:save")
    @SysLog("复制佣金配置")
    @ApiOperation(value = "复制佣金配置", notes = "")
    public R copyConfigs(@RequestParam Long fromSalesmanId, 
                        @RequestParam Long toSalesmanId,
                        @CookieValue String appid) {
        boolean success = commissionConfigService.copyConfigs(fromSalesmanId, toSalesmanId, appid);
        if (success) {
            return R.ok();
        } else {
            return R.error("复制配置失败");
        }
    }

    /**
     * 删除业务员的所有配置
     */
    @RequestMapping("/deleteConfigsBySalesman")
    @RequiresPermissions("salesman:commission:config:delete")
    @SysLog("删除业务员佣金配置")
    @ApiOperation(value = "删除业务员佣金配置", notes = "")
    public R deleteConfigsBySalesman(@RequestParam Long salesmanId, @CookieValue String appid) {
        boolean success = commissionConfigService.deleteConfigsBySalesman(salesmanId, appid);
        if (success) {
            return R.ok();
        } else {
            return R.error("删除配置失败");
        }
    }

    /**
     * 获取佣金类型选项
     */
    @RequestMapping("/getCommissionTypes")
    @RequiresPermissions("salesman:commission:config:list")
    @ApiOperation(value = "获取佣金类型选项", notes = "")
    public R getCommissionTypes() {
        return R.ok().put("commissionTypes", CommissionEnumUtils.getCommissionTypeOptions());
    }

    /**
     * 获取计算方式选项
     */
    @RequestMapping("/getCalculationTypes")
    @RequiresPermissions("salesman:commission:config:list")
    @ApiOperation(value = "获取计算方式选项", notes = "")
    public R getCalculationTypes(@RequestParam(required = false) Integer commissionType) {
        if (commissionType != null) {
            // 根据佣金类型返回支持的计算方式
            return R.ok().put("calculationTypes",
                    CommissionEnumUtils.getCalculationTypeOptionsByCommissionType(commissionType));
        } else {
            // 返回所有计算方式
            return R.ok().put("calculationTypes", CommissionEnumUtils.getCalculationTypeOptions());
        }
    }

    /**
     * 检查佣金配置是否已存在
     */
    @RequestMapping("/checkExists")
    @RequiresPermissions("salesman:commission:config:list")
    @ApiOperation(value = "检查佣金配置是否存在", notes = "")
    public R checkExists(@RequestParam Long salesmanId,
                        @RequestParam Integer commissionType,
                        @RequestParam(required = false) Long excludeId,
                        @CookieValue String appid) {
        boolean exists = commissionConfigService.existsConfig(salesmanId, commissionType, appid, excludeId);
        return R.ok().put("exists", exists);
    }
}
