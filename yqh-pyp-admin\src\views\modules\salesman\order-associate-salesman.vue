<template>
  <el-dialog :title="dialogTitle" :close-on-click-modal="false" :visible.sync="visible" width="600px">

    <!-- 订单信息 -->
    <div class="order-info">
      <h4>订单信息</h4>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderInfo.orderSn }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ orderInfo.amount }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">微信用户：</span>
            <span class="value">{{ orderInfo.wxUserName }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">下单时间：</span>
            <span class="value">{{ orderInfo.createOn }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 当前关联信息 -->
    <div v-if="mode === 'change' && orderInfo.salesmanName" class="current-association">
      <h4>当前关联业务员</h4>
      <div class="current-salesman">
        <span class="name">{{ orderInfo.salesmanName }}</span>
        <span class="code">（{{ orderInfo.salesmanCode }}）</span>
      </div>
    </div>

    <!-- 业务员选择 -->
    <div class="salesman-selection">
      <h4>{{ mode === 'change' ? '选择新业务员' : '选择业务员' }}</h4>
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-form-item label="业务员" prop="salesmanId">
          <el-select v-model="dataForm.salesmanId" placeholder="请选择业务员" filterable remote
            :remote-method="searchSalesmen" :loading="salesmanLoading" style="width: 100%;">
            <el-option v-for="salesman in salesmanOptions" :key="salesman.id"
              :label="`${salesman.name} (${salesman.code})`" :value="salesman.id">
              <span style="float: left">{{ salesman.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ salesman.code }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关联原因" prop="reason">
          <el-input v-model="dataForm.reason" type="textarea" :rows="3" placeholder="请输入关联原因">
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 推荐业务员 -->
    <div v-if="recommendedSalesman" class="recommended-salesman">
      <h4>推荐业务员</h4>
      <div class="recommendation">
        <el-alert :title="`推荐关联业务员：${recommendedSalesman.name} (${recommendedSalesman.code})`" type="info"
          :closable="false">
          <template slot="description">
            <p>推荐原因：该用户当前绑定的业务员</p>
            <el-button size="mini" type="primary" @click="useRecommended()">使用推荐</el-button>
          </template>
        </el-alert>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      mode: 'associate', // associate: 关联, change: 更换
      orderInfo: {},
      dataForm: {
        salesmanId: '',
        reason: ''
      },
      dataRule: {
        salesmanId: [
          { required: true, message: '业务员不能为空', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '关联原因不能为空', trigger: 'blur' }
        ]
      },
      salesmanOptions: [],
      salesmanLoading: false,
      recommendedSalesman: null
    }
  },
  computed: {
    dialogTitle() {
      return this.mode === 'change' ? '更换业务员' : '关联业务员'
    }
  },
  methods: {
    init(orderInfo, mode) {
      this.orderInfo = orderInfo
      this.mode = mode
      this.visible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.reason = mode === 'change' ? '管理员更换业务员' : '管理员手动关联'

        // 获取推荐业务员
        this.getRecommendedSalesman()
      })
    },

    // 获取推荐业务员
    getRecommendedSalesman() {
      this.$http({
        url: this.$http.adornUrl('/salesman/orderassociation/findSalesmanByUser'),
        method: 'get',
        params: this.$http.adornParams({
          userId: this.orderInfo.userId
        })
      }).then(({ data }) => {
        if (data && data.code ===200 && data.salesmanId) {
          // 获取业务员详细信息
          this.$http({
            url: this.$http.adornUrl(`/salesman/salesman/info/${data.salesmanId}`),
            method: 'get'
          }).then(({ data: salesmanData }) => {
            if (salesmanData && salesmanData.code ===200) {
              this.recommendedSalesman = salesmanData.salesman
            }
          })
        }
      })
    },

    // 使用推荐业务员
    useRecommended() {
      this.dataForm.salesmanId = this.recommendedSalesman.id
      this.salesmanOptions = [this.recommendedSalesman]
    },

    // 搜索业务员
    searchSalesmen(query) {
      if (query !== '') {
        this.salesmanLoading = true
        this.$http({
          url: this.$http.adornUrl('/salesman/salesman/search'),
          method: 'get',
          params: this.$http.adornParams({
            keyword: query,
            limit: 20
          })
        }).then(({ data }) => {
          this.salesmanLoading = false
          if (data && data.code ===200) {
            this.salesmanOptions = data.list || []
          }
        }).catch(() => {
          this.salesmanLoading = false
        })
      } else {
        this.salesmanOptions = []
      }
    },

    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const url = this.mode === 'change'
            ? '/salesman/orderassociation/change'
            : '/salesman/orderassociation/associate'

          this.$http({
            url: this.$http.adornUrl(url),
            method: 'post',
            params: this.$http.adornParams({
              rechargeRecordId: this.orderInfo.id,
              salesmanId: this.dataForm.salesmanId,
              reason: this.dataForm.reason
            })
          }).then(({ data }) => {
            if (data && data.code ===200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.order-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.order-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.info-item {
  margin-bottom: 10px;
}

.info-item .label {
  color: #606266;
  font-weight: 500;
}

.info-item .value {
  color: #303133;
}

.info-item .value.amount {
  color: #E6A23C;
  font-weight: bold;
}

.current-association {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff3cd;
  border-radius: 4px;
}

.current-association h4 {
  margin: 0 0 10px 0;
  color: #856404;
}

.current-salesman .name {
  font-weight: bold;
  color: #856404;
}

.current-salesman .code {
  color: #6c757d;
}

.salesman-selection h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.recommended-salesman {
  margin-top: 20px;
}

.recommended-salesman h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.recommendation {
  margin-bottom: 20px;
}
</style>
