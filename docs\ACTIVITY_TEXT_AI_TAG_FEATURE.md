# ActivityText AI标签功能文档

## 功能概述

为 ActivityText（活动文案）添加 AI 标签功能，实现与 ActivityImage 类似的标签管理能力，包括：
- AI 标签显示
- 单个文案标签设置
- 批量文案标签设置

## 功能特性

### 1. AI 标签显示
- 在文案管理列表中显示 AI 标签列
- 支持多标签显示（逗号分隔）
- 无标签时显示"通用文案"

### 2. 批量设置功能
- 支持选择多条文案进行批量标签设置
- 从活动配置的 AI 标签中选择
- 支持设置为通用文案（不选择任何标签）

### 3. 标签来源
- 使用活动配置中的 AI 标签
- 与图片管理保持一致的标签体系

## 技术实现

### 后端实现

#### 1. 控制器新增方法

**文件**: `ActivityTextController.java`

```java
/**
 * 批量设置AI标签
 */
@RequestMapping("/batchSetAiTag")
@RequiresPermissions("activity:activitytext:update")
@Transactional(rollbackFor = Exception.class)
public R batchSetAiTag(@RequestBody Map<String, Object> params) {
    @SuppressWarnings("unchecked")
    List<String> textIds = (List<String>) params.get("textIds");
    String aiTag = (String) params.get("aiTag");

    if (textIds == null || textIds.isEmpty()) {
        return R.error("请选择要设置标签的文案");
    }

    // 批量更新文案的AI标签
    for (String textId : textIds) {
        ActivityTextEntity text = new ActivityTextEntity();
        text.setId(Long.valueOf(textId));
        text.setAiTag(aiTag);
        text.setUpdateBy(getUserId());
        activityTextService.updateById(text);
    }

    return R.ok("批量设置AI标签成功");
}
```

#### 2. API 端点

- **批量设置标签**: `POST /activity/activitytext/batchSetAiTag`
- **获取活动标签**: `GET /web/activity/activitytext/getAiTags` (复用现有接口)

### 前端实现

#### 1. 页面结构修改

**文件**: `activitytext.vue`

**新增功能按钮**:
```html
<el-button v-if="isAuth('activity:activitytext:update')" type="warning" 
           @click="batchSetAiTagHandle()"
           :disabled="dataListSelections.length <= 0">
  批量设置AI标签
</el-button>
```

**新增表格列**:
```html
<el-table-column prop="aiTag" header-align="center" align="center" width="150" label="AI标签">
  <template slot-scope="scope">
    <div v-if="scope.row.aiTag" class="ai-tags">
      <el-tag v-for="tag in scope.row.aiTag.split(',')" :key="tag" size="mini" style="margin: 2px;">
        {{ tag.trim() }}
      </el-tag>
    </div>
    <span v-else class="no-tags">通用文案</span>
  </template>
</el-table-column>
```

**批量设置弹窗**:
```html
<el-dialog title="批量设置AI标签" :visible.sync="batchAiTagVisible" width="500px" append-to-body>
  <el-form :model="batchAiTagForm" label-width="120px">
    <el-form-item label="选择AI标签">
      <el-select v-model="batchAiTagForm.selectedTags" multiple 
                 placeholder="从活动AI标签中选择" style="width: 100%">
        <el-option v-for="tag in activityAiTags" :key="tag" :label="tag" :value="tag">
        </el-option>
      </el-select>
      <div style="margin-top: 10px; font-size: 12px; color: #909399;">
        <i class="el-icon-info"></i>
        将为选中的 {{ dataListSelections.length }} 条文案设置AI标签。不选择任何标签表示设置为通用文案
      </div>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="batchAiTagVisible = false">取消</el-button>
    <el-button type="primary" @click="confirmBatchSetAiTag" :loading="batchAiTagSubmitting">
      {{ batchAiTagSubmitting ? '设置中...' : '确定' }}
    </el-button>
  </span>
</el-dialog>
```

#### 2. JavaScript 功能

**数据定义**:
```javascript
data() {
  return {
    // 批量设置AI标签相关
    batchAiTagVisible: false,
    batchAiTagSubmitting: false,
    activityAiTags: [],
    batchAiTagForm: {
      selectedTags: []
    }
  }
}
```

**核心方法**:
```javascript
// 加载活动AI标签
loadActivityAiTags() {
  // 从活动配置中获取AI标签列表
}

// 批量设置AI标签
batchSetAiTagHandle() {
  // 验证选择和标签配置，显示设置弹窗
}

// 确认批量设置AI标签
confirmBatchSetAiTag() {
  // 调用后端API进行批量设置
}
```

## 使用流程

### 1. 查看文案标签

1. 进入活动文案管理页面
2. 在列表中查看"AI标签"列
3. 多标签以小标签形式显示
4. 无标签显示"通用文案"

### 2. 批量设置标签

1. 选择要设置标签的文案（支持多选）
2. 点击"批量设置AI标签"按钮
3. 在弹窗中选择要设置的标签（支持多选）
4. 点击"确定"完成设置

### 3. 设置为通用文案

1. 选择要设置的文案
2. 点击"批量设置AI标签"按钮
3. 在弹窗中不选择任何标签
4. 点击"确定"，文案将被设置为通用文案

## 数据结构

### ActivityTextEntity

```java
public class ActivityTextEntity {
    // 其他字段...
    
    /**
     * AI标签（多个标签用逗号分隔）
     */
    private String aiTag;
    
    // getter/setter...
}
```

### 标签格式

- **单标签**: `"美食"`
- **多标签**: `"美食,餐厅,推荐"`
- **通用文案**: `null` 或 `""`

## 权限控制

- **查看标签**: 需要 `activity:activitytext:list` 权限
- **批量设置**: 需要 `activity:activitytext:update` 权限

## 样式设计

```css
.ai-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.no-tags {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}
```

## 与图片标签的一致性

| 特性 | ActivityImage | ActivityText |
|------|---------------|--------------|
| 标签显示 | ✅ | ✅ |
| 批量设置 | ✅ | ✅ |
| 标签来源 | 活动AI标签 | 活动AI标签 |
| 通用标识 | "通用图片" | "通用文案" |
| 多标签支持 | ✅ | ✅ |

## 测试要点

### 功能测试
- [ ] 标签正确显示在列表中
- [ ] 批量设置功能正常工作
- [ ] 多标签正确分割和显示
- [ ] 通用文案正确标识

### 权限测试
- [ ] 无权限用户无法看到批量设置按钮
- [ ] 权限验证正确工作

### 数据测试
- [ ] 标签数据正确保存到数据库
- [ ] 多标签格式正确处理
- [ ] 空标签正确处理

## 后续扩展

1. **标签筛选**: 在列表页面添加按标签筛选功能
2. **标签统计**: 显示各标签下的文案数量
3. **标签管理**: 独立的标签管理界面
4. **标签推荐**: 基于文案内容自动推荐标签
