<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="会场名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="会场名称"></el-input>
      </el-form-item>
      <el-form-item label="是否直播" prop="isLive">
        <el-switch
          :width="35"
          v-model="dataForm.isLive"
          active-color="#60CA7E"
          inactive-color="#dcdfe6"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
      </el-form-item>
      <el-form-item label="直播状态" prop="playStatus"  v-if="dataForm.isLive">
        <el-select v-model="dataForm.playStatus" placeholder="直播状态" filterable>
          <el-option v-for="item in playStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日程封面" prop="cover">
          <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                      accept=".jpg, .jpeg, .png, .gif" :on-success="backgroundSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.cover" :src="dataForm.cover" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
      </el-form-item>
      <el-form-item label="直播时间" v-if="dataForm.isLive">
        <el-date-picker
          style="width: 100%"
          v-model="dataForm.liveTime"
          type="datetime"
          placeholder="请选择直播时间"
          value-format="yyyy/MM/dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
    <el-form-item label="是否显示倒计时" prop="isCountdown"  v-if="dataForm.isLive">
        <el-select v-model="dataForm.isCountdown" placeholder="是否显示倒计时" filterable>
        <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
      <el-form-item label="直播间通知" prop="advice" v-if="dataForm.isLive">
        <el-input v-model="dataForm.advice" placeholder="直播间通知"></el-input>
      </el-form-item>
      <el-form-item label="点击数"  prop="pvCount" v-if="dataForm.isLive">
        <el-input v-model="dataForm.pvCount" disabled placeholder="点击数"></el-input>
      </el-form-item>
      <el-form-item label="访问数"  prop="uvCount" v-if="dataForm.isLive">
        <el-input v-model="dataForm.uvCount" disabled placeholder="访问数"></el-input>
      </el-form-item>
      <el-form-item label="虚拟倍数"  prop="mulity" v-if="dataForm.isLive">
        <el-input v-model="dataForm.mulity" placeholder="虚拟倍数"></el-input>
      </el-form-item>
      <el-form-item label="虚拟点击数" prop="virtualPvCount" v-if="dataForm.isLive">
        <el-input v-model="dataForm.virtualPvCount" placeholder="虚拟点击数"></el-input>
      </el-form-item>
      <el-form-item label="虚拟访问数" prop="virtualUvCount" v-if="dataForm.isLive">
        <el-input v-model="dataForm.virtualUvCount" placeholder="虚拟访问数"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="paixu" >
        <el-input v-model="dataForm.paixu" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item label="弹窗通知显示时间" v-if="dataForm.isLive">
        <el-date-picker
          style="width: 100%"
          v-model="dataForm.notifyTime"
          type="datetime"
          placeholder="弹窗通知显示时间"
          value-format="yyyy/MM/dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="弹窗通知" prop="notify"  v-if="dataForm.isLive">
        <tinymce-editor
          ref="editor"
          v-model="dataForm.notify"
        ></tinymce-editor>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
  import { playStatus } from "@/data/place"
import { yesOrNo } from "@/data/common"
export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
  data() {
    return {
      yesOrNo,
      playStatus: playStatus,
      visible: false,
        url: '',
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        isLive: 0,
        playStatus: 0,
        liveTime: "",
        realityOnline: 0,
        pvCount: 0,
        uvCount: 0,
        virtualPvCount: 0,
        virtualUvCount: 0,
        paixu: 0,
          isCountdown: 1,
        cover: "",
        imGroupId: "",
        advice: "",
        notify: "",
        notifyTime: "",
        mulity:5 ,
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "会场名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init(activityId, id) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
      this.dataForm.activityId = activityId;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placeactivity/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm = data.placeActivity;
            }
          });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placeactivity/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              activityId: this.dataForm.activityId,
              name: this.dataForm.name,
              isLive: this.dataForm.isLive,
              liveTime: this.dataForm.liveTime,
              realityOnline: this.dataForm.realityOnline,
              pvCount: this.dataForm.pvCount,
              uvCount: this.dataForm.uvCount,
              virtualPvCount: this.dataForm.virtualPvCount,
              virtualUvCount: this.dataForm.virtualUvCount,
              cover: this.dataForm.cover,
              imGroupId: this.dataForm.imGroupId,
              advice: this.dataForm.advice,
              notify: this.dataForm.notify,
              playStatus: this.dataForm.playStatus,
              paixu: this.dataForm.paixu,
                'isCountdown': this.dataForm.isCountdown,
                'notifyTime': this.dataForm.notifyTime,
                'mulity': this.dataForm.mulity,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      // 上传成功（背景）
      backgroundSuccessHandle(response, file, fileList) {
          if (response && response.code === 200) {
              this.dataForm.cover = response.url;
              this.$message({
                  message: '上传成功',
                  type: 'success',
              })
          } else {
              this.$message.error(response.msg);
          }
      },
  },
};
</script>
