package com.cjy.pyp.modules.wx.controller;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.wx.form.WxMiniProgramSchemeForm;
import com.cjy.pyp.modules.wx.service.WxMiniProgramService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信小程序相关接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/miniprogram")
@RequiredArgsConstructor
@Api(tags = {"微信小程序"})
public class WxMiniProgramController {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    private WxMiniProgramService wxMiniProgramService;

    /**
     * 生成小程序URL Scheme
     */
    @GetMapping("/generateScheme")
    @ApiOperation(value = "生成小程序URL Scheme", notes = "生成用于H5跳转小程序的URL Scheme")
    public R generateScheme(@CookieValue String appid,
                                  @RequestParam("activityId") Long activityId,
                                  @RequestParam(value = "type", defaultValue = "activity",required = false) String type,
                                  @RequestParam(value = "path", defaultValue = "pages/index/index") String path) {
        try {

            WxMiniProgramSchemeForm form = new WxMiniProgramSchemeForm();
            form.setPath(path);
            form.setActivityId(activityId);
            form.setQuery("id=" + activityId + "&from=h5"+ "&type=" + type);
            form.setEnvVersion("release"); // 默认使用体验版
            form.setIsExpire(true);
            form.setExpireTime(System.currentTimeMillis() / 1000 + 3600); // 1小时后过期

            String scheme = wxMiniProgramService.generateUrlScheme(appid, form);
            return R.ok().put("scheme", scheme);
        } catch (WxErrorException e) {
            logger.error("生成URL Scheme失败", e);
            return R.error("生成URL Scheme失败: " + e.getError().getErrorMsg());
        } catch (Exception e) {
            logger.error("生成URL Scheme异常", e);
            return R.error("生成URL Scheme异常: " + e.getMessage());
        }
    }

    /**
     * 生成小店小程序URL Scheme
     */
    @GetMapping("/generateShopScheme")
    @ApiOperation(value = "生成小店小程序URL Scheme", notes = "生成用于H5跳转小店小程序的URL Scheme")
    public R generateShopScheme(@RequestParam("activityId") Long activityId,
                               @RequestParam("shopAppid") String shopAppid,
                               @RequestParam("path") String path) {
        try {

            WxMiniProgramSchemeForm form = new WxMiniProgramSchemeForm();
            form.setPath(path);
            form.setActivityId(activityId);
            form.setQuery("activityId=" + activityId + "&from=h5");
            form.setEnvVersion("release"); // 默认使用正式版
            form.setIsExpire(true);
            form.setExpireTime(System.currentTimeMillis() / 1000 + 3600); // 1小时后过期

            String scheme = wxMiniProgramService.generateShopUrlScheme(shopAppid, form);
            return R.ok().put("scheme", scheme);
        } catch (WxErrorException e) {
            logger.error("生成小店URL Scheme失败", e);
            return R.error("生成小店URL Scheme失败: " + e.getError().getErrorMsg());
        } catch (Exception e) {
            logger.error("生成小店URL Scheme异常", e);
            return R.error("生成小店URL Scheme异常: " + e.getMessage());
        }
    }


}
