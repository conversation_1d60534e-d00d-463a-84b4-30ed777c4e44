<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="关键词" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activityimage:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>

        <el-button v-if="isAuth('activity:activityimage:save')" type="success"
          @click="batchUploadHandle()">批量上传</el-button>

        <el-button v-if="isAuth('activity:activityimage:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>

        <el-button v-if="isAuth('activity:activityimage:update')" type="warning" @click="batchSetAiTagHandle()"
          :disabled="dataListSelections.length <= 0">批量设置AI标签</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="图片名称">
      </el-table-column>
      <el-table-column prop="mediaUrl" header-align="center" align="center" width="120" label="图片预览">
        <template slot-scope="scope">
          <div v-if="scope.row.mediaUrl" class="image-preview">
            <img :src="scope.row.mediaUrl" :alt="scope.row.name" @click="previewImage(scope.row.mediaUrl)"
              class="preview-img" />
          </div>
          <span v-else class="no-image">无图片</span>
        </template>
      </el-table-column>
      <el-table-column prop="fileSize" header-align="center" align="center" width="100" label="文件大小">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column prop="paixu" header-align="center" align="center" width="80" label="排序">
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" width="100" label="类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 0 ? 'info' : 'success'" size="small">
            {{ scope.row.type === 0 ? '素材' : '成品' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="useCount" header-align="center" align="center" width="120" label="全局使用次数">
      </el-table-column>
      <el-table-column prop="aiTag" header-align="center" align="center" width="150" label="AI标签">
        <template slot-scope="scope">
          <div v-if="scope.row.aiTag" class="ai-tags">
            <el-tag v-for="tag in scope.row.aiTag.split(',')" :key="tag" size="mini" style="margin: 2px;">
              {{ tag.trim() }}
            </el-tag>
          </div>
          <span v-else class="no-tags">通用图片</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" width="200" label="平台使用情况">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="showPlatformUsage(scope.row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="activityTextId" header-align="center" align="center" label="使用文案id">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <!-- 批量上传弹窗 -->
    <image-upload-modal
      :visible.sync="batchUploadVisible"
      :multiple="true"
      :max-count="20"
      @confirm="handleBatchUploadConfirm"
      @close="batchUploadVisible = false"
    ></image-upload-modal>

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="previewVisible" width="60%" append-to-body>
      <img width="100%" :src="previewImageUrl" alt="图片预览">
    </el-dialog>

    <!-- 平台使用情况弹窗 -->
    <el-dialog :visible.sync="platformUsageVisible" width="50%" title="平台使用情况" append-to-body>
      <div v-if="currentImageInfo">
        <h4>图片信息</h4>
        <p><strong>图片名称：</strong>{{ currentImageInfo.name }}</p>
        <p><strong>全局使用次数：</strong>{{ realTotalUsageCount }}</p>

        <h4 style="margin-top: 20px;">各平台使用情况</h4>
        <el-table :data="platformUsageList" border style="width: 100%;">
          <el-table-column prop="platform" header-align="center" align="center" label="平台">
            <template slot-scope="scope">
              <el-tag :type="getPlatformTagType(scope.row.platform)" size="small">
                {{ getPlatformName(scope.row.platform) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="useCount" header-align="center" align="center" label="使用次数">
          </el-table-column>
          <el-table-column prop="firstUsedTime" header-align="center" align="center" label="首次使用时间">
            <template slot-scope="scope">
              {{ scope.row.firstUsedTime ? formatDateTime(scope.row.firstUsedTime) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="lastUsedTime" header-align="center" align="center" label="最后使用时间">
            <template slot-scope="scope">
              {{ scope.row.lastUsedTime ? formatDateTime(scope.row.lastUsedTime) : '-' }}
            </template>
          </el-table-column>
        </el-table>

        <div v-if="platformUsageList.length === 0" style="text-align: center; padding: 20px; color: #999;">
          该图片暂未在任何平台使用
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="platformUsageVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 批量设置AI标签弹窗 -->
    <el-dialog title="批量设置AI标签" :visible.sync="batchAiTagVisible" width="500px" append-to-body>
      <el-form :model="batchAiTagForm" label-width="120px">
        <el-form-item label="选择AI标签">
          <el-select
            v-model="batchAiTagForm.selectedTags"
            multiple
            placeholder="从活动AI标签中选择"
            style="width: 100%">
            <el-option
              v-for="tag in activityAiTags"
              :key="tag"
              :label="tag"
              :value="tag">
            </el-option>
          </el-select>
          <div style="margin-top: 10px; font-size: 12px; color: #909399;">
            <i class="el-icon-info"></i>
            将为选中的 {{ dataListSelections.length }} 张图片设置AI标签。不选择任何标签表示设置为通用图片
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchAiTagVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSetAiTag" :loading="batchAiTagSubmitting">
          {{ batchAiTagSubmitting ? '设置中...' : '确定' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './activityimage-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        name: '',
        appid: '',
        activityId: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      batchUploadVisible: false,
      previewVisible: false,
      previewImageUrl: '',
      platformUsageVisible: false,
      currentImageInfo: null,
      platformUsageList: [],
      realTotalUsageCount: 0,
      // 批量设置AI标签相关
      batchAiTagVisible: false,
      batchAiTagSubmitting: false,
      activityAiTags: [],
      batchAiTagForm: {
        selectedTags: []
      }
    }
  },
  components: {
    AddOrUpdate,
    ImageUploadModal: () => import("@/components/image-upload-modal")
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList()
    this.loadActivityAiTags()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityimage/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'appid': this.$cookie.get('appid'),
          'activityId': this.dataForm.activityId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.dataForm.activityId)
      })
    },

    // 批量上传
    batchUploadHandle() {
      this.batchUploadVisible = true
    },

    // 处理批量上传确认
    handleBatchUploadConfirm(selectedImages) {
      if (!selectedImages || selectedImages.length === 0) {
        this.$message.warning('请选择要上传的图片')
        return
      }

      // 批量保存图片到活动图片表
      const promises = selectedImages.map((image, index) => {
        return this.$http({
          url: this.$http.adornUrl('/activity/activityimage/save'),
          method: 'post',
          data: this.$http.adornData({
            'activityId': this.dataForm.activityId,
            'name': image.fileName || `批量上传图片${index + 1}`,
            'mediaUrl': image.url,
            'fileSize': image.fileSize || 0,
            'type': 0, // 默认为素材类型
            'paixu': index + 1,
            'useCount': 0
          })
        })
      })

      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在保存图片...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      Promise.all(promises).then(responses => {
        loading.close()
        const successCount = responses.filter(res => res.data && res.data.code === 200).length
        const failCount = responses.length - successCount

        if (failCount === 0) {
          this.$message.success(`成功保存 ${successCount} 张图片`)
        } else {
          this.$message.warning(`成功保存 ${successCount} 张图片，失败 ${failCount} 张`)
        }

        // 刷新列表
        this.getDataList()
      }).catch(error => {
        loading.close()
        console.error('批量保存失败:', error)
        this.$message.error('批量保存失败，请重试')
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityimage/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 预览图片
    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
    },

    // 格式化文件大小 - 固定显示为MB，保留2位小数
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },

    // 显示平台使用情况
    showPlatformUsage(imageInfo) {
      this.currentImageInfo = imageInfo
      this.platformUsageVisible = true
      this.getPlatformUsageData(imageInfo.id)
    },

    // 获取平台使用数据
    getPlatformUsageData(imageId) {
      this.$http({
        url: this.$http.adornUrl('/activity/activityimage/platform-usage'),
        method: 'get',
        params: this.$http.adornParams({
          'imageId': imageId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.platformUsageList = data.data || []
          this.realTotalUsageCount = data.totalUsageCount || 0
        } else {
          this.platformUsageList = []
          this.realTotalUsageCount = 0
          this.$message.error(data.msg || '获取平台使用数据失败')
        }
      }).catch(error => {
        console.error('获取平台使用数据失败:', error)
        this.platformUsageList = []
        this.realTotalUsageCount = 0
        this.$message.error('获取平台使用数据失败')
      })
    },

    // 获取平台名称
    getPlatformName(platform) {
      const platformMap = {
        // 基础平台
        'douyin': '抖音',
        'xiaohongshu': '小红书',
        'dianping': '大众点评',
        'meituan': '美团点评',
        'ctrip': '携程',
        'kuaishou': '快手',
        'weixin': '微信朋友圈',
        'weibo': '微博',
        'bilibili': 'B站',
        'zhihu': '知乎',
        'taobao': '淘宝',
        'jingdong': '京东',
        'general': '通用',

        // 点评类平台
        'douyin_review': '抖音点评',
        'xiaohongshu_review': '小红书点评',
        'dianping_review': '大众点评',
        'meituan_review': '美团点评',
        'ctrip_review': '携程点评',

        // 携程特殊类型
        'ctrip_notes': '携程笔记',
        'ctrip_group_buying': '携程团购'
      }
      return platformMap[platform] || platform
    },

    // 获取平台标签类型
    getPlatformTagType(platform) {
      const typeMap = {
        // 基础平台
        'douyin': 'danger',
        'xiaohongshu': 'warning',
        'dianping': 'success',
        'meituan': 'primary',
        'ctrip': 'info',
        'kuaishou': 'danger',
        'weixin': 'success',
        'weibo': 'warning',
        'bilibili': 'primary',
        'zhihu': 'success',
        'taobao': 'warning',
        'jingdong': 'danger',
        'general': 'default',

        // 点评类平台（与基础平台保持一致）
        'douyin_review': 'danger',
        'xiaohongshu_review': 'warning',
        'dianping_review': 'success',
        'meituan_review': 'primary',
        'ctrip_review': 'info',

        // 携程特殊类型
        'ctrip_notes': 'info',
        'ctrip_group_buying': 'info'
      }
      return typeMap[platform] || 'default'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 加载活动AI标签
    loadActivityAiTags() {
      if (!this.dataForm.activityId) return

      this.$http({
        url: this.$http.adornUrl('/web/activity/activitytext/getAiTags'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: this.dataForm.activityId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityAiTags = data.tags || []
        }
      }).catch(err => {
        console.error('加载活动AI标签失败:', err)
        this.activityAiTags = []
      })
    },

    // 批量设置AI标签
    batchSetAiTagHandle() {
      if (this.dataListSelections.length <= 0) {
        this.$message.warning('请选择要设置标签的图片')
        return
      }

      if (this.activityAiTags.length === 0) {
        this.$message.warning('当前活动未配置AI标签，请先在活动管理中配置AI标签')
        return
      }

      this.batchAiTagForm.selectedTags = []
      this.batchAiTagVisible = true
    },

    // 确认批量设置AI标签
    confirmBatchSetAiTag() {
      this.batchAiTagSubmitting = true

      const imageIds = this.dataListSelections.map(item => item.id)
      const aiTag = this.batchAiTagForm.selectedTags.join(',')

      this.$http({
        url: this.$http.adornUrl('/activity/activityimage/batchSetAiTag'),
        method: 'post',
        data: this.$http.adornData({
          imageIds: imageIds,
          aiTag: aiTag
        })
      }).then(({ data }) => {
        this.batchAiTagSubmitting = false
        if (data && data.code === 200) {
          this.$message.success('批量设置AI标签成功')
          this.batchAiTagVisible = false
          this.getDataList() // 刷新列表
        } else {
          this.$message.error(data.msg || '批量设置AI标签失败')
        }
      }).catch(err => {
        this.batchAiTagSubmitting = false
        console.error('批量设置AI标签失败:', err)
        this.$message.error('批量设置AI标签失败')
      })
    }
  }
}
</script>

<style scoped>
.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-img:hover {
  transform: scale(1.1);
  border-color: #409EFF;
}

.no-image {
  color: #999;
  font-size: 12px;
}

.ai-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
}

.no-tags {
  color: #999;
  font-size: 12px;
  font-style: italic;
}
</style>
