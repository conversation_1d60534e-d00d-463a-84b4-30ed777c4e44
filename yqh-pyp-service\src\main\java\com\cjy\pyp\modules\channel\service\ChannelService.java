package com.cjy.pyp.modules.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.wx.entity.WxUser;

import java.util.List;
import java.util.Map;

/**
 * 渠道服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
public interface ChannelService extends IService<ChannelEntity> {

    /**
     * 根据应用ID查询渠道列表
     * @param appid 应用ID
     * @return 渠道列表
     */
    List<ChannelEntity> findByAppid(String appid);

    /**
     * 根据渠道编号查询渠道
     * @param code 渠道编号
     * @param appid 应用ID
     * @return 渠道信息
     */
    ChannelEntity findByCode(String code, String appid);

    /**
     * 检查渠道编号是否存在
     * @param code 渠道编号
     * @param appid 应用ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否存在
     */
    boolean existsByCode(String code, String appid, Long excludeId);

    /**
     * 分页查询渠道列表（包含统计信息）
     * @param params 查询参数
     * @return 分页结果
     */
    List<ChannelEntity> queryPageWithStats(Map<String, Object> params);

    /**
     * 计算渠道层级
     * @param parentId 上级渠道ID
     * @return 层级级别
     */
    Integer calculateLevel(Long parentId);

    /**
     * 更新渠道层级
     * @param channelId 渠道ID
     */
    void updateLevel(Long channelId);

    /**
     * 获取渠道的所有子渠道ID（递归）
     * @param channelId 渠道ID
     * @return 子渠道ID列表
     */
    List<Long> getAllChildChannelIds(Long channelId);

    /**
     * 根据渠道ID查询统计信息
     * @param channelId 渠道ID
     * @param appid 应用ID
     * @return 统计信息
     */
    Map<String, Object> getStatsByChannelId(Long channelId, String appid);
    Map<String, Object> getStatsByChannelIds( List<Long> channelIds, String appid);

    /**
     * 根据应用ID查询渠道总体统计
     * @param appid 应用ID
     * @return 统计信息
     */
    Map<String, Object> getOverallStatsByAppid(String appid);

    /**
     * 检查渠道是否有权限访问指定业务员
     * @param channelId 渠道ID
     * @param salesmanId 业务员ID
     * @return 是否有权限
     */
    boolean hasAccessToSalesman(Long channelId, Long salesmanId);

    /**
     * 检查渠道是否有权限访问指定活动
     * @param channelId 渠道ID
     * @param activityId 活动ID
     * @return 是否有权限
     */
    boolean hasAccessToActivity(Long channelId, Long activityId);

    /**
     * 检查渠道是否有权限访问指定订单
     * @param channelId 渠道ID
     * @param orderId 订单ID
     * @return 是否有权限
     */
    boolean hasAccessToOrder(Long channelId, Long orderId);

    /**
     * 检查渠道是否有权限访问指定客户
     * @param channelId 渠道ID
     * @param wxUserId 微信用户ID
     * @return 是否有权限
     */
    boolean hasAccessToCustomer(Long channelId, Long wxUserId);

    /**
     * 分页查询渠道客户列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<WxUser>  queryCustomerPage(Map<String, Object> params);

    /**
     * 获取渠道客户统计
     * @param params 查询参数
     * @return 统计数据
     */
    Map<String, Object> getCustomerStats(Map<String, Object> params);

    /**
     * 批量更新客户渠道归属
     * @param appid 应用ID
     */
    void batchUpdateCustomerChannel(String appid);
}
