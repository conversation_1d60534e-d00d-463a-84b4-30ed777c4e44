<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <!-- <div class="page-header">
      <van-nav-bar :title="tripInfo.inType == 0 ? '航班详情' : '火车详情'" left-arrow @click-left="goBack" />
    </div> -->

    <div class="trip-info-card">
      <div class="card-title">行程信息</div>
      <div class="flight-info">
        <div class="flight-route">
          <span class="city">{{ tripInfo.inStartPlace }}</span>
          <van-icon name="arrow" />
          <span class="city">{{ tripInfo.inEndPlace }}</span>
        </div>

        <div class="transport-info">
          <div class="detail-item">
            <span class="label">{{ tripInfo.inType == 0 ? '航班号' : '车次' }}：</span>
            <span class="value">{{ tripInfo.inNumber }}</span>
          </div>
          <div class="detail-item">
            <span class="label">出发时间：</span>
            <span class="value">{{ formatDateTime(tripInfo.inStartDate) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">到达时间：</span>
            <span class="value">{{ formatDateTime(tripInfo.inEndDate) }}</span>
          </div>
          <div class="detail-item" v-if="tripInfo.inType == 0">
            <span class="label">出发航站楼：</span>
            <span class="value">{{ tripInfo.inStartTerminal || '未知' }}</span>
          </div>
          <div class="detail-item" v-if="tripInfo.inType == 0">
            <span class="label">到达航站楼：</span>
            <span class="value">{{ tripInfo.inEndTerminal || '未知' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">出票状态：</span>
            <span class="value">
              <van-tag size="large" :type="tripInfo.isBuy == 1 ? 'success' : 'warning'" round>
                {{ isBuy[tripInfo.isBuy] ? isBuy[tripInfo.isBuy].value : '未出票' }}
              </van-tag>
            </span>
          </div>
          <div class="detail-item">
            <span class="label">行程状态：</span>
            <span class="value">
              <van-tag size="large" :type="getStatusTagType(tripInfo)" round>
                {{ tripInfo.inType == 0 ? 
                  (tripPlaneStatus.find(item => item.key === tripInfo.orderStatus) || {}).value : 
                  (tripTrainStatus.find(item => item.key === tripInfo.orderStatus) || {}).value }}
              </van-tag>
            </span>
          </div>
          <!-- <div class="detail-item" v-if="tripInfo.price">
            <span class="label">票价：</span>
            <span class="value">¥{{ tripInfo.price }}</span>
          </div> -->
        </div>
      </div>
    </div>

    
    <!-- 添加改签信息卡片 -->
    <div class="trip-info-card" v-if="tripInfo.isCha === 1">
      <div class="card-title">改签信息</div>
      <div class="flight-info">
        <div class="flight-route">
          <span class="city">{{ tripInfo.chaStartPlace }}</span>
          <van-icon name="arrow" />
          <span class="city">{{ tripInfo.chaEndPlace }}</span>
        </div>

        <div class="transport-info">
          <div class="detail-item">
            <span class="label">{{ tripInfo.inType == 0 ? '航班号' : '车次' }}：</span>
            <span class="value">{{ tripInfo.chaNumber }}</span>
          </div>
          <div class="detail-item">
            <span class="label">出发时间：</span>
            <span class="value">{{ formatDateTime(tripInfo.chaStartDate) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">到达时间：</span>
            <span class="value">{{ formatDateTime(tripInfo.chaEndDate) }}</span>
          </div>
          <div class="detail-item" v-if="tripInfo.inType == 0">
            <span class="label">出发航站楼：</span>
            <span class="value">{{ tripInfo.chaStartTerminal || '未知' }}</span>
          </div>
          <div class="detail-item" v-if="tripInfo.inType == 0">
            <span class="label">到达航站楼：</span>
            <span class="value">{{ tripInfo.chaEndTerminal || '未知' }}</span>
          </div>
          <!-- <div class="detail-item">
            <span class="label">改签状态：</span>
            <span class="value">
              <van-tag type="primary" round>
                {{ getChaStatusText(tripInfo.chaStatus) }}
              </van-tag>
            </span>
          </div> -->
          <div class="detail-item" v-if="tripInfo.chaDate">
            <span class="label">改签日期：</span>
            <span class="value">{{ formatDate(tripInfo.chaDate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <van-button 
        v-if="tripInfo.inType == 0 && tripInfo.orderStatus == 4 && tripInfo.isCha == 0" 
        type="primary" 
        block 
        @click="handleChangeTicket">申请改签</van-button>
      <van-button 
        v-if="tripInfo.isBuy != 1" 
        type="info" 
        block 
        @click="handleEdit" style="margin-top: 12px">编辑行程</van-button>
      <van-button 
        v-if="tripInfo.isBuy != 1" 
        type="primary" 
        block 
        @click="handleDelete" style="margin-top: 12px">删除行程</van-button>
      <van-button type="default" block @click="goBack" style="margin-top: 12px">返回</van-button>
    </div>

    <div class="ticket-image" v-if="tripInfo.image">
      <div class="card-title">票据图片</div>
      <div class="image-container" @click="previewImage(tripInfo.image)">
        <img :src="tripInfo.image" alt="票据图片">
      </div>
    </div>
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import { isBuy, tripPlaneStatus, tripTrainStatus } from "@/data/common";

export default {
  components: { pcheader },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      tripId: undefined,
      tripInfo: {},
      loading: false,
      isBuy,
      tripPlaneStatus,
      tripTrainStatus
    };
  },
  mounted() {
    this.tripId = this.$route.query.tripId;
    this.detailId = this.$route.query.detailId;
    
    if (this.tripId) {
      this.getTripInfo();
    } else {
      this.goBack();
      vant.Toast('行程信息不存在');
    }
  },
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return date.formatDate.format(new Date(dateTime), "yyyy/MM/dd hh:mm");
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      return date.formatDate.format(new Date(dateStr), "yyyy/MM/dd");
    },
    
    goBack() {
      this.$router.go(-1);
    },
    
    getTripInfo() {
      this.loading = true;
      this.$fly
        .get(`/pyp/web/activity/activityguest/getTripById/${this.tripId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.tripInfo = res.result;
          } else {
            vant.Toast(res.msg);
            this.tripInfo = {};
          }
        })
        .catch(() => {
          this.loading = false;
          vant.Toast('获取行程信息失败');
        });
    },
    
    // 获取状态标签类型
    getStatusTagType(item) {
      const status = item.orderStatus;
      if (status === undefined) return 'default';

      // 根据状态返回不同的标签类型
      if (status === 0) return 'primary'; // 待支付
      if (status === 1) return 'success'; // 已支付/已出票
      if (status === 2) return 'danger';  // 已取消/已退票
      return 'default';
    },
    
    handleChangeTicket() {
      // 跳转到改签页面
      this.$router.push({
        path: '/schedules/expert/tripChange',
        query: {
          detailId: this.detailId,
          tripId: this.tripId
        }
      });
    },
    
    handleEdit() {
      // 跳转到编辑页面
      this.$router.push({
        path: '/schedules/expertTripEdit',
        query: {
          detailId: this.detailId,
          tripId: this.tripId
        }
      });
    },
    handleDelete() {
      // confirm
      vant.Dialog.confirm({
        title: '提示',
        message: '确定删除该行程吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonColor: '#1989fa'
      }).then(() => {
        // 删除行程
        this.$fly.get('/pyp/web/activity/activityguest/deleteTrip', {
          tripId: this.tripId
        }).then((res) => {
          if (res.code == 200) {
            vant.Toast('删除行程成功');
            this.goBack();
          } else {
            vant.Toast(res.msg);
          }
        }).catch(() => {
          vant.Toast('删除行程失败');
        });
      });
    },
    
    previewImage(url) {
      vant.ImagePreview({
        images: [url],
        closeable: true
      });
    }
  }
};
</script>

<style lang="less" scoped>
.page-header {
  margin-bottom: 16px;
}

.trip-info-card {
  margin: 16px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
  border-left: 3px solid #1989fa;
  padding-left: 8px;
}

.flight-info {
  padding: 8px 0;
}

.flight-route {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: bold;
}

.city {
  padding: 0 12px;
}

.transport-info {
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 6px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  width: 100px;
  flex-shrink: 0;
}

.value {
  color: #333;
  font-weight: 500;
}

.action-buttons {
  margin: 16px;
}

.ticket-image {
  margin: 16px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.image-container {
  width: 100%;
  text-align: center;
  margin-top: 12px;
}

.image-container img {
  max-width: 100%;
  border-radius: 4px;
}
</style>