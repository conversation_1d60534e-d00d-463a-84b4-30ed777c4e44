package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金结算批次服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public interface SalesmanCommissionSettlementService extends IService<SalesmanCommissionSettlementEntity> {

    /**
     * 分页查询结算批次列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<SalesmanCommissionSettlementEntity> queryPage(Map<String, Object> params);

    /**
     * 根据批次号查询结算批次
     * @param batchNo 批次号
     * @param appid 应用ID
     * @return 结算批次
     */
    SalesmanCommissionSettlementEntity getByBatchNo(String batchNo, String appid);

    /**
     * 查询结算统计信息
     * @param params 查询参数
     * @return 统计信息
     */
    Map<String, Object> getSettlementStats(Map<String, Object> params);

    /**
     * 创建结算批次
     * @param salesmanIds 业务员ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param remarks 备注
     * @param appid 应用ID
     * @return 结算批次
     */
    SalesmanCommissionSettlementEntity createSettlementBatch(List<Long> salesmanIds, String startTime, 
                                                             String endTime, String remarks, String appid);

    /**
     * 执行结算
     * @param batchNo 批次号
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean executeSettlement(String batchNo, String appid);

    /**
     * 取消结算
     * @param batchNo 批次号
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean cancelSettlement(String batchNo, String appid);

    /**
     * 生成批次号
     * @return 批次号
     */
    String generateBatchNo();
}
