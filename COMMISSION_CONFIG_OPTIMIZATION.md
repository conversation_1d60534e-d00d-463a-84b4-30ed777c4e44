# 佣金配置保存逻辑优化

## 问题描述

原来的 `saveOrUpdateConfig` 方法存在以下问题：

1. **逻辑混乱**：当存在旧配置时，只是更新部分字段，没有考虑完整的业务逻辑
2. **审计字段缺失**：更新时没有正确处理 `updateBy` 和 `updateOn` 等审计字段
3. **业务规则不清晰**：新增和编辑的处理逻辑混在一起，容易出错
4. **错误处理不当**：没有明确的错误提示和异常处理

## 优化方案

### 1. 重构Service层逻辑

#### 原来的问题代码：
```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean saveOrUpdateConfig(SalesmanCommissionConfigEntity config) {
    // 检查是否已存在相同类型的配置
    QueryWrapper<SalesmanCommissionConfigEntity> wrapper = new QueryWrapper<>();
    wrapper.eq("salesman_id", config.getSalesmanId())
           .eq("commission_type", config.getCommissionType())
           .eq("appid", config.getAppid());
    
    if (config.getId() != null) {
        wrapper.ne("id", config.getId());
    }
    
    SalesmanCommissionConfigEntity existing = this.getOne(wrapper);
    if (existing != null) {
        // 问题：只更新部分字段，没有考虑审计字段
        existing.setCalculationType(config.getCalculationType());
        existing.setCommissionValue(config.getCommissionValue());
        // ... 其他字段
        return this.updateById(existing);
    } else {
        return this.save(config);
    }
}
```

#### 优化后的代码：
```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean saveOrUpdateConfig(SalesmanCommissionConfigEntity config) {
    if (config.getId() != null) {
        // 编辑模式：检查是否与其他配置冲突
        if (existsConfig(config.getSalesmanId(), config.getCommissionType(), 
                       config.getAppid(), config.getId())) {
            throw new RuntimeException("该业务员的此类型佣金配置已存在");
        }
        return this.updateById(config); // 使用MyBatis-Plus的自动填充功能
    } else {
        // 新增模式：检查是否已存在相同类型的配置
        if (existsConfig(config.getSalesmanId(), config.getCommissionType(), 
                       config.getAppid(), null)) {
            throw new RuntimeException("该业务员的此类型佣金配置已存在，请直接编辑现有配置");
        }
        return this.save(config); // 使用MyBatis-Plus的自动填充功能
    }
}
```

### 2. 新增独立的存在性检查方法

```java
@Override
public boolean existsConfig(Long salesmanId, Integer commissionType, String appid, Long excludeId) {
    QueryWrapper<SalesmanCommissionConfigEntity> wrapper = new QueryWrapper<>();
    wrapper.eq("salesman_id", salesmanId)
           .eq("commission_type", commissionType)
           .eq("appid", appid);
    
    if (excludeId != null) {
        wrapper.ne("id", excludeId);
    }
    
    return this.count(wrapper) > 0;
}
```

### 3. 优化Controller层逻辑

#### 分离保存和更新逻辑：
```java
/**
 * 保存
 */
@RequestMapping("/save")
public R save(@RequestBody SalesmanCommissionConfigEntity config, @CookieValue String appid) {
    ValidatorUtils.validateEntity(config);
    
    String validationResult = validateConfig(config);
    if (validationResult != null) {
        return R.error(validationResult);
    }
    
    config.setAppid(appid);
    config.setId(null); // 确保是新增操作
    
    try {
        commissionConfigService.saveOrUpdateConfig(config);
        return R.ok();
    } catch (Exception e) {
        return R.error(e.getMessage());
    }
}

/**
 * 修改
 */
@RequestMapping("/update")
public R update(@RequestBody SalesmanCommissionConfigEntity config, @CookieValue String appid) {
    ValidatorUtils.validateEntity(config);
    
    if (config.getId() == null) {
        return R.error("配置ID不能为空");
    }
    
    String validationResult = validateConfig(config);
    if (validationResult != null) {
        return R.error(validationResult);
    }
    
    config.setAppid(appid);
    
    try {
        commissionConfigService.saveOrUpdateConfig(config);
        return R.ok();
    } catch (Exception e) {
        return R.error(e.getMessage());
    }
}
```

#### 提取公共验证方法：
```java
private String validateConfig(SalesmanCommissionConfigEntity config) {
    // 验证佣金类型
    CommissionTypeEnum commissionType = CommissionTypeEnum.getByCode(config.getCommissionType());
    if (commissionType == null) {
        return "无效的佣金类型";
    }
    
    // 验证计算方式
    CalculationTypeEnum calculationType = CalculationTypeEnum.getByCode(config.getCalculationType());
    if (calculationType == null) {
        return "无效的计算方式";
    }
    
    // 用户转发佣金只支持固定金额
    if (commissionType == CommissionTypeEnum.USER_FORWARD && 
        calculationType != CalculationTypeEnum.FIXED_AMOUNT) {
        return "用户转发佣金只支持固定金额计算方式";
    }
    
    // 验证佣金值
    if (config.getCommissionValue() == null || 
        config.getCommissionValue().compareTo(BigDecimal.ZERO) <= 0) {
        return "佣金值必须大于0";
    }
    
    // 验证百分比范围
    if (calculationType == CalculationTypeEnum.PERCENTAGE && 
        (config.getCommissionValue().compareTo(BigDecimal.ZERO) <= 0 || 
         config.getCommissionValue().compareTo(BigDecimal.ONE) > 0)) {
        return "百分比佣金值应在0-1之间";
    }
    
    // 验证最小最大金额
    if (config.getMinAmount() != null && config.getMaxAmount() != null && 
        config.getMinAmount().compareTo(config.getMaxAmount()) > 0) {
        return "最小金额不能大于最大金额";
    }
    
    return null; // 验证通过
}
```

### 4. 新增配置存在性检查接口

```java
@RequestMapping("/checkExists")
@RequiresPermissions("salesman:commission:config:list")
@ApiOperation(value = "检查佣金配置是否存在", notes = "")
public R checkExists(@RequestParam Long salesmanId,
                    @RequestParam Integer commissionType,
                    @RequestParam(required = false) Long excludeId,
                    @CookieValue String appid) {
    boolean exists = commissionConfigService.existsConfig(salesmanId, commissionType, appid, excludeId);
    return R.ok().put("exists", exists);
}
```

## 优化效果

### 1. 逻辑清晰
- 新增和编辑操作分离，逻辑更清晰
- 统一的验证逻辑，减少重复代码
- 明确的错误处理和异常信息

### 2. 数据一致性
- 利用MyBatis-Plus的自动填充功能处理审计字段
- 正确的事务控制，保证数据一致性
- 完整的唯一性检查，避免重复配置

### 3. 用户体验
- 清晰的错误提示信息
- 前端可以通过检查接口提前验证
- 更好的表单验证和用户引导

### 4. 代码质量
- 单一职责原则，每个方法职责明确
- 可测试性更好，便于单元测试
- 更好的可维护性和扩展性

## 使用建议

1. **前端集成**：可以在表单提交前调用 `checkExists` 接口进行预检查
2. **错误处理**：统一处理后端返回的错误信息，给用户友好的提示
3. **数据验证**：充分利用前端验证和后端验证的双重保障
4. **测试覆盖**：针对各种边界情况编写完整的测试用例

这次优化解决了原有代码的逻辑问题，提高了系统的稳定性和用户体验。
