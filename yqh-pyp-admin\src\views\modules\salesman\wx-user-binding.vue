<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.wxUserName" placeholder="微信用户昵称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="绑定状态" clearable>
          <el-option label="有效" :value="1"></el-option>
          <el-option label="已失效" :value="0"></el-option>
          <el-option label="已解绑" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.bindingType" placeholder="绑定方式" clearable>
          <el-option label="二维码扫描" :value="1"></el-option>
          <el-option label="邀请链接" :value="2"></el-option>
          <el-option label="手动绑定" :value="3"></el-option>
          <el-option label="系统分配" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增绑定</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="warning" @click="batchUnbind()" :disabled="dataListSelections.length <= 0">批量解绑</el-button>
        <el-button type="success" @click="exportData()">导出数据</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <!-- <div class="stats-overview" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.totalBindings || 0 }}</div>
              <div class="stats-label">总绑定数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.activeBindings || 0 }}</div>
              <div class="stats-label">有效绑定</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.todayBindings || 0 }}</div>
              <div class="stats-label">今日新增</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.expiringBindings || 0 }}</div>
              <div class="stats-label">即将过期</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div> -->

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="customerName" header-align="center" align="center" label="微信用户">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.wxUserName }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.wxUserMobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="业务员">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.salesmanName }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.salesmanCode }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="bindingType" header-align="center" align="center" label="绑定方式">
        <template slot-scope="scope">
          <el-tag :type="getBindingTypeTagType(scope.row.bindingType)">
            {{ getBindingTypeText(scope.row.bindingType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bindingTime" header-align="center" align="center" width="150" label="绑定时间">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="expiryTime" header-align="center" align="center" width="150" label="失效时间">
        <template slot-scope="scope">
          <span v-if="scope.row.expiryTime">{{ scope.row.expiryTime }}</span>
          <span v-else style="color: #67C23A;">永久有效</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="viewHistory(scope.row.wxUserId)">历史</el-button>
          <el-button v-if="scope.row.status === 1" type="text" size="small"
            @click="unbindHandle(scope.row)">解绑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <!-- 绑定历史弹窗 -->
    <binding-history v-if="bindingHistoryVisible" ref="bindingHistory"></binding-history>
  </div>
</template>

<script>
import AddOrUpdate from './wx-user-binding-add-or-update'
import BindingHistory from './wx-user-binding-history'

export default {
  data() {
    return {
      dataForm: {
        customerName: '',
        salesmanName: '',
        status: '',
        bindingType: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      bindingHistoryVisible: false,
      overallStats: {}
    }
  },
  components: {
    AddOrUpdate,
    BindingHistory
  },
  activated() {
    this.getDataList()
    // this.getOverallStats()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/wxuserbinding/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'wxUserName': this.dataForm.wxUserName,
          'salesmanName': this.dataForm.salesmanName,
          'status': this.dataForm.status,
          'bindingType': this.dataForm.bindingType
        })
      }).then(({ data }) => {
        if (data && data.code ===200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    // 获取统计数据
    getOverallStats() {
      this.$http({
        url: this.$http.adornUrl('/salesman/wxuserbinding/stats'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code ===200) {
          this.overallStats = data.stats || {}
        }
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },

    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },

    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },

    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },

    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/wxuserbinding/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code ===200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 解绑
    unbindHandle(row) {
      this.$prompt('请输入解绑原因', '解绑确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '解绑原因不能为空'
      }).then(({ value }) => {
        this.$http({
          url: this.$http.adornUrl('/salesman/wxuserbinding/unbind'),
          method: 'post',
          params: this.$http.adornParams({
            wxUserId: row.wxUserId,
            salesmanId: row.salesmanId,
            reason: value
          })
        }).then(({ data }) => {
          if (data && data.code ===200) {
            this.$message({
              message: '解绑成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 批量解绑
    batchUnbind() {
      this.$confirm('确定要批量解绑选中的绑定关系吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现批量解绑逻辑
        this.$message.info('批量解绑功能开发中...')
      })
    },

    // 查看绑定历史
    viewHistory(wxUserId) {
      this.bindingHistoryVisible = true
      this.$nextTick(() => {
        this.$refs.bindingHistory.init(wxUserId)
      })
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    },

    // 获取绑定方式文本
    getBindingTypeText(type) {
      const typeMap = {
        1: '二维码扫描',
        2: '邀请链接',
        3: '手动绑定',
        4: '系统分配'
      }
      return typeMap[type] || '未知'
    },

    // 获取绑定方式标签类型
    getBindingTypeTagType(type) {
      const typeMap = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info'
      }
      return typeMap[type] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '已失效',
        1: '有效',
        2: '已解绑'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'danger',
        1: 'success',
        2: 'info'
      }
      return typeMap[status] || ''
    }
  }
}
</script>

<style>
.stats-overview .stats-card {
  text-align: center;
  padding: 20px;
}

.stats-overview .stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stats-overview .stats-label {
  font-size: 14px;
  color: #666;
}
</style>
