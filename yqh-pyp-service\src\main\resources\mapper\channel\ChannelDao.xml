<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.channel.dao.ChannelDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.channel.entity.ChannelEntity" id="channelMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="description" column="description"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactMobile" column="contact_mobile"/>
        <result property="contactEmail" column="contact_email"/>
        <result property="address" column="address"/>
        <result property="status" column="status"/>
        <result property="level" column="level"/>
        <result property="parentId" column="parent_id"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="remarks" column="remarks"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <result property="parentName" column="parent_name"/>
        <result property="childrenCount" column="children_count"/>
        <result property="salesmanCount" column="salesman_count"/>
        <result property="activeSalesmanCount" column="active_salesman_count"/>
        <result property="totalOrders" column="total_orders"/>
        <result property="activityOrders" column="activity_orders"/>
        <result property="rechargeOrders" column="recharge_orders"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalCommission" column="total_commission"/>
    </resultMap>

    <!-- 分页查询渠道列表（包含统计信息） -->
    <select id="selectPageWithStats" parameterType="java.util.Map" resultMap="channelMap">
        SELECT
            c.id,
            c.name,
            c.code,
            c.description,
            c.contact_name,
            c.contact_mobile,
            c.contact_email,
            c.address,
            c.status,
            c.level,
            c.parent_id,
            c.commission_rate,
            c.remarks,
            c.appid,
            c.create_on,
            c.create_by,
            c.update_on,
            c.update_by,
            parent_c.name as parent_name,
            COALESCE(children_stats.children_count, 0) as children_count,
            COALESCE(salesman_stats.salesman_count, 0) as salesman_count,
            COALESCE(salesman_stats.active_salesman_count, 0) as active_salesman_count,
            COALESCE(order_stats.total_orders, 0) as total_orders,
            COALESCE(order_stats.activity_orders, 0) as activity_orders,
            COALESCE(order_stats.recharge_orders, 0) as recharge_orders,
            COALESCE(order_stats.total_amount, 0) as total_amount,
            COALESCE(order_stats.total_commission, 0) as total_commission
        FROM channel c
        LEFT JOIN channel parent_c ON c.parent_id = parent_c.id
        LEFT JOIN (
            SELECT
                parent_id,
                COUNT(*) as children_count
            FROM channel
            WHERE status = 1
            GROUP BY parent_id
        ) children_stats ON c.id = children_stats.parent_id
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(*) as salesman_count,
                COUNT(CASE WHEN s.status = 1 THEN 1 END) as active_salesman_count
            FROM salesman s
            WHERE s.channel_id IS NOT NULL
            GROUP BY s.channel_id
        ) salesman_stats ON c.id = salesman_stats.channel_id
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(arr.id) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                COALESCE(SUM(arr.pay_amount), 0) as total_amount,
                COALESCE(SUM(scr.commission_amount), 0) as total_commission
            FROM salesman s
            LEFT JOIN activity_recharge_record arr ON s.id = arr.salesman_id AND arr.status = 1
            LEFT JOIN salesman_commission_record scr ON arr.id = scr.business_id AND scr.settlement_status != 2
            WHERE s.channel_id IS NOT NULL
            GROUP BY s.channel_id
        ) order_stats ON c.id = order_stats.channel_id
        WHERE c.appid = #{appid}
        <if test="channelIds != null and channelIds.size() > 0">
            AND c.id IN
            <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
        <if test="name != null and name.trim() != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code.trim() != ''">
            AND c.code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="status != null and status.trim() != ''">
            AND c.status = #{status}
        </if>
        <if test="parentId != null and parentId.trim() != ''">
            AND c.parent_id = #{parentId}
        </if>
        ORDER BY c.create_on DESC
    </select>

    <!-- 根据渠道ID查询统计信息 -->
    <select id="selectStatsByChannelId" resultType="java.util.Map">
        SELECT
            COALESCE(salesman_stats.salesman_count, 0) as salesmanCount,
            COALESCE(salesman_stats.active_salesman_count, 0) as activeSalesmanCount,
            COALESCE(order_stats.total_orders, 0) as totalOrders,
            COALESCE(order_stats.activity_orders, 0) as activityOrders,
            COALESCE(order_stats.recharge_orders, 0) as rechargeOrders,
            COALESCE(order_stats.total_amount, 0) as totalAmount,
            COALESCE(order_stats.total_commission, 0) as totalCommission
        FROM channel c
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(*) as salesman_count,
                COUNT(CASE WHEN s.status = 1 THEN 1 END) as active_salesman_count
            FROM salesman s
            WHERE s.channel_id = #{channelId}
            GROUP BY s.channel_id
        ) salesman_stats ON c.id = salesman_stats.channel_id
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(arr.id) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                COALESCE(SUM(arr.pay_amount), 0) as total_amount,
                COALESCE(SUM(scr.commission_amount), 0) as total_commission
            FROM salesman s
            LEFT JOIN activity_recharge_record arr ON s.id = arr.salesman_id AND arr.status = 1
            LEFT JOIN salesman_commission_record scr ON arr.id = scr.business_id AND scr.settlement_status != 2
            WHERE s.channel_id = #{channelId}
            GROUP BY s.channel_id
        ) order_stats ON c.id = order_stats.channel_id
        WHERE c.id = #{channelId} AND c.appid = #{appid}
    </select>

    <!-- 根据渠道ID查询统计信息 -->
    <select id="selectStatsByChannelIds" resultType="java.util.Map">
        SELECT
            COALESCE(salesman_stats.salesman_count, 0) as salesmanCount,
            COALESCE(salesman_stats.active_salesman_count, 0) as activeSalesmanCount,
            COALESCE(order_stats.total_orders, 0) as totalOrders,
            COALESCE(order_stats.activity_orders, 0) as activityOrders,
            COALESCE(order_stats.recharge_orders, 0) as rechargeOrders,
            COALESCE(order_stats.total_amount, 0) as totalAmount,
            COALESCE(order_stats.total_commission, 0) as totalCommission
        FROM channel c
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(*) as salesman_count,
                COUNT(CASE WHEN s.status = 1 THEN 1 END) as active_salesman_count
            FROM salesman s
            WHERE s.channel_id  IN
            <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
            GROUP BY s.channel_id
        ) salesman_stats ON c.id = salesman_stats.channel_id
        LEFT JOIN (
            SELECT
                s.channel_id,
                COUNT(arr.id) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                COALESCE(SUM(arr.pay_amount), 0) as total_amount,
                COALESCE(SUM(scr.commission_amount), 0) as total_commission
            FROM salesman s
            LEFT JOIN activity_recharge_record arr ON s.id = arr.salesman_id AND arr.status = 1
            LEFT JOIN salesman_commission_record scr ON arr.id = scr.business_id AND scr.settlement_status != 2
            WHERE s.channel_id  IN
            <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
            GROUP BY s.channel_id
        ) order_stats ON c.id = order_stats.channel_id
        WHERE c.id  IN
        <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
            #{channelId}
        </foreach>
        AND c.appid = #{appid}
    </select>

    <!-- 查询渠道的直接子渠道ID -->
    <select id="selectAllChildChannelIds" resultType="java.lang.Long">
        SELECT id FROM channel WHERE parent_id = #{channelId}
    </select>

    <!-- 根据应用ID查询渠道总体统计 -->
    <select id="selectOverallStatsByAppid" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT c.id) as channelCount,
            COUNT(DISTINCT CASE WHEN c.status = 1 THEN c.id END) as activeChannelCount,
            COALESCE(salesman_stats.total_salesman_count, 0) as totalSalesmanCount,
            COALESCE(salesman_stats.active_salesman_count, 0) as activeSalesmanCount,
            COALESCE(order_stats.total_orders, 0) as totalOrders,
            COALESCE(order_stats.activity_orders, 0) as activityOrders,
            COALESCE(order_stats.recharge_orders, 0) as rechargeOrders,
            COALESCE(order_stats.total_amount, 0) as totalAmount,
            COALESCE(order_stats.total_commission, 0) as totalCommission
        FROM channel c
        LEFT JOIN (
            SELECT
                COUNT(*) as total_salesman_count,
                COUNT(CASE WHEN status = 1 THEN 1 END) as active_salesman_count
            FROM salesman s
            WHERE s.channel_id IS NOT NULL AND s.appid = #{appid}
        ) salesman_stats ON 1=1
        LEFT JOIN (
            SELECT
                COUNT(arr.id) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                COALESCE(SUM(arr.pay_amount), 0) as total_amount,
                COALESCE(SUM(scr.commission_amount), 0) as total_commission
            FROM salesman s
            LEFT JOIN activity_recharge_record arr ON s.id = arr.salesman_id AND arr.status = 1
            LEFT JOIN salesman_commission_record scr ON arr.id = scr.business_id AND scr.settlement_status != 2
            WHERE s.channel_id IS NOT NULL AND s.appid = #{appid}
        ) order_stats ON 1=1
        WHERE c.appid = #{appid}
    </select>

    <!-- 检查订单是否属于指定的业务员列表 -->
    <select id="checkOrderBelongsToSalesmen" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM activity_recharge_record arr
        WHERE arr.id = #{orderId}
        AND arr.salesman_id IN
        <foreach collection="salesmanIds" item="salesmanId" open="(" separator="," close=")">
            #{salesmanId}
        </foreach>
    </select>

    <!-- 检查客户是否绑定了指定的业务员列表 -->
    <select id="checkCustomerBelongsToSalesmen" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM wx_user_salesman_binding wsb
        WHERE wsb.wx_user_id = #{wxUserId}
        AND wsb.status = 1
        AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
        AND wsb.salesman_id IN
        <foreach collection="salesmanIds" item="salesmanId" open="(" separator="," close=")">
            #{salesmanId}
        </foreach>
    </select>

    <!-- 分页查询渠道客户列表 -->
    <select id="selectCustomerPage" resultType="com.cjy.pyp.modules.wx.entity.WxUser">
        SELECT
            wu.*,
            s.name as salesman_name,
            s.code as salesman_code,
            c.name as channel_name,
            wsb.binding_time,
            wsb.status as binding_status
        FROM wx_user wu
        LEFT JOIN wx_user_salesman_binding wsb ON wu.id = wsb.wx_user_id AND wsb.status = 1
        LEFT JOIN salesman s ON wsb.salesman_id = s.id
        LEFT JOIN `channel` c ON wu.channel_id = c.id
        <where>
            <if test="appid != null and appid != ''">
                AND wu.appid = #{appid}
            </if>
            <if test="channelIds != null and channelIds.size() > 0">
                AND wu.channel_id IN
                <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                    #{channelId}
                </foreach>
            </if>
            <if test="nickname != null and nickname != ''">
                AND wu.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <if test="mobile != null and mobile != ''">
                AND wu.mobile LIKE CONCAT('%', #{mobile}, '%')
            </if>
            <if test="salesmanName != null and salesmanName != ''">
                AND s.name LIKE CONCAT('%', #{salesmanName}, '%')
            </if>
            <if test="channelId != null and channelId != ''">
                AND wu.channel_id = #{channelId}
            </if>
            <if test="bindingStatus != null and bindingStatus != ''">
                <if test="bindingStatus == '1'">
                    AND wsb.id IS NOT NULL
                </if>
                <if test="bindingStatus == '0'">
                    AND wsb.id IS NULL
                </if>
            </if>
        </where>
        ORDER BY wu.create_on DESC
    </select>

    <!-- 查询渠道客户统计 -->
    <select id="selectCustomerStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT wu.id) as totalCustomers,
            COUNT(DISTINCT CASE WHEN wu.status = 1 THEN wu.id END) as activeCustomers,
            COUNT(DISTINCT CASE WHEN wsb.id IS NOT NULL THEN wu.id END) as boundCustomers,
            COUNT(DISTINCT CASE WHEN arr.id IS NOT NULL THEN wu.id END) as payingCustomers,
            COUNT(DISTINCT CASE WHEN wu.channel_id IS NOT NULL THEN wu.id END) as channelCustomers,
            COALESCE(SUM(CASE WHEN arr.status = 1 THEN arr.pay_amount ELSE 0 END), 0) as totalRevenue
        FROM wx_user wu
        LEFT JOIN wx_user_salesman_binding wsb ON wu.id = wsb.wx_user_id AND wsb.status = 1
        LEFT JOIN activity_recharge_record arr ON wu.id = arr.user_id AND arr.status = 1
        <where>
            <if test="appid != null and appid != ''">
                AND wu.appid = #{appid}
            </if>
            <if test="channelIds != null and channelIds.size() > 0">
                AND wu.channel_id IN
                <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                    #{channelId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
