package com.cjy.pyp.modules.activity.service;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.vo.ActivityExpirationStatusVo;
import com.cjy.pyp.modules.activity.vo.RenewalOrderVo;

import java.util.Date;
import java.util.List;

/**
 * 活动过期管理服务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
public interface ActivityExpirationService {

    /**
     * 检查活动是否过期
     * @param activityId 活动ID
     * @return 是否过期
     */
    boolean isActivityExpired(Long activityId);

    /**
     * 检查活动是否即将过期（7天内）
     * @param activityId 活动ID
     * @return 是否即将过期
     */
    boolean isActivityExpiringSoon(Long activityId);

    /**
     * 获取活动过期状态详情
     * @param activityId 活动ID
     * @return 过期状态详情
     */
    ActivityExpirationStatusVo getActivityExpirationStatus(Long activityId);

    /**
     * 批量获取用户活动的过期状态
     * @param userId 用户ID
     * @param appid 应用ID
     * @return 活动过期状态列表
     */
    List<ActivityExpirationStatusVo> getUserActivitiesExpirationStatus(Long userId, String appid);

    /**
     * 更新活动过期状态
     * @param activityId 活动ID
     */
    void updateActivityExpirationStatus(Long activityId);

    /**
     * 批量更新过期状态（定时任务使用）
     */
    void batchUpdateExpirationStatus();

    /**
     * 设置活动过期时间
     * @param activityId 活动ID
     * @param expirationTime 过期时间
     * @return 操作结果
     */
    R setActivityExpirationTime(Long activityId, Date expirationTime);

    /**
     * 延长活动有效期
     * @param activityId 活动ID
     * @param days 延长天数
     * @return 操作结果
     */
    R extendActivityExpiration(Long activityId, Integer days);

    /**
     * 创建续费订单
     * @param renewalOrderVo 续费订单信息
     * @param userId 用户ID
     * @return 操作结果
     */
    R createRenewalOrder(RenewalOrderVo renewalOrderVo, Long userId);

    /**
     * 处理续费支付成功回调
     * @param orderSn 订单号
     * @param payTransaction 支付流水号
     * @param payType 支付方式
     * @return 操作结果
     */
    R handleRenewalPaymentSuccess(String orderSn, String payTransaction, String payType);

    /**
     * 获取即将过期的活动列表（用于发送提醒）
     * @param days 提前天数
     * @return 即将过期的活动列表
     */
    List<ActivityEntity> getActivitiesExpiringSoon(Integer days);

    /**
     * 获取已过期的活动列表
     * @return 已过期的活动列表
     */
    List<ActivityEntity> getExpiredActivities();

    /**
     * 发送过期提醒通知
     * @param activityId 活动ID
     * @param days 剩余天数
     * @return 操作结果
     */
    R sendExpirationReminder(Long activityId, Integer days);

    /**
     * 发送过期通知
     * @param activityId 活动ID
     * @return 操作结果
     */
    R sendExpirationNotification(Long activityId);
}
