<template>
  <div>
    <el-dialog title="更改房型" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm"  ref="dataForm" :rules="dataRule" label-width="120px">
        <el-row class="row">
          <el-col :span="12">
            <el-form-item label="新的酒店" prop="hotelActivityId">
              <el-select v-model="dataForm.hotelActivityId" @change="hotelChange">
                <el-option v-for="item in hotels" :key="item.id" :label="item.hotelName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新的房型" prop="hotelActivityRoomId">
              <el-select v-model="dataForm.hotelActivityRoomId" @change="roomChange">
                <el-option v-for="item in rooms" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
            <el-form-item label="酒店时间" prop="inDate">
              <el-date-picker
              v-model="times"
             :picker-options="pickerOptions"
              @change="dateChange"
              value-format="yyyy/MM/dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
            </el-form-item>
        <el-row class="row">
          <el-col :span="24">
              <el-form-item label="新的房间号" prop="roomNumber">
                <div style="display: flex;align-items: center;width: 400px;">
                <el-input v-model="dataForm.roomNumber" placeholder="新的房间号" ></el-input>
                <el-button type="primary" @click="addRoomNumber" style="margin-left: 10px;">选择已有房间号</el-button>
              </div>
              </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="indexRoom && indexRoom.bedNumber > 1" label="房间类型" prop="roomType">
              <el-select v-model="dataForm.roomType">
                <el-option v-for="item in roomType" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="indexRoom && indexRoom.bedNumber > 1 && dataForm.roomType != 0" label="校验男女"
              prop="checkSex">
              <el-switch v-model="dataForm.checkSex">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item>
        <el-input size="mini" v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="mini" v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="select">确认</el-button>
      </span>
    </el-dialog>
    <hotelactivityroomassignselectrommnumber v-if="hotelactivityroomassignselectrommnumberVisible"
      ref="hotelactivityroomassignselectrommnumber" @select="selectRoomNumber">
    </hotelactivityroomassignselectrommnumber>
  </div>
</template>

<script>
import hotelactivityroomassignselectrommnumber from './hotelactivityroomassign-selectrommnumber.vue'
import {orderStatus} from '@/data/common'
import { roomAssignStatus, roomType } from "@/data/room.js";
export default {
  data() {
    return {
      hotelactivityroomassignselectrommnumberVisible: false,
      times: [],
      hotels: [],
      rooms: [],
      indexRoom: {},
      roomType,
      roomAssignStatus,
      orderStatus,
      visible: false,
      dataForm: {
        contact: '',
        mobile: '',
        hotelActivityId: '',
        hotelActivityRoomId: '',
        roomNumber: '',
        activityId: '',
        inDate: '',
        outDate: '',
        roomType: 0,
        dayNumber: 1,
        checkSex: true,
      },
      dataList: [],
      dataListLoading: false,
      dataRule: {
        hotelActivityRoomId: [
          { required: true, message: '会议酒店房型id不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        roomNumber: [
          { required: true, message: '新的房间号不能为空', trigger: 'blur' }
        ],
        hotelActivityId: [
          { required: true, message: '会议酒店id不能为空', trigger: 'blur' }
        ],
        roomType: [
          { required: true, message: '房间类型不能为空', trigger: 'blur' }
        ],
        inDate: [
          { required: true, message: '入住日期不能为空', trigger: 'blur' }
        ],
        outDate: [
          { required: true, message: '退房日期不能为空', trigger: 'blur' }
        ],
        checkSex: [
          { required: true, message: '检验男女不能为空', trigger: 'blur' }
        ]
      },
      timeOptionRange: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            this.timeOptionRange = minDate
          }
          if (maxDate) {
            this.timeOptionRange = null
          }
        },
        disabledDate: (time) => {
          // 获取选中时间
          let timeOptionRange = this.timeOptionRange
          if (timeOptionRange) {
            return time.getTime() === timeOptionRange.getTime()
          }
        }
     }
    }
  },
  components: {
    tagsEditor: () => import("@/components/tags-editor"),
    hotelactivityroomassignselectrommnumber,
  },
  methods: {
    addRoomNumber() {
      this.hotelactivityroomassignselectrommnumberVisible = true
      this.$nextTick(() => {
        this.$refs.hotelactivityroomassignselectrommnumber.init(this.dataForm.activityId, this.dataForm.hotelActivityId, this.dataForm.hotelActivityRoomId)
      })
    },
    selectRoomNumber(v) {
      // 把房间号塞到数组里面
      this.dataForm.roomNumber = v
    },
    dateChange(v) {
      this.dataForm.inDate = v[0];
      this.dataForm.outDate = v[1];
      console.log(v)
      var times = new Date(v[1]).getTime() / 1000 - new Date(v[0]).getTime() / 1000;
      this.dataForm.dayNumber = parseInt(times / 60 / 60 / 24); //相差天数
    },
    init(id) {
      this.dataForm.id = id

      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/info/${this.dataForm.id}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm = data.hotelActivityRoomAssign;
          this.times = [data.hotelActivityRoomAssign.inDate,data.hotelActivityRoomAssign.outDate];
          this.$set(this.dataForm, "checkSex", true);
          if (this.dataForm.hotelActivityId) {
            this.findRoom(this.dataForm.hotelActivityId);
          }
          this.findHotel()
        }
      })
      this.visible = true
    },
    findHotel() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivity/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.hotels = data.result
        }
      })
    },
    hotelChange(v) {
      this.dataForm.hotelActivityRoomId = '';
      this.findRoom(v);
    },
    findRoom(v) {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroom/findByHotelActivityId/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.rooms = data.result
          if (this.dataForm.hotelActivityRoomId) {
            this.indexRoom = this.rooms.filter(e => e.id == this.dataForm.hotelActivityRoomId)[0];
          }
        }
      })
    },
    roomChange(v) {
      this.indexRoom = this.rooms.filter(e => e.id == v)[0];
      this.dataForm.roomType = this.indexRoom.bedNumber > 1 ? this.dataForm.roomType : 0;
      this.dataForm.checkSex = this.indexRoom.bedNumber > 1 ? this.dataForm.checkSex : true;
    },
    // 选择酒店
    select() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirm(`确认修改分房信息?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/change`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({ data }) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          })
        }
      })
    },
    closeDialog() {
      this.$emit('refreshDataList')
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    }
  }
}
</script>
