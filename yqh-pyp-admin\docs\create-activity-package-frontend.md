# 创建活动套餐前端功能完善文档

## 概述

本文档描述了为支持创建活动套餐功能而对前端管理界面进行的完善和修改。

## 功能特性

### 1. 套餐类型支持
- 支持两种套餐类型：
  - **充值次数套餐（packageType = 1）**：为现有活动增加使用次数
  - **创建活动套餐（packageType = 2）**：购买后自动创建新活动并分配使用次数

### 2. 套餐管理界面增强
- 添加套餐类型筛选功能
- 在套餐列表中显示套餐类型标签
- 套餐添加/编辑表单中增加套餐类型选择
- 添加"购买创建活动套餐"快捷入口

### 3. 充值记录管理增强
- 支持创建活动套餐类型的充值记录显示
- 新增"创建的活动ID"和"活动名称"列（仅在有创建活动套餐记录时显示）
- 充值类型筛选中增加"创建活动套餐"选项
- 详情显示中包含创建活动套餐的相关信息

### 4. 创建活动套餐购买页面
- 全新的套餐购买界面，专门用于创建活动套餐
- 套餐选择卡片式展示，支持热门和推荐标签
- 活动信息填写表单，包括活动名称和模板选择
- 订单确认和一键购买功能

## 修改的文件

### 1. 套餐管理相关
- `src/views/modules/activity/activityrechargepackage.vue`
  - 添加套餐类型筛选
  - 添加套餐类型列显示
  - 添加购买创建活动套餐按钮
  
- `src/views/modules/activity/activityrechargepackage-add-or-update.vue`
  - 添加套餐类型选择字段
  - 更新表单验证规则
  - 更新数据提交逻辑

### 2. 充值记录相关
- `src/views/modules/activity/activityrechargerecord.vue`
  - 添加创建活动套餐类型支持
  - 新增创建活动相关列
  - 更新详情显示逻辑
  - 更新充值类型映射

- `src/views/modules/activity/activityrechargerecord-gift.vue`
  - 限制套餐选择只显示充值次数套餐

### 3. 新增页面
- `src/views/modules/activity/create-activity-package-order.vue`
  - 全新的创建活动套餐购买页面
  - 套餐选择和活动信息填写
  - 订单创建和确认功能

### 4. 路由配置
- `src/router/index.js`
  - 添加创建活动套餐购买页面路由

## 主要功能说明

### 套餐类型区分
```javascript
// 套餐类型枚举
const PackageType = {
  RECHARGE_COUNT: 1,    // 充值次数套餐
  CREATE_ACTIVITY: 2    // 创建活动套餐
}

// 充值类型枚举
const RechargeType = {
  PACKAGE: 1,                    // 套餐充值
  CUSTOM: 2,                     // 自定义充值
  GIFT: 3,                       // 系统赠送
  CREATE_ACTIVITY_PACKAGE: 4     // 创建活动套餐
}
```

### API接口使用
```javascript
// 获取创建活动套餐列表
GET /web/activity/recharge/packages/createActivity

// 创建活动套餐订单
POST /web/activity/recharge/createActivityPackageOrder
{
  "packageId": 5,
  "activityName": "我的新活动",
  "activityTemplateId": 1,
  "remarks": "备注信息",
  "repeatToken": "unique-token-123"
}
```

### 界面特性
1. **套餐选择**：卡片式展示，支持热门/推荐标签
2. **价格显示**：现价、原价、折扣信息完整展示
3. **活动信息**：活动名称必填，模板选择可选
4. **订单确认**：显示套餐详情和总价
5. **响应式设计**：适配不同屏幕尺寸

## 使用流程

### 管理员操作流程
1. 进入"充值套餐管理"页面
2. 创建或编辑套餐时选择"创建活动套餐"类型
3. 设置套餐名称、描述、价格、使用次数等信息
4. 保存套餐配置

### 用户购买流程
1. 点击"购买创建活动套餐"按钮
2. 选择合适的套餐
3. 填写活动名称和选择模板（可选）
4. 确认订单信息
5. 点击"立即购买"创建订单
6. 系统跳转到充值记录页面查看订单状态

## 注意事项

1. **套餐类型限制**：创建活动套餐不能用于现有活动的充值
2. **活动名称**：必须填写，长度限制2-50个字符
3. **模板选择**：可选功能，不选择则使用默认配置
4. **权限控制**：需要相应的权限才能创建和管理套餐
5. **数据兼容**：兼容旧版本数据，packageType默认为1

## 测试建议

1. 测试套餐类型筛选功能
2. 测试创建不同类型的套餐
3. 测试创建活动套餐购买流程
4. 测试充值记录的显示和筛选
5. 验证权限控制是否正常
6. 测试响应式布局在不同设备上的表现

## 后续扩展

1. 支付集成：集成支付系统完成真实支付
2. 模板管理：完善活动模板的管理功能
3. 订单状态：增加更多订单状态的处理
4. 通知功能：订单状态变更时的通知机制
5. 统计报表：套餐销售和使用情况的统计分析
