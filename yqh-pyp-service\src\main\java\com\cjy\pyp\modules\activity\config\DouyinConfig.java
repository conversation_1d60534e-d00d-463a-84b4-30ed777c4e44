package com.cjy.pyp.modules.activity.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 抖音开放平台配置类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-24
 */
@Configuration
public class DouyinConfig {

    /**
     * 抖音开放平台App Key
     */
    @Value("${douyin.appKey:}")
    private String appKey;

    /**
     * 抖音开放平台App Secret
     */
    @Value("${douyin.appSecret:}")
    private String appSecret;

    /**
     * 是否启用抖音分享功能
     */
    @Value("${douyin.enabled:true}")
    private Boolean enabled;

    /**
     * 分享类型：0-分享到编辑页，1-直接分享到发布页
     */
    @Value("${douyin.shareToPublish:1}")
    private Integer shareToPublish;

    /**
     * client_token缓存时间（秒）
     */
    @Value("${douyin.clientTokenCacheTime:7200}")
    private Integer clientTokenCacheTime;

    /**
     * ticket缓存时间（秒）
     */
    @Value("${douyin.ticketCacheTime:7200}")
    private Integer ticketCacheTime;

    /**
     * API请求超时时间（毫秒）
     */
    @Value("${douyin.requestTimeout:30000}")
    private Integer requestTimeout;

    // Getters
    public String getAppKey() {
        return appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public Integer getShareToPublish() {
        return shareToPublish;
    }

    public Integer getClientTokenCacheTime() {
        return clientTokenCacheTime;
    }

    public Integer getTicketCacheTime() {
        return ticketCacheTime;
    }

    public Integer getRequestTimeout() {
        return requestTimeout;
    }

    /**
     * 验证配置是否完整
     */
    public boolean isConfigValid() {
        return appKey != null && !appKey.trim().isEmpty() && 
               appSecret != null && !appSecret.trim().isEmpty();
    }

    /**
     * 获取缓存键前缀
     */
    public String getCacheKeyPrefix() {
        return "douyin:" + appKey + ":";
    }

    /**
     * 获取client_token缓存键
     */
    public String getClientTokenCacheKey() {
        return getCacheKeyPrefix() + "client_token";
    }

    /**
     * 获取ticket缓存键
     */
    public String getTicketCacheKey() {
        return getCacheKeyPrefix() + "ticket";
    }
}
