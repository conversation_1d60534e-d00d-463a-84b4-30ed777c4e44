<template>
  <div style="background: white;margin-bottom: 20px;">
    <van-row gutter="20">
      <van-col span="16">
        <van-swipe :autoplay="3000" style="width: 100%">
          <van-swipe-item v-for="(image, index) in activityInfo.appFileList" :key="index">
            <van-image width="100%" :src="image.url"> </van-image>
          </van-swipe-item>
        </van-swipe>
      </van-col>
      <van-col span="8">
        <div style="margin-top: 20px">
          <van-cell title="会议名称" :value="activityInfo.name" />
          <van-cell v-if="activityInfo.address" title="会议地点" :value="activityInfo.address" />
          <van-cell title="会议开始时间" :value="activityInfo.startTime" />
          <van-cell title="会议结束时间" :value="activityInfo.endTime" />
          <van-cell title="登录信息">
            <template #default>
              <van-button @click="showLogin" v-if="!userInfo" type="info" size="small" round>未登录(点击登录)</van-button>
              <van-button v-else type="primary" size="small" round>{{ userInfo.username }}({{ userInfo.mobile
              }})</van-button>
            </template>
          </van-cell>
          <van-cell v-if="activityId != '1736999159118508033'" title="报名状态">
            <template #default>
              <van-button v-if="isPay == 1" @click="turnApply" type="primary" size="small" round>已报名</van-button>
              <van-button v-else-if="isPay == 2" type="warning" size="small" round @click="turnApply">已报名未交费</van-button>
              <van-button v-else type="info" size="small" round @click="turnApply">未报名</van-button>
              <!-- <van-button v-else type="info" size="small" round @click="turnApply">未报名(点击报名)</van-button> -->
            </template>
          </van-cell>
        </div>
      </van-col>
    </van-row>
    <van-tabs class="nav" v-model="cmsId" @click="onClick">
      <van-tab v-for="item in cmsList" :key="item.id" :title="item.title" :name="item.id">
      </van-tab>
    </van-tabs>
    <pclogin />
  </div>
</template>

<script>
import { isURL } from "@/js/validate";
import deviceVersion from "@/js/deviceVersion.js";
import pclogin from "@/components/pclogin.vue";
export default {
  components: {
    pclogin,
  },
  data() {
    return {
      activityId: undefined,
      bannerIndex: 0,
      cmsId: undefined,
      cmsList: [],
      cmsInfo: {},
      activityInfo: {},
      // userInfo: {},
      // isPay: 0,
    };
  },
  computed: {
    userInfo: {
      get() {
        return this.$store.state.user.userInfo;
      },
      set(val) {
        this.$store.commit("user/update", val);
      },
    },
    isPay: {
      get() {
        return this.$store.state.apply.isPay;
      },
      set(val) {
        this.$store.commit("apply/update", val);
      },
    },
  },
  mounted() {
    let logAddTime = new Date().getTime();
    localStorage.setItem("logAddTime", logAddTime + 3600000);
    this.openid = this.$cookie.get("openid");
    this.activityId = this.$route.query.id;
    this.cmsId = sessionStorage.getItem("cmsId") || this.$route.query.cmsId;
    this.checkLogin();
    this.getActivityInfo();
    this.activityLogCount();
  },
  methods: {
    showLogin() {
      this.$store.commit("user/changePcLogin", true);
    },
    checkLogin() {
      this.$fly.get(`/pyp/web/user/checkLogin`).then((res) => {
        this.userInfo = res.result;
        if (this.userInfo) {
          this.checkApply();
        } else {
          // this.$store.commit("user/changePcLogin", true);
          this.isPay = 0;
        }
      });
    },
    // 轮播图事件监听
    bannerIndexChange(index) {
      this.bannerIndex = index;
    },
    checkApply() {
      this.$fly
        .get("/pyp/web/activity/activityuserapplyorder/checkApply", {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.isPay = res.isPay;
            if (this.isPay == 1) {

            } else if (this.isPay == 2) {
              vant.Dialog.alert({
                title: "提示",
                message: "您有一笔注册费待支付，点击跳转",
              }).then(() => {
                // on close
                this.$router.push({
                  name: "applySuccess",
                  query: {
                    orderId: res.result,
                    id: this.activityId
                  },
                });
              });
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },

    changeShowType(c) {
      this.$emit("changeShowType", c);
    },

    turnApply() {
      var cmsInfos = this.cmsList.filter((q) => q.model && q.model.includes("applyIndex"));
      if (cmsInfos) {
        let cmsInfo = cmsInfos[0];
        sessionStorage.setItem("cmsId", cmsInfo.id);
        var result = cmsInfo.model.replace(
          "${activityId}",
          cmsInfo.activityId
        );
        this.$router.push(JSON.parse(result));
      } else {
        vant.Toast("暂未开启报名");
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            document.title = this.activityInfo.name;
            // 处理倒计时
            let t1 = this.activityInfo.startTime;
            let dateEnd = new Date(t1.replace(/-/g, "/"));
            let dateBegin = new Date(); //当前时间数据
            let dateCompare = dateEnd.getTime() - dateBegin.getTime();
            this.dateCompare = dateCompare > 0 ? dateCompare : 0;
            this.getCmsList();
          } else {
            // vant.Toast(res.msg);
            // this.activityInfo = {};
          }
        });
    },
    // pv，uv记录
    activityLogCount() {
      let logAddTime = localStorage.getItem("logAddTime");
      let nowTime = new Date().getTime();
      if (nowTime > logAddTime) {
        this.$fly
          .post(`/pyp/activity/activityviewlog/count`, {
            activityId: this.activityId,
            device: deviceVersion.getVersion(),
          })
          .then((res) => { });
      }
    },
    getCmsList() {
      this.$fly
        .get(`/pyp/cms/cms/findByActivityId/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.cmsList = res.result;
            // this.onClick(this.cmsList[0].id);
            if (this.cmsId) {
              this.changeShowType(this.cmsId);
            } else {
              this.changeShowType(this.cmsList[0].id);
            }
          } else {
            vant.Toast(res.msg);
            this.cmsList = [];
          }
        });
    },
    onClick(item) {
      console.log(item);
      sessionStorage.setItem("cmsId", item);
      this.cmsInfo = this.cmsList.filter((q) => q.id == item)[0];
      if (this.cmsInfo.url && isURL(this.cmsInfo.url)) {
        location.href = this.cmsInfo.url;
      } else if (
        this.cmsInfo.model &&
        this.isJSON(this.cmsInfo.model)
      ) {
        var result = this.cmsInfo.model.replace(
          "${activityId}",
          this.cmsInfo.activityId
        );
        this.$router.push(JSON.parse(result));
      } else {
        if (this.$route.name == 'cmsIndex') {
          this.changeShowType(item);
        } else {
          this.$router.push({
            name: 'cmsIndex',
            query: {
              id: this.activityId,
              cmsId: item
            }
          })
        }
      }
    },
    // 轮播图事件监听
    bannerIndexChange(index) {
      this.bannerIndex = index;
    },
    isJSON(v) {
      try {
        JSON.parse(v);
        return true;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.coutdown {
  .van-count-down {
    line-height: 24px;
  }

  .van-cell__title,
  .van-cell__value {
    text-align: center;
  }
}

.nav /deep/ .van-tabs__wrap {
  height: 60px;
  // border-radius: 12px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
</style>