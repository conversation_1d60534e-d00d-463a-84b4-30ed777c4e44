<template>
  <div class="mod-config">
    <el-row :gutter="24">
      <el-col :span="17">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('cms:cms:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('cms:cms:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <!-- <el-button type="success" @click="$router.go(-1)">返回</el-button> -->
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="activityName" header-align="center" align="center" label="会议名称">
      </el-table-column>
      <el-table-column prop="title" header-align="center" align="center" label="标题">
      </el-table-column>
      <el-table-column prop="pname" header-align="center" align="center" label="父节点">
      </el-table-column>
      <el-table-column prop="mobileIcon" header-align="center" align="center" label="移动端图标">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.mobileIcon)" :src="scope.row.mobileIcon" />
          <a :href="scope.row.mobileIcon" target="_blank" v-else>{{scope.row.mobileIcon}}</a>
        </div>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="url" header-align="center" align="center" label="外部链接">
      </el-table-column>
      <el-table-column prop="model" header-align="center" align="center" label="模块跳转">
        <div slot-scope="scope">
          <span>{{scope.row.model | modelFilter}}</span>
        </div>
      </el-table-column>
      <!-- <el-table-column show-overflow-tooltip prop="content" header-align="center" align="center" label="内容">
      </el-table-column> -->
      <!-- <el-table-column prop="mobileContent" header-align="center" align="center" label="移动端内容">
      </el-table-column> -->
      <el-table-column prop="paixu" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
            <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
            <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button></template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
      </el-col> 
      <el-col :span="7">
      
  <div class="mod-user">
    <div style="display: flex;align-items: center;justify-content: center;">
    <vue-qrcode :options="{ width: 120 }" :value="iframeSrc"></vue-qrcode>
    <div style="margin-left: 20px;font-size: 24px;font-weight: 600;">扫码通过手机预览</div>
  </div>
        
    <iframe ref="iframeaaa" style="width:90%;height: 800px" :src="iframeSrc" ></iframe>
  </div></el-col>
  </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
  import AddOrUpdate from './cms-add-or-update'
  export default {
    data() {
      return {
        appid: '',
        iframeSrc: '',
        dataForm: {
          key: '',
          activityId: undefined
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        activityInfo: {}
      }
    },
    components: {
      AddOrUpdate,
      VueQrcode
    },
    filters: {
      modelFilter(v) {
        var modelList = [
          {name: '个人中心',value:'{"name": "meMine","query": {"id": "${activityId}"}}'},
          {name: '参会报名',value:'{"name": "applyIndex","query": {"id": "${activityId}"}}'},
          {name: '会议议程',value:'{"name": "schedulesIndex","query": {"id": "${activityId}"}}'},
          { name: '会议议程(新)', value: '{"name": "schedulesIndexNew","query": {"id": "${activityId}"}}' },
          {name: '嘉宾列表',value:'{"name": "schedulesExperts","query": {"id": "${activityId}"}}'},
          {name: '酒店预订',value:'{"name": "hotelIndex","query": {"id": "${activityId}"}}'},
          {name: '会议直播',value:'{"name": "livesIndex","query": {"id": "${activityId}"}}'},
          {name: '考试&问卷',value:'{"name": "examIndex","query": {"id": "${activityId}"}}'},
          {name: '展商列表',value:'{"name": "merchantIndex","query": {"id": "${activityId}"}}'},
        { name: '导航列表', value: '{"name": "divNav","query": {"id": "${activityId}"}}' },
        { name: '座位查询', value: '{"name": "divZuowei","query": {"id": "${activityId}"}}' },
        { name: '专家信息确认', value: '{"name": "expertIndexCheck","query": {"id": "${activityId}"}}' },
        ];
        let value = modelList.filter(item => item.value === v)
        if (value.length >= 1) {
          return value[0].name;
        }
      }
    },
    activated() {
    this.appid = this.$cookie.get("appid");
      this.dataForm.activityId = this.$route.query.activityId;
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/cms/cms/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'key': this.dataForm.key,
            'activityId': this.dataForm.activityId
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      this.getAccountInfo()
      },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
          this.iframeSrc = this.wxAccount.baseUrl + 'cms/index?pc=false&id=' + this.dataForm.activityId;
    //       this.iframeSrc = (this.activityInfo.appid == 'wx0770d56458b33c67' ? 
    // 'http://fjmeeting.com/mp_fjsd/#/cms/index?pc=false&id=' : this.activityInfo.appid == 'wx0f8d389094ac6910' ? 
    // 'http://ztmeeting.com/mp/#/cms/index?pc=false&id=' : 'http://zhaoshengniuren.com/mp_yqh/#/cms/index?pc=false&id=') + this.dataForm.activityId;
          
  }
      });
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
      this.getActivity()
        }
      });
    },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
        })
      },
      // 删除
      deleteHandle(id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/cms/cms/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      isImageUrl(url) {
        return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
      }
    }
  }
</script>
