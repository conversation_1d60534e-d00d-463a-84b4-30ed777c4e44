<template>
  <div class="mod-commission-record">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.totalRecords || 0 }}</div>
            <div class="stats-label">总记录数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.totalAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">总佣金金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.unsettledAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">待结算金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.settledAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">已结算金额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.commissionType" placeholder="佣金类型" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="创建活动佣金" :value="1"></el-option>
          <el-option label="充值次数佣金" :value="2"></el-option>
          <el-option label="用户转发佣金" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.settlementStatus" placeholder="结算状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="未结算" :value="0"></el-option>
          <el-option label="已结算" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="getStats()">刷新统计</el-button>
        <el-button type="primary" @click="exportData()">导出</el-button>
        <el-button type="warning" @click="batchSettlement()" :disabled="dataListSelections.length === 0">批量结算</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="业务员">
      </el-table-column>
      <el-table-column prop="commissionTypeDesc" header-align="center" align="center" label="佣金类型">
      </el-table-column>
      <el-table-column prop="commissionAmount" header-align="center" align="center" label="佣金金额">
        <template slot-scope="scope">
          ¥{{ scope.row.commissionAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" header-align="center" align="center" label="订单金额">
        <template slot-scope="scope">
          <span v-if="scope.row.orderAmount">¥{{ scope.row.orderAmount.toFixed(2) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="commissionRate" header-align="center" align="center" label="佣金比例">
        <template slot-scope="scope">
          <span v-if="scope.row.commissionRate">{{ (scope.row.commissionRate * 100).toFixed(2) }}%</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatusDesc" header-align="center" align="center" label="结算状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.settlementStatus)">
            {{ scope.row.settlementStatusDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementBatchNo" header-align="center" align="center" label="结算批次">
        <template slot-scope="scope">
          <span v-if="scope.row.settlementBatchNo">{{ scope.row.settlementBatchNo }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="businessTime" header-align="center" align="center" width="150" label="业务时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetails(scope.row)">详情</el-button>
          <el-button v-if="scope.row.settlementStatus === 0" type="text" size="small" @click="singleSettlement(scope.row)">结算</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 详情弹窗 -->
    <el-dialog title="佣金记录详情" :visible.sync="detailsDialogVisible" width="70%" class="commission-detail-dialog">
      <div class="detail-content">
        <!-- 基本信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-user"></i>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">业务员：</span>
                <span class="value">{{ selectedRecord.salesmanName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">业务员编号：</span>
                <span class="value">{{ selectedRecord.salesmanCode || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">用户：</span>
                <span class="value">{{ selectedRecord.userName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">活动：</span>
                <span class="value">{{ selectedRecord.activityName || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 佣金信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-money"></i>
            <span>佣金信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">佣金类型：</span>
                <span class="value">{{ selectedRecord.commissionTypeDesc || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">计算方式：</span>
                <span class="value">{{ selectedRecord.calculationTypeDesc || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">佣金金额：</span>
                <span class="value amount">¥{{ (selectedRecord.commissionAmount || 0).toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">订单金额：</span>
                <span class="value amount" v-if="selectedRecord.orderAmount">¥{{ selectedRecord.orderAmount.toFixed(2) }}</span>
                <span class="value" v-else>-</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">佣金比例：</span>
                <span class="value" v-if="selectedRecord.commissionRate">{{ (selectedRecord.commissionRate * 100).toFixed(2) }}%</span>
                <span class="value" v-else>-</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 结算信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-finished"></i>
            <span>结算信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">结算状态：</span>
                <el-tag :type="getStatusTagType(selectedRecord.settlementStatus)" size="medium">
                  {{ selectedRecord.settlementStatusDesc }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">结算批次：</span>
                <span class="value">{{ selectedRecord.settlementBatchNo || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">结算时间：</span>
                <span class="value">{{ selectedRecord.settlementTime || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">业务时间：</span>
                <span class="value">{{ selectedRecord.businessTime || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="detail-item">
                <span class="label">结算备注：</span>
                <span class="value">{{ selectedRecord.settlementRemarks || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 其他信息卡片 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-info"></i>
            <span>其他信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ selectedRecord.createOn || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="detail-item">
                <span class="label">描述：</span>
                <span class="value">{{ selectedRecord.description || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-dialog>

    <!-- 批量结算弹窗 -->
    <el-dialog title="批量结算" :visible.sync="batchSettlementDialogVisible" width="50%">
      <el-form :model="settlementForm" :rules="settlementRules" ref="settlementForm" label-width="100px">
        <el-form-item label="结算备注" prop="remarks">
          <el-input v-model="settlementForm.remarks" type="textarea" :rows="3" placeholder="请输入结算备注"></el-input>
        </el-form-item>
        <el-form-item label="选中记录">
          <span>共选中 {{ dataListSelections.length }} 条记录，总金额：¥{{ selectedTotalAmount.toFixed(2) }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSettlementDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSettlement()" :loading="settlementLoading">确定结算</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      salesmanId: '',
      salesmanName: '',
      dataForm: {
        salesmanId: '',
        salesmanName: '',
        commissionType: '',
        settlementStatus: ''
      },
      dateRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      stats: {},
      detailsDialogVisible: false,
      selectedRecord: {},
      batchSettlementDialogVisible: false,
      settlementLoading: false,
      settlementForm: {
        remarks: ''
      },
      settlementRules: {
        remarks: [
          { required: true, message: '请输入结算备注', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 计算选中记录的总金额
    selectedTotalAmount() {
      return this.dataListSelections.reduce((total, record) => {
        return total + (record.commissionAmount || 0)
      }, 0)
    }
  },
  activated() {
    this.salesmanId = this.$route.query.salesmanId;
    this.salesmanName = this.$route.query.salesmanName;
    this.getDataList()
    this.getStats()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'salesmanId': this.salesmanId,
        'salesmanName': this.dataForm.salesmanName,
        'commissionType': this.dataForm.commissionType,
        'settlementStatus': this.dataForm.settlementStatus
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取统计数据
    getStats() {
      let params = {
        'salesmanId': this.salesmanId,
        'salesmanName': this.dataForm.salesmanName,
        'commissionType': this.dataForm.commissionType,
        'settlementStatus': this.dataForm.settlementStatus
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/stats'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.stats = data.stats
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 查看详情
    viewDetails(row) {
      this.selectedRecord = row
      this.detailsDialogVisible = true
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'warning'  // 未结算
        case 1: return 'success'  // 已结算
        case 2: return 'danger'   // 已取消
        default: return 'info'
      }
    },
    // 导出数据
    exportData() {
      // TODO: 实现数据导出功能
      this.$message.info('导出功能开发中...')
    },
    // 单独结算
    singleSettlement(row) {
      this.$confirm(`确定要结算业务员 ${row.salesmanName} 的这条佣金记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doSingleSettlement(row)
      }).catch(() => {
        // 取消操作
      })
    },
    // 执行单独结算
    doSingleSettlement(row) {
      const loading = this.$loading({
        lock: true,
        text: '结算中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/batchUpdateSettlement'),
        method: 'post',
        data: this.$http.adornData({
          recordIds: [row.id],
          settlementStatus: 1, // 已结算
          settlementBatchNo: this.generateBatchNo(),
          settlementRemarks: '单独结算'
        })
      }).then(({ data }) => {
        loading.close()
        if (data && data.code === 200) {
          this.$message.success('结算成功')
          this.getDataList()
          this.getStats()
        } else {
          this.$message.error(data.msg || '结算失败')
        }
      }).catch(() => {
        loading.close()
        this.$message.error('结算失败')
      })
    },
    // 批量结算
    batchSettlement() {
      // 检查选中的记录是否都是未结算状态
      const unsettledRecords = this.dataListSelections.filter(record => record.settlementStatus === 0)
      if (unsettledRecords.length === 0) {
        this.$message.warning('请选择未结算的记录')
        return
      }
      if (unsettledRecords.length !== this.dataListSelections.length) {
        this.$message.warning('选中的记录中包含已结算的记录，请重新选择')
        return
      }

      this.settlementForm.remarks = ''
      this.batchSettlementDialogVisible = true
    },
    // 确认批量结算
    confirmBatchSettlement() {
      this.$refs.settlementForm.validate((valid) => {
        if (valid) {
          this.doBatchSettlement()
        }
      })
    },
    // 执行批量结算
    doBatchSettlement() {
      this.settlementLoading = true

      const recordIds = this.dataListSelections.map(record => record.id)

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/batchUpdateSettlement'),
        method: 'post',
        data: this.$http.adornData({
          recordIds: recordIds,
          settlementStatus: 1, // 已结算
          settlementBatchNo: this.generateBatchNo(),
          settlementRemarks: this.settlementForm.remarks
        })
      }).then(({ data }) => {
        this.settlementLoading = false
        if (data && data.code === 200) {
          this.$message.success(`批量结算成功，共处理 ${data.updateCount} 条记录`)
          this.batchSettlementDialogVisible = false
          this.getDataList()
          this.getStats()
        } else {
          this.$message.error(data.msg || '批量结算失败')
        }
      }).catch(() => {
        this.settlementLoading = false
        this.$message.error('批量结算失败')
      })
    },
    // 生成批次号
    generateBatchNo() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hour = String(now.getHours()).padStart(2, '0')
      const minute = String(now.getMinutes()).padStart(2, '0')
      const second = String(now.getSeconds()).padStart(2, '0')
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')

      return `BATCH${year}${month}${day}${hour}${minute}${second}${random}`
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

/* 佣金详情弹窗样式 */
.commission-detail-dialog .el-dialog__body {
  padding: 20px;
  background-color: #f5f7fa;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.card-header i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  min-height: 24px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
  line-height: 24px;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  line-height: 24px;
  word-break: break-all;
}

.detail-item .value.amount {
  font-weight: 600;
  color: #e6a23c;
  font-size: 16px;
}

.commission-detail-dialog .el-card__header {
  background-color: #fafbfc;
  border-bottom: 1px solid #ebeef5;
  padding: 15px 20px;
}

.commission-detail-dialog .el-card__body {
  padding: 20px;
}

.commission-detail-dialog .el-tag {
  font-weight: 500;
}
</style>
