<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      
      <el-form-item label="报名通道" prop="applyActivityCchannelIdhannelConfigId">
      <el-select v-model="dataForm.channelId" placeholder="报名通道" filterable>
        <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        activityId: '',
        channelId: '',
      },
      channelList: [],
    }
  },
  methods: {
    init(id) {
      this.dataForm.activityId = id
      this.visible = true
      this.getChannelByActivityId();
    },
      getChannelByActivityId() {
        this.$http({
              url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/findByActivityId/${this.dataForm.activityId}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.channelList = data.result;
                if(this.channelList.length == 1) {
                  this.dataForm.channelId = this.channelList[0].id;
                }
              }
            })
      },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if(!this.dataForm.channelId) {
            this.$message.error("请选择报名通道");
            return false;
          }
          var url = this.$http.adornUrl("/activity/activityuserapplyorder/exportDemo?" + [
            "token=" + this.$cookie.get('token'),
            "channelId=" + this.dataForm.channelId,
            "activityId=" + this.dataForm.activityId
          ].join('&'));
          window.open(url);
        }
      })
    }
  }
}
</script>
