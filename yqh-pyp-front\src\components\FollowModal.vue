<template>
    <van-dialog :close-on-click-overlay="true" v-model="show" :title="activityId == '1818160474906226689' ? '温馨提示' : appid == 'wx0770d56458b33c67' ? '长按二维码关注' : '长按二维码'" :showConfirmButton="false"  @confirm="$emit('close')">
        <div class="text-center padding">
            <van-image width="200" :src="(appid == 'wx0770d56458b33c67' && !qrcodeImgUrl) ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230601/aa9aa8fe9c1d4084a05562d55f623664.png' : qrcodeImgUrl">
                <template v-slot:error>二维码加载失败
                </template>
            </van-image>
        <div v-if="activityId == '1674612614545080321'" style="color: grey; font-size: 14px">恭喜您成功参与活动<br/>拼团进度详情请关注企来嗨公众号-售后服务一我的进行查询</div>
        <div v-else-if="activityId != '1818160474906226689'" style="color: grey; font-size: 14px">关注后，点击“参会记录”菜单<br/>即可看到会议报名信息</div>
        </div>
    </van-dialog>
</template>
<script>
    export default {
        name: 'FollowModal',
        props: {
            show: {
                type: Boolean,
                default: false
            },
            qrcodeImgUrl: {
                type: String,
                default: ''
            },
            activityId: {
                type: String,
                default: ''
            },
            appid: {
                type: String,
                default: ''
            },
        },
        data() {
            return {
                // qrcodeImgUrl:process.env.VUE_APP_WX_QRCODE
            }
        }
    }
</script>