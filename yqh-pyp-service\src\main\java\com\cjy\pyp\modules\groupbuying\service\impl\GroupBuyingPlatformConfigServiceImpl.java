package com.cjy.pyp.modules.groupbuying.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.modules.groupbuying.dao.GroupBuyingPlatformConfigDao;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingPlatformConfigEntity;
import com.cjy.pyp.modules.groupbuying.service.GroupBuyingPlatformConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 团购券平台配置服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@Service("groupBuyingPlatformConfigService")
public class GroupBuyingPlatformConfigServiceImpl extends ServiceImpl<GroupBuyingPlatformConfigDao, GroupBuyingPlatformConfigEntity> implements GroupBuyingPlatformConfigService {

    @Override
    public List<GroupBuyingPlatformConfigEntity> getEnabledPlatforms() {
        return this.list(new QueryWrapper<GroupBuyingPlatformConfigEntity>()
                .eq("status", 1)
                .orderByAsc("sort_order"));
    }

    @Override
    public GroupBuyingPlatformConfigEntity getByPlatformType(String platformType) {
        return this.getOne(new QueryWrapper<GroupBuyingPlatformConfigEntity>()
                .eq("platform_type", platformType)
                .eq("status", 1));
    }

    @Override
    public String generateJumpUrl(String platformType, String couponId, boolean isApp) {
        GroupBuyingPlatformConfigEntity config = getByPlatformType(platformType);
        if (config == null) {
            return null;
        }

        String template = isApp ? config.getUrlScheme() : config.getWebUrlTemplate();
        if (StringUtils.isBlank(template) || StringUtils.isBlank(couponId)) {
            return null;
        }

        return template.replace("{coupon_id}", couponId);
    }
}
