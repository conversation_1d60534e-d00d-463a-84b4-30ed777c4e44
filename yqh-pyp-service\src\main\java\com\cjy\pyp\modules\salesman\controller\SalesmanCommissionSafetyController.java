package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionSafetyService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 业务员佣金安全管理控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("salesman/commissionsafety")
@Api(tags = "业务员佣金安全管理")
public class SalesmanCommissionSafetyController extends AbstractController {

    @Autowired
    private SalesmanCommissionSafetyService commissionSafetyService;

    /**
     * 检查佣金记录是否已存在
     */
    @RequestMapping("/checkExists")
    // @RequiresPermissions("salesman:commissionsafety:check")
    @ApiOperation(value = "检查佣金存在", notes = "检查指定业务的佣金记录是否已存在")
    public R checkCommissionExists(@RequestParam String businessType,
                                  @RequestParam Long businessId,
                                  @CookieValue String appid) {
        boolean exists = commissionSafetyService.isCommissionExists(businessType, businessId, appid);
        return R.ok().put("exists", exists);
    }

    /**
     * 重试失败的佣金计算
     */
    @RequestMapping("/retryFailed")
    // @RequiresPermissions("salesman:commissionsafety:retry")
    @ApiOperation(value = "重试失败佣金", notes = "重试失败的佣金计算")
    public R retryFailedCommissions(@CookieValue String appid) {
        try {
            Integer retryCount = commissionSafetyService.retryFailedCommissions(appid);
            return R.ok()
                    .put("retryCount", retryCount)
                    .put("message", "成功重试 " + retryCount + " 个失败的佣金计算");
        } catch (Exception e) {
            return R.error("重试失败的佣金计算异常：" + e.getMessage());
        }
    }

    /**
     * 验证佣金计算的数据一致性
     */
    @RequestMapping("/validateConsistency")
    // @RequiresPermissions("salesman:commissionsafety:validate")
    @ApiOperation(value = "验证数据一致性", notes = "验证佣金计算的数据一致性")
    public R validateCommissionConsistency(@RequestParam Long commissionRecordId) {
        String validationResult = commissionSafetyService.validateCommissionConsistency(commissionRecordId);
        
        if (validationResult == null) {
            return R.ok().put("consistent", true).put("message", "数据一致性验证通过");
        } else {
            return R.ok().put("consistent", false).put("message", validationResult);
        }
    }

    /**
     * 修复数据不一致的佣金记录
     */
    @RequestMapping("/repairInconsistent")
    // @RequiresPermissions("salesman:commissionsafety:repair")
    @ApiOperation(value = "修复不一致数据", notes = "修复数据不一致的佣金记录")
    public R repairInconsistentCommission(@RequestParam Long commissionRecordId) {
        try {
            boolean success = commissionSafetyService.repairInconsistentCommission(commissionRecordId);
            if (success) {
                return R.ok().put("message", "修复成功");
            } else {
                return R.error("修复失败");
            }
        } catch (Exception e) {
            return R.error("修复异常：" + e.getMessage());
        }
    }

    /**
     * 批量验证佣金数据一致性
     */
    @RequestMapping("/batchValidate")
    // @RequiresPermissions("salesman:commissionsafety:validate")
    @ApiOperation(value = "批量验证一致性", notes = "批量验证佣金数据一致性")
    public R batchValidateCommissionConsistency(@CookieValue String appid) {
        try {
            Integer inconsistentCount = commissionSafetyService.batchValidateCommissionConsistency(appid);
            return R.ok()
                    .put("inconsistentCount", inconsistentCount)
                    .put("message", "发现 " + inconsistentCount + " 个不一致的佣金记录");
        } catch (Exception e) {
            return R.error("批量验证异常：" + e.getMessage());
        }
    }

    /**
     * 清理过期的锁和临时数据
     */
    @RequestMapping("/cleanup")
    // @RequiresPermissions("salesman:commissionsafety:cleanup")
    @ApiOperation(value = "清理过期数据", notes = "清理过期的锁和临时数据")
    public R cleanupExpiredData(@CookieValue String appid) {
        try {
            Integer cleanupCount = commissionSafetyService.cleanupExpiredData(appid);
            return R.ok()
                    .put("cleanupCount", cleanupCount)
                    .put("message", "成功清理 " + cleanupCount + " 个过期数据");
        } catch (Exception e) {
            return R.error("清理过期数据异常：" + e.getMessage());
        }
    }

    /**
     * 获取分布式锁状态
     */
    @RequestMapping("/lockStatus")
    // @RequiresPermissions("salesman:commissionsafety:check")
    @ApiOperation(value = "锁状态查询", notes = "查询分布式锁状态")
    public R getLockStatus(@RequestParam String businessType,
                          @RequestParam Long businessId,
                          @RequestParam Long salesmanId,
                          @CookieValue String appid) {
        String lockKey = commissionSafetyService.generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
        // 这里可以添加查询锁状态的逻辑
        return R.ok()
                .put("lockKey", lockKey)
                .put("message", "锁状态查询功能待完善");
    }

    /**
     * 手动释放锁
     */
    @RequestMapping("/releaseLock")
    // @RequiresPermissions("salesman:commissionsafety:manage")
    @ApiOperation(value = "释放锁", notes = "手动释放分布式锁")
    public R releaseLock(@RequestParam String businessType,
                        @RequestParam Long businessId,
                        @RequestParam Long salesmanId,
                        @CookieValue String appid) {
        try {
            String lockKey = commissionSafetyService.generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
            boolean success = commissionSafetyService.releaseLock(lockKey);
            if (success) {
                return R.ok().put("message", "锁释放成功");
            } else {
                return R.error("锁释放失败");
            }
        } catch (Exception e) {
            return R.error("释放锁异常：" + e.getMessage());
        }
    }

    /**
     * 生成佣金计算的唯一标识
     */
    @RequestMapping("/generateUniqueKey")
    // @RequiresPermissions("salesman:commissionsafety:check")
    @ApiOperation(value = "生成唯一标识", notes = "生成佣金计算的唯一标识")
    public R generateUniqueKey(@RequestParam String businessType,
                              @RequestParam Long businessId,
                              @RequestParam Long salesmanId,
                              @CookieValue String appid) {
        String uniqueKey = commissionSafetyService.generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
        return R.ok().put("uniqueKey", uniqueKey);
    }
}
