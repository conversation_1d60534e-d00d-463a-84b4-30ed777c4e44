<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.contact" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="答题状态" filterable>
          <el-option label="全部" value=""></el-option>
          <el-option v-for="item in examActivityUserStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('exam:examactivityuser:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityuserapplyorder:save')" type="success" @click="exportHandle()">导出</el-button>
        <el-button v-if="isAuth('exam:examactivityuser:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="activityName"
        header-align="center"
        align="center"
        label="会议名称">
      </el-table-column>
      <el-table-column
        prop="examName"
        header-align="center"
        align="center"
        label="考卷名称">
      </el-table-column>
      <el-table-column
        prop="activityUserName"
        header-align="center"
        align="center"
        label="姓名">
      </el-table-column>
      <el-table-column
        prop="activityUserMobile"
        header-align="center"
        align="center"
        label="联系方式">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="考试状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-'+(scope.row.status)">{{examActivityUserStatus[scope.row.status].value}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column
        prop="points"
        header-align="center"
        align="center"
        label="得分">
      </el-table-column>
      <el-table-column
        prop="passPoints"
        header-align="center"
        align="center"
        label="及格分数">
      </el-table-column>
      <el-table-column
        prop="createOn"
        header-align="center"
        align="center"
        label="创建时间">
      </el-table-column>
      <el-table-column
        prop="updateOn"
        header-align="center"
        align="center"
        label="更新时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="optionHandle(scope.row.id)">答题情况</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <showanswer v-if="showanswerVisible" ref="showanswer"></showanswer>
  </div>
</template>

<script>
  import { examActivityUserStatus } from "@/data/exam"
  import AddOrUpdate from './examactivityuser-add-or-update'
  import showanswer from './examactivityuser-showanswer'
  export default {
    data () {
      return {
        examActivityUserStatus: examActivityUserStatus,
        dataForm: {
          contact: '',
          mobile: '',
          status: '',
          examId: undefined,
          activityId: undefined
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        showanswerVisible: false,
      }
    },
    components: {
      AddOrUpdate,
      showanswer,
    },
    activated () {
      this.dataForm.activityId = this.$route.query.activityId;
      this.dataForm.examId = this.$route.query.examId;
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/exam/examactivityuser/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'contact': this.dataForm.contact,
            'mobile': this.dataForm.mobile,
            'activityId': this.dataForm.activityId,
            'status': this.dataForm.status,
            'examId': this.dataForm.examId
          })
        }).then(({data}) => {
          if (data && data.code ===200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(this.dataForm.activityId,this.dataForm.examId, id)
        })
      },
      // 查看答题情况
      optionHandle (id) {
        this.showanswerVisible = true
        this.$nextTick(() => {
          this.$refs.showanswer.init(id)
        })
      },
      // 导出
      exportHandle() {
          var url = this.$http.adornUrl("/exam/examactivityuser/export?" + [
              "token=" + this.$cookie.get('token'),
              "page=" + 1,
              "limit=65535",
              "mobile=" + this.dataForm.mobile,
              "contact=" + this.dataForm.contact,
              "examId=" + this.dataForm.examId,
              "activityId=" + this.dataForm.activityId,
              "status=" + this.dataForm.status
          ].join('&'));
          window.open(url);
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/exam/examactivityuser/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code ===200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
