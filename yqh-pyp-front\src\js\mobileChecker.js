import fly from './request'
import router from '@/router'

/**
 * 检查用户是否绑定手机号的工具函数
 */

/**
 * 检查当前用户是否已绑定手机号
 * @param {string} returnUrl - 绑定成功后的返回地址
 * @returns {Promise<boolean>} - 返回是否已绑定手机号
 */
export function checkUserMobile(returnUrl = window.location.href) {
    return new Promise((resolve, reject) => {
        fly.get("/pyp/wxUser/getUserInfo").then((res) => {
            if (res.code === 200) {
                const userInfo = res.data;
                if (!userInfo.mobile) {
                    // 用户没有手机号，需要绑定
                    console.log("用户未绑定手机号");
                    resolve(false);
                } else {
                    // 用户已有手机号
                    console.log("用户已绑定手机号:", userInfo.mobile);
                    resolve(true);
                }
            } else {
                console.error("获取用户信息失败:", res.msg);
                reject(new Error(res.msg || "获取用户信息失败"));
            }
        }).catch((error) => {
            console.error("检查用户手机号失败:", error);
            reject(error);
        });
    });
}

/**
 * 检查用户手机号，如果未绑定则跳转到绑定页面
 * @param {string} returnUrl - 绑定成功后的返回地址
 * @returns {Promise<boolean>} - 返回是否已绑定手机号
 */
export function checkAndRedirectToBindMobile(returnUrl = window.location.href) {
    return checkUserMobile(returnUrl).then((hasMobile) => {
        if (!hasMobile) {
            // 跳转到绑定手机号页面
            console.log("跳转到绑定手机号页面");
            vant.Toast("请先绑定手机号");
            
            setTimeout(() => {
                router.push({
                    name: 'bindMobile',
                    query: {
                        returnUrl: encodeURIComponent(returnUrl)
                    }
                });
            }, 1000);
            
            return false;
        }
        return true;
    }).catch((error) => {
        console.error("检查手机号失败:", error);
        vant.Toast("检查用户信息失败，请重试");
        return false;
    });
}

/**
 * 在微信授权成功后检查手机号
 * @param {string} returnUrl - 最终跳转地址
 * @returns {Promise<void>}
 */
export function checkMobileAfterWxAuth(returnUrl = window.location.href) {
    return checkUserMobile(returnUrl).then((hasMobile) => {
        if (!hasMobile) {
            // 用户没有手机号，跳转到绑定手机号页面
            console.log("用户未绑定手机号，跳转到绑定页面");
            vant.Toast("请先绑定手机号");
            // 立即跳转，不使用延时
            router.push({
                name: 'bindMobile',
                query: {
                    returnUrl: encodeURIComponent(returnUrl)
                }
            });
        } else {
            // 用户已有手机号，直接跳转
            console.log("用户已绑定手机号，跳转到目标页面");
            // 立即跳转，不使用延时
            location.href = returnUrl;
        }
    }).catch((error) => {
        console.error("检查用户手机号失败:", error);
        vant.Toast("检查用户信息失败，请重试");
        // 出错时也立即跳转
        location.href = returnUrl;
    });
}

/**
 * 检查API响应中是否包含手机号相关错误
 * @param {Object} response - API响应对象
 * @param {string} returnUrl - 返回地址
 * @returns {boolean} - 是否处理了手机号相关错误
 */
export function handleMobileError(response, returnUrl = window.location.href) {
    if (response && response.msg === '用户手机号不存在') {
        console.log("API返回用户手机号不存在，跳转到绑定页面");
        router.push({
            name: 'bindMobile',
            query: {
                returnUrl: encodeURIComponent(returnUrl)
            }
        });
        return true;
    }
    return false;
}
