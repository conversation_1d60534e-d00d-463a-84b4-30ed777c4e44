<template>
  <div style="padding-bottom: 50px;">
    <van-swipe :autoplay="3000">
      <van-swipe-item v-for="(image, index) in indexActivity" :key="index">
        <van-image width="100%" :src="image.appFileList[0].url" @click="turnUrl(image)"> </van-image>
      </van-swipe-item>
    </van-swipe>
    <van-grid style="margin-top: -4px" :column-num="3" icon-size="50px" :border="false">
      <van-grid-item icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250213/abe31eaa7eb64d86b8d0ad098df2184e.png"
        @click="turnHome()" text="搜会议" />
      <van-grid-item v-if="appid != 'wx0f8d389094ac6910'"
        icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250213/370ed0cc06a241eaa881fb67022367f3.png"
        @click="turnNews()" text="热门资讯" />
      <van-grid-item v-else
        icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250213/370ed0cc06a241eaa881fb67022367f3.png"
        @click="turnCompany()" text="公司介绍" />
      <van-grid-item icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250213/f9ad33d050b64e7380725174f7c9f6ea.png"
        @click="turnContact()" text="联系我们" />
    </van-grid>
    <!-- <div style="margin: 8px 0 " class="nav-title">
        <div class="color"></div>
        <div class="text">最新会议</div>
      </div>
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <van-card style="background: white" v-for="item in dataList" :key="item.id" @click="turnUrl(item)"
          :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 3px;font-size: 14px;color:grey" v-if="item.startTime == item.endTime">
            {{ item.startTime.substring(0, 10) }}</div>
          <div slot="desc" style="padding-top: 3px;font-size: 14px;color:grey" v-else>
            {{ item.startTime.substring(0, 10) }} 至 {{ item.endTime.substring(5, 10) }}</div>
          <div slot="price" style="font-size: 14px">{{ item.address }}</div>
        </van-card>
      </van-list> -->
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">热门会议</div>
    </div>
    <van-card :style="appid == 'wx0f8d389094ac6910' ? specialStyle : defaultStyle" v-for="item in hotActivity"
      :key="item.id" @click="turnUrl(item)" :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]">
      <div slot="title" class="title">{{ item.name }}</div>
      <div slot="desc" style="padding-top: 3px; font-size: 14px; color: grey" v-if="item.startTime == item.endTime">
        {{ item.startTime.substring(0, 10) }}
      </div>
      <div slot="desc" style="padding-top: 3px; font-size: 14px; color: grey" v-else>
        {{ item.startTime.substring(0, 10) }} 至
        {{ item.endTime.substring(5, 10) }}
      </div>
      <div slot="price" style="font-size: 14px">{{ item.address }}</div>
    </van-card>
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">近期会议</div>
    </div>
    <van-card :style="appid == 'wx0f8d389094ac6910' ? specialStyle : defaultStyle" v-for="item in nearActivity"
      :key="item.id" @click="turnUrl(item)" :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]">
      <div slot="title" class="title">{{ item.name }}</div>
      <div slot="desc" style="padding-top: 3px; font-size: 14px; color: grey" v-if="item.startTime == item.endTime">
        {{ item.startTime.substring(0, 10) }}
      </div>
      <div slot="desc" style="padding-top: 3px; font-size: 14px; color: grey" v-else>
        {{ item.startTime.substring(0, 10) }} 至
        {{ item.endTime.substring(5, 10) }}
      </div>
      <div slot="price" style="font-size: 14px">{{ item.address }}</div>
    </van-card>
    <sub-modal :show="showFollowModal" :qrcodeImgUrl="subUrl" @close="showFollowModal = false"></sub-modal>
    <img class="back" v-if="!userInfo.subscribe && showSub == 1 && appid == 'wx0770d56458b33c67'"
      @click="showFollowModal = true;"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/0b7689735164454a876666e00f2272d9.png" alt="" />
    <img class="back" v-else-if="!userInfo.subscribe && showSub == 1" @click="showFollowModal = true;"
      src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230609/a040f0b471a34b178e83d94ab937476d.png" alt="" />
    <!-- <div v-if="appid == 'wx0770d56458b33c67'" @click="$router.push({
        name: 'yunhuiyi'
      })" class="bottomdiy">
        <div class="item">技术支持 易企化</div>
      </div> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      indexActivity: [],
      hotActivity: [],
      nearActivity: [],
      dataForm: {
        name: '',
        configActivityTypeId: '',
      },
      loading: false,
      finished: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      userInfo: {},
      showFollowModal: false,
      appid: "",
      subUrl: "",
      showSub: 0,
      defaultStyle: {
        background: 'white'
      },
      specialStyle: {
        width: '94%',
        marginLeft: '3%',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
        background: 'white',
        // backgroundImage: 'url(http://mpjoy.oss-cn-beijing.aliyuncs.com/********/63d0bd69aa034e218f5159002a680ec6.jpg)',
        // backgroundSize: '100% 100%',
      }
    };
  },
  components: {
    SubModal: () => import("@/components/SubModal"),
  },
  mounted() {
    document.title = "首页";
    console.log(this.$route.query);
    this.dataForm.configActivityTypeId = this.$route.query.configActivityTypeId || '';
    this.$wxShare(
      this.$cookie.get("accountName"),
      this.$cookie.get("logo"),
      this.$cookie.get("slog")
    );
    this.subUrl = this.$cookie.get("subUrl");
    this.showSub = this.$cookie.get("showSub");
    this.appid = this.$cookie.get("appid");
    //加载微信分享
    this.getActivityList();
    if (this.showSub == 1) {
      this.getUserInfo();
    }
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityListAll();
    },
    onLoad() {
      this.getActivityListAll();
    },
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
          if (!res.data.subscribe) {
            this.showFollowModal = true;
          }
        } else {
          vant.Toast(res.msg);
        }
      });
    },
    getActivityListAll() {
      this.$fly.get('/pyp/web/activity/activity/list', {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'name': this.dataForm.name,
        'configActivityTypeId': this.dataForm.configActivityTypeId,
      }).then(res => {
        this.loading = false;
        if (res.code == 200) {
          if (res.page.list && res.page.list.length > 0) {
            res.page.list.forEach(e => {
              this.dataList.push(e);
            })
            this.totalPage = res.page.totalPage
            this.pageIndex++;
            this.loading = false
            if (this.totalPage < this.pageIndex) {
              this.finished = true
            } else {
              this.finished = false
            }
          } else {
            this.finished = true
          }
        } else {
          // vant.Toast(res.msg);
          this.dataList = []
          this.totalPage = 0
          this.finished = true
        }
      });
    },
    getActivityList() {
      this.$fly.get("/pyp/web/index").then((res) => {
        if (res.code == 200) {
          this.indexActivity = res.indexActivity;
          this.hotActivity = res.hotActivity;
          this.nearActivity = res.nearAllActivity;
        } else {
          // vant.Toast(res.msg);
        }
      });
    },
    turnUrl(item) {
      if (item.turnurl) {
        location.href = item.turnurl
      } else {
        if (this.$cookie.get("appid") != item.appid) {
          if (item.appid == 'wx0f8d389094ac6910') {
            location.href = 'http://ztmeeting.com/mp/#/cms/index?id=' + item.id;
          }
          if (item.appid == 'wx0770d56458b33c67') {
            location.href = 'http://fjmeeting.com/mp_fjsd/#/cms/index?id=' + item.id;
          }
        } else {
          this.$router.push({ name: 'cmsIndex', query: { id: item.id } })
        }
      }
    },
    turnHome() {
      this.$router.push({
        name: 'indexHome',
      })
    },
    turnNews() {
      this.$router.push({
        name: 'newsIndex',
      })
    },
    turnBusiness() {
      this.$router.push({
        name: 'businessIndex',
      })
    },
    turnContact() {
      this.$router.push({
        name: 'contact',
      })
    },
    turnCompany() {
      this.$router.push({
        name: 'company',
      })
    },
  },
};
</script>

<style lang="less" scoped>
.van-card {
  width: 94%;
  margin-left: 3%;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
}

.van-grid {
  background: white;
}

.van-card__thumb {
  width: 156px;
}

.bottomdiy {
  margin-top: 30px;
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  background-color: black;
  opacity: 0.3;

  .item {
    color: white;
  }
}

.title {
  font-size: 18px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>