<template>
  <el-dialog :title="'明细'" :close-on-click-modal="false" :visible.sync="visible">
    <el-table :data="supplierProductStockEntities" border style="width: 100%;">
      <el-table-column prop="payTime" header-align="center" align="center" label="付款时间">
        <div slot-scope="scope">
            <div>{{ scope.row.payTime }}</div>
        </div>
      </el-table-column>
      <el-table-column prop="priceBankName" header-align="center" align="center" label="收/付款账号">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="金额">
      </el-table-column>
      <el-table-column prop="remarks" :show-overflow-tooltip="true" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" style="color: red" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button @click="visible = false">取消</el-button> -->
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { yesOrNo } from "@/data/common"
export default {
  data() {
    return {
      loading: false,
      yesOrNo: yesOrNo,
      titleDetail: '',
      visible: false,
      contractPriceCycleId: '',
      supplierProductStockEntities: [],
    }
  },
  methods: {
    init(id) {
      this.contractPriceCycleId = id
      this.visible = true
      this.getResult();
    },
    getResult() {

      this.$http({
        url: this.$http.adornUrl(`/activity/activitysettlepricelog/findByActivitySettleId`),
        method: 'get',
        params: this.$http.adornParams({
          activitySettleId: this.contractPriceCycleId
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.supplierProductStockEntities = data.result
        }
      })
    },
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.id;
        });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/activity/activitysettlepricelog/delete"),
          method: "post",
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getResult();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.visible = false
      this.$emit('refreshDataList')
    }
  }
}
</script>
