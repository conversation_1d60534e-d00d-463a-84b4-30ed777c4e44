export default {
    getVersion() {
        let ua = navigator.userAgent || window.navigator.userAgent;
        let huawei = /HUAWEI|HONOR/ig;
        let android = /[^;]+(?= Build)/ig;
        let iPhone = /CPU iPhone OS \d[_\d]*/ig;
        let iPad = /CPU OS \d[_\d]*/ig;
        let windows = /Windows NT \d[\.\d]*/ig;
        let linux = /Linux x\d[_\d]*/ig;
        let mac = /Mac OS X \d[_|\.\d]*/ig;
        if (/Android/ig.test(ua)) {
            if (huawei.test(ua)) {
                return ua.match(huawei)[0] + ua.match(android)[0];
            }
            return ua.match(android)[0];
        } else if (/iPhone/ig.test(ua)) {
            return ua.match(iPhone)[0].replace(/CPU iPhone OS/ig, 'iPhone').replace(/_/g, '.');
        } else if (/iPad/ig.test(ua)) {
            return ua.match(iPad)[0].replace(/CPU OS/ig, 'iPad').replace(/_/g, '.');
        } else if (/Windows/ig.test(ua)) {
            return "Windows " + (Math.min(parseInt(ua.match(windows)[0].replace(/Windows NT /, '')) + 1, 10) < 7 ?
                "XP" :
                Math.min(parseInt(ua.match(windows)[0].replace(/Windows NT /, '')) + 1, 10));
        } else if (/Linux/ig.test(ua)) {
            return ua.match(linux)[0];
        } else if (/Macintosh/ig.test(ua)) {
            return ua.match(mac)[0].replace(/_/g, '.');
        }
        return "unknown";
    }
}