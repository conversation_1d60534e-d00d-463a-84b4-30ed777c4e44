{"name": "wx-manage", "version": "0.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.0", "@tinymce/tinymce-vue": "^3.2.6", "axios": "^0.19.0", "element-china-area-data": "^5.0.2", "vue": "^2.6.12", "print-js": "^1.6.0", "compressorjs": "^1.1.1", "vue-clipboard2": "^0.3.1", "vue-cookie": "^1.1.4", "vue-router": "^3.4.9", "vuex": "^3.6.0"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.2.0", "@vue/cli-plugin-babel": "^4.5.9", "@vue/cli-service": "^4.5.9", "sass": "^1.51.0", "sass-loader": "10.2.0", "vue-template-compiler": "^2.6.12"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}