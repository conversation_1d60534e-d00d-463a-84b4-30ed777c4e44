<!-- FlightSelectorDialog.vue -->
<template>
    <el-dialog title="选择航班" :visible.sync="visible" width="800px" @close="handleClose">
        <div class="flight-container">
            <!-- 日期导航 -->
            <el-row type="flex" justify="space-between" align="middle" class="date-nav">
                <el-col :span="4">
                    <el-button plain @click="switchDay(-1)" :disabled="isLoading">
                        前一天
                    </el-button>
                </el-col>
                <el-col :span="16" class="current-date">
                    {{ inDate }}
                </el-col>
                <el-col :span="4" class="text-right">
                    <el-button plain @click="switchDay(1)" :disabled="isLoading">
                        后一天
                    </el-button>
                </el-col>
            </el-row>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-overlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">航班信息加载中...</div>
            </div>
            <!-- 航班列表 -->
            <template v-if="!isLoading">
                <div v-if="flights.length > 0" class="flight-list">
                    <div v-for="flight in flights" :key="flight.flightNo" class="flight-item"
                        :class="{ 'selected': selectedFlight === flight.flightNo }"
                        @click="selectFlight(flight.flightNo)">
                        <div class="time-row">
                            <span class="departure-time">{{ flight.fromDateTime }}</span>
                            <span class="duration">{{ flight.flyDuration }}时</span>
                            <span class="arrival-time">{{ flight.toDateTime }}</span>
                        </div>

                        <div class="airport-row">
                            <span class="departure-airport">{{ flight.fromAirportName }}-{{ flight.fromTerminal
                            }}</span>
                            <div>
                                <span class="flight-number">{{ flight.flightNo }}</span>
                                <span class="flight-number">{{ flight.airlineCompany }}</span>
                                <span class="flight-number">{{ flight.meals }}</span>
                            </div>
                            <span class="arrival-airport">{{ flight.toAirportName }}-{{ flight.toTerminal }}</span>
                        </div>
                        <div v-for="item in flight.cabins" :key="item.cabinBookPara" class="flight-item"
                            :class="{ 'selected-item': selectedFlightItem.cabinBookPara === item.cabinBookPara }"
                            @click="selectFlightItem(item)">
                            <div class="time-row">
                                <span class="duration">{{ item.cabinName }}</span>
                                <span >原价：{{ item.cabinPrice.adultFarePrice }}</span>
                                <span >售价：{{ item.cabinPrice.adultSalePrice }}</span>
                            </div>
                    </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <el-empty v-else description="暂无航班信息" />
            </template>
        </div>

        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="confirmSelection">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>

import { format } from "@/utils/date.js";
export default {
    name: 'FlightSelectorDialog',
    data() {
        return {
            isLoading: false,
            visible: false,
            inDate: '',
            startCityCode: '',
            endCityCode: '',
            selectedFlight: null,
            selectedFlightItem: {},
            flights: [],
            callback: null // 添加回调函数属性
            // minDate: new Date('2024-01-17'),  // 限制最早可选日期
            // maxDate: new Date('2024-01-19')   // 限制最晚可选日期
        }
    },
    computed: {
        // formattedDate() {
        //     const month = (this.currentDate.getMonth() + 1).toString().padStart(2, '0')
        //     const day = this.currentDate.getDate().toString().padStart(2, '0')
        //     const weekdays = ['日', '一', '二', '三', '四', '五', '六']
        //     return `${month}月${day}日 周${weekdays[this.currentDate.getDay()]}`
        // },
        // isPrevDisabled() {
        //     return this.currentDate <= this.minDate
        // },
        // isNextDisabled() {
        //     return this.currentDate >= this.maxDate
        // }
    },
    methods: {
        init(startCityCode, endCityCode, inDate, callback) {
      // 保存回调函数
      this.callback = callback || null;
            this.startCityCode = startCityCode;
            this.endCityCode = endCityCode;
            this.inDate = inDate;
            this.visible = true;
            this.flights = [];
            this.selectedFlight = null;
            this.selectedFlightItem = {}
            this.loadFlights();

        },
        loadFlights() {
            this.isLoading = true
            this.$http({
                url: this.$http.adornUrl("/panhe/searchPlane"),
                method: "get",
                params: this.$http.adornParams({
                    fromCity: this.startCityCode,
                    toCity: this.endCityCode,
                    fromDate: this.inDate,
                }),
            }).then(({ data }) => {
                this.isLoading = false;
                if (data && data.code === 200) {
                    this.flights = data.result;
                } else {
                    this.$message.error(data.msg)
                }
            });
        },
        switchDay(offset) {
            const newDate = new Date(this.inDate)
            newDate.setDate(newDate.getDate() + offset)
            this.inDate = format(newDate, "yyyy/MM/dd");
            this.flights = [];
            this.selectedFlight = null;
            this.selectedFlightItem = {}
            this.loadFlights()
        },
        selectFlight(number) {
            this.selectedFlight = number
        },
        selectFlightItem(number) {
            this.selectedFlightItem = number
        },
        confirmSelection() {
            if (this.selectedFlight && this.selectFlightItem) {
                const flight = this.flights.find(f => f.flightNo === this.selectedFlight)
                flight.cabinBookPara = this.selectedFlightItem.cabinBookPara;
                flight.cabinCode = this.selectedFlightItem.cabinCode;
                flight.price = this.selectedFlightItem.cabinPrice.adultSalePrice;
                // 如果有回调函数，则调用回调
                if (this.callback) {
                    this.callback(flight);
                } else {
                    // 原有的emit逻辑
                    this.$emit('select', flight);
                }
                this.visible = false
            } else {
                this.$message.warning('请先选择航班和仓位')
            }
        },
        handleClose() {
            this.selectedFlight = null
        }
    },
}
</script>

<style lang="scss" scoped>
.flight-container {
    padding: 10px;
}

.date-nav {
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;

    .current-date {
        text-align: center;
        font-size: 16px;
        color: #303133;
        font-weight: 500;
    }
}

.flight-list {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
    height: 600px;
    overflow-y: scroll;
}

.flight-item {
    padding: 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: all 0.2s;
}

.flight-item:hover {
    background: #f5f7fa;
}

.flight-item.selected {
    background: #ecf5ff;
    border-left: 3px solid #409eff;
}
.flight-item.selected-item {
    background: #ffdbdb;
    border-left: 3px solid #ff4040;
}

.time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.departure-time,
.arrival-time {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.duration {
    font-size: 14px;
    color: #666;
}

.airport-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.departure-airport,
.arrival-airport {
    font-size: 14px;
    color: #666;
    max-width: 40%;
}

.flight-number {
    font-size: 13px;
    color: #409eff;
    padding: 4px 8px;
    background: #e8f4ff;
    border-radius: 4px;
    margin-left: 10px;
}

::v-deep .el-dialog__body {
    padding: 20px;
}

.el-empty {
    padding: 40px 0;
}

.loading-overlay {
    position: relative;
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>