<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
    <el-form-item label="机场名称" prop="airportName">
              <el-input v-model="dataForm.airportName" placeholder="机场名称"></el-input>
          </el-form-item>
    <el-form-item label="机场简称" prop="airportShortName">
              <el-input v-model="dataForm.airportShortName" placeholder="机场简称"></el-input>
          </el-form-item>
    <el-form-item label="机场三字码" prop="airportCode">
              <el-input v-model="dataForm.airportCode" placeholder="机场三字码"></el-input>
          </el-form-item>
    <el-form-item label="城市名称" prop="cityName">
              <el-input v-model="dataForm.cityName" placeholder="城市名称"></el-input>
          </el-form-item>
    <el-form-item label="城市三字码" prop="cityCode">
              <el-input v-model="dataForm.cityCode" placeholder="城市三字码"></el-input>
          </el-form-item>
    <el-form-item label="城市拼音" prop="cityPinYin">
              <el-input v-model="dataForm.cityPinYin" placeholder="城市拼音"></el-input>
          </el-form-item>
    <el-form-item label="城市拼音简称" prop="cityShortChar">
              <el-input v-model="dataForm.cityShortChar" placeholder="城市拼音简称"></el-input>
          </el-form-item>
    <el-form-item label="所属国家名称" prop="countryName">
              <el-input v-model="dataForm.countryName" placeholder="所属国家名称"></el-input>
          </el-form-item>
    <el-form-item label="所属国家代码" prop="countryCode">
              <el-input v-model="dataForm.countryCode" placeholder="所属国家代码"></el-input>
          </el-form-item>
    <el-form-item label="所属洲" prop="continent">
              <el-input v-model="dataForm.continent" placeholder="所属洲"></el-input>
          </el-form-item>
    <el-form-item label="是否国内（大陆）机场城市" prop="isInternal">
              <el-input v-model="dataForm.isInternal" placeholder="是否国内（大陆）机场城市"></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
                    
          airportName: '',
    
          airportShortName: '',
    
          airportCode: '',
    
          cityName: '',
    
          cityCode: '',
    
          cityPinYin: '',
    
          cityShortChar: '',
    
          countryName: '',
    
          countryCode: '',
    
          continent: '',
    
          isInternal: ''
        },
        dataRule: {
          airportName: [
            { required: true, message: '机场名称不能为空', trigger: 'blur' }
          ],
          airportShortName: [
            { required: true, message: '机场简称不能为空', trigger: 'blur' }
          ],
          airportCode: [
            { required: true, message: '机场三字码不能为空', trigger: 'blur' }
          ],
          cityName: [
            { required: true, message: '城市名称不能为空', trigger: 'blur' }
          ],
          cityCode: [
            { required: true, message: '城市三字码不能为空', trigger: 'blur' }
          ],
          cityPinYin: [
            { required: true, message: '城市拼音不能为空', trigger: 'blur' }
          ],
          cityShortChar: [
            { required: true, message: '城市拼音简称不能为空', trigger: 'blur' }
          ],
          countryName: [
            { required: true, message: '所属国家名称不能为空', trigger: 'blur' }
          ],
          countryCode: [
            { required: true, message: '所属国家代码不能为空', trigger: 'blur' }
          ],
          continent: [
            { required: true, message: '所属洲不能为空', trigger: 'blur' }
          ],
          isInternal: [
            { required: true, message: '是否国内（大陆）机场城市不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/config/configairport/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.airportName = data.configAirPort.airportName
                this.dataForm.airportShortName = data.configAirPort.airportShortName
                this.dataForm.airportCode = data.configAirPort.airportCode
                this.dataForm.cityName = data.configAirPort.cityName
                this.dataForm.cityCode = data.configAirPort.cityCode
                this.dataForm.cityPinYin = data.configAirPort.cityPinYin
                this.dataForm.cityShortChar = data.configAirPort.cityShortChar
                this.dataForm.countryName = data.configAirPort.countryName
                this.dataForm.countryCode = data.configAirPort.countryCode
                this.dataForm.continent = data.configAirPort.continent
                this.dataForm.isInternal = data.configAirPort.isInternal
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/config/configairport/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                                                                                                                            'airportName': this.dataForm.airportName,
                            'airportShortName': this.dataForm.airportShortName,
                            'airportCode': this.dataForm.airportCode,
                            'cityName': this.dataForm.cityName,
                            'cityCode': this.dataForm.cityCode,
                            'cityPinYin': this.dataForm.cityPinYin,
                            'cityShortChar': this.dataForm.cityShortChar,
                            'countryName': this.dataForm.countryName,
                            'countryCode': this.dataForm.countryCode,
                            'continent': this.dataForm.continent,
                            'isInternal': this.dataForm.isInternal,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
