<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">

    <el-form-item label="名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="名称"></el-input>
    </el-form-item>
      <el-form-item label="地址" prop="url">
          <el-upload class="avatar-uploader" list-type="picture-card" :before-upload="checkFileSize" :show-file-list="false"
                      :on-success="backgroundSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.url" :src="dataForm.url" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        url: '',
        visible: false,
        dataForm: {
          id: 0,
          name: '',
          url: '',
        },
        dataRule: {
          name: [
            { required: true, message: '名称不能为空', trigger: 'blur' }
          ],
          url: [
            { required: true, message: '地址不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sys/sysmusic/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.url = data.sysMusic.url
                this.dataForm.name = data.sysMusic.name
              }
            })
          }
        })
      },
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 1) {
          this.$message.error(`${file.name}文件大于1MB，请选择小于1MB大小的MP3`)
          return false
        }
        return true
      },
      // 上传成功（背景）
      backgroundSuccessHandle(response, file, fileList) {
          if (response && response.code === 200) {
              this.dataForm.url = response.url;
              this.$message({
                  message: '上传成功',
                  type: 'success',
              })
          } else {
              this.$message.error(response.msg);
          }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/sys/sysmusic/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'url': this.dataForm.url,
                'name': this.dataForm.name,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
