package com.cjy.pyp.modules.salesman.service.impl;

import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionSafetyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * 业务员佣金安全处理服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Service("salesmanCommissionSafetyService")
public class SalesmanCommissionSafetyServiceImpl implements SalesmanCommissionSafetyService {

    private static final Logger logger = LoggerFactory.getLogger(SalesmanCommissionSafetyServiceImpl.class);

    private static final String COMMISSION_LOCK_PREFIX = "commission:lock:";
    private static final String COMMISSION_PROCESSING_PREFIX = "commission:processing:";
    private static final String COMMISSION_FAILURE_PREFIX = "commission:failure:";
    
    private static final int DEFAULT_LOCK_EXPIRE_TIME = 30; // 30秒
    private static final int MAX_RETRY_TIMES = 3;

    @Autowired
    private SalesmanCommissionRecordService commissionRecordService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity safeGenerateCreateActivityCommission(Long salesmanId, Long rechargeRecordId, 
                                                                               BigDecimal orderAmount, String appid) {
        String businessType = "CREATE_ACTIVITY";
        String lockKey = generateLockKey(businessType, rechargeRecordId, salesmanId, appid);
        
        // 获取分布式锁
        if (!acquireLock(lockKey, DEFAULT_LOCK_EXPIRE_TIME)) {
            logger.warn("获取佣金计算锁失败: {}", lockKey);
            return null;
        }
        
        try {
            // 防重复检查
            if (isCommissionExists(businessType, rechargeRecordId, appid)) {
                logger.info("佣金记录已存在，跳过重复生成: businessType={}, businessId={}", businessType, rechargeRecordId);
                return null;
            }
            
            // 标记正在处理
            markProcessing(businessType, rechargeRecordId, salesmanId, appid);
            
            // 生成佣金记录
            SalesmanCommissionRecordEntity record = commissionRecordService.generateCreateActivityCommission(
                    salesmanId, rechargeRecordId, orderAmount, appid);
            
            if (record != null) {
                logger.info("安全生成创建活动佣金成功: salesmanId={}, rechargeRecordId={}, amount={}", 
                           salesmanId, rechargeRecordId, record.getCommissionAmount());
                // 清除处理标记
                clearProcessing(businessType, rechargeRecordId, salesmanId, appid);
            } else {
                logger.warn("生成创建活动佣金失败: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId);
                recordCommissionFailure(businessType, rechargeRecordId, salesmanId, "佣金生成返回null", appid);
            }
            
            return record;
        } catch (Exception e) {
            logger.error("安全生成创建活动佣金异常: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId, e);
            recordCommissionFailure(businessType, rechargeRecordId, salesmanId, e.getMessage(), appid);
            throw e;
        } finally {
            releaseLock(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity safeGenerateRechargeCountCommission(Long salesmanId, Long rechargeRecordId, 
                                                                              BigDecimal orderAmount, String appid) {
        String businessType = "RECHARGE_COUNT";
        String lockKey = generateLockKey(businessType, rechargeRecordId, salesmanId, appid);
        
        if (!acquireLock(lockKey, DEFAULT_LOCK_EXPIRE_TIME)) {
            logger.warn("获取佣金计算锁失败: {}", lockKey);
            return null;
        }
        
        try {
            if (isCommissionExists(businessType, rechargeRecordId, appid)) {
                logger.info("佣金记录已存在，跳过重复生成: businessType={}, businessId={}", businessType, rechargeRecordId);
                return null;
            }
            
            markProcessing(businessType, rechargeRecordId, salesmanId, appid);
            
            SalesmanCommissionRecordEntity record = commissionRecordService.generateRechargeCountCommission(
                    salesmanId, rechargeRecordId, orderAmount, appid);
            
            if (record != null) {
                logger.info("安全生成充值次数佣金成功: salesmanId={}, rechargeRecordId={}, amount={}", 
                           salesmanId, rechargeRecordId, record.getCommissionAmount());
                clearProcessing(businessType, rechargeRecordId, salesmanId, appid);
            } else {
                logger.warn("生成充值次数佣金失败: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId);
                recordCommissionFailure(businessType, rechargeRecordId, salesmanId, "佣金生成返回null", appid);
            }
            
            return record;
        } catch (Exception e) {
            logger.error("安全生成充值次数佣金异常: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId, e);
            recordCommissionFailure(businessType, rechargeRecordId, salesmanId, e.getMessage(), appid);
            throw e;
        } finally {
            releaseLock(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionRecordEntity safeGenerateUserForwardCommission(Long salesmanId, Long usageRecordId, String appid) {
        String businessType = "USER_FORWARD";
        String lockKey = generateLockKey(businessType, usageRecordId, salesmanId, appid);
        
        if (!acquireLock(lockKey, DEFAULT_LOCK_EXPIRE_TIME)) {
            logger.warn("获取佣金计算锁失败: {}", lockKey);
            return null;
        }
        
        try {
            if (isCommissionExists(businessType, usageRecordId, appid)) {
                logger.info("佣金记录已存在，跳过重复生成: businessType={}, businessId={}", businessType, usageRecordId);
                return null;
            }
            
            markProcessing(businessType, usageRecordId, salesmanId, appid);
            
            SalesmanCommissionRecordEntity record = commissionRecordService.generateUserForwardCommission(
                    salesmanId, usageRecordId, appid);
            
            if (record != null) {
                logger.info("安全生成用户转发佣金成功: salesmanId={}, usageRecordId={}, amount={}", 
                           salesmanId, usageRecordId, record.getCommissionAmount());
                clearProcessing(businessType, usageRecordId, salesmanId, appid);
            } else {
                logger.warn("生成用户转发佣金失败: salesmanId={}, usageRecordId={}", salesmanId, usageRecordId);
                recordCommissionFailure(businessType, usageRecordId, salesmanId, "佣金生成返回null", appid);
            }
            
            return record;
        } catch (Exception e) {
            logger.error("安全生成用户转发佣金异常: salesmanId={}, usageRecordId={}", salesmanId, usageRecordId, e);
            recordCommissionFailure(businessType, usageRecordId, salesmanId, e.getMessage(), appid);
            throw e;
        } finally {
            releaseLock(lockKey);
        }
    }

    @Override
    public boolean isCommissionExists(String businessType, Long businessId, String appid) {
        return commissionRecordService.existsByBusiness(businessType, businessId, appid);
    }

    @Override
    public boolean acquireLock(String lockKey, int expireTime) {
        try {
            String fullKey = COMMISSION_LOCK_PREFIX + lockKey;
            Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(fullKey, "1", expireTime, TimeUnit.SECONDS);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            logger.error("获取分布式锁失败: lockKey={}", lockKey, e);
            return false;
        }
    }

    @Override
    public boolean releaseLock(String lockKey) {
        try {
            String fullKey = COMMISSION_LOCK_PREFIX + lockKey;
            Boolean success = stringRedisTemplate.delete(fullKey);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            logger.error("释放分布式锁失败: lockKey={}", lockKey, e);
            return false;
        }
    }

    @Override
    public void recordCommissionFailure(String businessType, Long businessId, Long salesmanId, 
                                       String errorMessage, String appid) {
        try {
            String failureKey = COMMISSION_FAILURE_PREFIX + generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
            String failureInfo = String.format("businessType=%s,businessId=%d,salesmanId=%d,error=%s,time=%d", 
                                              businessType, businessId, salesmanId, errorMessage, System.currentTimeMillis());
            
            // 记录失败信息，保存24小时
            stringRedisTemplate.opsForValue().set(failureKey, failureInfo, 24, TimeUnit.HOURS);
            
            logger.error("记录佣金计算失败: {}", failureInfo);
        } catch (Exception e) {
            logger.error("记录佣金计算失败信息时发生异常", e);
        }
    }

    @Override
    public Integer retryFailedCommissions(String appid) {
        // TODO: 实现重试失败的佣金计算逻辑
        // 1. 扫描失败记录
        // 2. 重新尝试计算
        // 3. 更新重试状态
        logger.info("重试失败的佣金计算功能待实现: appid={}", appid);
        return 0;
    }

    @Override
    public String validateCommissionConsistency(Long commissionRecordId) {
        // TODO: 实现佣金数据一致性验证逻辑
        // 1. 查询佣金记录
        // 2. 验证关联的业务记录
        // 3. 检查金额计算是否正确
        logger.info("佣金数据一致性验证功能待实现: commissionRecordId={}", commissionRecordId);
        return null;
    }

    @Override
    public boolean repairInconsistentCommission(Long commissionRecordId) {
        // TODO: 实现修复不一致佣金记录的逻辑
        logger.info("修复不一致佣金记录功能待实现: commissionRecordId={}", commissionRecordId);
        return false;
    }

    @Override
    public Integer batchValidateCommissionConsistency(String appid) {
        // TODO: 实现批量验证佣金数据一致性的逻辑
        logger.info("批量验证佣金数据一致性功能待实现: appid={}", appid);
        return 0;
    }

    @Override
    public String generateCommissionUniqueKey(String businessType, Long businessId, Long salesmanId, String appid) {
        return String.format("%s:%d:%d:%s", businessType, businessId, salesmanId, appid);
    }

    @Override
    public Integer cleanupExpiredData(String appid) {
        // TODO: 实现清理过期数据的逻辑
        logger.info("清理过期数据功能待实现: appid={}", appid);
        return 0;
    }

    /**
     * 生成锁键
     */
    private String generateLockKey(String businessType, Long businessId, Long salesmanId, String appid) {
        return generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
    }

    /**
     * 标记正在处理
     */
    private void markProcessing(String businessType, Long businessId, Long salesmanId, String appid) {
        try {
            String processingKey = COMMISSION_PROCESSING_PREFIX + generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
            stringRedisTemplate.opsForValue().set(processingKey, "1", 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.warn("标记佣金处理状态失败", e);
        }
    }

    /**
     * 清除处理标记
     */
    private void clearProcessing(String businessType, Long businessId, Long salesmanId, String appid) {
        try {
            String processingKey = COMMISSION_PROCESSING_PREFIX + generateCommissionUniqueKey(businessType, businessId, salesmanId, appid);
            stringRedisTemplate.delete(processingKey);
        } catch (Exception e) {
            logger.warn("清除佣金处理状态失败", e);
        }
    }
}
