<template>
  <div>
    <el-dialog title="选择房间号" :close-on-click-modal="false" :visible.sync="visible">
      
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.number" placeholder="房间号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
      <el-table size="mini" :data="dataList" border v-loading="dataListLoading" 
      style="width: 100%;">
      <el-table-column prop="roomName" header-align="center" align="center" label="房型名称">
      </el-table-column>
      <el-table-column prop="number" header-align="center" align="center" label="房号">
      </el-table-column>
      <el-table-column prop="isAssign" header-align="center" align="center" label="是否分配">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + scope.row.isAssign">{{
            yesOrNo[scope.row.isAssign].value }}</el-tag>
        </div>
      </el-table-column>
      <div v-if="dataList.length > 0">
        <el-table-column header-align="center" align="center" v-for="(item, index) in dataList[0].assignVos"
          :key="index">
          <template slot="header">
            {{ item.date }}
          </template>
          <!-- index对应下面动态列（answerList）的索引，取出值渲染 -->
          <template slot-scope="scope">{{ scope.row.assignVos[index].number }}
            <span v-if="scope.row.assignVos[index].number > 0">({{ scope.row.assignVos[index].number < 1 ?
              (
              roomType[scope.row.assignVos[index].roomType].value) : '满' }})</span>
          </template>
        </el-table-column></div>
      <el-table-column fixed="right" header-align="center" align="center" width="180" label="操作">
        <template slot-scope="scope">
          <el-button style="color: red" type="text" size="small" @click="assignHandle(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import { roomType } from '@/data/room'
import { yesOrNo } from '@/data/common'
export default {
  data() {
    return {
      
     visible:false,
      appid: '',
      roomType,
      hotels: [],
      rooms: [],
      yesOrNo,
      dataForm: {
        activityId: '',
        hotelActivityId: '',
        hotelActivityRoomId: '',
        number: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      addnumberVisible: false,
      assignVisible: false,
      assignpeopleVisible: false,
    }
  },
  components: {
    tagsEditor: () => import("@/components/tags-editor"),
  },
  methods: {
    init(activityId,hotelActivityId,hotelActivityRoomId) {
      this.dataForm.activityId = activityId
      this.dataForm.hotelActivityId = hotelActivityId
      this.dataForm.hotelActivityRoomId = hotelActivityRoomId
      this.visible = true
      this.getDataList()
    },
    
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hotel/hotelactivityroomnumber/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'number': this.dataForm.number,
          'activityId': this.dataForm.activityId,
          'hotelActivityId': this.dataForm.hotelActivityId,
          'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    assignHandle(row) {
      // 把房间号返回上一页
      this.$emit('select', row.number)
      this.visible = false
    }
  }
}
</script>
