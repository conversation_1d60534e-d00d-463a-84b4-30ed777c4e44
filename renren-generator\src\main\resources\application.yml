server:
  port: 80

# mysql
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #MySQL配置
    # driverClassName: com.mysql.cj.jdbc.Driver
    # url: **************************************************************************************************************************
    # username: cjy
    # password: Cjymp@123
    # driverClassName: com.mysql.cj.jdbc.Driver
    # url: **************************************************************************************************************************
    # username: jilemall
    # password: jilemall@123
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************
    username: pyp
    password: pyp@123
    #oracle配置
    #    driverClassName: oracle.jdbc.OracleDriver
    #    url: ****************************************
    #    username: renren
    #    password: 123456
    #SQLServer配置
    #    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #    url: jdbc:sqlserver://*************:1433;DatabaseName=renren_fast
    #    username: sa
    #    password: 123456
    #PostgreSQL配置
  #    driverClassName: org.postgresql.Driver
  #    url: ************************************************
  #    username: postgres
  #    password: 123456



  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  resources:
    static-locations: classpath:/static/,classpath:/views/

#mongodb:
#  host: localhost
#  port: 27017
#  auth: false #是否使用密码验证
#  username: tincery
#  password: renren
#  source: 123456
#  database: test

mybatis-plus:
  mapperLocations: classpath:mapper/**/*.xml


pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


#指定数据库，可选值有【mysql、oracle、sqlserver、postgresql、mongodb】
renren:
  database: mysql

