<template>
    <div :class="isMobilePhone ? '' : 'pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <div class="page-header">
            <van-nav-bar title="航班改签申请" left-arrow @click-left="goBack" />
        </div>

        <div class="trip-info-card">
            <div class="card-title">原行程信息</div>
            <div class="flight-info">
                <div class="flight-route">
                    <span class="city">{{ tripInfo.inStartPlace }}</span>
                    <van-icon name="arrow" />
                    <span class="city">{{ tripInfo.inEndPlace }}</span>
                </div>
                <div class="flight-details">
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '航班号：' : '车次：' }}</span>
                        <span class="value">{{ tripInfo.inNumber }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '起飞时间：' : '出发时间：' }}</span>
                        <span class="value">{{ formatDateTime(tripInfo.inStartDate) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '到达时间：' : '到站时间：' }}</span>
                        <span class="value">{{ formatDateTime(tripInfo.inEndDate) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="change-form">
            <div class="form-title">改签信息</div>

            <!-- 参考tripEdit.vue的查询模式UI -->
            <div class="travel-info-input">
                <div class="location-selector">
                    <div class="location from" @click="showCitySelector('start')">
                        <div class="location-text">{{ searchParams.inStartPlace || '出发城市' }}</div>
                    </div>
                    <div class="location-arrow">
                        <van-icon name="arrow" />
                    </div>
                    <div class="location to" @click="showCitySelector('end')">
                        <div class="location-text">{{ searchParams.inEndPlace || '到达城市' }}</div>
                    </div>
                </div>

                <div class="date-selector" @click="showTravelDatePicker = true">
                    <div class="date">
                        {{ formatDisplayDate(searchParams.travelDate) || '请选择日期' }}
                    </div>
                    <van-icon name="calendar-o" />
                </div>

                <div class="search-button" @click="goToSelectTransport">
                    <span>{{ tripInfo.inType == 0 ? '搜索机票' : '搜索火车票' }}</span>
                </div>
            </div>

            <!-- 已选择的航班/火车票信息 -->
            <div v-if="hasSelectedTransport" class="selected-transport">
                <div class="card-title">{{ tripInfo.inType == 0 ? '已选择航班' : '已选择火车票' }}</div>
                <div class="transport-info">
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '航班号：' : '车次：' }}</span>
                        <span class="value">{{ changeForm.chaNumber }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">出发地：</span>
                        <span class="value">{{ changeForm.chaStartPlace }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">目的地：</span>
                        <span class="value">{{ changeForm.chaEndPlace }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '起飞时间：' : '出发时间：' }}</span>
                        <span class="value">{{ formatDateTime(changeForm.chaStartDate) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">{{ tripInfo.inType == 0 ? '到达时间：' : '到站时间：' }}</span>
                        <span class="value">{{ formatDateTime(changeForm.chaEndDate) }}</span>
                    </div>
                    <!-- <div v-if="tripInfo.inType == 0" class="detail-item">
                        <span class="label">舱位：</span>
                        <span class="value">{{ changeForm.cabinCode }}</span>
                    </div> -->
                </div>
                <div style="margin: 16px;">
                    <van-button round block type="info" @click="onSubmit">提交改签申请</van-button>
                </div>
                <div style="margin: 16px;">
                    <van-button round block type="default" @click="goBack">取消</van-button>
                </div>
            </div>
        </div>

        <!-- 添加城市搜索弹窗 -->
        <van-popup v-model="showStartSearch" position="bottom" :style="{ height: '70%' }" round>
            <div class="search-header">
                <van-search v-model="startSearchText" placeholder="请输入城市名称或机场/车站名称" @input="searchStartPlaces"
                    shape="round" background="#f7f8fa" autofocus />
                <van-icon name="cross" @click="showStartSearch = false" class="close-icon" />
            </div>
            <div class="search-results">
                <div v-if="startSearchResults.length === 0 && startSearchText && !startLoading" class="empty-result">
                    <van-empty description="暂无搜索结果" />
                </div>
                <div v-for="(item, index) in startSearchResults" :key="index" class="search-item"
                    @click="selectStartPlace(item)">
                    <div class="item-main">{{ tripInfo.inType == 0 ? item.airportName : item.stationName }}</div>
                    <div class="item-sub">{{ item.cityName }}</div>
                </div>
            </div>
        </van-popup>

        <van-popup v-model="showEndSearch" position="bottom" :style="{ height: '70%' }" round>
            <div class="search-header">
                <van-search v-model="endSearchText" placeholder="请输入城市名称或机场/车站名称" @input="searchEndPlaces" shape="round"
                    background="#f7f8fa" autofocus />
                <van-icon name="cross" @click="showEndSearch = false" class="close-icon" />
            </div>
            <div class="search-results">
                <div v-if="endSearchResults.length === 0 && endSearchText && !endLoading" class="empty-result">
                    <van-empty description="暂无搜索结果" />
                </div>
                <div v-for="(item, index) in endSearchResults" :key="index" class="search-item"
                    @click="selectEndPlace(item)">
                    <div class="item-main">{{ tripInfo.inType == 0 ? item.airportName : item.stationName }}</div>
                    <div class="item-sub">{{ item.cityName }}</div>
                </div>
            </div>
        </van-popup>

        <!-- 出行日期选择弹窗 -->
        <van-popup v-model="showTravelDatePicker" position="bottom">
            <van-datetime-picker v-model="travelDate" type="date" title="选择出行日期" :min-date="minDate" :max-date="maxDate"
                @confirm="onTravelDateConfirm" @cancel="showTravelDatePicker = false" />
        </van-popup>
    </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";

export default {
    components: { pcheader },
    data() {
        return {
            isMobilePhone: isMobilePhone(),
            id: undefined,
            tripId: undefined,
            tripInfo: {},
            loading: false,
            showDatePicker: false,
            showTravelDatePicker: false,
            minDate: new Date(2024, 0, 1),
            maxDate: new Date(2030, 10, 1),
            currentDate: new Date(),
            travelDate: new Date(),
            hasSelectedTransport: false,

            // 城市搜索相关
            showStartSearch: false,
            showEndSearch: false,
            startSearchText: '',
            endSearchText: '',
            startSearchResults: [],
            endSearchResults: [],
            startLoading: false,
            endLoading: false,

            // 搜索参数
            searchParams: {
                startCity: '',
                startCityCode: '',
                endCity: '',
                endCityCode: '',
                travelDate: ''
            },

            changeForm: {
                chaDate: '',
                chaNumber: '',
                chaEndPlace: '',
                chaEndDate: '',
                chaStartDate: '',
                chaPrice: '',
                chaStartPlace: '',
                chaStartCityCode: '',
                chaEndCityCode: '',
                chaStartCity: '',
                chaEndCity: '',
                chaStartTerminal: '',
                chaEndTerminal: '',
                chaTicketNo: '',
                cabinCode: '',
                cabinBookPara: '',
                tripId: ''
            }
        };
    },
    mounted() {
        this.id = this.$route.query.detailId;
        this.tripId = this.$route.query.tripId;
        this.changeForm.tripId = this.tripId;

        if (this.tripId) {
            this.getTripInfo();
        } else {
            this.goBack();
            vant.Toast('行程信息不存在');
        }

        // 检查是否有从选择页面返回的数据
        this.checkSelectedTransport();
    },
    // 添加 activated 生命周期钩子，处理页面激活时的逻辑
    activated() {
        // 检查是否有从选择页面返回的数据
        this.checkSelectedTransport();
    },
    methods: {
        // 显示城市选择器
        showCitySelector(type) {
            if (type === 'start') {
                this.showStartSearch = true;
            } else {
                this.showEndSearch = true;
            }
        },
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            return date.formatDate.format(new Date(dateTime), "yyyy/MM/dd hh:mm");
        },

        formatDisplayDate(dateStr) {
            if (!dateStr) return '';
            const dateObj = new Date(dateStr);
            const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            const month = dateObj.getMonth() + 1;
            const day = dateObj.getDate();
            const weekDay = weekDays[dateObj.getDay()];
            return `${month}月${day}日 ${weekDay}`;
        },

        goBack() {
            this.$router.go(-1);
        },

        getTripInfo() {
            this.loading = true;
            this.$fly
                .get(`/pyp/web/activity/activityguest/getTripById/${this.tripId}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.tripInfo = res.result;
                        // 设置当前日期为改签日期
                        this.currentDate = new Date();
                        this.changeForm.chaDate = date.formatDate.format(this.currentDate, "yyyy/MM/dd");

                        // 预填充搜索参数
                        this.searchParams.inStartPlace = this.tripInfo.inStartPlace || '';
                        this.searchParams.inEndPlace = this.tripInfo.inEndPlace || '';
                        this.searchParams.startCityCode = this.tripInfo.startCityCode || '';
                        this.searchParams.endCityCode = this.tripInfo.endCityCode || '';

                        // 设置出行日期为原行程日期
                        if (this.tripInfo.inStartDate) {
                            this.travelDate = new Date(this.tripInfo.inStartDate);
                            this.searchParams.travelDate = date.formatDate.format(this.travelDate, "yyyy/MM/dd");
                        } else {
                            this.travelDate = new Date();
                            this.searchParams.travelDate = date.formatDate.format(this.travelDate, "yyyy/MM/dd");
                        }
                    } else {
                        vant.Toast(res.msg);
                        this.tripInfo = {};
                    }
                })
                .catch(() => {
                    this.loading = false;
                    vant.Toast('获取行程信息失败');
                });
        },

        // 搜索出发地
        searchStartPlaces() {
            if (this.startSearchText === '') {
                this.startSearchResults = [];
                return;
            }

            this.startLoading = true;
            clearTimeout(this.startTimeout);

            this.startTimeout = setTimeout(() => {
                const url = this.tripInfo.inType == 0
                    ? "/pyp/web/config/configairport/findByName"
                    : "/pyp/web/config/configtrainstation/findByName";

                this.$fly.get(url, {
                    name: this.startSearchText
                }).then(res => {
                    this.startLoading = false;
                    if (res && res.code === 200) {
                        this.startSearchResults = res.result;
                    } else {
                        vant.Toast(res.msg || '搜索失败');
                        this.startSearchResults = [];
                    }
                }).catch(() => {
                    this.startLoading = false;
                    vant.Toast('网络错误，请重试');
                });
            }, 300);
        },

        // 搜索目的地
        searchEndPlaces() {
            if (this.endSearchText === '') {
                this.endSearchResults = [];
                return;
            }

            this.endLoading = true;
            clearTimeout(this.endTimeout);

            this.endTimeout = setTimeout(() => {
                const url = this.tripInfo.inType == 0
                    ? "/pyp/web/config/configairport/findByName"
                    : "/pyp/web/config/configtrainstation/findByName";

                this.$fly.get(url, {
                    name: this.endSearchText
                }).then(res => {
                    this.endLoading = false;
                    if (res && res.code === 200) {
                        this.endSearchResults = res.result;
                    } else {
                        vant.Toast(res.msg || '搜索失败');
                        this.endSearchResults = [];
                    }
                }).catch(() => {
                    this.endLoading = false;
                    vant.Toast('网络错误，请重试');
                });
            }, 300);
        },

        // 选择出发地
        selectStartPlace(item) {
            if (this.tripInfo.inType == 0) {
                console.log(item)
                // 飞机
                this.searchParams.inStartPlace = item.airportName;
                this.searchParams.inStartCity = item.cityName;
                this.searchParams.startCityCode = item.cityCode;
            } else {
                // 火车
                this.searchParams.inStartPlace = item.stationName;
                this.searchParams.inStartCity = item.cityName;
                this.searchParams.startCityCode = item.stationCode;
            }
            this.showStartSearch = false;
        },

        // 选择目的地
        selectEndPlace(item) {
            if (this.tripInfo.inType == 0) {
                // 飞机
                this.searchParams.inEndPlace = item.airportName;
                this.searchParams.inEndCity = item.cityName;
                this.searchParams.endCityCode = item.cityCode;
            } else {
                // 火车
                this.searchParams.inEndPlace = item.stationName;
                this.searchParams.inEndCity = item.cityName;
                this.searchParams.endCityCode = item.stationCode;
            }
            this.showEndSearch = false;
        },

        // 确认出行日期
        onTravelDateConfirm(value) {
            this.travelDate = value;
            this.searchParams.travelDate = date.formatDate.format(value, "yyyy/MM/dd");
            this.showTravelDatePicker = false;
        },

        goToSelectTransport() {
            // 验证搜索参数
            if (!this.searchParams.inStartPlace) {
                vant.Toast('请选择出发地');
                return;
            }

            if (!this.searchParams.inEndPlace) {
                vant.Toast('请选择目的地');
                return;
            }

            if (!this.searchParams.travelDate) {
                vant.Toast('请选择出行日期');
                return;
            }

            // 根据行程类型跳转到对应的选择页面
            const query = {
                from: 'change',
                startCity: this.searchParams.inStartPlace,
                startCityCode: this.searchParams.startCityCode,
                endCity: this.searchParams.inEndPlace,
                endCityCode: this.searchParams.endCityCode,
                date: this.searchParams.travelDate,
                tripId: this.tripId
            };

            if (this.tripInfo.inType == 0) {
                // 飞机
                this.$router.push({
                    path: '/schedules/expert/components/plane-change-select',
                    query: query
                });
            } else {
                // 火车
                this.$router.push({
                    path: '/schedules/expert/components/train-select',
                    query: query
                });
            }
        },
        // 检查是否有选中的交通工具数据
        checkSelectedTransport() {
            const selectedTransport = localStorage.getItem('selectedTransport');
            if (selectedTransport) {
                // 处理数据
                this.handleSelectTransport(JSON.parse(selectedTransport));
                // 清除存储的数据，避免重复处理
                localStorage.removeItem('selectedTransport');
            }
        },
        handleSelectTransport(data) {
            console.log(data);
            if (!data) return;

            // 根据行程类型处理选择的数据
            if (this.tripInfo.inType == 0) {
                // 飞机
                this.changeForm.chaNumber = data.flightNo || data.inNumber;
                this.changeForm.chaStartPlace = data.fromAirportName || data.inStartPlace;
                this.changeForm.chaEndPlace = data.toAirportName || data.inEndPlace;
                this.changeForm.chaStartDate = data.fromDateTime ? (data.fromDateTime + ':00').replaceAll("-", "/") : data.inStartDate;
                this.changeForm.chaEndDate = data.toDateTime ? (data.toDateTime + ':00').replaceAll("-", "/") : data.inEndDate;
                this.changeForm.chaStartCity = this.searchParams.inStartCity;
                this.changeForm.chaEndCity = this.searchParams.inEndCity;
                this.changeForm.chaStartTerminal = data.fromTerminal || data.inStartTerminal;
                this.changeForm.chaEndTerminal = data.toTerminal || data.inEndTerminal;
                this.changeForm.cabinCode = data.cabinCode || '';
                this.changeForm.cabinBookPara = data.cabinBookPara || '';
                this.changeForm.chaStartCityCode = data.fromAirportCode || data.startCityCode || '';
                this.changeForm.chaEndCityCode = data.toAirportCode || data.endCityCode || '';
                this.changeForm.price = data.price || '';
            } else {
                // 火车
                this.changeForm.chaNumber = data.trainNo || data.inNumber;
                this.changeForm.chaStartPlace = data.depStation || data.inStartPlace;
                this.changeForm.chaEndPlace = data.arrStation || data.inEndPlace;
                this.changeForm.chaStartDate = data.depTime || data.inStartDate;
                this.changeForm.chaEndDate = data.arrTime || data.inEndDate;
                this.changeForm.chaStartCity = this.searchParams.inStartCity;
                this.changeForm.chaEndCity = this.searchParams.inEndCity;
                this.changeForm.chaStartCityCode = data.startCityCode || '';
                this.changeForm.chaEndCityCode = data.endCityCode || '';
                this.changeForm.price = data.price || '';
            }

            this.hasSelectedTransport = true;
        },

        onDateConfirm(value) {
            this.currentDate = value;
            this.changeForm.chaDate = date.formatDate.format(value, "yyyy/MM/dd");
            this.showDatePicker = false;
        },

        onSubmit() {
            // 表单验证通过后提交改签申请
            this.loading = true;

            this.$fly
                .post('/pyp/web/activity/activityguest/applyChangeTicket', this.changeForm)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        vant.Toast.success('改签申请提交成功');
                        setTimeout(() => {
                            this.goBack();
                        }, 1500);
                    } else {
                        vant.Toast(res.msg || '提交失败');
                    }
                })
                .catch(() => {
                    this.loading = false;
                    vant.Toast('网络错误，请稍后重试');
                });
        }
    }
};
</script>

<style lang="less" scoped>
.page-header {
    margin-bottom: 16px;
}

.trip-info-card {
    margin: 16px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-title,
.form-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #333;
    border-left: 3px solid #1989fa;
    padding-left: 8px;
}

.flight-info {
    padding: 8px 0;
}

.flight-route {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: bold;
}

.city {
    padding: 0 12px;
}

.flight-details,
.transport-info {
    background-color: #f8f8f8;
    padding: 12px;
    border-radius: 6px;
}

.detail-item {
    display: flex;
    margin-bottom: 8px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.label {
    color: #666;
    width: 80px;
    flex-shrink: 0;
}

.value {
    color: #333;
    font-weight: 500;
}

.change-form {
    margin: 16px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.form-title {
    margin-bottom: 16px;
}

.travel-info-input {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.location-selector {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.location {
    flex: 1;
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    text-align: center;
}

.location-text {
    font-size: 16px;
    color: #333;
}

.location-arrow {
    padding: 0 12px;
    color: #999;
}

.date-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: white;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
}

.search-button {
    background-color: #1989fa;
    color: white;
    text-align: center;
    padding: 12px;
    border-radius: 4px;
    font-weight: bold;
}

.select-buttons {
    margin-bottom: 20px;
}

.selected-transport {
    margin: 16px 0;
    padding: 16px;
    background-color: #f8f8f8;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
}


/* 修改搜索弹窗相关样式 */
.search-header {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
}

.search-header .van-search {
    flex: 1;
    margin-right: 10px;
}

.close-icon {
    padding: 0 10px;
    font-size: 20px;
    color: #666;
}

.search-results {
    // height: calc(100% - 64px);
    overflow-y: auto;
    padding-bottom: 20px;
}

/* 新增搜索结果项样式 */
.search-item {
    padding: 15px;
    position: relative;
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
}

.search-item:active {
    background-color: #f2f3f5;
}

.item-main {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.item-sub {
    font-size: 14px;
    color: #999;
}

.loading-more {
    text-align: center;
    padding: 15px 0;
    color: #666;
}

/* 修改输入框样式 */
.location .van-field {
    padding: 0;
    background-color: transparent;
}

.location .van-field__control {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.location .van-field__control::placeholder {
    color: #999;
    font-weight: normal;
}
</style>