<template>
  <div
    :class="isMobilePhone ? '' : 'pc-container'"
    
  >
    <pcheader v-if="!isMobilePhone" />
    <van-search
      v-model="dataForm.name"
      placeholder="请输入您要搜索的展商名称"
      show-action
      shape="round"
    >
      <div slot="action" @click="onSearch" class="search-text">搜索</div>
    </van-search>

  <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">展商列表</div>
        </div>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
      style="display: flex;flex-wrap: wrap;justify-content: space-between;"
    >
      <van-card
        :class="!item.isBig ? 'small' : 'big'"
        style="background: white"
        v-for="item in dataList"
        :key="item.id"
        @click="turnDetail(item)"
        :thumb="!item.picUrl ? 'van-icon' : item.picUrl"
      >
        <div v-if="item.isBig" slot="title" style="font-size: 18px; overflow:hidden; text-overflow:ellipsis;white-space: nowrap;">{{ item.name }}</div>
        <div v-if="item.isBig"
          slot="desc"
          style="padding-top: 10px; font-size: 14px; color: grey"
        >
        <div>{{item.brief}}</div>
        </div>
        <template #num>
          <van-button v-if="item.isBig" size="small" round type="primary" plain
            >查看详情</van-button
          >
        </template>
      </van-card>
    </van-list>
    <!-- 返回按钮 -->
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
  </div>
</template>

<script>
import { isMobilePhone } from "@/js/validate";
import date from "@/js/date.js";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: { pcheader },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      activityInfo: {},
      flag: false,
      openid: undefined,
      activityId: undefined,
      dataForm: {
        name: "",
        status: 1,
      },
      loading: false,
      finished: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid");
    this.getActivityInfo();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      if (!this.flag) {
      this.getActivityList();
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
          this.activityInfo.backImg =
            this.activityInfo.backImg ||
            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = this.activityInfo.name +"-展商列表";
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "展商列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "展商列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/merchant/merchant/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          name: this.dataForm.name,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    turnDetail(item) {
        if(item.url) {
            location.href = item.url;
        } else {
        this.$router.push({
            name: 'merchantDetail',
            query: { merchantId: item.id, id: item.activityId },
          })

        }
    },
    cmsTurnBack() {
      if(this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
  },
};
</script>

<style lang="less" scoped>
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
.van-list /deep/ .van-list__finished-text {
  width: 100%;
  text-align: center;
}
.big {
  width: 100%;
}
.small {
  width: 49%;
}
.van-card {
  margin-top: 8px;
}
.van-card__header {
  align-items: center;
  justify-content: center;
}
.small /deep/ .van-card__content {
  flex: 0;
}
</style>
