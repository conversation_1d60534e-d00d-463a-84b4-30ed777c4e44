<template>
  <div class="my-salesman-page">
    <!-- 头部 -->
    <div class="header">
      <van-nav-bar
        title="我的专属业务员"
        left-text="返回"
        left-arrow
        @click-left="$router.go(-1)"
      />
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 绑定状态 -->
      <div v-if="!loading">
        <!-- 已绑定业务员 -->
        <div v-if="binding" class="salesman-card">
          <div class="salesman-info">
            <div class="avatar">
              <van-image
                :src="salesmanAvatar"
                fit="cover"
                round
                width="60"
                height="60"
              >
                <template v-slot:error>
                  <van-icon name="user-o" size="30" />
                </template>
              </van-image>
            </div>
            <div class="info">
              <div class="name">{{ binding.salesmanName }}</div>
              <div class="code">编号：{{ binding.salesmanCode }}</div>
              <div class="binding-time">绑定时间：{{ formatDate(binding.bindingTime) }}</div>
            </div>
            <div class="status">
              <van-tag type="success" size="medium">已绑定</van-tag>
            </div>
          </div>
          
          <!-- 联系方式 -->
          <div class="contact-section">
            <div class="section-title">联系方式</div>
            <div class="contact-item" v-if="binding.salesmanMobile">
              <van-icon name="phone-o" />
              <span class="contact-text">{{ binding.salesmanMobile }}</span>
              <van-button
                type="primary"
                size="small"
                @click="callSalesman"
              >
                拨打电话
              </van-button>
            </div>
            <div class="contact-item">
              <van-icon name="chat-o" />
              <span class="contact-text">微信咨询</span>
              <van-button
                type="primary"
                size="small"
                @click="contactWechat"
              >
                联系微信
              </van-button>
            </div>
          </div>

          <!-- 服务说明 -->
          <div class="service-section">
            <div class="section-title">专属服务</div>
            <div class="service-list">
              <div class="service-item">
                <van-icon name="service-o" color="#1989fa" />
                <span>一对一专业咨询</span>
              </div>
              <div class="service-item">
                <van-icon name="gift-o" color="#1989fa" />
                <span>专享优惠活动</span>
              </div>
              <div class="service-item">
                <van-icon name="star-o" color="#1989fa" />
                <span>优先技术支持</span>
              </div>
              <div class="service-item">
                <van-icon name="medal-o" color="#1989fa" />
                <span>定制化解决方案</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <van-button
              type="warning"
              size="large"
              @click="showUnbindDialog"
              block
            >
              解除绑定
            </van-button>
          </div>
        </div>

        <!-- 未绑定业务员 -->
        <div v-else class="no-binding">
          <div class="empty-state">
            <van-empty
              image="https://img.yzcdn.cn/vant/custom-empty-image.png"
              description="您还没有绑定专属业务员"
            />
          </div>
          
          <div class="binding-options">
            <div class="option-title">绑定方式</div>
            <div class="option-list">
              <div class="option-item" @click="scanQrCode">
                <van-icon name="scan" size="24" color="#1989fa" />
                <div class="option-content">
                  <div class="option-name">扫描二维码</div>
                  <div class="option-desc">扫描业务员专属二维码进行绑定</div>
                </div>
                <van-icon name="arrow" />
              </div>
              
              <div class="option-item" @click="showInviteCodeDialog">
                <van-icon name="edit" size="24" color="#1989fa" />
                <div class="option-content">
                  <div class="option-name">输入邀请码</div>
                  <div class="option-desc">输入业务员提供的邀请码</div>
                </div>
                <van-icon name="arrow" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-state">
        <van-loading type="spinner" color="#1989fa">加载中...</van-loading>
      </div>
    </div>

    <!-- 解绑确认弹窗 -->
    <van-dialog
      v-model="unbindDialogVisible"
      title="解除绑定"
      show-cancel-button
      @confirm="unbindSalesman"
    >
      <div class="unbind-content">
        <p>确定要解除与业务员的绑定关系吗？</p>
        <p style="color: #999; font-size: 14px;">解绑后将无法享受专属服务</p>
        <van-field
          v-model="unbindReason"
          type="textarea"
          placeholder="请输入解绑原因（可选）"
          rows="3"
          maxlength="200"
          show-word-limit
        />
      </div>
    </van-dialog>

    <!-- 邀请码输入弹窗 -->
    <van-dialog
      v-model="inviteCodeDialogVisible"
      title="输入邀请码"
      show-cancel-button
      @confirm="bindByInviteCode"
    >
      <div class="invite-code-content">
        <van-field
          v-model="inviteCode"
          placeholder="请输入6-8位邀请码"
          maxlength="8"
          clearable
        />
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'MySalesman',
  data() {
    return {
      loading: true,
      binding: null,
      unbindDialogVisible: false,
      unbindReason: '',
      inviteCodeDialogVisible: false,
      inviteCode: ''
    }
  },
  computed: {
    salesmanAvatar() {
      // 默认头像或业务员头像
      return 'https://img.yzcdn.cn/vant/cat.jpeg'
    }
  },
  mounted() {
    this.getMyBinding()
  },
  methods: {
    // 获取我的绑定信息
    async getMyBinding() {
      try {
        this.loading = true
        const response = await this.$fly.get('/pyp/web/salesman/binding/myBinding')
        if (response.code ===200) {
          this.binding = response.binding
        }
      } catch (error) {
        this.$toast('获取绑定信息失败')
      } finally {
        this.loading = false
      }
    },

    // 扫描二维码
    scanQrCode() {
      // 调用扫码功能
      if (window.wx) {
        window.wx.scanQRCode({
          needResult: 1,
          scanType: ["qrCode"],
          success: (res) => {
            this.bindByQrCode(res.resultStr)
          },
          fail: () => {
            this.$toast('扫码失败，请重试')
          }
        })
      } else {
        // 非微信环境，跳转到扫码页面
        this.$router.push('/salesman/scan-qrcode')
      }
    },

    // 通过二维码绑定
    async bindByQrCode(qrCodeContent) {
      try {
        const response = await this.$fly.post('/pyp/web/salesman/binding/bindByQrCode', {
          qrCodeContent
        })
        if (response.code ===200) {
          this.$toast.success('绑定成功')
          this.getMyBinding()
        } else {
          this.$toast(response.data.msg || '绑定失败')
        }
      } catch (error) {
        this.$toast('绑定失败，请重试')
      }
    },

    // 显示邀请码输入弹窗
    showInviteCodeDialog() {
      this.inviteCode = ''
      this.inviteCodeDialogVisible = true
    },

    // 通过邀请码绑定
    async bindByInviteCode() {
      if (!this.inviteCode.trim()) {
        this.$toast('请输入邀请码')
        return
      }

      try {
        const response = await this.$fly.post('/pyp/web/salesman/binding/bindByInviteCode', {
          inviteCode: this.inviteCode.trim()
        })
        if (response.code ===200) {
          this.$toast.success('绑定成功')
          this.inviteCodeDialogVisible = false
          this.getMyBinding()
        } else {
          this.$toast(response.data.msg || '绑定失败')
        }
      } catch (error) {
        this.$toast('绑定失败，请重试')
      }
    },

    // 拨打电话
    callSalesman() {
      if (this.binding && this.binding.salesmanMobile) {
        window.location.href = `tel:${this.binding.salesmanMobile}`
      }
    },

    // 联系微信
    contactWechat() {
      this.$toast('请联系业务员获取微信号')
    },

    // 显示解绑弹窗
    showUnbindDialog() {
      this.unbindReason = ''
      this.unbindDialogVisible = true
    },

    // 解除绑定
    async unbindSalesman() {
      try {
        const response = await this.$fly.post('/pyp/web/salesman/binding/unbind', {
          reason: this.unbindReason || '用户主动解除绑定'
        })
        if (response.code ===200) {
          this.$toast.success('解绑成功')
          this.unbindDialogVisible = false
          this.getMyBinding()
        } else {
          this.$toast(response.data.msg || '解绑失败')
        }
      } catch (error) {
        this.$toast('解绑失败，请重试')
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.my-salesman-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 16px;
}

.salesman-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.salesman-info {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
}

.avatar {
  margin-right: 16px;
}

.info {
  flex: 1;
}

.name {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.code {
  font-size: 14px;
  color: #969799;
  margin-bottom: 4px;
}

.binding-time {
  font-size: 12px;
  color: #c8c9cc;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 12px;
}

.contact-section {
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.contact-item .van-icon {
  margin-right: 12px;
  color: #1989fa;
}

.contact-text {
  flex: 1;
  font-size: 14px;
  color: #323233;
}

.service-section {
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
}

.service-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.service-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #323233;
}

.service-item .van-icon {
  margin-right: 8px;
}

.action-buttons {
  padding: 20px;
}

.no-binding {
  text-align: center;
}

.empty-state {
  margin-bottom: 32px;
}

.binding-options {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.option-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebedf0;
  cursor: pointer;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item .van-icon:first-child {
  margin-right: 12px;
}

.option-content {
  flex: 1;
}

.option-name {
  font-size: 16px;
  color: #323233;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 14px;
  color: #969799;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.unbind-content {
  padding: 16px;
}

.unbind-content p {
  margin: 0 0 16px 0;
  text-align: center;
}

.invite-code-content {
  padding: 16px;
}
</style>
