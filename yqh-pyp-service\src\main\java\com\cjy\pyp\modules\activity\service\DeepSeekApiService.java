// package com.cjy.pyp.modules.activity.service;

// import com.fasterxml.jackson.databind.ObjectMapper;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.http.HttpEntity;
// import org.springframework.http.HttpHeaders;
// import org.springframework.http.MediaType;
// import org.springframework.stereotype.Service;
// import org.springframework.web.client.ResourceAccessException;
// import org.springframework.web.client.RestTemplate;

// import java.net.SocketTimeoutException;
// import java.util.*;

// /**
//  * DeepSeek API服务类
//  * 
//  * <AUTHOR>
//  * @date 2025-06-27
//  */
// @Service
// public class DeepSeekApiService {

//     @Autowired
//     private RestTemplate restTemplate;

//     @Value("${deepseek.api.key}")
//     private String deepseekApiKey;

//     @Value("${deepseek.api.url:https://api.deepseek.com}")
//     private String deepseekApiUrl;

//     @Value("${deepseek.api.max-retries:3}")
//     private int maxRetries;

//     @Value("${deepseek.api.retry-delay:2000}")
//     private int retryDelay;

//     /**
//      * 调用DeepSeek API生成文本，带重试机制
//      *
//      * @param prompt 提示词
//      * @param model 模型名称
//      * @return 生成的文本内容
//      * @throws Exception 调用失败时抛出异常
//      */
//     public String generateText(String prompt, String model) throws Exception {
//         for (int attempt = 1; attempt <= maxRetries; attempt++) {
//             try {
//                 return doCallDeepSeekApi(prompt, model);
//             } catch (ResourceAccessException e) {
//                 if (e.getCause() instanceof SocketTimeoutException || 
//                     e.getMessage().contains("Read timed out")) {
                    
//                     if (attempt == maxRetries) {
//                         throw new Exception("DeepSeek API调用超时，已重试" + maxRetries + "次，请稍后再试");
//                     }
                    
//                     // 等待后重试，使用递增延迟
//                     try {
//                         Thread.sleep(retryDelay * attempt);
//                     } catch (InterruptedException ie) {
//                         Thread.currentThread().interrupt();
//                         throw new Exception("API调用被中断");
//                     }
//                 } else {
//                     throw new Exception("DeepSeek API网络连接失败: " + e.getMessage());
//                 }
//             } catch (Exception e) {
//                 if (attempt == maxRetries) {
//                     throw new Exception("DeepSeek API调用失败: " + e.getMessage());
//                 }
                
//                 // 对于其他异常也进行重试
//                 try {
//                     Thread.sleep(retryDelay);
//                 } catch (InterruptedException ie) {
//                     Thread.currentThread().interrupt();
//                     throw new Exception("API调用被中断");
//                 }
//             }
//         }
        
//         throw new Exception("DeepSeek API调用失败，已达到最大重试次数");
//     }

//     /**
//      * 实际调用DeepSeek API的方法
//      */
//     private String doCallDeepSeekApi(String prompt, String model) throws Exception {
//         // 验证输入参数
//         if (prompt == null || prompt.trim().isEmpty()) {
//             throw new Exception("提示词不能为空");
//         }

//         HttpHeaders headers = new HttpHeaders();
//         headers.setContentType(MediaType.APPLICATION_JSON);
//         headers.set("Authorization", "Bearer " + deepseekApiKey);

//         ObjectMapper mapper = new ObjectMapper();

//         // 构建请求消息
//         Map<String, Object> message = new HashMap<>();
//         message.put("role", "user");
//         message.put("content", prompt);

//         List<Map<String, Object>> messages = new ArrayList<>();
//         messages.add(message);

//         Map<String, Object> requestBody = new HashMap<>();
//         requestBody.put("model", model != null ? model : "deepseek-chat");
//         requestBody.put("messages", messages);
//         requestBody.put("stream", false);
//         requestBody.put("max_tokens", 4000); // 设置最大token数
//         requestBody.put("temperature", 0.7); // 设置创造性

//         HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(requestBody), headers);
        
//         // 发送请求
//         String response = restTemplate.postForObject(deepseekApiUrl + "/v1/chat/completions", request, String.class);

//         if (response == null || response.trim().isEmpty()) {
//             throw new Exception("DeepSeek API返回空响应");
//         }

//         // 解析响应
//         return parseApiResponse(response, mapper);
//     }

//     /**
//      * 解析API响应
//      */
//     private String parseApiResponse(String response, ObjectMapper mapper) throws Exception {
//         try {
//             @SuppressWarnings("unchecked")
//             Map<String, Object> responseMap = mapper.readValue(response, Map.class);
            
//             // 检查是否有错误
//             if (responseMap.containsKey("error")) {
//                 @SuppressWarnings("unchecked")
//                 Map<String, Object> error = (Map<String, Object>) responseMap.get("error");
//                 String errorMessage = (String) error.get("message");
//                 String errorType = (String) error.get("type");
//                 throw new Exception("DeepSeek API错误 [" + errorType + "]: " + errorMessage);
//             }
            
//             // 获取生成的内容
//             List<?> choices = (List<?>) responseMap.get("choices");
//             if (choices == null || choices.isEmpty()) {
//                 throw new Exception("DeepSeek API返回的choices为空");
//             }
            
//             @SuppressWarnings("unchecked")
//             Map<String, Object> choice = (Map<String, Object>) choices.get(0);
//             @SuppressWarnings("unchecked")
//             Map<String, Object> message = (Map<String, Object>) choice.get("message");
            
//             String content = (String) message.get("content");
//             if (content == null || content.trim().isEmpty()) {
//                 throw new Exception("DeepSeek API返回的内容为空");
//             }
            
//             return content;
            
//         } catch (Exception e) {
//             if (e.getMessage().contains("DeepSeek API")) {
//                 throw e; // 重新抛出我们自定义的异常
//             }
//             throw new Exception("解析DeepSeek API响应失败: " + e.getMessage());
//         }
//     }

//     /**
//      * 检查API配置是否正确
//      */
//     public boolean isConfigured() {
//         return deepseekApiKey != null && !deepseekApiKey.trim().isEmpty() &&
//                deepseekApiUrl != null && !deepseekApiUrl.trim().isEmpty();
//     }

//     /**
//      * 获取API配置信息（用于调试）
//      */
//     public Map<String, Object> getConfigInfo() {
//         Map<String, Object> config = new HashMap<>();
//         config.put("apiUrl", deepseekApiUrl);
//         config.put("maxRetries", maxRetries);
//         config.put("retryDelay", retryDelay);
//         config.put("hasApiKey", deepseekApiKey != null && !deepseekApiKey.trim().isEmpty());
//         return config;
//     }
// }
