<template>
  <div class="file-size-test">
    <h2>文件大小格式化测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>文件大小格式化测试</span>
      </div>
      
      <el-table :data="testData" border style="width: 100%">
        <el-table-column prop="description" label="描述" width="200"></el-table-column>
        <el-table-column prop="bytes" label="字节数" width="150"></el-table-column>
        <el-table-column prop="formatted" label="格式化结果" width="150">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.bytes) }}
          </template>
        </el-table-column>
        <el-table-column prop="expected" label="预期结果" width="150"></el-table-column>
        <el-table-column label="是否正确" width="100">
          <template slot-scope="scope">
            <el-tag :type="formatFileSize(scope.row.bytes) === scope.row.expected ? 'success' : 'danger'">
              {{ formatFileSize(scope.row.bytes) === scope.row.expected ? '✓' : '✗' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <el-card>
      <div slot="header">
        <span>自定义测试</span>
      </div>
      
      <el-form :inline="true">
        <el-form-item label="输入字节数:">
          <el-input-number 
            v-model="customBytes" 
            :min="0" 
            :max="999999999999"
            placeholder="请输入字节数"
          ></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addCustomTest">测试</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="customResult" class="custom-result">
        <h4>测试结果：</h4>
        <p><strong>输入:</strong> {{ customBytes }} 字节</p>
        <p><strong>输出:</strong> {{ customResult }}</p>
        <p><strong>计算:</strong> {{ customBytes }} ÷ (1024 × 1024) = {{ (customBytes / (1024 * 1024)).toFixed(6) }}</p>
        <p><strong>保留2位小数:</strong> {{ (customBytes / (1024 * 1024)).toFixed(2) }} MB</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FileSizeTest',
  data() {
    return {
      customBytes: 0,
      customResult: '',
      testData: [
        {
          description: '0字节',
          bytes: 0,
          expected: '0.00 MB'
        },
        {
          description: '1字节',
          bytes: 1,
          expected: '0.00 MB'
        },
        {
          description: '1KB (1024字节)',
          bytes: 1024,
          expected: '0.00 MB'
        },
        {
          description: '100KB',
          bytes: 102400,
          expected: '0.10 MB'
        },
        {
          description: '500KB',
          bytes: 512000,
          expected: '0.49 MB'
        },
        {
          description: '1MB',
          bytes: 1048576,
          expected: '1.00 MB'
        },
        {
          description: '1.5MB',
          bytes: 1572864,
          expected: '1.50 MB'
        },
        {
          description: '2.25MB',
          bytes: 2359296,
          expected: '2.25 MB'
        },
        {
          description: '10MB',
          bytes: 10485760,
          expected: '10.00 MB'
        },
        {
          description: '100MB',
          bytes: 104857600,
          expected: '100.00 MB'
        },
        {
          description: '1GB (1024MB)',
          bytes: 1073741824,
          expected: '1024.00 MB'
        }
      ]
    }
  },
  methods: {
    // 格式化文件大小 - 固定显示为MB，保留2位小数
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },
    
    addCustomTest() {
      this.customResult = this.formatFileSize(this.customBytes)
    }
  }
}
</script>

<style scoped>
.file-size-test {
  padding: 20px;
}

.custom-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.custom-result h4 {
  margin-top: 0;
  color: #409EFF;
}

.custom-result p {
  margin: 8px 0;
  font-family: 'Courier New', monospace;
}
</style>
