<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-card style="background: white"
      :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
      <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary" plain>{{
          guestInfo.unit }}</van-tag>
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning" plain>{{
          guestInfo.duties }}</van-tag>
      </div>
    </van-card>
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">劳务费信息填写</div>
    </div>
    <van-cell-group inset>
      <van-field v-model="guestInfo.mobile" name="联系方式" label="联系方式" required
        :rules="[{ required: true, message: '请填写联系方式' }]">
      </van-field>
            <van-field v-model="guestInfo.unit" name="工作单位" label="工作单位" required
            :rules="[{ required: true, message: '请填写工作单位' }]">
            </van-field>
      <van-cell title="身份证类型" :value="guestInfo.idCardType" @click="idCardTypeShow = true" is-link required>
      </van-cell>
      <van-field v-model="guestInfo.idCard" name="身份证" label="身份证" required
        :rules="[{ required: true, message: '请填写身份证' }]">
      </van-field>
      <van-cell v-model="guestInfo.areaName" required title="地区" is-link @click="areaShow = true" />
      <van-field v-model="guestInfo.bank" name="银行卡号" label="银行卡号" required
        :rules="[{ required: true, message: '请填写银行卡号' }]">
      </van-field>
      <div style="font-size: 14px;color: red;margin-left: 10px;">开户行（具体到支行）</div>
      <van-field v-model="guestInfo.kaihuhang" name="开户行" label="开户行" required
        :rules="[{ required: true, message: '请填写开户行(精确到支行)' }]">
      </van-field>
      <van-cell title="身份证正面" required :rules="[{ required: true, message: '请上传身份证正面' }]">
        <van-uploader :after-read="afterRead" name="idCardZheng" :before-read="beforeRead" accept="image/*">
          <van-icon v-if="!guestInfo.idCardZheng" slot="default" name="plus" size="50px"
            style="margin-left: 5px"></van-icon>
          <van-image v-else height="50px" :src="guestInfo.idCardZheng" fit="contain" />
        </van-uploader>
      </van-cell>
      <van-cell title="身份证反面" required :rules="[{ required: true, message: '请上传身份证反面' }]">
        <van-uploader :after-read="afterRead" name="idCardFan" :before-read="beforeRead" accept="image/*">
          <van-icon v-if="!guestInfo.idCardFan" slot="default" name="plus" size="50px"
            style="margin-left: 5px"></van-icon>
          <van-image v-else height="50px" :src="guestInfo.idCardFan" fit="contain" />
        </van-uploader>
      </van-cell>
      <!-- <van-cell title="确认签名" required :rules="[{ required: true, message: '确认签名' }]">

        <van-image v-if="guestInfo.serviceSign" height="50px" :src="guestInfo.serviceSign" fit="contain" />
        <van-button v-if="!guestInfo.serviceSign && !guestInfo.isServiceSign" size="small" type="primary"
          @click="signVisible = true">点击签名</van-button>
        <van-button v-else-if="guestInfo.serviceSign && !guestInfo.isServiceSign" size="small" type="primary"
          @click="signVisible = true">重新签名</van-button>
      </van-cell> -->
    </van-cell-group>
    <div style="margin: 16px">
      <van-button round block type="info" @click="submit" :loading="loading" loading-text="提交中">核对无误，请点击确认</van-button>
    </div>
    <div style="margin: 16px">
      <van-button round block type="primary" @click="
        $router.replace({
          path: '/schedules/expertIndex',
          query: { detailId: id },
        });">返回上一页面</van-button>
    </div>
    <!-- 省市区弹窗 -->
    <van-popup v-model="areaShow" position="bottom" :style="{ height: '45%' }">
      <van-area @cancel="areaShow = false" @confirm="areaSelect" title="区域选择" :area-list="areaList" :value="areaCode" />
    </van-popup>
    <van-action-sheet v-model="idCardTypeShow" :actions="idCardType" @select="idCardTypeSelect" />
    <div class="sign" v-show="signVisible">
      <sign-canvas class="sign-canvas" ref="SignCanvas" :options="options" v-model="value" />

      <div style="
          height: 60px;
          width: 100%;
          position: absolute;
          z-index: 999;
          bottom: 0;
          background-color: #f1f1f1;
          display: flex;
          align-items: center;
          justify-content: space-around;
        ">
        <div class="button" @click="signVisible = false" style="background: #07c160">
          返回
        </div>
        <div class="button" style="background: #66c6f2" @click="cleanSign">
          清空
        </div>
        <div class="button" @click="submitSign">保存</div>
      </div>
    </div>
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone,isIdCard ,isMobile} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import SignCanvas from "sign-canvas";
import { idCardType } from "@/data/common";
import { areaList } from "@vant/area-data";
export default {
  components: { pcheader, SignCanvas },
  data() {
    return {
      areaList,
      areaCode: '',
      idCardType,
      loading: false,
      areaShow: false,
      idCardTypeShow: false,
      signVisible: false,
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activeName: ["1", "2", "3"],
      activityId: undefined,
      id: undefined,
      guestInfo: {},
      schedule: [],
      scheduleDiscuss: [],
      scheduleSpeaker: [],
      topic: [],
      topicSpeaker: [],
      activityInfo: {},
      value: null,
      options: {
        isFullScreen: true, ////是否全屏手写 [Boolean] 可选
        isFullCover: false, //是否全屏模式下覆盖所有的元素 [Boolean]   可选
        isDpr: false, //是否使用dpr兼容高分屏 [Boolean] 可选
        lastWriteSpeed: 1, //书写速度 [Number] 可选
        lastWriteWidth: 2, //下笔的宽度 [Number] 可选
        lineCap: "round", //线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square]	正方形线帽
        lineJoin: "bevel", //线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
        canvasWidth: 350, //canvas宽高 [Number] 可选
        canvasHeight: 370, //高度  [Number] 可选
        isShowBorder: false, //是否显示边框 [可选]
        bgColor: "#ffffff", //背景色 [String] 可选
        borderWidth: 1, // 网格线宽度  [Number] 可选
        borderColor: "#ff787f", //网格颜色  [String] 可选
        writeWidth: 5, //基础轨迹宽度  [Number] 可选
        maxWriteWidth: 30, // 写字模式最大线宽  [Number] 可选
        minWriteWidth: 5, // 写字模式最小线宽  [Number] 可选
        writeColor: "#101010", // 轨迹颜色  [String] 可选
        isSign: true, //签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
        imgType: "png", //下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
      },
    };
  },
  mounted() {
    // this.activityId = this.$route.query.id;
    this.id = this.$route.query.detailId;
    this.openid = this.$cookie.get("openid");
    this.getActivityList();
    this.getTopicAndSchedule();
  },
  methods: {
    preImage(v) {
      vant.ImagePreview({
        images: [v], // 图片集合
        closeable: true, // 关闭按钮
      });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = "劳务费信息填写-" + this.guestInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-劳务费信息填写-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-劳务费信息填写-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getTopicAndSchedule() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getTopicAndSchedule/${this.id}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.topic = res.result.topic;
            this.topicSpeaker = res.result.topicSpeaker;
            this.schedule = res.result.schedule;
            this.scheduleSpeaker = res.result.scheduleSpeaker;
            this.scheduleDiscuss = res.result.scheduleDiscuss;
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
        .then((res) => {
          if (res.code == 200) {
            this.guestInfo = res.result;
            this.activityId = res.result.activityId;
            this.getActivityInfo();
          } else {
            vant.Toast(res.msg);
            this.guestInfo = {};
          }
        });
    },
    showTask() {
      this.$router.push({
        name: "schedulesExpertDetail",
        query: { detailId: this.id, id: this.activityId },
      });
    },
    cmsTurnBack() {
      if (this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
    afterRead(e, name) {
      console.log(e);
      let filedName = name.name;
      e.status = "uploading";
      e.message = "上传中...";
      let formData = new FormData();
      // formData.append("pushKey", this.pushKey);
      // formData.append("activityId", this.activityId);
      formData.append("file", e.file);

      this.$fly.post("/pyp/web/upload", formData).then((res) => {
        if (res && res.code === 200) {
          this.$set(this.guestInfo, filedName, res.result);
        }
      });
    },
    beforeRead(file) {
      // if (file.type !== 'image/jpeg') {
      //   Toast('请上传 jpg 格式图片');
      //   return false;
      // }
      return true;
    },
    cleanSign() {
      this.$refs.SignCanvas.canvasClear();
    },
    submitSign() {
      const img = this.$refs.SignCanvas.saveAsImg();
      let arr = img.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let suffix = mime.split("/")[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      let file = new File([u8arr], `sign.${suffix}`, {
        type: mime,
      });

      let formData = new FormData();
      // formData.append("pushKey", this.pushKey);
      // formData.append("activityId", this.activityId);
      formData.append("file", file);

      this.$fly.post("/pyp/web/upload", formData).then((res) => {
        if (res && res.code === 200) {
          this.signVisible = false;
          this.$set(this.guestInfo, 'serviceSign', res.result);
        }
      });
    },
    idCardTypeSelect(v) {
      this.idCardTypeShow = false;
      this.guestInfo.idCardType = v.name;
    },
    areaSelect(v) {
      let areaCode = v[0].code + ',' + v[1].code + ',' + v[2].code;
      let areaName = v[0].name + ',' + v[1].name + ',' + v[2].name;
      this.areaCode = v[2].code;
      this.$set(this.guestInfo, 'area', areaCode);
      this.$set(this.guestInfo, 'areaName', areaName);
      this.areaShow = false;
    },
    submit() {
      if (!this.guestInfo.mobile) {
        vant.Toast("请输入联系方式");
        return false;
      }
      // 手机号校验
      if (!isMobile(this.guestInfo.mobile)) {
        vant.Toast("请输入正确的手机号");
        return false;
      }
      if (!this.guestInfo.unit) {
        vant.Toast("请输入工作单位");
        return false;
      }
      if (!this.guestInfo.idCard) {
        vant.Toast("请输入身份证");
        return false;
      }
      // 身份证校验
      if (!isIdCard(this.guestInfo.idCard)) {
        vant.Toast("请输入正确的身份证");
        return false;
      }
      if (!this.guestInfo.bank) {
        vant.Toast("请输入银行卡号");
        return false;
      }
      if (!this.guestInfo.kaihuhang) {
        vant.Toast("请输入开户行");
        return false;
      }
      // if (!this.guestInfo.idCardZheng) {
      //   vant.Toast("请上传身份证正面");
      //   return false;
      // }
      // if (!this.guestInfo.idCardFan) {
      //   vant.Toast("请上传身份证反面");
      //   return false;
      // }
      this.guestInfo.isService = 1;
      this.guestInfo.isServiceTime = date.formatDate.format(new Date(), "yyyy/MM/dd hh:mm:ss");
      this.loading = true;
      // 保存
      this.$fly
        .post(
          "/pyp/web/activity/activityguest/updateInfo",
          this.guestInfo
        )
        .then((res) => {
          this.loading = false;
          if (res && res.code === 200) {
            vant.Dialog.confirm({
              title: "更新成功",
              message: "点击确定，返回继续完善其他信息",
            })
              .then(() => {
                this.$router.replace({
                  path: '/schedules/expertIndex',
                  query: { detailId: this.id },
                });
              })
              .catch(() => {
                this.getActivityList();
              });
            // 绑定手机
          } else {
            vant.Toast(res.msg);
          }
        });
    }
  },
};
</script>
<style lang="less" scoped>
.transparent {
  /deep/.van-collapse-item__content {
    background: transparent;
  }
}

.content {
  /deep/ img {
    max-width: 100%;
    height: auto;
  }
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.van-cell__title {
  flex: none;
  box-sizing: border-box;
  width: 6.2em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
  line-height: 33px;
}

.van-cell__value {
  text-align: left;
  display: flex;
  align-items: center;
}

.sign {
  position: fixed;
  top: 0;
  background: white;
  height: 100%;

  width: 100%;

  .button {
    text-align: center;
    width: 30%;
    height: 40px;
    line-height: 40px;
    background: #4485ff;
    border-radius: 20px;
    text-align: center;
    color: white;
  }
}

.sign-btns {
  display: flex;
  justify-content: space-between;

  #clear,
  #clear1,
  #save {
    display: inline-block;
    padding: 5px 10px;
    width: 76px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #eee;
    background: #e1e1e1;
    border-radius: 10px;
    text-align: center;
    margin: 20px auto;
    cursor: pointer;
  }
}
</style>