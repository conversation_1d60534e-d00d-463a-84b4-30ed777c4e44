<template>
  <div class="page">
    <van-empty :description="(hotelOrder.totalAmount == 0 && statusText == '已付款')
          ? '已预订' : statusText" :image="hotelOrder.status == 0 ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/2eaf8b0e481b47eab31cece39a2bfec0.png'
      : hotelOrder.status == 1 ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png'
        : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png'">
      <div class="orderInfo"  v-if="activityId != 1831530102796124161">
        <div style="color: #969799;font-size: 16px;padding-bottom: 20px;text-align: center;" >
          订单费用：￥{{ hotelOrder.totalAmount }}元</div>
      </div>
      <div class="botton" v-if="activityId != 1674612614545080321">
        <div class="button-item">
          <img class="button-image" @click="back"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/191a0fb893ae4ab8af27f20d0f395b7d.png" alt="" />
        </div>
        <div class="button-item" v-if="hotelOrder.status == 0 || hotelOrder.status == 1">
          <img class="button-image" @click="calcelOrder"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/7b6f394d16be435b963ae7b2101207a5.png" alt="" />
        </div>
        <div class="button-item" v-if="hotelRooms && hotelRooms.isBankTransfer == 1 && hotelOrder.status == 0">
          <img class="button-image" @click="bankTransfer"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/75b6dacaf5714fa28408a26203d52e92.png" alt="" />
        </div>
        <div class="button-item" v-if="hotelRooms && hotelRooms.isWechatPay == 1 && hotelOrder.status == 0">
          <img class="button-image" @click="weixin"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/1d27eefbace94b3c9b993312f66b5501.png" alt="" />
        </div>
        <div class="button-item" v-if="hotelRooms && hotelRooms.isAliPay == 1 && hotelOrder.status == 0">
          <img class="button-image" @click="ali"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c70a37e66e8d494bad00a643ad642d07.png" alt="" />
        </div>
        <div class="button-item" v-if="activityId != 1651597072446533634">
          <img class="button-image" @click="
            $router.push({
              name: 'cmsIndex',
              query: { id: hotelOrder.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c1d6aa26252a40e294ae986c180abc8b.png" alt="" />
        </div>
        <div class="button-item">
          <img class="button-image" @click="
            $router.push({
              name: 'meMine',
              query: { id: hotelOrder.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/b8711e1f644f4c919ad39a3186a77c83.png" alt="" />
        </div>
      </div>
    </van-empty>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activityId: undefined,
      orderId: undefined,
      statusText: '',
      hotelOrder: {},
      hotelRooms: {},
      orderDetail: [],
    };
  },
  mounted() {
    document.title = "预订提交成功";
    this.activityId = this.$route.query.id;
    this.orderId = this.$route.query.orderId;
    this.rebuildUrl();
    this.getActivityInfo();
  },
  methods: {
    back() {
      this.$router.push({
        name: 'cmsIndex',
        query: {
          id: this.hotelOrder.activityId
        },
      });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/web/hotel/hotelorder/info/${this.orderId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.hotelOrder = res.result;
            this.orderDetail = res.orderDetail;
            this.statusText = res.statusText;
            if (this.orderDetail && this.orderDetail.length > 0) {
              this.getRooms(this.orderDetail[0].hotelActivityRoomId);
            }
          } else {
            vant.Toast(res.msg);
            this.hotelOrder = {};
            this.statusText = '';
          }
        });
    },
    getRooms(v) {
      this.$fly
        .get(`/pyp/web/hotel/hotelactivityroom/info/${v}`)
        .then((res) => {
          if (res.code == 200) {
            this.hotelRooms = res.result;
            console.log(this.hotelRooms);
          }
        });
    },
    bankTransfer() {
      vant.Dialog.confirm({
        title: "付款须知",
        message: this.hotelRooms.bankTransferNotify,
        confirmButtonText: "取消",
        cancelButtonText: "已缴费(忽略)",
      })
        .then(() => {
          // on close
        })
        .catch(() => {
          // on close
        });
    },
    calcelOrder() {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认取消订单?",
      })
        .then(() => {
          this.$fly
            .get("/pyp/web/hotel/hotelorder/cancel", {
              orderId: this.orderId,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("取消成功");
                this.getActivityInfo();
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => { });
    },
    ali() {
      this.$fly
        .get(
          "/pyp/web/hotel/hotelactivity/payAli",
          { orderId: this.orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            this.$router.push({
              name: 'commonAlipay',
              query: {
                form: encodeURIComponent(res1.result)
              }
            })
          } else {
            vant.Toast(res1.msg);
          }
        })
    },
    weixin() {
      var that = this;
      // 如果需要支付，调用支付接口，并且设置了微信支付，没有设置银行支付的前提下
      this.$fly
        .get(
          "/pyp/web/hotel/hotelactivity/pay",
          { orderId: that.orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            WeixinJSBridge.invoke(
              'getBrandWCPayRequest', {
              "appId": res1.result.appId,
              "timeStamp": res1.result.timeStamp,
              "nonceStr": res1.result.nonceStr,
              "package": res1.result.packageValue,
              "signType": res1.result.signType,
              "paySign": res1.result.paySign
            },
              function (res2) {
                console.log("开始支付")
                location.reload();
                // if (res2.err_msg == "get_brand_wcpay_request:ok") {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + that.orderId; //支付成功跳转到详情页
                // } else if (res2.err_msg == "get_brand_wcpay_request:cancel") {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + that.orderId;
                // } else {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + that.orderId;
                // }
              });
          } else {
            vant.Toast(res1.msg);

          }
        })
    },
    rebuildUrl() {
      let {
        href,
        protocol,
        host,
        pathname,
        search,
        hash
      } = window.location
      console.log(window.location)
      search = search || '?'
      let newHref = `${protocol}//${host}${pathname}${search}${hash}`
      console.log(newHref)
      if (newHref !== href) {
        window.location.replace(newHref)
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #f6f6f6;
}

.bottom-button {
  width: 32%;
  height: 40px;
  margin-bottom: 10px;
}

/deep/ .van-empty__description {
  color: black;
  font-weight: bold;
  font-size: 20px;
}

/deep/ .van-empty__bottom {
  width: 90%;
}

.botton {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .button-item {
    width: 33%;
    // height: 50px;
    // line-height: 50px;
  }

  .button-image {
    width: 100%;
  }
}
</style>