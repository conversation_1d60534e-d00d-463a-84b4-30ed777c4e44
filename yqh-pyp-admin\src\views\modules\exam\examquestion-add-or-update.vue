<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="题目类型" prop="type">
        <el-select v-model="dataForm.type" @change="changeTypeHandle" placeholder="题目类型" filterable>
          <el-option v-for="item in examQuestionType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目名称" prop="name">
        <tinymce-editor ref="editor" v-model="dataForm.name"></tinymce-editor>
      </el-form-item>
      <el-form-item label="自定义分值" prop="points">
        <el-input v-model="dataForm.points" placeholder="自定义分值"></el-input>
      </el-form-item>
      <div v-if="dataForm.type != 2">
        <el-form-item label="选项">
          <el-button type="primary" @click="addOptionHandle(options.length)" size="small">添加选项</el-button>
          <el-table :data="options">
            <el-table-column prop="optionId" header-align="center" align="center" label="选项">
            </el-table-column>
            <el-table-column prop="name" header-align="center" align="center" label="内容">
              <div slot-scope="scope">
                <el-input v-model="scope.row.name" placeholder="内容"></el-input>
              </div>
            </el-table-column>
            <el-table-column header-align="center" align="center" label="操作">
              <div slot-scope="scope">
                <el-button type="primary" size="small" @click="deletePaymentHandle(scope.row)">删除</el-button>
              </div>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item v-if="dataForm.type == 0" label="答案" prop="charOptionId">
          <el-select v-model="dataForm.charOptionId" placeholder="请选择类型">
            <el-option v-for="item in options" :key="item.optionId" :label="item.optionId"
              :value="item.optionId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="答案" prop="charOptionId">
          <el-select v-model="dataForm.charOptionId" multiple placeholder="请选择类型">
            <el-option v-for="item in options" :key="item.optionId" :label="item.optionId"
              :value="item.optionId"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <!-- 填空题 -->
      <div v-else>
        <el-form-item label="答案" prop="charOptionId">
          <el-input v-model="dataForm.charOptionId" placeholder="答案"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { examQuestionType } from '@/data/exam'
export default {
  data() {
    return {
      examQuestionType: examQuestionType,
      visible: false,
      dataForm: {
        id: 0,
        activityId: '',
        examId: '',
        name: '',
        type: '',
        optionId: '',
        charOptionId: '',
        points: 0,
      },
      options: [{
        optionId: 'A',
        name: ''
      }],
      character: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"],
      dataRule: {
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        examId: [
          { required: true, message: '考卷id不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '题目名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '题目类型不能为空', trigger: 'blur' }
        ],
        optionId: [
          { required: true, message: '正确答案不能为空', trigger: 'blur' }
        ],
        points: [
          { required: true, message: '自定义分值不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor"),
    OssUploader: () =>
      import('../oss/oss-uploader')
  },
  methods: {
    init(activityId, examId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.examId = examId;
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.options = [];
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/exam/examquestion/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.examQuestion.activityId
              this.dataForm.examId = data.examQuestion.examId
              this.dataForm.name = data.examQuestion.name
              this.dataForm.type = data.examQuestion.type
              if (this.dataForm.type == 1) {
                this.dataForm.charOptionId = data.examQuestion.charOptionId.split(',');
              } else {
                this.dataForm.charOptionId = data.examQuestion.charOptionId;
              }
              this.dataForm.points = data.examQuestion.points
            }
          })
          this.$http({
            url: this.$http.adornUrl(`/exam/examquestionoption/findByQuestionId/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.options = data.result;
            }
          })
        }
      })
    },
    changeTypeHandle() {

      if (this.dataForm.type == 1) {
        // 多选
        this.dataForm.charOptionId = []
      } else {
        // 单选,填空
        this.dataForm.charOptionId = ""
      }
    },
    addOptionHandle(length) {
      if (length >= 26) {
        this.$message.error("选项数量已上线");
        return false
      }
      this.options.push({
        optionId: this.character[length],
        name: ''
      })
      this.$forceUpdate()
    },
    deletePaymentHandle(item) {
      var index = this.options.indexOf(item)
      if (this.options.length <= 1) {
        this.$message.error("至少保留一个选项");
      } else {
        if (index !== -1) {
          if (index == this.options.length - 1) {
            // 最后一个选项
            this.options.splice(index, 1)
          } else {
            // 答案处理
            if (this.dataForm.type == 0) {
              // 单选
              if (this.dataForm.charOptionId = this.options[index].id) {
                this.dataForm.charOptionId = ''
              }
            } else if (this.dataForm.type == 1) {
              // 多选
              if (this.dataForm.charOptionId.indexOf(this.options[index].id) != -1) {
                this.dataForm.charOptionId.splice(this.dataForm.charOptionId.indexOf(this.options[index].id), 1)
                for (let e in this.dataForm.charOptionId) {
                  if (this.character.indexOf(this.dataForm.charOptionId[e]) > this.character.indexOf(this.options[index].id)) {
                    // 在删除选项之后
                    this.dataForm.charOptionId[e] = this.character[this.character.indexOf(this.dataForm.charOptionId[e]) - 1]
                  }
                }
              }
            }
            let e
            for (let e in this.options) {
              if (e > index) {
                this.options[e].optionId = this.character[e - 1]
              }
            }
            this.options.splice(index, 1)
          }
          this.$forceUpdate()
        }
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.type != 2) {
            for (let i = 0; i < this.options.length; i++) {
              if (this.options[i].optionId == "" || this.options[i].name == "") {
                this.$message.error("选项不为空，请填写完整")
                return false
              }
            }
          }
          // 多选赋值
          if (this.dataForm.type == 1) {
            this.dataForm.charOptionId = this.dataForm.charOptionId.toString();
          }
          this.$http({
            url: this.$http.adornUrl(`/exam/examquestion/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'examId': this.dataForm.examId,
              'name': this.dataForm.name,
              'type': this.dataForm.type,
              'optionId': this.dataForm.optionId,
              'charOptionId': this.dataForm.charOptionId,
              'points': this.dataForm.points,
              'examQuestionOptionEntities': this.options,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
