-- 为ActivityVideo表添加平台和媒体类型字段

-- 添加平台字段
ALTER TABLE `activity_video` ADD COLUMN `platform` varchar(50) DEFAULT NULL COMMENT '平台类型（douyin, xiaohongshu, kuaishou等）';

-- 添加媒体类型字段
ALTER TABLE `activity_video` ADD COLUMN `media_type` varchar(20) DEFAULT 'video' COMMENT '媒体类型（video: 视频, image: 图片）';

-- 添加图片数量字段（当media_type为image时使用）
ALTER TABLE `activity_video` ADD COLUMN `image_count` int(11) DEFAULT 1 COMMENT '图片数量（当media_type为image时使用）';

-- 添加索引
ALTER TABLE `activity_video` ADD INDEX `idx_platform` (`platform`);
ALTER TABLE `activity_video` ADD INDEX `idx_media_type` (`media_type`);
ALTER TABLE `activity_video` ADD INDEX `idx_type_platform_media` (`type`, `platform`, `media_type`);
