<template>
  <div class="recharge-payment">
    <!-- 订单信息 -->
    <div class="order-info">
      <h3>{{ orderTitle }}</h3>
      <div class="order-details">
        <div class="detail-item">
          <span class="label">套餐名称：</span>
          <span class="value">{{ orderInfo.packageName }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ orderType === 'activity' ? '可创建活动：' : '充值次数：' }}</span>
          <span class="value">{{ orderInfo.countValue }}{{ orderType === 'activity' ? '个' : '次' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">订单金额：</span>
          <span class="value price">¥{{ orderInfo.amount }}</span>
        </div>
        <div class="detail-item">
          <span class="label">订单编号：</span>
          <span class="value">{{ orderInfo.orderSn }}</span>
        </div>
      </div>
    </div>

    <!-- 支付方式选择 -->
    <div class="payment-methods">
      <h4>选择支付方式</h4>
      <van-radio-group v-model="selectedPayment">
        <van-cell-group>
          <van-cell clickable @click="selectedPayment = 'wechat'">
            <template #title>
              <div class="payment-option">
                <img src="http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250627/e7171ae049dd4c7dacb4020ea0aef415.png" alt="微信支付" class="payment-icon">
                <span>微信支付</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio name="wechat" />
            </template>
          </van-cell>
          <!-- <van-cell clickable @click="selectedPayment = 'alipay'">
            <template #title>
              <div class="payment-option">
                <img src="http://yqhpyp.oss-cn-shanghai.aliyuncs.com/20250627/c7856ed0dc93448d86b3bf4a907a9c57.png" alt="支付宝" class="payment-icon">
                <span>支付宝</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio name="alipay" />
            </template>
          </van-cell> -->
        </van-cell-group>
      </van-radio-group>
    </div>

    <!-- 支付按钮 -->
    <div class="payment-actions">
      <van-button 
        type="primary" 
        size="large" 
        @click="submitPayment"
        :loading="paying">
        立即支付 ¥{{ orderInfo.amount }}
      </van-button>
    </div>

    <!-- 业务员信息 -->
    <div class="salesman-info" v-if="salesmanInfo">
      <h4>推荐业务员</h4>
      <div class="salesman-card">
        <div class="avatar">
          <img :src="salesmanInfo.avatar || '../../assets/mine.png'" alt="业务员头像">
        </div>
        <div class="info">
          <div class="name">{{ salesmanInfo.name || salesmanInfo.realName }}</div>
          <div class="contact">{{ salesmanInfo.mobile || salesmanInfo.phone }}</div>
          <div class="company" v-if="salesmanInfo.company">{{ salesmanInfo.company }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RechargePayment',
  data() {
    return {
      orderInfo: {
        packageName: '',
        countValue: 0,
        amount: 0,
        orderSn: ''
      },
      salesmanInfo: null,
      selectedPayment: 'wechat',
      paying: false,
      orderType: 'recharge' // 'recharge' 或 'activity'
    }
  },
  computed: {
    orderTitle() {
      return this.orderType === 'activity' ? '创建活动订单' : '充值订单'
    }
  },
  mounted() {
    this.rebuildUrl();
    this.loadOrderInfo()
  },
  methods: {
    // 加载订单信息
    loadOrderInfo() {
      const orderId = this.$route.query.orderId
      const from = this.$route.query.from
      const type = this.$route.query.type

      // 设置订单类型
      this.orderType = type || 'recharge'

      if (!orderId) {
        this.$toast('订单参数错误')
        // this.$router.back()
        return
      }

      // 根据来源选择不同的API
      let apiUrl = '/pyp/web/salesman/getRechargeOrderInfo'
      if (from === 'account') {
        apiUrl = '/pyp/web/account/getOrderInfo'
      }

      // 调用API获取订单详情
      this.$fly.get(apiUrl, {
        orderId
      }).then(res => {
        if (res.code === 200 || res.code === 200) {
          let orderInfo, packageInfo

          if (from === 'account') {
            // 账户页面的响应格式
            orderInfo = res.orderInfo || res.data
            packageInfo = res.packageInfo || res.data
          } else {
            // 业务员页面的响应格式
            orderInfo = res.orderInfo
            packageInfo = res.packageInfo
          }

          this.orderInfo = {
            packageName: packageInfo.name || packageInfo.packageName,
            countValue: packageInfo.countValue,
            amount: orderInfo.payAmount || orderInfo.amount || orderInfo.totalAmount,
            orderSn: orderInfo.orderSn
          }
        } else {
          this.$toast(res.msg || '加载订单信息失败')
          // this.$router.back()
        }
      }).catch(err => {
        console.error('加载订单信息失败:', err)
        this.$toast('加载订单信息失败')
        // this.$router.back()
      })

      // 如果有业务员ID，获取业务员信息
      const salesmanId = this.$route.query.salesmanId
      if (salesmanId) {
        this.loadSalesmanInfo(salesmanId)
      }
    },

    // 加载业务员信息
    loadSalesmanInfo(salesmanId) {
      this.$fly.get(`/pyp/web/salesman/info/${salesmanId}`).then(res => {
        if (res.code === 200) {
          this.salesmanInfo = res.result || res.salesman
        } else {
          console.error('加载业务员信息失败:', res.msg)
        }
      }).catch(err => {
        console.error('加载业务员信息失败:', err)
      })
    },

    // 提交支付
    submitPayment() {
      if (!this.selectedPayment) {
        this.$toast('请选择支付方式')
        return
      }

      this.paying = true

      const orderId = this.$route.query.orderId
      
      if (this.selectedPayment === 'wechat') {
        this.payWithWechat(orderId)
      } else if (this.selectedPayment === 'alipay') {
        this.payWithAlipay(orderId)
      }
    },

    // 微信支付
    payWithWechat(orderId) {
      this.$fly.get('/pyp/web/activity/recharge/pay/wechat', {
        orderSn: this.orderInfo.orderSn
      }).then(res => {
        if (res.code === 200) {
          // 调用微信支付
          if (typeof WeixinJSBridge !== 'undefined') { 
            WeixinJSBridge.invoke('getBrandWCPayRequest', {
              appId: res.result.appId,
              timeStamp: res.result.timeStamp,
              nonceStr: res.result.nonceStr,
              package: res.result.packageValue,
              signType: res.result.signType,
              paySign: res.result.paySign
            }, (result) => {
              if (result.err_msg === 'get_brand_wcpay_request:ok') {
                this.$toast.success('支付成功')
                this.handlePaymentSuccess(orderId)
              } else {
                this.$toast('支付失败')
              }
              this.paying = false
            })
          } else {
            this.$toast('请在微信中打开')
            this.paying = false
          }
        } else {
          this.$toast(res.msg || '支付失败')
          this.paying = false
        }
      }).catch(err => {
        console.error('微信支付失败:', err)
        this.$toast('支付失败')
        this.paying = false
      })
    },

    // 支付宝支付
    payWithAlipay(orderId) {
      this.$fly.get('/pyp/web/activity/recharge/pay/alipay', {
        orderSn: this.orderInfo.orderSn
      }).then(res => {
        if (res.code === 200) {
          // 跳转到支付宝支付页面
          this.$router.push({
            name: 'commonAlipay',
            query: {
              form: encodeURIComponent(res.result)
            }
          })
        } else {
          this.$toast(res.msg || '支付失败')
        }
        this.paying = false
      }).catch(err => {
        console.error('支付宝支付失败:', err)
        this.$toast('支付失败')
        this.paying = false
      })
    },

    rebuildUrl() {
      let {
        href,
        protocol,
        host,
        pathname,
        search,
        hash
      } = window.location
      console.log(window.location)
      search = search || '?'
      let newHref = `${protocol}//${host}${pathname}${search}${hash}`
      console.log(newHref)
      if (newHref !== href) {
        window.location.replace(newHref)
      }
    },
    // 处理支付成功
    handlePaymentSuccess(orderId) {
      const from = this.$route.query.from
      const salesmanId = this.$route.query.salesmanId

      if (from === 'account') {
        // 从账户页面来的，返回账户页面并刷新
        this.$router.push({
          name: 'account',
          query: {
            activityId: this.$route.query.activityId,
            refresh: 'true'
          }
        })
      } else {
        // 其他来源，跳转到支付成功页面
        const query = {
          orderId: orderId,
          type: this.orderType,
          from: from
        }

        // 如果有业务员ID，也传递过去
        if (salesmanId) {
          query.salesmanId = salesmanId
        }

        this.$router.push({
          name: 'paymentSuccess',
          query: query
        })
      }
    }
  }
}
</script>

<style scoped>
.recharge-payment {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.order-info {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.order-info h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
}

.order-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;
}

.value.price {
  color: #ff4444;
  font-weight: bold;
  font-size: 16px;
}

.payment-methods {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.payment-methods h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.payment-option {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.payment-actions {
  padding: 20px;
  background: white;
  border-radius: 10px;
  margin-bottom: 20px;
}

.salesman-info {
  background: white;
  border-radius: 10px;
  padding: 20px;
}

.salesman-info h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.salesman-card {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.salesman-card .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.salesman-card .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.salesman-card .name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.salesman-card .contact {
  font-size: 12px;
  color: #666;
}

.salesman-card .company {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}
</style>
