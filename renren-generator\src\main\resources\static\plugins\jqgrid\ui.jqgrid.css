/*Grid*/
.ui-jqgrid {
	position: relative;
	-moz-box-sizing: content-box; 
	-webkit-box-sizing: content-box; 
	box-sizing: content-box;
}
.ui-jqgrid .ui-jqgrid-view {position: relative;left:0; top: 0; padding: 0; font-size:11px; z-index:100;}
.ui-jqgrid .ui-common-table {border-width: 0px; border-style: none; border-spacing: 0px; padding: 0;}
/* caption*/
.ui-jqgrid .ui-jqgrid-titlebar {height:19px; padding: .3em .2em .2em .3em; position: relative; font-size: 12px; border-left: 0 none;border-right: 0 none; border-top: 0 none;}
.ui-jqgrid .ui-jqgrid-caption {text-align: left;}
.ui-jqgrid .ui-jqgrid-title { margin: .1em 0 .2em; }
.ui-jqgrid .ui-jqgrid-titlebar-close { position: absolute;top: 50%; width: 19px; margin: -10px 0 0 0; padding: 1px; height:18px; cursor:pointer;}
.ui-jqgrid .ui-jqgrid-titlebar-close span { display: block; margin: 1px; }
.ui-jqgrid .ui-jqgrid-titlebar-close:hover { padding: 0; }
/* header*/
.ui-jqgrid .ui-jqgrid-hdiv {position: relative; margin: 0;padding: 0; overflow: hidden; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
.ui-jqgrid .ui-jqgrid-hbox {float: left; padding-right: 20px;}
.ui-jqgrid .ui-jqgrid-htable {table-layout:fixed;margin:0;border-collapse: separate;}
.ui-jqgrid .ui-jqgrid-htable th { height: 27px; padding: 0 2px 0 2px;}
.ui-jqgrid .ui-jqgrid-htable th div {overflow: hidden; position:relative;margin: .1em 0em .1em 0em;}
.ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {overflow: hidden;white-space: nowrap;text-align:center;border-top : 0 none;border-bottom : 0 none;}
.ui-th-column-header, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header {overflow: hidden;white-space: nowrap;text-align:center;border-top : 0 none; height: 26px;}
.ui-th-ltr, .ui-jqgrid .ui-jqgrid-htable th.ui-th-ltr {border-left : 0 none;}
.ui-th-rtl, .ui-jqgrid .ui-jqgrid-htable th.ui-th-rtl {border-right : 0 none;}
.ui-first-th-ltr {border-right: 1px solid; }
.ui-first-th-rtl {border-left: 1px solid; }
.ui-jqgrid .ui-th-div-ie {white-space: nowrap; zoom :1; height:17px;}
.ui-jqgrid .ui-jqgrid-resize {height:20px !important;position: relative; cursor :e-resize;display: inline;overflow: hidden;}
.ui-jqgrid .ui-grid-ico-sort {overflow:hidden;position:absolute;display:inline; cursor: pointer !important;}
.ui-jqgrid .ui-icon-asc {margin-top:-3px; height:12px;}
.ui-jqgrid .ui-icon-desc {margin-top:3px;margin-left:-1px;height:12px;}
.ui-jqgrid .ui-i-asc {margin-top:0;height:18px;}
.ui-jqgrid .ui-i-desc {margin-top:0;margin-left:12px;height:18px;}
.ui-jqgrid .ui-single-sort-asc {margin-top:0;height:18px;}
.ui-jqgrid .ui-single-sort-desc {margin-top:-1px;height:18px;}
.ui-jqgrid .ui-jqgrid-sortable {cursor:pointer;height:14px}
.ui-jqgrid tr.ui-search-toolbar th { }
.ui-jqgrid .ui-search-table td.ui-search-clear { width:25px;}
.ui-jqgrid tr.ui-search-toolbar td > input { padding-right: 0px; width: 95%;}
.ui-jqgrid tr.ui-search-toolbar select {}
/* body */ 
.ui-jqgrid .ui-jqgrid-bdiv {position: relative; margin: 0; padding:0; overflow: auto; text-align:left;z-index: 101;}
.ui-jqgrid .ui-jqgrid-btable {table-layout:fixed; margin:0; outline-style: none; border-collapse: separate;}
.ui-jqgrid tr.jqgrow { outline-style: none; }
.ui-jqgrid tr.jqgroup { outline-style: none; }
.ui-jqgrid tr.jqgrow td {font-weight: normal; overflow: hidden; white-space: pre; height: 23px;padding: 1px 2px 1px 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.jqgfirstrow td {padding: 0 2px 0 2px;border-right-width: 1px; border-right-style: solid; height:auto;}
.ui-jqgrid tr.jqgroup td {font-weight: normal; overflow: hidden; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.jqfoot td {font-weight: bold; overflow: hidden; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.ui-row-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
.ui-jqgrid tr.ui-row-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
.ui-jqgrid td.jqgrid-rownum { padding: 0 2px 0 2px; margin: 0; border: 0 none;}
.ui-jqgrid .ui-jqgrid-resize-mark { width:2px; left:0; background-color:#777; cursor: e-resize; cursor: col-resize; position:absolute; top:0; height:100px; overflow:hidden; display:none; border:0 none; z-index: 99999;}
/* footer */
.ui-jqgrid .ui-jqgrid-sdiv {position: relative; margin: 0;padding: 0; overflow: hidden; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
.ui-jqgrid .ui-jqgrid-ftable {table-layout:fixed; margin-bottom:0;border-collapse: separate;}
.ui-jqgrid tr.footrow td {font-weight: bold; overflow: hidden; white-space:nowrap; height: 20px;padding: 0 2px 0 2px;border-top-width: 1px; border-top-color: inherit; border-top-style: solid;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.footrow-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
.ui-jqgrid tr.footrow-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
/* Pager*/
.ui-jqgrid .ui-jqgrid-pager { border-left: 0 none !important;border-right: 0 none !important; border-bottom: 0 none !important; border-top: 0 none; margin: 0 !important; padding: 0 !important; position: relative; height: auto; min-height: 28px; white-space: nowrap;overflow: hidden;font-size:11px; z-index:101}
.ui-jqgrid .ui-jqgrid-toppager .ui-pager-control, .ui-jqgrid .ui-jqgrid-pager .ui-pager-control {position: relative;border-left: 0;border-bottom: 0;border-top: 0; height: 28px;}
.ui-jqgrid .ui-pg-table {position: relative; padding: 1px 0; width:auto; margin: 0;}
.ui-jqgrid .ui-pg-table td {font-weight:normal; vertical-align:middle; padding:0px 1px;}
.ui-jqgrid .ui-pg-button  { height:auto}
.ui-jqgrid .ui-pg-button span { display: block; margin: 2px; float:left;}
.ui-jqgrid .ui-pg-button:hover { padding: 0;}
.ui-jqgrid .ui-state-disabled:hover {padding:0px;}
.ui-jqgrid .ui-pg-input,.ui-jqgrid .ui-jqgrid-toppager .ui-pg-input { height:14px;width: auto;font-size:.9em; margin:0;line-height: inherit;border: none; padding: 3px 2px}
.ui-jqgrid .ui-pg-selbox, .ui-jqgrid .ui-jqgrid-toppager .ui-pg-selbox {font-size:.9em; line-height:inherit; display:block; height:19px; margin: 0; padding: 3px 0px; border:none;}
.ui-jqgrid .ui-separator {height: 18px; border-left: 2px solid #ccc ;}
.ui-separator-li {height: 2px; border : none;border-top: 2px solid #ccc ; margin: 0; padding: 0; width:100%}
.ui-jqgrid  .dropdownmenu {
	padding: 3px 0 3px 0;
	margin-left: 4px;
}
.ui-jqgrid .ui-jqgrid-pager .ui-pg-div,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div
{padding:1px 0;float:left;position:relative; line-height: 20px;}
.ui-jqgrid .ui-jqgrid-pager .ui-pg-button,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-button
{ cursor:pointer; }
.ui-jqgrid .ui-jqgrid-pager .ui-pg-div  span.ui-icon,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div  span.ui-icon
{float:left;margin: 2px; width:18px;}
.ui-jqgrid td input, .ui-jqgrid td select, .ui-jqgrid td textarea { margin: 0; padding-top:5px;padding-bottom: 5px;}
.ui-jqgrid td textarea {width:auto;height:auto;}
.ui-jqgrid .ui-jqgrid-toppager {border-left: 0 none !important;border-right: 0 none !important; border-top: 0 none !important; margin: 0 !important; padding: 0 !important; position: relative;white-space: nowrap;overflow: hidden;}
.ui-jqgrid .ui-jqgrid-pager .ui-pager-table,
.ui-jqgrid .ui-jqgrid-toppager .ui-pager-table 
{
	width:100%;
	table-layout:fixed;
	height:100%;
}
.ui-jqgrid .ui-jqgrid-pager .ui-paging-info,
.ui-jqgrid .ui-jqgrid-toppager .ui-paging-info
{ 
	font-weight: normal;
	height:auto; 
	margin-top:3px;
	margin-right:4px;
	display: inline;
}
.ui-jqgrid .ui-jqgrid-pager .ui-paging-pager,
.ui-jqgrid .ui-jqgrid-toppager .ui-paging-pager
{
	table-layout:auto;
	height:100%;
}
.ui-jqgrid .ui-jqgrid-pager .navtable,
.ui-jqgrid .ui-jqgrid-toppager .navtable
{
	float:left;
	table-layout:auto;
	height:100%;
}

/*.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div {padding:1px 0;float:left;position:relative; line-height: 20px; margin-right:3px;}
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-button { cursor:pointer; }
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div  span.ui-icon {float:left;margin: 2px; width:18px;}
*/

/*subgrid*/
.ui-jqgrid .ui-jqgrid-btable .ui-sgcollapsed span {display: block;}
.ui-jqgrid .ui-subgrid {margin:0;padding:0; width:100%;}
.ui-jqgrid .ui-subgrid table {table-layout: fixed;}
.ui-jqgrid .ui-subgrid tr.ui-subtblcell td {height:18px;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid .ui-subgrid td.subgrid-data {border-top:  0 none !important; border-left: 0 none !important;}
.ui-jqgrid .ui-subgrid td.subgrid-cell {border-width: 0 1px 1px 0;}
.ui-jqgrid .ui-th-subgrid {height:20px;}
/* loading */
.ui-jqgrid .loading {position: absolute; top: 45%;left: 45%;width: auto;z-index:101;padding: 6px; margin: 5px;text-align: center;font-weight: bold;display: none;border-width: 2px !important; font-size:11px;}
.ui-jqgrid .jqgrid-overlay {display:none;z-index:100;}
/* IE * html .jqgrid-overlay {width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');} */
* .jqgrid-overlay iframe {position:absolute;top:0;left:0;z-index:-1;}
/* IE width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
/* end loading div */
/* toolbar */
.ui-jqgrid .ui-userdata {border-left: 0 none;    border-right: 0 none;	height : 27px;overflow: hidden;	}
/*Modal Window */
.ui-jqdialog { font-size:11px !important; }
.ui-jqdialog { display: none; width: 300px; position: absolute; padding: .2em; font-size:11px; overflow:visible;}
.ui-jqdialog .ui-jqdialog-titlebar { padding: .3em .2em; position: relative; height:20px;}
.ui-jqdialog .ui-jqdialog-title { margin: .3em .2em .2em .2em;} 
.ui-jqdialog .ui-jqdialog-titlebar-close { position: absolute;  top: 50%; width: 19px; margin: -12px 0 0 0; padding: 1px; height: 18px; cursor:pointer;}

.ui-jqdialog .ui-jqdialog-titlebar-close span { display: block; margin: 1px; }
.ui-jqdialog .ui-jqdialog-titlebar-close:hover, .ui-jqdialog .ui-jqdialog-titlebar-close:focus { padding: 0; }
.ui-jqdialog-content, .ui-jqdialog .ui-jqdialog-content { border: 0; padding: .3em .2em; background: none; height:auto;}
.ui-jqdialog .ui-jqconfirm {padding: .4em 1em; border-width:3px;position:absolute;bottom:10px;right:10px;overflow:visible;display:none;height:80px;width:220px;text-align:center;}
.ui-jqdialog>.ui-resizable-se { bottom: -3px; right: -3px}
.ui-jqgrid>.ui-resizable-se { bottom: -3px; right: -3px }
/* end Modal window*/
/* Form edit */
.ui-jqdialog-content .FormGrid {margin: 0; overflow:auto;position:relative;}
.ui-jqdialog-content .EditTable { width: 100%; margin-bottom:0;}
.ui-jqdialog-content .DelTable { width: 100%; margin-bottom:0;}
.EditTable td input, .EditTable td select, .EditTable td textarea {margin: 0;}
.EditTable td textarea { width:auto; height:auto;}
.ui-jqdialog-content td.EditButton {text-align: right;border-top: 0 none;border-left: 0 none;border-right: 0 none; padding-bottom:5px; padding-top:5px;}
.ui-jqdialog-content td.navButton {text-align: center; border-left: 0 none;border-top: 0 none;border-right: 0 none; padding-bottom:5px; padding-top:5px;}
.ui-jqdialog-content input.FormElement {padding: .5em .3em; margin-bottom: 3px}
.ui-jqdialog-content select.FormElement {padding:.3em; margin-bottom: 3px;}
.ui-jqdialog-content .data-line {padding-top:.1em;border: 0 none;}

.ui-jqdialog-content .CaptionTD {vertical-align: middle;border: 0 none; padding: 2px;white-space: nowrap;}
.ui-jqdialog-content .DataTD {padding: 2px; border: 0 none; vertical-align: top;}
.ui-jqdialog-content .form-view-data {white-space:pre}
.fm-button { height: 18px; display: inline-block; margin:2px 4px 0 0; padding: .6em .5em .2em .5em; text-decoration:none !important; cursor:pointer; position: relative; text-align: center; zoom: 1; }
.fm-button-icon-left { padding-left: 1.9em; }
.fm-button-icon-right { padding-right: 1.9em; }
.fm-button-icon-left .ui-icon { right: auto; left: .2em; margin-left: 0; position: absolute; top: 50%; margin-top: -8px; }
.fm-button-icon-right .ui-icon { left: auto; right: .2em; margin-left: 0; position: absolute; top: 50%; margin-top: -8px;}
#nData, #pData { float: left; margin:3px;padding: 0; width: 15px; }
.ViewTable {
	border-width: 0; 
	border-style: none; 
	border-spacing: 1px;
	padding: 4px;
	table-layout: fixed;
}
.ViewTable .CaptionTD, .ViewTable .DataTD {padding : 4px;} 
/* End Eorm edit */
/*cell edit*/
.ui-jqgrid .edit-cell {
	padding: 4px 0px 4px 4px;
}
.ui-jqgrid .selected-row, div.ui-jqgrid .selected-row td {font-style : normal;border-left: 0 none;}
/* inline edit actions button*/
.ui-inline-del.ui-state-hover span, .ui-inline-edit.ui-state-hover span,
.ui-inline-save.ui-state-hover span, .ui-inline-cancel.ui-state-hover span {
    margin: -1px;
}
.ui-inline-del, .ui-inline-cancel {
    margin-left: 8px;
}

.ui-jqgrid .inline-edit-cell {
	padding: 4px 0px 4px 4px;
}
/* Tree Grid */
.ui-jqgrid .tree-wrap {float: left; position: relative;height: 18px;white-space: nowrap;overflow: hidden;}
.ui-jqgrid .tree-minus {position: absolute; height: 18px; width: 18px; overflow: hidden;}
.ui-jqgrid .tree-plus {position: absolute;	height: 18px; width: 18px;	overflow: hidden;}
.ui-jqgrid .tree-leaf {position: absolute;	height: 18px; width: 18px;overflow: hidden;}
.ui-jqgrid .treeclick {cursor: pointer;}
/* moda dialog */
* iframe.jqm {position:absolute;top:0;left:0;z-index:-1;}
/*	 width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
.ui-jqgrid-dnd tr td {border-right-width: 1px; border-right-color: inherit; border-right-style: solid; height:20px}
/* RTL Support */
.ui-jqgrid .ui-jqgrid-caption-rtl {text-align: right;}
.ui-jqgrid .ui-jqgrid-hbox-rtl {float: right;}
.ui-jqgrid .ui-jqgrid-resize-ltr {float: right;margin: -2px -2px -2px 0;}
.ui-jqgrid .ui-jqgrid-resize-rtl {float: left;margin: -2px 0 -1px -3px;}
.ui-jqgrid .ui-sort-rtl {left:0;}
.ui-jqgrid .tree-wrap-ltr {float: left;}
.ui-jqgrid .tree-wrap-rtl {float: right;}
.ui-jqgrid .ui-ellipsis {-moz-text-overflow:ellipsis;text-overflow:ellipsis;}

/* Toolbar Search Menu , Nav menu*/
.ui-search-menu, 
.ui-nav-menu {
	position: absolute; 
	padding: 2px 5px; 
	z-index:99999;
	-webkit-box-shadow: 7px 7px 5px 0px rgba(50, 50, 50, 0.75);
	-moz-box-shadow:    7px 7px 5px 0px rgba(50, 50, 50, 0.75);
	box-shadow:         7px 7px 5px 0px rgba(50, 50, 50, 0.75);
}
.ui-search-menu.ui-menu .ui-menu-item,
.ui-nav-menu.ui-menu .ui-menu-item
{ 
	list-style-image: none; 
	padding-right: 0; 
	padding-left: 0; 
}
.ui-search-menu.ui-menu .ui-menu-item a, 
.ui-nav-menu.ui-menu .ui-menu-item a 
{ 
	display: block; 
}
.ui-search-menu.ui-menu .ui-menu-item a.g-menu-item:hover,
.ui-nav-menu.ui-menu .ui-menu-item a.g-menu-item:hover 
{ 
	margin: -1px; 
	font-weight: normal; 
}
.ui-jqgrid .ui-search-table { padding: 0; border: 0 none; height:20px; width:100%;}
.ui-jqgrid .ui-search-table .ui-search-oper { width:20px; }
a.g-menu-item, a.soptclass, a.clearsearchclass { cursor: pointer; } 
.ui-jqgrid .ui-jqgrid-view input,
.ui-jqgrid .ui-jqgrid-view select,
.ui-jqgrid .ui-jqgrid-view textarea,
.ui-jqgrid .ui-jqgrid-view button {
    font-size: 11px;
}
.ui-jqgrid .ui-scroll-popup {width: 95px;}
.ui-search-table select,
.ui-search-table input 
{
	padding: 4px 3px;
}

.ui-jqgrid .ui-pg-table .ui-pg-button.ui-state-disabled:hover > .ui-separator {
	margin-left: 3px;
	margin-right: 3px;
}

.ui-jqgrid .ui-pg-table .ui-pg-button.ui-state-disabled:hover > .ui-pg-div > .ui-icon {
	margin-left: 3px;
	margin-right: 3px;
}
/* Column menu */
.ui-jqgrid .ui-jqgrid-htable .colmenu {
	position:absolute;
	right:1px;
	height:100%;
	color : black;
}
.ui-jqgrid .ui-jqgrid-htable .colmenu-rtl {
	right:auto;
	left : 1px;
}
.ui-jqgrid .ui-jqgrid-htable .colmenuspan {
	display:inline-block;
}

.ui-jqgrid .ui-jqgrid-htable .ui-th-div {
	height:17px;
	margin-top:5px;
	display:inine-block;
}
.column-menu, .ui-search-menu {
	padding: 10px 10px;
}
.column-menu .divider {
	background-color: #e5e5e5; 
	height: 1px;
	padding:0 0;
	margin: 5px 0;  
	overflow: hidden;
}
.ui-menu-item .ui-common-table .menu_icon {
	white-space: pre;
	padding-right: 4px;
	padding-left: 4px;
	width : auto;
}
.ui-menu-item .ui-common-table .menu_icon .ui-icon {
	display : inline-block;
	position: relative;
}
td.menu_text {
	width: auto;
	white-space: nowrap;
}
.ui-search-menu .ui-menu-item {
	padding : 0 0;
}
.ui-col-menu .ui-menu-item td.menu_text{
	padding-top: 0;
	padding-bottom: 0;
	padding-left : 1px;
}
.ui-col-menu .ui-menu-item td.menu_icon{
	padding-top: 0;
	padding-bottom: 0;
	vertical-align: middle;
}
.ui-col-menu .ui-menu-item td.menu_icon input{
	margin: 2px 0;
	
}
#search_menu .ui-menu-item div {
	margin: 3px 0;
	white-space: nowrap;
}

#search_menu .ui-menu-item div input,
#search_menu .ui-menu-item div select
{
	padding: 3px 2px;
}
#search_menu  .search_buttons {
	display:inline-block;
	width:50%;
}
#column_menu.ui-menu .ui-menu-item {
	position :static;
}