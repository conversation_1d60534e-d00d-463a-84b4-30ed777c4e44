package com.cjy.pyp.modules.salesman.enums;

/**
 * 佣金计算方式枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public enum CalculationTypeEnum {
    
    /**
     * 固定金额
     */
    FIXED_AMOUNT(1, "固定金额"),
    
    /**
     * 百分比
     */
    PERCENTAGE(2, "百分比");
    
    private final Integer code;
    private final String desc;
    
    CalculationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static CalculationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CalculationTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
