package com.cjy.pyp.modules.groupbuying.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 团购券平台配置实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@Data
@TableName("group_buying_platform_config")
public class GroupBuyingPlatformConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 平台类型：douyin、meituan、dianping
     */
    private String platformType;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台图标URL
     */
    private String iconUrl;

    /**
     * APP跳转URL Scheme模板
     */
    private String urlScheme;

    /**
     * 网页跳转URL模板
     */
    private String webUrlTemplate;

    /**
     * API配置信息（JSON格式）
     */
    private String apiConfig;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;
}
