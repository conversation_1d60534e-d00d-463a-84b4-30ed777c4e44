package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 活动图片素材Web接口
 */
@RestController
@RequestMapping("web/activity/activityimage")
public class WebActivityImageController extends AbstractController {
    
    @Autowired
    private ActivityImageService activityImageService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            PageUtils page = activityImageService.queryPage(params);
            return R.ok().put("page", page);
        } catch (Exception e) {
            return R.error("获取图片列表失败: " + e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        try {
            ActivityImageEntity activityImage = activityImageService.getById(id);
            return R.ok().put("activityImage", activityImage);
        } catch (Exception e) {
            return R.error("获取图片信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ActivityImageEntity activityImage) {
        try {
            // 设置创建用户
            activityImage.setCreateBy(getUserId());
            activityImageService.save(activityImage);
            return R.ok();
        } catch (Exception e) {
            return R.error("保存图片失败: " + e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody ActivityImageEntity activityImage) {
        try {
            // 设置更新用户
            activityImage.setUpdateBy(getUserId());
            activityImageService.updateById(activityImage);
            return R.ok();
        } catch (Exception e) {
            return R.error("更新图片失败: " + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        try {
            activityImageService.removeByIds(Arrays.asList(ids));
            return R.ok();
        } catch (Exception e) {
            return R.error("删除图片失败: " + e.getMessage());
        }
    }

}
