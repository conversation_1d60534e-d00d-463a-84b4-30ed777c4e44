<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.SalesmanDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.SalesmanEntity" id="salesmanMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="email" column="email"/>
        <result property="code" column="code"/>
        <result property="department" column="department"/>
        <result property="position" column="position"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
        <result property="tags" column="tags"/>
        <result property="parentId" column="parent_id"/>
        <result property="level" column="level"/>
        <result property="appid" column="appid"/>
        <result property="channelId" column="channel_id"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <result property="totalOrders" column="total_orders"/>
        <result property="activityOrders" column="activity_orders"/>
        <result property="rechargeOrders" column="recharge_orders"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalPayAmount" column="total_pay_amount"/>
        <result property="totalCommission" column="total_commission"/>
        <result property="parentName" column="parent_name"/>
        <result property="childrenCount" column="children_count"/>
        <result property="channelName" column="channel_name"/>
    </resultMap>

    <!-- 分页查询业务员列表（包含统计信息） -->
    <select id="selectPageWithStats" parameterType="java.util.Map"  resultMap="salesmanMap">
        SELECT
            s.id,
            s.name,
            s.mobile,
            s.email,
            s.code,
            s.department,
            s.position,
            s.status,
            s.remarks,
            s.tags,
            s.parent_id,
            s.level,
            s.appid,
            s.channel_id,
            s.create_on,
            s.create_by,
            s.update_on,
            s.update_by,
            COALESCE(stats.total_orders, 0) as total_orders,
            COALESCE(stats.activity_orders, 0) as activity_orders,
            COALESCE(stats.recharge_orders, 0) as recharge_orders,
            COALESCE(stats.total_amount, 0) as total_amount,
            COALESCE(stats.total_pay_amount, 0) as total_pay_amount,
            COALESCE(commission_stats.total_commission, 0) as total_commission,
            parent_s.name as parent_name,
            COALESCE(children_stats.children_count, 0) as children_count,
            c.name as channel_name
        FROM salesman s
        LEFT JOIN salesman parent_s ON s.parent_id = parent_s.id
        LEFT JOIN channel c ON s.channel_id = c.id
        LEFT JOIN (
            SELECT
                arr.salesman_id,
                COUNT(*) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                SUM(CASE WHEN arr.status = 1 THEN COALESCE(arr.pay_amount, 0) ELSE 0 END) as total_pay_amount,
                SUM( COALESCE(arr.amount, 0)) as total_amount
            FROM activity_recharge_record arr
            WHERE arr.salesman_id IS NOT NULL
            <if test="appid != null and appid != ''">
                AND arr.appid = #{appid}
            </if>
            GROUP BY arr.salesman_id
        ) stats ON s.id = stats.salesman_id
        LEFT JOIN (
            SELECT
                scr.salesman_id,
                SUM(COALESCE(scr.commission_amount, 0)) as total_commission
            FROM salesman_commission_record scr
            WHERE scr.settlement_status != 2  -- 排除已取消的佣金记录
            <if test="appid != null and appid != ''">
                AND scr.appid = #{appid}
            </if>
            GROUP BY scr.salesman_id
        ) commission_stats ON s.id = commission_stats.salesman_id
        LEFT JOIN (
            SELECT
                parent_id,
                COUNT(*) as children_count
            FROM salesman
            WHERE parent_id IS NOT NULL
            GROUP BY parent_id
        ) children_stats ON s.id = children_stats.parent_id
        <where>
            <if test="appid != null and appid != ''">
                AND s.appid = #{appid}
            </if>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="mobile != null and mobile != ''">
                AND s.mobile LIKE CONCAT('%', #{mobile}, '%')
            </if>
            <if test="channelId != null and channelId != ''">
                AND s.channel_id = #{channelId}
            </if>
            <if test="channelIds != null and channelIds.size() > 0">
                AND s.channel_id IN
                <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                    #{channelId}
                </foreach>
            </if>
        </where>
        ORDER BY s.create_on DESC
    </select>

    <!-- 查询业务员订单总体统计 -->
    <select id="selectOrderStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT s.id) as totalSalesmen,
            COUNT(DISTINCT CASE WHEN s.status = 1 THEN s.id END) as activeSalesmen,
            COALESCE(order_stats.total_orders, 0) as totalOrders,
            COALESCE(order_stats.activity_orders, 0) as activityOrders,
            COALESCE(order_stats.recharge_orders, 0) as rechargeOrders,
            COALESCE(order_stats.paid_orders, 0) as paidOrders,
            COALESCE(order_stats.total_amount, 0) as totalAmount,
            COALESCE(order_stats.total_pay_amount, 0) as totalPayAmount,
            COALESCE(commission_stats.total_commission, 0) as totalCommission
        FROM salesman s
        LEFT JOIN (
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN arr.recharge_type = 4 THEN 1 END) as activity_orders,
                COUNT(CASE WHEN arr.recharge_type IN (1, 2) THEN 1 END) as recharge_orders,
                COUNT(CASE WHEN arr.status = 1 THEN 1 END) as paid_orders,
                SUM(CASE WHEN arr.status = 1 THEN COALESCE(arr.pay_amount, 0) ELSE 0 END) as total_pay_amount,
                SUM( COALESCE(arr.amount, 0) ) as total_amount
            FROM activity_recharge_record arr
            WHERE arr.salesman_id IS NOT NULL
            <if test="appid != null and appid != ''">
                AND arr.appid = #{appid}
            </if>
        ) order_stats ON 1=1
        LEFT JOIN (
            SELECT
                SUM(COALESCE(scr.commission_amount, 0)) as total_commission
            FROM salesman_commission_record scr
            WHERE scr.settlement_status != 2  -- 排除已取消的佣金记录
            <if test="appid != null and appid != ''">
                AND scr.appid = #{appid}
            </if>
        ) commission_stats ON 1=1
        <where>
            <if test="appid != null and appid != ''">
                AND s.appid = #{appid}
            </if>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="mobile != null and mobile != ''">
                AND s.mobile LIKE CONCAT('%', #{mobile}, '%')
            </if>
            <if test="channelId != null and channelId != ''">
                AND s.channel_id = #{channelId}
            </if>
            <if test="channelIds != null and channelIds.size() > 0">
                AND s.channel_id IN
                <foreach collection="channelIds" item="channelId" open="(" separator="," close=")">
                    #{channelId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
