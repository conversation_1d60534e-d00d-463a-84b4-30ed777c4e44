<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.activity.dao.ActivityRefundRecordDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.activity.entity.ActivityRefundRecordEntity" id="activityRefundRecordMap">
        <result property="id" column="id"/>
        <result property="rechargeRecordId" column="recharge_record_id"/>
        <result property="originalOrderSn" column="original_order_sn"/>
        <result property="refundOrderSn" column="refund_order_sn"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="originalAmount" column="original_amount"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="payType" column="pay_type"/>
        <result property="status" column="status"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="refundTransactionId" column="refund_transaction_id"/>
        <result property="applyTime" column="apply_time"/>
        <result property="auditTime" column="audit_time"/>
        <result property="refundTime" column="refund_time"/>
        <result property="auditBy" column="audit_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

</mapper>
