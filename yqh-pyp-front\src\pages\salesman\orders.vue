<template>
  <div class="orders-page">
    <pcheader v-if="!isMobilePhone" />
    
    <!-- 自定义导航栏 -->
    <div class="custom-navbar">
      <van-icon name="arrow-left" @click="$router.go(-1)" class="nav-back" />
      <span class="nav-title">订单管理</span>
      <div class="nav-actions">
        <van-icon name="filter-o" @click="showFilter = true" class="nav-action" />
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <div class="stats-card">
          <div class="card-header">
            <div class="header-icon">
              <van-icon name="orders-o" size="20" />
            </div>
            <div class="header-text">
              <h3>订单概览</h3>
              <p>团队订单统计</p>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ orderStats.totalOrders }}</div>
              <div class="stat-label">总订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">¥{{ orderStats.totalAmount.toFixed(2) }}</div>
              <div class="stat-label">总金额</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ orderStats.todayOrders }}</div>
              <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">¥{{ orderStats.monthAmount.toFixed(2) }}</div>
              <div class="stat-label">本月业绩</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选标签 -->
      <div class="filter-tabs">
        <div 
          class="filter-tab" 
          :class="{ active: activeFilter === 'all' }"
          @click="setFilter('all')"
        >
          全部
        </div>
        <div 
          class="filter-tab" 
          :class="{ active: activeFilter === 'my' }"
          @click="setFilter('my')"
        >
          我的订单
        </div>
        <div 
          class="filter-tab" 
          :class="{ active: activeFilter === 'team' }"
          @click="setFilter('team')"
        >
          团队订单
        </div>
      </div>

      <!-- 订单列表 -->
      <div class="orders-list">
        <div 
          v-for="order in filteredOrders" 
          :key="order.id"
          class="order-card"
          @click="viewOrderDetails(order)"
        >
          <div class="order-header">
            <div class="order-info">
              <div class="order-number">订单号：{{ order.orderNo }}</div>
              <div class="order-time">{{ formatDate(order.createTime) }}</div>
            </div>
            <div class="order-status">
              <van-tag :type="getStatusType(order.status)" size="mini">
                {{ getStatusText(order.status) }}
              </van-tag>
            </div>
          </div>
          
          <div class="order-content">
            <div class="product-info">
              <div class="product-name">{{ order.productName }}</div>
              <div class="product-spec">{{ order.packageName }}</div>
            </div>
            <div class="order-amount">
              <div class="amount">¥{{ order.amount.toFixed(2) }}</div>
              <div class="quantity">x{{ order.quantity }}</div>
            </div>
          </div>
          
          <div class="order-footer">
            <div class="salesman-info">
              <van-icon name="manager-o" size="12" />
              <span>{{ order.salesmanName }}</span>
            </div>
            <div class="commission-info" v-if="order.commission">
              <span class="commission-label">佣金：</span>
              <span class="commission-amount">¥{{ order.commission.toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <van-button 
          type="default" 
          size="small" 
          :loading="loading"
          @click="loadMore"
          block
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </van-button>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredOrders.length === 0 && !loading" class="empty-state">
        <van-empty description="暂无订单数据" />
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup v-model="showFilter" position="bottom" round>
      <div class="filter-popup">
        <div class="filter-header">
          <div class="header-left">
            <div class="filter-icon">
              <van-icon name="filter-o" size="20" />
            </div>
            <h3>筛选条件</h3>
          </div>
          <div class="header-right">
            <van-icon name="cross" @click="showFilter = false" class="close-btn" />
          </div>
        </div>

        <div class="filter-content">
          <!-- 时间筛选 -->
          <div class="filter-section">
            <div class="section-title">
              <van-icon name="clock-o" size="16" />
              <h4>时间范围</h4>
            </div>
            <div class="filter-options">
              <div
                class="filter-option"
                :class="{ active: filterTime === 'all' }"
                @click="filterTime = 'all'"
              >
                <span>全部</span>
                <van-icon v-if="filterTime === 'all'" name="success" class="check-icon" />
              </div>
              <div
                class="filter-option"
                :class="{ active: filterTime === 'today' }"
                @click="filterTime = 'today'"
              >
                <span>今天</span>
                <van-icon v-if="filterTime === 'today'" name="success" class="check-icon" />
              </div>
              <div
                class="filter-option"
                :class="{ active: filterTime === 'week' }"
                @click="filterTime = 'week'"
              >
                <span>本周</span>
                <van-icon v-if="filterTime === 'week'" name="success" class="check-icon" />
              </div>
              <div
                class="filter-option"
                :class="{ active: filterTime === 'month' }"
                @click="filterTime = 'month'"
              >
                <span>本月</span>
                <van-icon v-if="filterTime === 'month'" name="success" class="check-icon" />
              </div>
            </div>
          </div>

          <!-- 状态筛选 -->
          <div class="filter-section">
            <div class="section-title">
              <van-icon name="orders-o" size="16" />
              <h4>订单状态</h4>
            </div>
            <div class="filter-options">
              <div
                class="filter-option"
                :class="{ active: filterStatus === 'all' }"
                @click="filterStatus = 'all'"
              >
                <span>全部</span>
                <van-icon v-if="filterStatus === 'all'" name="success" class="check-icon" />
              </div>
              <div
                class="filter-option"
                :class="{ active: filterStatus === 'pending' }"
                @click="filterStatus = 'pending'"
              >
                <span>待支付</span>
                <van-icon v-if="filterStatus === 'pending'" name="success" class="check-icon" />
              </div>
              <div
                class="filter-option"
                :class="{ active: filterStatus === 'paid' }"
                @click="filterStatus = 'paid'"
              >
                <span>已支付</span>
                <van-icon v-if="filterStatus === 'paid'" name="success" class="check-icon" />
              </div>
            </div>
          </div>
        </div>

        <div class="filter-actions">
          <van-button @click="resetFilter" size="large" class="reset-btn" round>
            <van-icon name="replay" size="16" />
            重置
          </van-button>
          <van-button @click="applyFilter" size="large" type="primary" class="apply-btn" round>
            <van-icon name="success" size="16" />
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  isMobilePhone
} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";

export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      loading: false,
      orders: [],
      orderStats: {
        totalOrders: 0,
        totalAmount: 0,
        todayOrders: 0,
        monthAmount: 0
      },
      activeFilter: 'all',
      showFilter: false,
      filterTime: 'all',
      filterStatus: 'all',
      page: 1,
      pageSize: 20,
      hasMore: true
    }
  },
  computed: {
    filteredOrders() {
      let filtered = this.orders;
      
      // 按类型筛选
      if (this.activeFilter === 'my') {
        filtered = filtered.filter(order => order.isMyOrder);
      } else if (this.activeFilter === 'team') {
        filtered = filtered.filter(order => !order.isMyOrder);
      }
      
      return filtered;
    }
  },
  mounted() {
    document.title = "订单管理";
    this.loadOrders();
    this.loadOrderStats();
  },
  methods: {
    // 加载订单列表
    async loadOrders(isLoadMore = false) {
      try {
        this.loading = true;

        const params = {
          page: isLoadMore ? this.page + 1 : 1,
          limit: this.pageSize,
          timeRange: this.filterTime,
          status: this.filterStatus,
          type: this.activeFilter // all, my, team
        };

        const res = await this.$fly.get('/pyp/web/salesman/orders', params);

        if (res.code === 200) {
          const orderList = res.page.list || res.orders || [];
          if (isLoadMore) {
            this.orders = [...this.orders, ...orderList];
            this.page++;
          } else {
            this.orders = orderList;
            this.page = 1;
          }

          this.hasMore = orderList.length === this.pageSize;
        } else {
          vant.Toast(res.msg || '加载失败');
        }
      } catch (error) {
        console.error('加载订单列表失败:', error);
        // 临时模拟数据，用于测试
        if (!isLoadMore) {
          this.orders = [];
          this.page = 1;
          this.hasMore = false;
        }
        vant.Toast('接口未实现，显示模拟数据');
      } finally {
        this.loading = false;
      }
    },

    // 加载订单统计
    async loadOrderStats() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/orderStats');

        if (res.code === 200) {
          this.orderStats = res.stats || {
            totalOrders: 0,
            totalAmount: 0,
            todayOrders: 0,
            monthAmount: 0
          };
        }
      } catch (error) {
        console.error('加载订单统计失败:', error);
        // 设置模拟数据
        this.orderStats = {
        };
      }
    },

    // 设置筛选
    setFilter(filter) {
      this.activeFilter = filter;
      this.loadOrders(); // 重新加载数据
    },

    // 加载更多
    loadMore() {
      this.loadOrders(true);
    },

    // 查看订单详情
    viewOrderDetails(order) {
      // 可以显示订单详情弹窗或跳转到详情页面
      vant.Dialog.alert({
        title: '订单详情',
        message: `
          订单号：${order.orderNo}
          产品：${order.productName}
          金额：¥${order.amount.toFixed(2)}
          状态：${this.getStatusText(order.status)}
          业务员：${order.salesmanName}
          创建时间：${this.formatDate(order.createTime)}
        `
      });
    },

    // 应用筛选
    applyFilter() {
      this.showFilter = false;
      this.loadOrders();
    },

    // 重置筛选
    resetFilter() {
      this.filterTime = 'all';
      this.filterStatus = 'all';
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 'pending':
          return 'warning';
        case 'paid':
          return 'primary';
        case 'completed':
          return 'success';
        case 'cancelled':
          return 'danger';
        default:
          return 'default';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'pending':
          return '待支付';
        case 'paid':
          return '已支付';
        case 'completed':
          return '已完成';
        case 'cancelled':
          return '已取消';
        default:
          return '未知';
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    },

  }
}
</script>

<style lang="less" scoped>
.orders-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: transparent;
  position: relative;
  z-index: 10;
}

.nav-back {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  background: transparent;
}

.nav-actions {
  display: flex;
  gap: 12px;
}

.nav-action {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.nav-action:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 内容区域 */
.content-section {
  background: #f8f9fa;
  border-radius: 12px ;
  margin-top: 20px;
  position: relative;
  z-index: 2;
  min-height: calc(100vh - 100px);
  padding: 20px 12px ;
  margin: 10px;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ff976a 0%, #ff6b6b 100%);
  border-radius: 10px;
  margin-right: 12px;
  color: white;
}

.header-text h3 {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.header-text p {
  margin: 0;
  font-size: 12px;
  color: #999;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 16px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 10px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* 订单列表 */
.orders-list {
  margin-bottom: 20px;
}

.order-card {
  background: white;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
  color: #999;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.product-spec {
  font-size: 12px;
  color: #666;
}

.order-amount {
  text-align: right;
}

.amount {
  font-size: 16px;
  font-weight: 700;
  color: #ff6b6b;
  margin-bottom: 2px;
}

.quantity {
  font-size: 12px;
  color: #999;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.salesman-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.commission-info {
  font-size: 12px;
}

.commission-label {
  color: #999;
}

.commission-amount {
  color: #07c160;
  font-weight: 600;
}

/* 加载更多 */
.load-more {
  margin: 20px 0;
}

/* 空状态 */
.empty-state {
  margin-top: 100px;
  text-align: center;
}

/* 筛选弹窗 */
.filter-popup {
  padding: 0;
  background: #f8f9fa;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  color: white;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #333;
}

.close-btn {
  padding: 8px;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #666;
  background: #f5f5f5;
  border-radius: 50%;
}

.filter-content {
  padding: 20px 24px;
}

.filter-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.filter-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-title .van-icon {
  color: #667eea;
}

.section-title h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.filter-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.filter-option:hover {
  background: #f0f0f0;
  border-color: #e0e0e0;
}

.filter-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.check-icon {
  color: white;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 16px;
  padding: 20px 24px 24px 24px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.reset-btn {
  flex: 1;
  background: #f8f9fa !important;
  border: 1px solid #e0e0e0 !important;
  color: #666 !important;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.reset-btn:hover {
  background: #f0f0f0 !important;
  border-color: #d0d0d0 !important;
}

.apply-btn {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.apply-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .content-section {
    padding: 16px 12px 30px 12px;
  }

  .order-card {
    padding: 12px;
  }
}
</style>
