<template>
  <div :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="130px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="会议名称"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="会议编号" prop="code">
            <el-input v-model="dataForm.code" placeholder="会议编号"></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="dataForm.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="管理员账号" prop="adminAccount">
            <el-input v-model="dataForm.adminAccount" placeholder="请输入管理员账号"></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- <el-form-item label="会议日期" prop="times">
        <el-date-picker v-model="dataForm.times" style="width: 100%" @change="dateChange" type="datetimerange"
          value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="区域地址" prop="cityId">
        <el-select v-model="dataForm.provinceId" placeholder="省" @change="provinceChange" filterable>
          <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select style="margin-left: 10px;" v-model="dataForm.cityId" placeholder="市" @change="getCityName"
          filterable>
          <el-option v-for="item in cities" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="dataForm.address" placeholder="详细地址"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="会议图片">
        <el-upload list-type="picture-card" :before-upload="checkFileSize" :on-success="successHandle"
          :file-list="fileList" :action="url">
          <i slot="default" class="el-icon-plus"></i>
          <div slot="file" slot-scope="{file}">
            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
        <el-dialog :visible.sync="imgDialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
        <div style="color: red">建议尺寸：1920*1080，大小2mb以下</div>
      </el-form-item> -->
      <el-row>
        <el-col :span="4">
          <el-form-item label="logo" prop="logo">
            <div class="image-upload-container">
              <el-button type="primary" @click="openImageModal('logo')">
                <i class="el-icon-picture"></i> 选择Logo
              </el-button>
              <div v-if="logoImages.length > 0" class="selected-images-preview">
                <div class="image-preview-item">
                  <img :src="logoImages[0].url" :alt="logoImages[0].url" @click="previewImage(logoImages[0].url)" />
                  <div class="image-actions">
                    <i class="el-icon-zoom-in" @click="previewImage(logoImages[0].url)"></i>
                    <i class="el-icon-delete" @click="removeImage('logo', 0)"></i>
                  </div>
                </div>
              </div>
            </div>
            <div style="color: red; margin-top: 10px;">建议尺寸：200*200，大小100kb以下</div>
          </el-form-item>
        </el-col>
      <el-col :span="12">
      <el-form-item label="主图片" prop="mobileBanner">
        <div class="image-upload-container">
          <el-button type="primary" @click="openImageModal('mobileBanner')">
            <i class="el-icon-picture"></i> 选择图片
          </el-button>
          <div v-if="mobileBannerImages.length > 0" class="selected-images-preview">
            <div
              v-for="(image, index) in mobileBannerImages"
              :key="index"
              class="image-preview-item"
            >
              <img :src="image.url" :alt="image.url" @click="previewImage(image.url)" />
              <div class="image-actions">
                <i class="el-icon-zoom-in" @click="previewImage(image.url)"></i>
                <i class="el-icon-delete" @click="removeImage('mobileBanner', index)"></i>
              </div>
            </div>
          </div>
        </div>
        <div style="color: red; margin-top: 10px;">建议尺寸：1920*1080，大小2mb以下</div>
      </el-form-item></el-col>
        <el-col :span="4">
      <el-form-item label="背景图" prop="background">
        <div class="image-upload-container">
          <el-button type="primary" @click="openImageModal('background')">
            <i class="el-icon-picture"></i> 选择背景图
          </el-button>
          <div v-if="backgroundImages.length > 0" class="selected-images-preview">
            <div class="image-preview-item">
              <img :src="backgroundImages[0].url" :alt="backgroundImages[0].url" @click="previewImage(backgroundImages[0].url)" />
              <div class="image-actions">
                <i class="el-icon-zoom-in" @click="previewImage(backgroundImages[0].url)"></i>
                <i class="el-icon-delete" @click="removeImage('background', 0)"></i>
              </div>
            </div>
          </div>
        </div>
        <div style="color: red; margin-top: 10px;">建议尺寸：1080*1920，大小1mb以下</div>
      </el-form-item></el-col>
        <el-col :span="4">
          <el-form-item label="分享图" prop="shareUrl">
            <div class="image-upload-container">
              <el-button type="primary" @click="openImageModal('shareUrl')">
                <i class="el-icon-picture"></i> 选择分享图
              </el-button>
              <div v-if="shareUrlImages.length > 0" class="selected-images-preview">
                <div class="image-preview-item">
                  <img :src="shareUrlImages[0].url" :alt="shareUrlImages[0].url" @click="previewImage(shareUrlImages[0].url)" />
                  <div class="image-actions">
                    <i class="el-icon-zoom-in" @click="previewImage(shareUrlImages[0].url)"></i>
                    <i class="el-icon-delete" @click="removeImage('shareUrl', 0)"></i>
                  </div>
                </div>
              </div>
            </div>
            <div style="color: red; margin-top: 10px;">建议尺寸：400*400，大小300kb以下</div>
          </el-form-item>
        </el-col>
    </el-row>
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item label="会议模板" prop="templateId">
            <el-select v-model="dataForm.templateId" placeholder="会议模板" filterable>
              <el-tooltip placement="right" v-for="item in sysTemplate" :key="item.templateCode">
                <div slot="content">
                  <img style="height: 600px;" :src="item.url" alt="">
                </div>
                <el-option :label="item.name" :value="item.templateCode"></el-option>
              </el-tooltip>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="九宫格字体颜色" prop="fontColor">
            <el-color-picker v-model="dataForm.fontColor"></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="九宫格底部展示" prop="type">
            <el-select v-model="dataForm.type" placeholder="类型" filterable multiple>
              <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="底部颜色配置" prop="bottomColor">
            <el-color-picker v-model="dataForm.bottomColor"></el-color-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="背景音乐" prop="musicUrl">
            <el-select v-model="dataForm.musicUrl" placeholder="背景音乐" filterable>
              <el-option label="无" value=""></el-option>
              <el-option v-for="item in music" :key="item.url" :label="item.name" :value="item.url"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动过期时间" prop="expirationTime">
            <el-date-picker
              v-model="dataForm.expirationTime"
              type="datetime"
              placeholder="选择过期时间（留空表示永不过期）"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%">
            </el-date-picker>
            <div style="color: #909399; font-size: 12px; margin-top: 5px;">
              设置活动的过期时间，过期后活动将无法正常使用。留空表示永不过期。
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row >
        <el-col :span="6">
          <el-form-item label="广告底部颜色" prop="adColor">
            <el-color-picker v-model="dataForm.adColor"></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="广告停留时间" prop="adTime">
            <el-input v-model="dataForm.adTime" placeholder="广告停留时间"><template slot="append">
                秒
              </template></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="首页进入广告" prop="ad">
            <div class="image-upload-container">
              <el-button type="primary" @click="openImageModal('ad')">
                <i class="el-icon-picture"></i> 选择广告图
              </el-button>
              <div v-if="adImages.length > 0" class="selected-images-preview">
                <div class="image-preview-item">
                  <img :src="adImages[0].url" :alt="adImages[0].url" @click="previewImage(adImages[0].url)" />
                  <div class="image-actions">
                    <i class="el-icon-zoom-in" @click="previewImage(adImages[0].url)"></i>
                    <i class="el-icon-delete" @click="removeImage('ad', 0)"></i>
                  </div>
                </div>
              </div>
            </div>
            <div style="color: red; margin-top: 10px;">建议尺寸：1000*2000，大小2mb以下</div>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="抖音类型" prop="douyinType">
            <el-radio-group v-model="dataForm.douyinType">
              <el-radio :label="0">视频</el-radio>
              <el-radio :label="1">图文</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小红书类型" prop="xiaohongshuType">
            <el-radio-group v-model="dataForm.xiaohongshuType">
              <el-radio :label="0">视频</el-radio>
              <el-radio :label="1">图文</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 标题生成配置 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="标题生成配置" style="margin-bottom: 10px;">
            <span style="color: #909399; font-size: 12px;">配置AI生成标题的相关参数</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="标题生成模式" prop="nameMode">
            <el-radio-group v-model="dataForm.nameMode">
              <el-radio label="ai">AI生成</el-radio>
              <el-radio label="manual">手动填写</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="默认标题" prop="defaultName">
            <el-input v-model="dataForm.defaultName" placeholder="请输入默认标题" maxlength="50" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="默认提示词" prop="defaultTitle">
            <el-input v-model="dataForm.defaultTitle" placeholder="请输入默认提示词" maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 默认用户输入配置 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="默认用户输入" prop="defaultUserInput">
            <el-input type="textarea" v-model="dataForm.defaultUserInput"
              placeholder="设置用户自定义补充字段的默认值，如：请在这里补充您的特殊要求或想法"
              :rows="2" maxlength="200" show-word-limit>
            </el-input>
            <div style="margin-top: 5px; font-size: 12px; color: #909399;">
              <i class="el-icon-info"></i>
              这个值会作为文案生成页面"自定义补充"字段的默认提示文字
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- AI标签配置 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="AI标签" prop="aiTag">
            <tags-editorlist
              v-model="aiTagList"
              placeholder="添加AI标签，如：男、女、儿童、老人等"
              :max-tags="8"
              :max-length="10"
              @change="onAiTagChange">
            </tags-editorlist>
            <div style="margin-top: 5px; font-size: 12px; color: #909399;">
              <i class="el-icon-info"></i>
              AI标签用于生成针对特定受众的文案。用户在"换一篇"时可以选择不同标签生成对应的文案，建议不超过8个标签
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="抖音POI" prop="douyinPoi">
            <el-input v-model="dataForm.douyinPoi" placeholder="请输入抖音POI"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="抖音点评" prop="douyindianping">
            <el-input v-model="dataForm.douyindianping" placeholder="请输入抖音点评"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="美团" prop="meituan">
            <el-input v-model="dataForm.meituan" placeholder="请输入美团"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="大众点评" prop="dazhongdianping">
            <el-input v-model="dataForm.dazhongdianping" placeholder="请输入大众点评"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业微信" prop="qiyeweixin">
            <el-input v-model="dataForm.qiyeweixin" placeholder="请输入企业微信"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="WiFi账号" prop="wifiAccount">
            <el-input v-model="dataForm.wifiAccount" placeholder="请输入WiFi账号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="WiFi密码" prop="wifiPassword">
            <el-input v-model="dataForm.wifiPassword" placeholder="请输入WiFi密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 携程三段式配置 -->
      <el-row>
        <el-col :span="24">
          <h4 style="margin: 20px 0 10px 0; color: #409EFF;">携程三段式配置</h4>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="携程首页配置" prop="ctripConfig">
            <el-input v-model="dataForm.ctripConfig" placeholder="请输入携程首页配置信息"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="携程点评配置" prop="ctripReviewConfig">
            <el-input v-model="dataForm.ctripReviewConfig" placeholder="请输入携程点评配置信息"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="携程笔记配置" prop="ctripNotesConfig">
            <el-input v-model="dataForm.ctripNotesConfig" placeholder="请输入携程笔记配置信息"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="公众号图片" prop="wechatQrCode">
            <div class="image-upload-container">
              <el-button type="primary" @click="openImageModal('wechatQrCode')">
                <i class="el-icon-picture"></i> 选择公众号图片
              </el-button>
              <div v-if="wechatQrCodeImages.length > 0" class="selected-images-preview">
                <div class="image-preview-item">
                  <img :src="wechatQrCodeImages[0].url" :alt="wechatQrCodeImages[0].url" @click="previewImage(wechatQrCodeImages[0].url)" />
                  <div class="image-actions">
                    <i class="el-icon-zoom-in" @click="previewImage(wechatQrCodeImages[0].url)"></i>
                    <i class="el-icon-delete" @click="removeImage('wechatQrCode', 0)"></i>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="抖音主页" prop="zhuyeDouyin">
            <el-input v-model="dataForm.zhuyeDouyin" placeholder="请输入抖音主页"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="快手主页" prop="zhuyeKuaishou">
            <el-input v-model="dataForm.zhuyeKuaishou" placeholder="请输入快手主页"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6" v-for="flag in platformVisibilityFlags" :key="flag.key">
          <el-form-item :label="flag.label">
            <el-switch v-model="dataForm[flag.key]" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 我的小店配置 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="我的小店配置" style="margin-bottom: 10px;">
            <span style="color: #909399; font-size: 12px;">配置商户自己的网页或小程序跳转</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否显示团购券">
            <el-switch v-model="dataForm.showGroupBuying" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否显示我的小店">
            <el-switch v-model="dataForm.showMyShop" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="dataForm.showMyShop">
          <el-form-item label="小店类型" prop="shopType">
            <el-radio-group v-model="dataForm.shopType">
              <el-radio :label="0">网页</el-radio>
              <el-radio :label="1">小程序</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dataForm.showMyShop">
          <el-form-item label="小店描述" prop="shopDescription">
            <el-input v-model="dataForm.shopDescription" placeholder="请输入小店描述，如：商户专属店铺"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dataForm.shopType === 0 && dataForm.showMyShop">
        <el-col :span="24">
          <el-form-item label="小店网页URL" prop="shopUrl">
            <el-input v-model="dataForm.shopUrl" placeholder="请输入小店网页URL，如：https://www.example.com/shop"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dataForm.shopType === 1 && dataForm.showMyShop">
        <el-col :span="12">
          <el-form-item label="小程序AppID" prop="shopAppid">
            <el-input v-model="dataForm.shopAppid" placeholder="请输入小程序AppID"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小程序页面路径" prop="shopPagePath">
            <el-input v-model="dataForm.shopPagePath" placeholder="请输入页面路径，如：pages/shop/index"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="turnBack()">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>

    <!-- 图片上传弹窗 -->
    <ImageUploadModal
      :visible.sync="imageModalVisible"
      :multiple="currentImageField === 'mobileBanner'"
      :max-count="currentImageField === 'mobileBanner' ? 9 : 1"
      :default-images="getCurrentImages()"
      @confirm="handleImageConfirm"
    />

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="imgDialogVisible" width="60%">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import Compressor from 'compressorjs';
import { yesOrNo } from "@/data/common"
export default {
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor"),
    OssUploader: () =>
      import('../oss/oss-uploader'),
    ImageUploadModal: () =>
      import("@/components/image-upload-modal"),
    TagsEditorlist: () =>
      import("@/components/tags-editorlist")
  },
  data() {
    var conferenceCode = (rule, value, callback) => {
      var reg = /^[^\u4e00-\u9fa5]{3,10}$/;
      if (!reg.test(value)) {
        callback(new Error("会议编号3到10位，不能包含中文"));
      } else {
        callback()
      }
    };
    var faceRateRule = (rule, value, callback) => {
      var reg = /^(100)$|^((\d|[1-9]\d)(\.\d{1,2})?)$/;
      if (value && !reg.test(value)) {
        callback(new Error("请填入0-100之间的数，最多保留2位小数"));
      } else {
        callback()
      }
    };
    return {
      platformVisibilityFlags: [
        { key: 'showDouyin', label: '是否显示抖音' },
        { key: 'showXiaohongshu', label: '是否显示小红书' },
        { key: 'showKuaishou', label: '是否显示快手' },
        { key: 'showShipinhao', label: '是否显示视频号' },
        { key: 'showDouyindianping', label: '是否显示抖音点评' },
        { key: 'showDazhongdianping', label: '是否显示大众点评' },
        { key: 'showMeituandianping', label: '是否显示美团点评' },
        { key: 'showQiyeweixin', label: '是否显示企业微信' },
        { key: 'showMiniProgram', label: '是否显示微信小程序' },
        { key: 'showWifi', label: '是否显示wifi' },
        { key: 'showGuanzhukuaishou', label: '是否显示关注快手' },
        { key: 'showGuanzhudouyin', label: '是否显示关注抖音' },
        { key: 'showShipindianzan', label: '是否显示视频点赞' },
        { key: 'showCtrip', label: '是否显示携程首页' },
        { key: 'showCtripReview', label: '是否显示携程点评' },
        { key: 'showCtripNotes', label: '是否显示携程笔记' },
        { key: 'showWechatQr', label: '是否显示微信公众号二维码' }
      ],
      yesOrNo: yesOrNo,
      btnLoading: false,
      configActivityType: [],
      appFileList: [],
      con: [],
      fileList: [],
      cityName: "",
      imgDialogVisible: false,
      imgAppDialogVisible: false,
      dialogImageUrl: '',
      dialogAppImageUrl: '',
      // 图片上传弹窗相关
      imageModalVisible: false,
      currentImageField: '',
      mobileBannerImages: [],
      logoImages: [],
      backgroundImages: [],
      shareUrlImages: [],
      wechatQrCodeImages: [],
      adImages: [],
      music: [],
      provinces: [],
      cities: [],
      url: '',
      dataForm: {
        id: 0,
        times: [],
        code: '',
        name: '',
        startTime: '',
        endTime: '',
        provinceId: '',
        cityId: '',
        pcBanner: '',
        mobileBanner: '',
        background: '',
        type: ['0'],
        subscribeImg: '',
        applyBackground: '',
        backImg: '',
        pvCount: '',
        uvCount: '',
        longitude: '',
        latitude: '',
        address: '',
        bottomColor: '',
        fontColor: '',
        templateId: '1',
        applyNotify: '',
        hotelNotify: '',
        turnurl: '',
        isCountdown: 1,
        isShow: 1,
        showSub: 0,
        isHot: 0,
        isIndex: 0,
        appid: '',
        topicGuest: 0,
        topicSpeaker: 0,
        scheduleGuest: 0,
        scheduleSpeaker: 0,
        scheduleDiscuss: 0,
        introduction: '',
        showApplyNumber: 0,
        hotelNeedApply: 0,
        liveNeedApply: 1,
        applySuccessLive: 1,
        applySuccessHotel: 1,
        onlyOneHotel: 0,
        cancelApplyHotel: 0,
        backUrl: '',
        musicUrl: '',
        ad: '',
        adColor: '',
        adTime: '',
        shareUrl: '',
        configActivityTypeId: '',
        logo: '',
        showDouyin: 1,
        showXiaohongshu: 1,
        showKuaishou: 1,
        showShipinhao: 1,
        showDouyindianping: 1,
        showDazhongdianping: 1,
        showMeituandianping: 1,
        showQiyeweixin: 1,
        showMiniProgram: 1,
        showWifi: 1,
        showGuanzhukuaishou: 1,
        showGuanzhudouyin: 1,
        showShipindianzan: 1,
        douyinType: 0,
        xiaohongshuType: 1,
        douyinPoi: '',
        douyindianping: '',
        meituan: '',
        dazhongdianping: '',
        qiyeweixin: '',
        wifiAccount: '',
        wifiPassword: '',
        wechatQrCode: '',
        zhuyeDouyin: '',
        zhuyeKuaishou: '',
        nameMode: 'ai',
        defaultName: '',
        defaultTitle: '',
        defaultUserInput: '',
        aiTag: '',
        showCtrip: 0,
        ctripConfig: '',
        showCtripReview: 0,
        showCtripNotes: 0,
        ctripReviewConfig: '',
        ctripNotesConfig: '',
        showWechatQr: 0,
        mobile: '',
        adminAccount: '',
        expirationTime: null,
        shopType: 0,
        shopUrl: '',
        shopAppid: '',
        shopPagePath: '',
        shopDescription: '商户专属店铺',
        showMyShop: 0,
        showGroupBuying: 0,
      },
      typeList: [
        { id: '0', name: '自定义' },
        { id: '1', name: 'banner广告' },
        { id: '2', name: '文件下载' },
      ],
      templateList: [
        { id: '1', name: '模板1-传统九宫格' },
        { id: '2', name: '模板2-含背景九宫格' },
        { id: '31', name: '模板3-4114布局' },
        { id: '4', name: '模板4-圆形九宫格' },
        { id: '5', name: '模板5-纯图标九宫格' },
        { id: '61', name: '模板61-3-3(1-2)-3-1布局' },
        { id: '62', name: '模板62-3-3(1-2)-2布局' },
        { id: '63', name: '模板63-3-3(1-2)-2-2-2布局' },
      ],
      sysTemplate: [],
      // AI标签列表
      aiTagList: [],
      dataRule: {
        code: [{
          required: true,
          message: '会议编号不能为空',
          trigger: 'blur'
        }, {
          validator: conferenceCode,
          trigger: "blur"
        }],
        name: [{
          required: true,
          message: '会议名称不能为空',
          trigger: 'blur'
        }],
        mobile: [{
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur'
        }],
        times: [{
          required: true,
          message: '会议时间不能为空',
          trigger: 'blur'
        }],
        provinceId: [{
          required: true,
          message: '省份不能为空',
          trigger: 'blur'
        }],
        cityId: [{
          required: true,
          message: '城市不能为空',
          trigger: 'blur'
        }],
        pcBanner: [{
          required: true,
          message: '会议图片不能为空',
          trigger: 'blur'
        }],
        mobileBanner: [{
          required: true,
          message: '手机端图片不能为空',
          trigger: 'blur'
        }],
        isShow: [{
          required: true,
          message: '公众号显示不能为空',
          trigger: 'blur'
        }],
      }
    }
  },

  activated() {
    this.init()
  },
  methods: {
    init() {
      this.dataForm.id = this.$route.query.id || 0;
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.appFileList = [];
      this.fileList = [];
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.appid = this.$cookie.get("appid");
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$set(this.dataForm, "times", [data.activity.startTime, data.activity.endTime])
              this.appFileList = data.activity.appFileList
              this.fileList = data.activity.fileList
              this.dataForm.code = data.activity.code
              this.dataForm.name = data.activity.name
              this.dataForm.startTime = data.activity.startTime
              this.dataForm.endTime = data.activity.endTime
              this.dataForm.provinceId = data.activity.provinceId
              this.dataForm.cityId = data.activity.cityId
              this.dataForm.pcBanner = data.activity.pcBanner
              this.dataForm.mobileBanner = data.activity.mobileBanner
              this.dataForm.subscribeImg = data.activity.subscribeImg
              this.dataForm.applyBackground = data.activity.applyBackground
              this.dataForm.backImg = data.activity.backImg
              this.dataForm.background = data.activity.background
              this.dataForm.type = data.activity.type ? data.activity.type.split(',') : []
              this.dataForm.pvCount = data.activity.pvCount
              this.dataForm.uvCount = data.activity.uvCount
              this.dataForm.longitude = data.activity.longitude
              this.dataForm.latitude = data.activity.latitude
              this.dataForm.address = data.activity.address
              this.dataForm.introduction = data.activity.introduction
              this.dataForm.bottomColor = data.activity.bottomColor
              this.dataForm.fontColor = data.activity.fontColor
              this.dataForm.templateId = data.activity.templateId
              this.dataForm.applyNotify = data.activity.applyNotify
              this.dataForm.hotelNotify = data.activity.hotelNotify
              this.dataForm.isCountdown = data.activity.isCountdown
              this.dataForm.isShow = data.activity.isShow
              this.dataForm.showSub = data.activity.showSub
              this.dataForm.isHot = data.activity.isHot
              this.dataForm.isIndex = data.activity.isIndex
              this.dataForm.appid = data.activity.appid
              this.dataForm.topicGuest = data.activity.topicGuest
              this.dataForm.topicSpeaker = data.activity.topicSpeaker
              this.dataForm.scheduleGuest = data.activity.scheduleGuest
              this.dataForm.scheduleSpeaker = data.activity.scheduleSpeaker
              this.dataForm.scheduleDiscuss = data.activity.scheduleDiscuss
              this.dataForm.showApplyNumber = data.activity.showApplyNumber
              this.dataForm.hotelNeedApply = data.activity.hotelNeedApply
              this.dataForm.liveNeedApply = data.activity.liveNeedApply
              this.dataForm.applySuccessLive = data.activity.applySuccessLive
              this.dataForm.applySuccessHotel = data.activity.applySuccessHotel
              this.dataForm.onlyOneHotel = data.activity.onlyOneHotel
              this.dataForm.cancelApplyHotel = data.activity.cancelApplyHotel
              this.dataForm.turnurl = data.activity.turnurl
              this.dataForm.backUrl = data.activity.backUrl
              this.dataForm.musicUrl = data.activity.musicUrl
              this.dataForm.ad = data.activity.ad
              this.dataForm.adColor = data.activity.adColor
              this.dataForm.adTime = data.activity.adTime
              this.dataForm.shareUrl = data.activity.shareUrl
              this.dataForm.configActivityTypeId = data.activity.configActivityTypeId
              this.dataForm.showDouyin = data.activity.showDouyin
              this.dataForm.showXiaohongshu = data.activity.showXiaohongshu
              this.dataForm.showKuaishou = data.activity.showKuaishou
              this.dataForm.showShipinhao = data.activity.showShipinhao
              this.dataForm.showDouyindianping = data.activity.showDouyindianping
              this.dataForm.showDazhongdianping = data.activity.showDazhongdianping
              this.dataForm.showMeituandianping = data.activity.showMeituandianping
              this.dataForm.showQiyeweixin = data.activity.showQiyeweixin
              this.dataForm.showMiniProgram = data.activity.showMiniProgram
              this.dataForm.showWifi = data.activity.showWifi
              this.dataForm.showGuanzhukuaishou = data.activity.showGuanzhukuaishou
              this.dataForm.showGuanzhudouyin = data.activity.showGuanzhudouyin
              this.dataForm.showShipindianzan = data.activity.showShipindianzan
              this.dataForm.logo = data.activity.logo
              this.dataForm.douyinType = data.activity.douyinType
              this.dataForm.xiaohongshuType = data.activity.xiaohongshuType
              this.dataForm.douyinPoi = data.activity.douyinPoi
              this.dataForm.douyindianping = data.activity.douyindianping
              this.dataForm.meituan = data.activity.meituan
              this.dataForm.dazhongdianping = data.activity.dazhongdianping
              this.dataForm.qiyeweixin = data.activity.qiyeweixin
              this.dataForm.wifiAccount = data.activity.wifiAccount
              this.dataForm.wifiPassword = data.activity.wifiPassword
              this.dataForm.wechatQrCode = data.activity.wechatQrCode
              this.dataForm.zhuyeDouyin = data.activity.zhuyeDouyin
              this.dataForm.zhuyeKuaishou = data.activity.zhuyeKuaishou
              this.dataForm.nameMode = data.activity.nameMode || 'ai'
              this.dataForm.defaultName = data.activity.defaultName || ''
              this.dataForm.defaultTitle = data.activity.defaultTitle || ''
              this.dataForm.defaultUserInput = data.activity.defaultUserInput || ''
              this.dataForm.aiTag = data.activity.aiTag || ''
              // 将AI标签字符串转换为数组
              this.aiTagList = data.activity.aiTag ? data.activity.aiTag.split(',').map(tag => tag.trim()).filter(tag => tag) : []
              this.dataForm.showCtrip = data.activity.showCtrip || 0
              this.dataForm.ctripConfig = data.activity.ctripConfig || ''
              this.dataForm.showCtripReview = data.activity.showCtripReview || 0
              this.dataForm.showCtripNotes = data.activity.showCtripNotes || 0
              this.dataForm.ctripReviewConfig = data.activity.ctripReviewConfig || ''
              this.dataForm.ctripNotesConfig = data.activity.ctripNotesConfig || ''
              this.dataForm.showWechatQr = data.activity.showWechatQr || 0
              this.dataForm.mobile = data.activity.mobile || ''
              this.dataForm.adminAccount = data.activity.adminAccount || ''
              this.dataForm.shopType = data.activity.shopType || 0
              this.dataForm.shopUrl = data.activity.shopUrl || ''
              this.dataForm.shopAppid = data.activity.shopAppid || ''
              this.dataForm.shopPagePath = data.activity.shopPagePath || ''
              this.dataForm.shopDescription = data.activity.shopDescription || '商户专属店铺'
              this.dataForm.showMyShop = data.activity.showMyShop || 0
              this.dataForm.showGroupBuying = data.activity.showGroupBuying || 0

              // 初始化图片数组
              this.initImageArrays()
              // this.$http({
              //   url: this.$http.adornUrl(
              //     `/sys/region/pid/${this.dataForm.provinceId}`
              //   ),
              //   method: "get",
              //   params: this.$http.adornParams()
              // }).then(({
              //   data
              // }) => {
              //   if (data && data.code === 200) {
              //     this.cities = data.list;
              //   } else {
              //     this.cities = [];
              //   }
              // });
            }
          })
        }
      })
      this.getProvinces();
      this.getMusic();
      this.getTempalte();
      this.getConfigActivityType();
    },
    turnBack() {
      this.$router.go(-1);
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activity/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'code': this.dataForm.code,
              'name': this.dataForm.name,
              'startTime': this.dataForm.startTime,
              'endTime': this.dataForm.endTime,
              'provinceId': this.dataForm.provinceId,
              'cityId': this.dataForm.cityId,
              'pcBanner': this.dataForm.pcBanner,
              'mobileBanner': this.dataForm.mobileBanner,
              'subscribeImg': this.dataForm.subscribeImg,
              'applyBackground': this.dataForm.applyBackground,
              'backImg': this.dataForm.backImg,
              'background': this.dataForm.background,
              'type': this.dataForm.type.toString(),
              'pvCount': this.dataForm.pvCount,
              'uvCount': this.dataForm.uvCount,
              'longitude': this.dataForm.longitude,
              'latitude': this.dataForm.latitude,
              'address': this.dataForm.address,
              'introduction': this.dataForm.introduction,
              'bottomColor': this.dataForm.bottomColor,
              'fontColor': this.dataForm.fontColor,
              'templateId': this.dataForm.templateId,
              'applyNotify': this.dataForm.applyNotify,
              'hotelNotify': this.dataForm.hotelNotify,
              'isCountdown': this.dataForm.isCountdown,
              'isShow': this.dataForm.isShow,
              'showSub': this.dataForm.showSub,
              'isIndex': this.dataForm.isIndex,
              'isHot': this.dataForm.isHot,
              'appid': this.dataForm.appid,
              'topicGuest': this.dataForm.topicGuest,
              'topicSpeaker': this.dataForm.topicSpeaker,
              'scheduleGuest': this.dataForm.scheduleGuest,
              'scheduleSpeaker': this.dataForm.scheduleSpeaker,
              'scheduleDiscuss': this.dataForm.scheduleDiscuss,
              'showApplyNumber': this.dataForm.showApplyNumber,
              'hotelNeedApply': this.dataForm.hotelNeedApply,
              'liveNeedApply': this.dataForm.liveNeedApply,
              'applySuccessLive': this.dataForm.applySuccessLive,
              'applySuccessHotel': this.dataForm.applySuccessHotel,
              'onlyOneHotel': this.dataForm.onlyOneHotel,
              'cancelApplyHotel': this.dataForm.cancelApplyHotel,
              'turnurl': this.dataForm.turnurl,
              'backUrl': this.dataForm.backUrl,
              'musicUrl': this.dataForm.musicUrl,
              'ad': this.dataForm.ad,
              'adColor': this.dataForm.adColor,
              'adTime': this.dataForm.adTime,
              'shareUrl': this.dataForm.shareUrl,
              'configActivityTypeId': this.dataForm.configActivityTypeId,
              'showDouyin': this.dataForm.showDouyin,
              'showXiaohongshu': this.dataForm.showXiaohongshu,
              'showKuaishou': this.dataForm.showKuaishou,
              'showShipinhao': this.dataForm.showShipinhao,
              'showDouyindianping': this.dataForm.showDouyindianping,
              'showDazhongdianping': this.dataForm.showDazhongdianping,
              'showMeituandianping': this.dataForm.showMeituandianping,
              'showQiyeweixin': this.dataForm.showQiyeweixin,
              'showMiniProgram': this.dataForm.showMiniProgram,
              'showWifi': this.dataForm.showWifi,
              'showGuanzhukuaishou': this.dataForm.showGuanzhukuaishou,
              'showGuanzhudouyin': this.dataForm.showGuanzhudouyin,
              'showShipindianzan': this.dataForm.showShipindianzan,
              'logo': this.dataForm.logo,
              'douyinType': this.dataForm.douyinType,
              'xiaohongshuType': this.dataForm.xiaohongshuType,
              'douyinPoi': this.dataForm.douyinPoi,
              'douyindianping': this.dataForm.douyindianping,
              'meituan': this.dataForm.meituan,
              'dazhongdianping': this.dataForm.dazhongdianping,
              'qiyeweixin': this.dataForm.qiyeweixin,
              'wifiAccount': this.dataForm.wifiAccount,
              'wifiPassword': this.dataForm.wifiPassword,
              'wechatQrCode': this.dataForm.wechatQrCode,
              'zhuyeDouyin': this.dataForm.zhuyeDouyin,
              'zhuyeKuaishou': this.dataForm.zhuyeKuaishou,
              'nameMode': this.dataForm.nameMode,
              'defaultName': this.dataForm.defaultName,
              'defaultTitle': this.dataForm.defaultTitle,
              'defaultUserInput': this.dataForm.defaultUserInput,
              'aiTag': this.aiTagList.join(','),
              'showCtrip': this.dataForm.showCtrip,
              'ctripConfig': this.dataForm.ctripConfig,
              'showCtripReview': this.dataForm.showCtripReview,
              'showCtripNotes': this.dataForm.showCtripNotes,
              'ctripReviewConfig': this.dataForm.ctripReviewConfig,
              'ctripNotesConfig': this.dataForm.ctripNotesConfig,
              'showWechatQr': this.dataForm.showWechatQr,
              'mobile': this.dataForm.mobile,
              'adminAccount': this.dataForm.adminAccount,
              'expirationTime': this.dataForm.expirationTime,
              'shopType': this.dataForm.shopType,
              'shopUrl': this.dataForm.shopUrl,
              'shopAppid': this.dataForm.shopAppid,
              'shopPagePath': this.dataForm.shopPagePath,
              'shopDescription': this.dataForm.shopDescription,
              'showMyShop': this.dataForm.showMyShop,
              'showGroupBuying': this.dataForm.showGroupBuying,
            })
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.turnBack();
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getProvinces() {
      this.$http({
        url: this.$http.adornUrl("/sys/region/pid/100000"),
        method: "get",
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.provinces = data.list;
        } else {
          this.provinces = [];
        }
      });
    },
    getMusic() {
      this.$http({
        url: this.$http.adornUrl("/sys/sysmusic/findAll"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.music = data.result;
        } else {
          this.music = [];
        }
      });
    },
    getTempalte() {
      this.$http({
        url: this.$http.adornUrl("/sys/systemplate/findAll"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.sysTemplate = data.result;
        } else {
          this.sysTemplate = [];
        }
      });
    },
    getConfigActivityType() {
      this.$http({
        url: this.$http.adornUrl("/config/configactivitytype/findByAppid"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.configActivityType = data.result;
        } else {
          this.configActivityType = [];
        }
      });
    },
    provinceChange(val) {
      if (val === undefined) {
        return;
      }
      this.cities = {};
      this.dataForm.cityId = undefined;
      this.dataForm.jieSongCityName = [];
      this.$http({
        url: this.$http.adornUrl(`/sys/region/pid/${val}`),
        method: "get",
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.cities = data.list;
        } else {
          this.cities = [];
        }
      });
    },
    getCityName(v) {
      var obj = this.cities.find((item) => { //这里的userList就是上面遍历的数据源
        return item.id === v; //筛选出匹配数据
      });
      this.cityName = obj.name.replace("市", ""); //我这边的name就是对应label的
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // 上传成功
    successHandle(response, file, fileList) {
      this.fileList = fileList;
      this.successNum1++;
      if (response && response.code === 200) {
        if (!this.dataForm.pcBanner || this.dataForm.pcBanner.length == 0) {
          this.dataForm.pcBanner = response.url
        } else {
          this.dataForm.pcBanner += "," + response.url;
        }
      } else {
        this.$message.error(response.msg);
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      this.appFileList = fileList;
      this.successNum++;
      if (response && response.code === 200) {
        if (!this.dataForm.mobileBanner || this.dataForm.mobileBanner.length == 0) {
          this.dataForm.mobileBanner = response.url
        } else {
          this.dataForm.mobileBanner += "," + response.url;
        }
      } else {
        this.$message.error(response.msg);
      }
    },
    // 上传成功（背景）
    backgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.background = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    // 上传成功（报名完关注二维码）
    subscribeImgSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.subscribeImg = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    applyBackgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.applyBackground = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    backImgSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.backImg = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    adSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.ad = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    shareUrlSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.shareUrl = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    logoSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.logo = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },
    handleRemove(file) {
      this.dataForm.pcBanner = (',' + this.dataForm.pcBanner + ',').replace(',' + file.url + ',', ',').substr(1).replace(/,$/, '')
      this.fileList.splice(this.fileList.indexOf(file), 1)
    },
    handleAppRemove(file) {
      this.dataForm.mobileBanner = (',' + this.dataForm.mobileBanner + ',').replace(',' + file.url + ',', ',').substr(1).replace(/,$/, '')
      this.appFileList.splice(this.appFileList.indexOf(file), 1)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.imgDialogVisible = true;
    },
    handleAppPictureCardPreview(file) {
      this.dialogAppImageUrl = file.url;
      this.imgAppDialogVisible = true;
    },
    dateChange(v) {
      this.dataForm.startTime = v[0];
      this.dataForm.endTime = v[1];
      console.log(v)
    },

    // 图片上传弹窗相关方法
    openImageModal(field) {
      this.currentImageField = field
      this.imageModalVisible = true
    },

    getCurrentImages() {
      switch (this.currentImageField) {
        case 'mobileBanner':
          return this.mobileBannerImages
        case 'logo':
          return this.logoImages
        case 'background':
          return this.backgroundImages
        case 'shareUrl':
          return this.shareUrlImages
        case 'wechatQrCode':
          return this.wechatQrCodeImages
        case 'ad':
          return this.adImages
        default:
          return []
      }
    },

    handleImageConfirm(images) {
      switch (this.currentImageField) {
        case 'mobileBanner':
          this.mobileBannerImages = images
          this.dataForm.mobileBanner = images.map(img => img.url).join(',')
          break
        case 'logo':
          this.logoImages = images
          this.dataForm.logo = images.length > 0 ? images[0].url : ''
          break
        case 'background':
          this.backgroundImages = images
          this.dataForm.background = images.length > 0 ? images[0].url : ''
          break
        case 'shareUrl':
          this.shareUrlImages = images
          this.dataForm.shareUrl = images.length > 0 ? images[0].url : ''
          break
        case 'wechatQrCode':
          this.wechatQrCodeImages = images
          this.dataForm.wechatQrCode = images.length > 0 ? images[0].url : ''
          break
        case 'ad':
          this.adImages = images
          this.dataForm.ad = images.length > 0 ? images[0].url : ''
          break
      }
    },

    removeImage(field, index) {
      switch (field) {
        case 'mobileBanner':
          this.mobileBannerImages.splice(index, 1)
          this.dataForm.mobileBanner = this.mobileBannerImages.map(img => img.url).join(',')
          break
        case 'logo':
          this.logoImages.splice(index, 1)
          this.dataForm.logo = this.logoImages.length > 0 ? this.logoImages[0].url : ''
          break
        case 'background':
          this.backgroundImages.splice(index, 1)
          this.dataForm.background = this.backgroundImages.length > 0 ? this.backgroundImages[0].url : ''
          break
        case 'shareUrl':
          this.shareUrlImages.splice(index, 1)
          this.dataForm.shareUrl = this.shareUrlImages.length > 0 ? this.shareUrlImages[0].url : ''
          break
        case 'wechatQrCode':
          this.wechatQrCodeImages.splice(index, 1)
          this.dataForm.wechatQrCode = this.wechatQrCodeImages.length > 0 ? this.wechatQrCodeImages[0].url : ''
          break
        case 'ad':
          this.adImages.splice(index, 1)
          this.dataForm.ad = this.adImages.length > 0 ? this.adImages[0].url : ''
          break
      }
    },

    previewImage(url) {
      this.dialogImageUrl = url
      this.imgDialogVisible = true
    },

    // 初始化图片数组
    initImageArrays() {
      // 主图片（支持多张）
      if (this.dataForm.mobileBanner) {
        this.mobileBannerImages = this.dataForm.mobileBanner.split(',').map((url, index) => ({
          id: `mobile_${index}`,
          url: url.trim(),
          createDate: new Date().toISOString()
        })).filter(img => img.url)
      }

      // Logo（单张）
      if (this.dataForm.logo) {
        this.logoImages = [{
          id: 'logo_1',
          url: this.dataForm.logo,
          createDate: new Date().toISOString()
        }]
      }

      // 背景图（单张）
      if (this.dataForm.background) {
        this.backgroundImages = [{
          id: 'background_1',
          url: this.dataForm.background,
          createDate: new Date().toISOString()
        }]
      }

      // 分享图（单张）
      if (this.dataForm.shareUrl) {
        this.shareUrlImages = [{
          id: 'share_1',
          url: this.dataForm.shareUrl,
          createDate: new Date().toISOString()
        }]
      }

      // 公众号图片（单张）
      if (this.dataForm.wechatQrCode) {
        this.wechatQrCodeImages = [{
          id: 'wechatQrCode_1',
          url: this.dataForm.wechatQrCode,
          createDate: new Date().toISOString()
        }]
      }

      // 广告图（单张）
      if (this.dataForm.ad) {
        this.adImages = [{
          id: 'ad_1',
          url: this.dataForm.ad,
          createDate: new Date().toISOString()
        }]
      }
    },
    // AI标签变化处理
    onAiTagChange(tags) {
      this.aiTagList = tags;
      this.dataForm.aiTag = tags.join(',');
    }
  }
}
</script>

<style scoped>
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.selected-images-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.image-preview-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-item:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.3s;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
