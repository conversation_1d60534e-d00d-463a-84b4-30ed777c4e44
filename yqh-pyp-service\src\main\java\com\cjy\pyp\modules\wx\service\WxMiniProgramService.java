package com.cjy.pyp.modules.wx.service;

import com.cjy.pyp.modules.wx.form.WxMiniProgramSchemeForm;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * 微信小程序服务接口
 * 
 * <AUTHOR>
 */
public interface WxMiniProgramService {

    /**
     * 生成小程序URL Scheme
     *
     * @param appid 公众号appid
     * @param form 生成参数
     * @return URL Scheme
     * @throws WxErrorException 微信接口异常
     */
    String generateUrlScheme(String appid, WxMiniProgramSchemeForm form) throws WxErrorException;

    /**
     * 生成小店小程序URL Scheme
     *
     * @param shopAppid 小店小程序appid
     * @param form 生成参数
     * @return URL Scheme
     * @throws WxErrorException 微信接口异常
     */
    String generateShopUrlScheme(String shopAppid, WxMiniProgramSchemeForm form) throws WxErrorException;

}
