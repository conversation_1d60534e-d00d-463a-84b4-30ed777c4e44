/**
 * 全站路由配置
 *
 * 建议:
 * 1. 代码中路由统一使用name属性跳转(不使用path属性)
 */
// import Vue from 'vue'
// import Router from 'vue-router'
import http from '@/utils/httpRequest'
import { isURL } from '@/utils/validate'
import { clearLoginInfo } from '@/utils'

// Vue.use(Router)

const _import = require('./import-views')
// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
  { path: '/404', component: () => import('@/views/common/404'), name: '404', meta: { title: '404未找到' } },
  { path: '/login', component: () => import('@/views/common/login'), name: 'login', meta: { title: '登录' } },
  { path: '/activityuserapplyordersignqrcode', component: () => import('@/views/modules/activity/activityuserapplyordersignqrcode'), name: 'activityuserapplyordersignqrcode', meta: { title: '电子签到码' } },
]

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = {
  path: '/',
  component: () => import('@/views/main'),
  name: 'main',
  redirect: { name: 'home' },
  meta: { title: '主入口整体布局' },
  children: [
    // 通过meta对象设置路由展示方式
    // 1. isTab: 是否通过tab展示内容, true: 是, false: 否
    // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
    // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
    { path: '/home', component: () => import('@/views/common/home'), name: 'home', meta: { title: '首页' } },
    { path: '/theme', component: () => import('@/views/common/theme'), name: 'theme', meta: { title: '主题' } },
    { path: '/cms', component: () => import('@/views/modules/cms/cms'), name: 'cms', meta: { title: '网站设置' } },
    { path: '/activityAddOrUpdate', component: () => import('@/views/modules/activity/activity-add-or-update'), name: 'activityAddOrUpdate', meta: { title: '会议新增或修改' } },
    { path: '/activityextraAddOrUpdate', component: () => import('@/views/modules/activity/activityextra-add-or-update'), name: 'activityextraAddOrUpdate', meta: { title: '会议活动表新增或修改' } },
    { path: '/applyActivityChannelConfig', component: () => import('@/views/modules/apply/applyactivitychannelconfig'), name: 'applyActivityChannelConfig', meta: { title: '会议报名通道配置' } },
    { path: '/applyActivityConfig', component: () => import('@/views/modules/apply/applyactivityconfig'), name: 'applyActivityConfig', meta: { title: '会议报名配置' } },
    { path: '/activityConfig', component: () => import('@/views/modules/activity/config'), name: 'activityConfig', meta: { title: '会议配置' } },
    { path: '/activityuserapplyorder', component: () => import('@/views/modules/activity/activityuserapplyorder'), name: 'activityuserapplyorder', meta: { title: '报名订单' } },
    { path: '/hotelactivity', component: () => import('@/views/modules/hotel/hotelactivity'), name: 'hotelactivity', meta: { title: '会议酒店' } },
    { path: '/hotelactivityroom', component: () => import('@/views/modules/hotel/hotelactivityroom'), name: 'hotelactivityroom', meta: { title: '会议酒店房型' } },
    { path: '/hotelactivityroomnumber', component: () => import('@/views/modules/hotel/hotelactivityroomnumber'), name: 'hotelactivityroomnumber', meta: { title: '酒店房号' } },
    { path: '/hotelorder', component: () => import('@/views/modules/hotel/hotelorder'), name: 'hotelorder', meta: { title: '会议酒店订单' } },
    { path: '/placeactivity', component: () => import('@/views/modules/place/placeactivity'), name: 'placeactivity', meta: { title: '会议场地' } },
    { path: '/placeactivitytopic', component: () => import('@/views/modules/place/placeactivitytopic'), name: 'placeactivitytopic', meta: { title: '会议主题' } },
    { path: '/placeactivitytopicschedule', component: () => import('@/views/modules/place/placeactivitytopicschedule'), name: 'placeactivitytopicschedule', meta: { title: '会议日程' } },
    { path: '/activityguest', component: () => import('@/views/modules/activity/activityguest'), name: 'activityguest', meta: { title: '会议嘉宾' } },
    { path: '/activityguestservicefee', component: () => import('@/views/modules/activity/activityguestservicefee'), name: 'activityguestservicefee', meta: { title: '专家劳务费' } },
    { path: '/activitybottom', component: () => import('@/views/modules/activity/activitybottom'), name: 'activitybottom', meta: { title: '底部配置' } },
    { path: '/placelivetabconfig', component: () => import('@/views/modules/place/placelivetabconfig'), name: 'placelivetabconfig', meta: { title: '直播标签' } },
    { path: '/exam', component: () => import('@/views/modules/exam/exam'), name: 'exam', meta: { title: '考卷管理' } },
    { path: '/examquestion', component: () => import('@/views/modules/exam/examquestion'), name: 'examquestion', meta: { title: '考卷题目管理' } },
    { path: '/examactivityuser', component: () => import('@/views/modules/exam/examactivityuser'), name: 'examactivityuser', meta: { title: '考试管理' } },
    { path: '/activityviewlog', component: () => import('@/views/modules/activity/activityviewlog'), name: 'activityviewlog', meta: { title: '会议pv&uv记录' } },
    { path: '/placeactivityhourlog', component: () => import('@/views/modules/place/placeactivityhourlog'), name: 'placeactivityhourlog', meta: { title: '直播观看记录' } },
    { path: '/placeactivityvideo', component: () => import('@/views/modules/place/placeactivityvideo'), name: 'placeactivityvideo', meta: { title: '录播视频管理' } },
    { path: '/chat', component: () => import('@/views/modules/place/chat'), name: 'chat', meta: { title: '聊天室' } },
    { path: '/applyinvitecode', component: () => import('@/views/modules/apply/applyinvitecode'), name: 'applyinvitecode', meta: { title: '邀请码管理' } },
    { path: '/merchant', component: () => import('@/views/modules/merchant/merchant'), name: 'merchant', meta: { title: '展商管理' } },
    { path: '/hotelactivityroomassign', component: () => import('@/views/modules/hotel/hotelactivityroomassign'), name: 'hotelactivityroomassign', meta: { title: '分房管理' } },
    { path: '/activityrole', component: () => import('@/views/modules/activity/activityrole'), name: 'activityrole', meta: { title: '会议工作人员' } },
    { path: '/hotelassigntag', component: () => import('@/views/modules/hotel/hotelassigntag'), name: 'hotelassigntag', meta: { title: '酒店分房标签' } },
    { path: '/activitynav', component: () => import('@/views/modules/activity/activitynav'), name: 'activitynav', meta: { title: '导航管理' } },
    { path: '/activityguestplane', component: () => import('@/views/modules/activity/activityguestplane'), name: 'activityguestplane', meta: { title: '专家来程返程信息' } },
    { path: '/activityconfigupdate', component: () => import('@/views/modules/activity/activityconfigupdate'), name: 'activityconfigupdate', meta: { title: '更新配置' } },
    { path: '/supplierthing', component: () => import('@/views/modules/supplier/supplierthing'), name: 'supplierthing', meta: { title: '供应商资料' } },
    { path: '/clientthing', component: () => import('@/views/modules/client/clientthing'), name: 'clientthing', meta: { title: '客户资料' } },
    { path: '/activitysettle', component: () => import('@/views/modules/activity/activitysettle'), name: 'activitysettle', meta: { title: '项目结算表' } },
    { path: '/activitynotify', component: () => import('@/views/modules/activity/activitynotify'), name: 'activitynotify', meta: { title: '站内通知' } },
    { path: '/activitytext', component: () => import('@/views/modules/activity/activitytext'), name: 'activitytext', meta: { title: '文案管理' } },
    { path: '/adtypeconfig', component: () => import('@/views/modules/activity/adtypeconfig'), name: 'adtypeconfig', meta: { title: '广告类型配置' } },
    { path: '/activityimage', component: () => import('@/views/modules/activity/activityimage'), name: 'activityimage', meta: { title: '图片素材' } },
    { path: '/activityvideo', component: () => import('@/views/modules/activity/activityvideo'), name: 'activityvideo', meta: { title: '视频素材' } },
    { path: '/activityfinishedvideo', component: () => import('@/views/modules/activity/activityfinishedvideo'), name: 'activityfinishedvideo', meta: { title: '成品视频' } },
    { path: '/activityrechargepackage', component: () => import('@/views/modules/activity/activityrechargepackage'), name: 'activityrechargepackage', meta: { title: '充值套餐管理' } },
    { path: '/activityrechargerecord', component: () => import('@/views/modules/activity/activityrechargerecord'), name: 'activityrechargerecord', meta: { title: '充值记录管理' } },
    { path: '/create-activity-package-order', component: () => import('@/views/modules/activity/create-activity-package-order'), name: 'create-activity-package-order', meta: { title: '创建活动套餐购买' } },

    // 业务员管理相关路由
    { path: '/salesman', component: () => import('@/views/modules/salesman/salesman'), name: 'salesman', meta: { title: '业务员管理' } },
    { path: '/salesman-order', component: () => import('@/views/modules/salesman/salesman-order'), name: 'salesman-order', meta: { title: '业务员订单管理' } },
    { path: '/salesman-commission-config', component: () => import('@/views/modules/salesman/commission-config'), name: 'salesman-commission-config', meta: { title: '业务员佣金配置' } },
    { path: '/salesman-commission-record', component: () => import('@/views/modules/salesman/commission-record'), name: 'salesman-commission-record', meta: { title: '业务员佣金记录' } },
    { path: '/salesman-commission-settlement', component: () => import('@/views/modules/salesman/commission-settlement'), name: 'salesman-commission-settlement', meta: { title: '业务员佣金结算' } },

    // 业务员绑定管理相关路由
    { path: '/salesman-wx-user-binding', component: () => import('@/views/modules/salesman/wx-user-binding'), name: 'salesman-wx-user-binding', meta: { title: '微信用户业务员绑定管理' } },
    { path: '/salesman-order-association', component: () => import('@/views/modules/salesman/order-association'), name: 'salesman-order-association', meta: { title: '订单业务员关联管理' } },
    { path: '/salesman-commission-safety', component: () => import('@/views/modules/salesman/commission-safety'), name: 'salesman-commission-safety', meta: { title: '佣金安全管理' } },
    { path: '/salesman-wx-user-binding', component: () => import('@/views/modules/salesman/wx-user-binding'), name: 'salesman-wx-user-binding', meta: { title: '业务员绑定用户' } },

    // 渠道管理相关路由
    { path: '/channel', component: () => import('@/views/modules/channel/channel'), name: 'channel', meta: { title: '渠道管理' } },
    { path: '/channel-salesman', component: () => import('@/views/modules/channel/channel-salesman'), name: 'channel-salesman', meta: { title: '渠道业务员管理' } },
    { path: '/channel-customer', component: () => import('@/views/modules/channel/channel-customer'), name: 'channel-customer', meta: { title: '渠道客户管理' } },
    { path: '/channel-activity', component: () => import('@/views/modules/channel/channel-activity'), name: 'channel-activity', meta: { title: '渠道活动查看' } },
    { path: '/channel-statistics', component: () => import('@/views/modules/channel/channel-statistics'), name: 'channel-statistics', meta: { title: '渠道统计报表' } },
  
    // 团购券
    { path: '/groupbuying-coupon', component: () => import('@/views/modules/groupbuying/groupbuying-coupon'), name: 'groupbuying-coupon', meta: { title: '团购券管理' } },

  
  ],
  beforeEnter(to, from, next) {
    let token = Vue.cookie.get('token')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      next({ name: 'login' })
    }
    next()
  }
}

const router = new VueRouter({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
  routes: globalRoutes.concat(mainRoutes)
})

router.beforeEach((to, from, next) => {
  // 添加动态(菜单)路由
  // 1. 已经添加 or 全局路由, 直接访问
  // 2. 获取菜单列表, 添加并保存本地存储
  if (router.options.isAddDynamicMenuRoutes || fnCurrentRouteType(to, globalRoutes) === 'global') {
    next()
  } else {
    http({
      url: http.adornUrl('/sys/menu/nav'),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      if (data && data.code === 200) {
        fnAddDynamicMenuRoutes(data.menuList)
        router.options.isAddDynamicMenuRoutes = true
        sessionStorage.setItem('menuList', JSON.stringify(data.menuList || '[]'))
        sessionStorage.setItem('permissions', JSON.stringify(data.permissions || '[]'))
        next({ ...to, replace: true })
      } else {
        sessionStorage.setItem('menuList', '[]')
        sessionStorage.setItem('permissions', '[]')
        next()
      }
    }).catch((e) => {
      console.log(`%c${e} 请求菜单列表和权限失败，跳转至登录页！！`, 'color:blue')
      router.push({ name: 'login' })
    })
  }
})

/**
 * 判断当前路由类型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType(route, globalRoutes = []) {
  var temp = []
  for (var i = 0; i < globalRoutes.length; i++) {
    if (route.path === globalRoutes[i].path) {
      return 'global'
    } else if (globalRoutes[i].children && globalRoutes[i].children.length >= 1) {
      temp = temp.concat(globalRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteType(route, temp) : 'main'
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes(menuList = [], routes = []) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].list && menuList[i].list.length >= 1) {
      temp = temp.concat(menuList[i].list)
    } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, '')
      var route = {
        path: menuList[i].url.replace('/', '-'),
        component: null,
        name: menuList[i].url.replace('/', '-'),
        meta: {
          menuId: menuList[i].menuId,
          title: menuList[i].name,
          isDynamic: true,
          isTab: true,
          iframeUrl: ''
        }
      }
      // url以http[s]://开头, 通过iframe展示
      if (isURL(menuList[i].url)) {
        route['path'] = `i-${menuList[i].menuId}`
        route['name'] = `i-${menuList[i].menuId}`
        route['meta']['iframeUrl'] = menuList[i].url
      } else {
        try {
          route['component'] = _import(`modules/${menuList[i].url}`) || null
          // route['component'] = ()=>import(`@/views/modules/${menuList[i].url}.vue`) || null
        } catch (e) { }
      }
      routes.push(route)
    }
  }
  if (temp.length >= 1) {
    fnAddDynamicMenuRoutes(temp, routes)
  } else {
    mainRoutes.name = 'main-dynamic'
    mainRoutes.children = routes
    router.addRoutes([
      mainRoutes,
      { path: '*', redirect: { name: '404' } }
    ])
    sessionStorage.setItem('dynamicMenuRoutes', JSON.stringify(mainRoutes.children || '[]'))
    console.log('\n')
    console.log('%c!<-------------------- 动态(菜单)路由 s -------------------->', 'color:blue')
    console.log(mainRoutes.children)
    console.log('%c!<-------------------- 动态(菜单)路由 e -------------------->', 'color:blue')
  }
}
export default router
