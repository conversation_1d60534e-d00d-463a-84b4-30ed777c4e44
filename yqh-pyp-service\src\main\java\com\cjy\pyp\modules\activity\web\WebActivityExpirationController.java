package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.service.ActivityExpirationService;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.vo.ActivityExpirationStatusVo;
import com.cjy.pyp.modules.activity.vo.RenewalOrderVo;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 活动过期管理控制器（前端移动端）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/web/activity/expiration")
public class WebActivityExpirationController extends AbstractController {

    @Autowired
    private ActivityExpirationService activityExpirationService;

    @Autowired
    private ActivityRechargePackageService activityRechargePackageService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取活动过期状态
     */
    @RequestMapping("/status/{activityId}")
    public R getExpirationStatus(@PathVariable("activityId") Long activityId) {
        ActivityExpirationStatusVo status = activityExpirationService.getActivityExpirationStatus(activityId);
        if (status == null) {
            return R.error("活动不存在");
        }
        return R.ok().put("status", status);
    }

    /**
     * 获取用户所有活动的过期状态
     */
    @RequestMapping("/userActivitiesStatus")
    public R getUserActivitiesExpirationStatus(@CookieValue String appid) {
        List<ActivityExpirationStatusVo> statusList = activityExpirationService
                .getUserActivitiesExpirationStatus(getUserId(), appid);
        return R.ok().put("statusList", statusList);
    }

    /**
     * 检查活动是否过期
     */
    @RequestMapping("/checkExpired/{activityId}")
    public R checkActivityExpired(@PathVariable("activityId") Long activityId) {
        boolean isExpired = activityExpirationService.isActivityExpired(activityId);
        boolean isExpiringSoon = activityExpirationService.isActivityExpiringSoon(activityId);
        
        return R.ok()
                .put("isExpired", isExpired)
                .put("isExpiringSoon", isExpiringSoon);
    }

    /**
     * 获取续费套餐列表
     */
    @RequestMapping("/renewalPackages")
    public R getRenewalPackages(@CookieValue String appid) {
        return R.ok().put("packages", 
                activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, 3));
    }

    /**
     * 创建续费订单
     */
    @SysLog("用户创建续费订单")
    @RequestMapping("/createRenewalOrder")
    @Transactional(rollbackFor = Exception.class)
    public R createRenewalOrder(@Valid @RequestBody RenewalOrderVo renewalOrderVo, 
                               @CookieValue String appid) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), 
                renewalOrderVo.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }

        // 设置应用ID
        renewalOrderVo.setAppid(appid);
        
        R orderResult = activityExpirationService.createRenewalOrder(renewalOrderVo, getUserId());
        if (orderResult.get("code").equals(200)) {
            // 订单创建成功，可以跳转到支付页面
            return orderResult;
        }

        return orderResult;
    }

    /**
     * 处理续费支付成功回调
     */
    @SysLog("续费支付成功回调")
    @RequestMapping("/paymentSuccess")
    public R handleRenewalPaymentSuccess(@RequestParam String orderSn,
                                        @RequestParam String payTransaction,
                                        @RequestParam String payType) {
        return activityExpirationService.handleRenewalPaymentSuccess(orderSn, payTransaction, payType);
    }

    /**
     * 获取活动续费历史记录
     */
    @RequestMapping("/renewalHistory/{activityId}")
    public R getRenewalHistory(@PathVariable("activityId") Long activityId) {
        // TODO: 实现获取续费历史记录的逻辑
        return R.ok().put("renewalHistory", Collections.emptyList());
    }

    /**
     * 获取用户的过期提醒设置
     */
    @RequestMapping("/reminderSettings")
    public R getReminderSettings() {
        // TODO: 实现获取用户过期提醒设置的逻辑
        return R.ok().put("reminderSettings", Collections.emptyMap());
    }

    /**
     * 更新用户的过期提醒设置
     */
    @SysLog("更新过期提醒设置")
    @RequestMapping("/updateReminderSettings")
    public R updateReminderSettings(@RequestBody Object settings) {
        // TODO: 实现更新用户过期提醒设置的逻辑
        return R.ok();
    }
}
