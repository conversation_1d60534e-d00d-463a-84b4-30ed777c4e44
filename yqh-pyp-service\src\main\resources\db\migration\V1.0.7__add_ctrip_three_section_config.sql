-- 携程三段式配置数据库变更
-- 为tb_activity表添加携程三段式配置字段，支持团购、点评、笔记三个独立配置


-- 添加携程点评显示控制字段
ALTER TABLE `tb_activity` ADD COLUMN `show_ctrip_review` tinyint(1) DEFAULT 0 COMMENT '是否显示携程点评：0-不显示，1-显示';

-- 添加携程笔记显示控制字段
ALTER TABLE `tb_activity` ADD COLUMN `show_ctrip_notes` tinyint(1) DEFAULT 0 COMMENT '是否显示携程笔记：0-不显示，1-显示';


-- 添加携程点评配置信息字段
ALTER TABLE `tb_activity` ADD COLUMN `ctrip_review_config` text DEFAULT NULL COMMENT '携程点评配置信息，JSON格式';

-- 添加携程笔记配置信息字段
ALTER TABLE `tb_activity` ADD COLUMN `ctrip_notes_config` text DEFAULT NULL COMMENT '携程笔记配置信息，JSON格式';

-- 添加索引优化查询性能
ALTER TABLE `tb_activity` ADD INDEX `idx_show_ctrip_group_buying` (`show_ctrip_group_buying`);
ALTER TABLE `tb_activity` ADD INDEX `idx_show_ctrip_review` (`show_ctrip_review`);
ALTER TABLE `tb_activity` ADD INDEX `idx_show_ctrip_notes` (`show_ctrip_notes`);

-- 数据迁移：将现有的showCtrip和ctripConfig数据迁移到新的三段式配置
-- 如果现有活动启用了携程功能，默认迁移到点评配置（保持现有行为）
UPDATE `tb_activity` 
SET 
    `show_ctrip_review` = `show_ctrip`,
    `ctrip_review_config` = `ctrip_config`
WHERE 
    `show_ctrip` = 1 AND `ctrip_config` IS NOT NULL AND `ctrip_config` != '';

-- 为携程笔记添加广告类型配置
INSERT INTO `ad_type_config` (`type_code`, `type_name`, `platform`, `content_type`, `title_length`, `content_length`, `topics_count`, `topics_format`, `requirements`, `style`, `prompt_template`, `sort_order`, `status`) VALUES
('ctrip_notes', '携程笔记', 'ctrip', 'notes', 50, 500, 3, '#话题#', '适合携程平台的种草笔记内容，突出旅游体验和推荐', 'casual', '请为携程平台生成一篇关于{keyword}的种草笔记，内容要生动有趣，突出旅游体验和实用信息，字数控制在{content_length}字以内，标题控制在{title_length}字以内，包含{topics_count}个相关话题标签。', 40, 1);

-- 为携程团购添加广告类型配置
INSERT INTO `ad_type_config` (`type_code`, `type_name`, `platform`, `content_type`, `title_length`, `content_length`, `topics_count`, `topics_format`, `requirements`, `style`, `prompt_template`, `sort_order`, `status`) VALUES
('ctrip_group_buying', '携程团购', 'ctrip', 'promotion', 30, 200, 2, '#优惠#', '适合携程团购的促销文案，突出优惠和性价比', 'promotional', '请为携程团购生成关于{keyword}的促销文案，突出优惠力度和性价比，文案要简洁有力，字数控制在{content_length}字以内，标题控制在{title_length}字以内，包含{topics_count}个相关优惠话题。', 41, 1);
