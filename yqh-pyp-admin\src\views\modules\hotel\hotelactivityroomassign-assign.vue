<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" @closed="closeDialog"
      :visible.sync="visible">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-select size="mini" v-model="dataForm.hotelActivityId" @change="hotelChange">
            <el-option label="全部(酒店)" value=""></el-option>
            <el-option v-for="item in hotels" :key="item.id" :label="item.hotelName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select size="mini" v-model="dataForm.hotelActivityRoomId">
            <el-option label="全部(房型)" value=""></el-option>
            <el-option v-for="item in rooms" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input size="mini" v-model="dataForm.orderSn" placeholder="订单编号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input size="mini" v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input size="mini" v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" @click="onSearch()">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table height="400" size="mini" :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
        <el-table-column show-overflow-tooltip prop="orderSn" header-align="center" align="center" label="订单号">
        </el-table-column>
        <el-table-column prop="orderStatus" header-align="center" align="center" width="75" label="订单状态">
          <div slot-scope="scope">
            <el-tag type="primary"
              :class="'tag-color-mini tag-color-' + (scope.row.orderStatus)">{{ orderStatus[scope.row.orderStatus].value }}</el-tag>
          </div>
        </el-table-column>
        <el-table-column prop="roomType" header-align="center" align="center" width="75" label="房间类型">
          <div slot-scope="scope">
            <el-tag type="primary" :class="'tag-color-mini tag-color-' + (scope.row.roomType)">{{
              roomType[scope.row.roomType].value }}</el-tag>
          </div>
        </el-table-column>
        <el-table-column prop="address" header-align="center" align="center" width="60" label="已分/总">
          <div slot-scope="scope">
            {{ scope.row.assignNumber }}/{{ scope.row.number }}
          </div>
        </el-table-column>
        <el-table-column prop="contact" header-align="center" align="center" width="130" label="联系人">
          <div slot-scope="scope">
            <el-input size="mini" width="100%" :disabled="scope.row.assignNumber == scope.row.number"
              v-model="scope.row.contact" placeholder="输入联系人" />
          </div>
        </el-table-column>
        <el-table-column prop="mobile" header-align="center" align="center" width="130" label="联系方式">
          <div slot-scope="scope">
            <el-input size="mini" width="100%" :disabled="scope.row.assignNumber == scope.row.number"
              v-model="scope.row.mobile" placeholder="输入联系方式" />
          </div>
        </el-table-column>
        <el-table-column prop="checkSex" header-align="center" align="center" width="70" label="校验男女">
          <div slot-scope="scope">
            <el-switch v-if="scope.row.roomType != 0" :disabled="scope.row.assignNumber == scope.row.number"
              v-model="scope.row.checkSex">
            </el-switch>
          </div>
        </el-table-column>
        <el-table-column prop="tag" header-align="center" align="center" width="130" label="备注(选填)">
          <div slot-scope="scope">
            <el-select filterable size="mini" v-model="scope.row.tag">
              <el-option v-for="item in assignTag" :key="item.id" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </div>
        </el-table-column>
        <el-table-column fixed="right" header-align="center" align="center" width="80" label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.assignNumber != scope.row.number" size="small"
              @click="selectNumber(scope.row)">选择房号</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭页面</el-button>
      </span>

    </el-dialog>
    <el-dialog title="选择房间号" :close-on-click-modal="false" :visible.sync="numberVisible">
      <el-form :inline="true" :model="indexOrder" label-width="80px">
        <el-form-item label="房间号" prop="number">
          <div style="display: flex;align-items: center;">
            <tags-editor :limit="indexOrder.number - indexOrder.assignNumber" v-model="indexOrder.roomNumber"
              :name="'直接新增房间号'"></tags-editor>
            <el-button style="margin-left: 10px;" v-show="(indexOrder.number - indexOrder.assignNumber) > indexOrder.roomNumber.length" type="primary" size="small"
              @click="addRoomNumber">选择已有房间号</el-button>
          </div>
        </el-form-item>
        <!-- <el-form-item>
        <el-input size="mini" v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="mini" v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="numberVisible = false">取消</el-button>
        <el-button type="primary" @click="select">确认</el-button>
        <el-button type="success" @click="selectAndPrint">确认并打印</el-button>
      </span>
    </el-dialog>
    <hotelactivityroomassignselectrommnumber v-if="hotelactivityroomassignselectrommnumberVisible"
      ref="hotelactivityroomassignselectrommnumber" @select="selectRoomNumber">
    </hotelactivityroomassignselectrommnumber>
  </div>
</template>

<script>
import hotelactivityroomassignselectrommnumber from './hotelactivityroomassign-selectrommnumber.vue'
import { orderStatus } from '@/data/common'
import { roomAssignStatus, roomType, roomTypeFjsd } from "@/data/room.js";
export default {
  data() {
    return {
      hotelactivityroomassignselectrommnumberVisible: false,
      appid: '',
      hotels: [],
      rooms: [],
      roomType,
      roomTypeFjsd,
      roomAssignStatus,
      orderStatus,
      visible: false,
      numberVisible: false,
      dataForm: {
        contact: '',
        mobile: '',
        hotelActivityId: '',
        hotelActivityRoomId: '',
        numberId: '',
        activityId: '',
      },
      assignTag: [],
      dataList: [],
      dataListLoading: false,
      indexOrder: {
        orderDetailId: '',
        contact: '',
        mobile: '',
        roomNumber: [],
      },
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
    }
  },
  components: {
    tagsEditor: () => import("@/components/tags-editorlist"),
    hotelactivityroomassignselectrommnumber,
  },
  methods: {
    init(activityId, hotelActivityId, hotelActivityRoomId) {
      this.dataForm.activityId = activityId
      this.dataForm.hotelActivityId = hotelActivityId
      this.dataForm.hotelActivityRoomId = hotelActivityRoomId
      if (this.dataForm.hotelActivityId) {
        this.findRoom(this.dataForm.hotelActivityId);
      }
      this.visible = true
      this.getDataList();
      this.findHotel()
      this.getTag()
      this.appid = this.$cookie.get("appid");
    },
    getTag() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelassigntag/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.assignTag = data.result
        }
      })
    },
    findHotel() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivity/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.hotels = data.result
        }
      })
    },
    hotelChange(v) {
      this.dataForm.hotelActivityRoomId = '';
      this.findRoom(v);
    },
    findRoom(v) {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroom/findByHotelActivityId/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.rooms = data.result
        }
      })
    },
    onSearch() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelorderdetail/findByParams`),
        method: 'get',
        params: this.$http.adornParams({
          'activityId': this.dataForm.activityId,
          'roomId': this.dataForm.hotelActivityRoomId,
          'hotelActivityId': this.dataForm.hotelActivityId,
          'orderSn': this.dataForm.orderSn,
          'contact': this.dataForm.contact,
          'mobile': this.dataForm.mobile,
          'page': this.pageIndex,
          'limit': this.pageSize,
        })
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
      })
    },
    addRoomNumber() {
      this.hotelactivityroomassignselectrommnumberVisible = true
      this.$nextTick(() => {
        this.$refs.hotelactivityroomassignselectrommnumber.init(this.dataForm.activityId, this.indexOrder.hotelActivityId, this.indexOrder.hotelActivityRoomId)
      })
    },
    selectRoomNumber(v) {
      // 把房间号塞到数组里面
      this.indexOrder.roomNumber.push(v)
    },
    selectNumber(v) {
      this.indexOrder = JSON.parse(JSON.stringify(v));
      this.$set(this.indexOrder, "roomNumber", []);
      this.numberVisible = true;
    },
    // 选择酒店
    select() {
      if (!this.indexOrder.roomNumber) {
        this.$message.error("请选择房间号");
        return false;
      }
      this.$confirm(`确认分房?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/assignBatch`),
          method: 'post',
          data: this.$http.adornData({
            'roomNumber': this.indexOrder.roomNumber,
            'orderDetailId': this.indexOrder.id,
            'mobile': this.indexOrder.mobile,
            'contact': this.indexOrder.contact,
            'hotelActivityRoomId': this.indexOrder.hotelActivityRoomId,
            'checkSex': this.indexOrder.checkSex,
            'tag': this.indexOrder.tag,
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.numberVisible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    selectAndPrint() {
      if (!this.indexOrder.roomNumber) {
        this.$message.error("请选择房间号");
        return false;
      }
      this.$confirm(`确认分房?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/assignBatch`),
          method: 'post',
          data: this.$http.adornData({
            'roomNumber': this.indexOrder.roomNumber,
            'orderDetailId': this.indexOrder.id,
            'mobile': this.indexOrder.mobile,
            'contact': this.indexOrder.contact,
            'hotelActivityRoomId': this.indexOrder.hotelActivityRoomId,
            'checkSex': this.indexOrder.checkSex,
            'tag': this.indexOrder.tag,
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.numberVisible = false
                this.$emit('print',data.result)
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    closeDialog() {
      this.$emit('refreshDataList')
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    }
  }
}
</script>
