package com.cjy.pyp.modules.groupbuying.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingCouponEntity;
import com.cjy.pyp.modules.groupbuying.service.GroupBuyingCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 团购券管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("groupbuying/coupon")
@Api(tags = "团购券管理")
public class GroupBuyingCouponController {
    @Autowired
    private GroupBuyingCouponService groupBuyingCouponService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("groupbuying:coupon:list")
    @ApiOperation(value = "团购券列表", notes = "")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = groupBuyingCouponService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("groupbuying:coupon:info")
    @ApiOperation(value = "团购券信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        GroupBuyingCouponEntity coupon = groupBuyingCouponService.getById(id);
        return R.ok().put("coupon", coupon);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("groupbuying:coupon:save")
    @SysLog("保存团购券")
    @ApiOperation(value = "保存团购券", notes = "")
    public R save(@RequestBody GroupBuyingCouponEntity coupon) {
        ValidatorUtils.validateEntity(coupon);
        groupBuyingCouponService.save(coupon);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("groupbuying:coupon:update")
    @SysLog("修改团购券")
    @ApiOperation(value = "修改团购券", notes = "")
    public R update(@RequestBody GroupBuyingCouponEntity coupon) {
        ValidatorUtils.validateEntity(coupon);
        groupBuyingCouponService.updateById(coupon);
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("groupbuying:coupon:delete")
    @SysLog("删除团购券")
    @ApiOperation(value = "删除团购券", notes = "")
    public R delete(@RequestBody Long[] ids) {
        groupBuyingCouponService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 批量上架/下架
     */
    @RequestMapping("/updateStatus")
    @RequiresPermissions("groupbuying:coupon:update")
    @SysLog("批量更新团购券状态")
    @ApiOperation(value = "批量更新团购券状态", notes = "")
    public R updateStatus(@RequestBody Map<String, Object> params) {
        Long[] ids = (Long[]) params.get("ids");
        Integer status = (Integer) params.get("status");
        
        for (Long id : ids) {
            GroupBuyingCouponEntity coupon = new GroupBuyingCouponEntity();
            coupon.setId(id);
            coupon.setStatus(status);
            groupBuyingCouponService.updateById(coupon);
        }
        
        return R.ok();
    }
}
