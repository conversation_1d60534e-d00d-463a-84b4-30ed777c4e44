package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务员实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@Data
@TableName("salesman")
@Accessors(chain = true)
public class SalesmanEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务员姓名
     */
    private String name;

    /**
     * 业务员手机号
     */
    private String mobile;

    /**
     * 业务员邮箱
     */
    private String email;

    /**
     * 业务员编号
     */
    private String code;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 标签
     */
    private String tags;

    /**
     * 上级业务员ID
     */
    private Long parentId;

    /**
     * 层级级别
     */
    private Integer level;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 所属渠道ID
     */
    private Long channelId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    /**
     * 防重令牌
     */
    @TableField(exist = false)
    private String repeatToken;

    /**
     * 总订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer totalOrders;

    /**
     * 活动订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer activityOrders;

    /**
     * 充值订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer rechargeOrders;

    /**
     * 销售总额（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;
    @TableField(exist = false)
    private BigDecimal totalPayAmount;

    /**
     * 佣金总额（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal totalCommission;

    /**
     * 上级业务员姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 下级业务员数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer childrenCount;

    /**
     * 渠道名称（非数据库字段）
     */
    @TableField(exist = false)
    private String channelName;
}
