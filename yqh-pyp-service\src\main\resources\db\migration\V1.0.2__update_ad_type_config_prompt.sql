-- 更新广告类型配置的Prompt模板，解决话题标签混入content的问题

-- 更新抖音配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含任何#号或话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'douyin';

-- 更新小红书配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含话题标签）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}（每个话题前加#号，用空格分隔）

风格特点：{style}'
WHERE `type_code` = 'xiaohongshu';

-- 更新快手配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含任何#号或话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'kuaishou';

-- 更新大众点评配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含话题标签）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'dianping';

-- 更新美团点评配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含话题标签）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'meituan';

-- 更新微信朋友圈配置
UPDATE `ad_type_config` SET 
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含话题标签）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'weixin';

-- 更新抖音点评配置
UPDATE `ad_type_config` SET
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含任何#号或话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'douyin_review';

-- 更新通用文案配置
UPDATE `ad_type_config` SET
`prompt_template` = '请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}，注意：内容中不要包含话题标签）
- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）

重要提醒：
- content字段中不要包含话题标签
- 所有话题标签都应该单独放在topics字段中
- topics格式：{topics_format}

风格特点：{style}'
WHERE `type_code` = 'general';
