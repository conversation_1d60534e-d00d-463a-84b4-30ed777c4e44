package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户业务员绑定变更记录实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Data
@TableName("wx_user_salesman_binding_log")
@Accessors(chain = true)
public class WxUserSalesmanBindingLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 微信用户ID
     */
    private Long wxUserId;

    /**
     * 原业务员ID
     */
    private Long oldSalesmanId;

    /**
     * 新业务员ID
     */
    private Long newSalesmanId;

    /**
     * 操作类型：1-新增绑定，2-更换业务员，3-解除绑定，4-系统自动解绑
     */
    private Integer operationType;

    /**
     * 操作原因
     */
    private String operationReason;

    /**
     * 绑定来源
     */
    private String bindingSource;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人类型：1-客户自己，2-业务员，3-管理员，4-系统
     */
    private Integer operatorType;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    // 关联查询字段
    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 客户手机号
     */
    @TableField(exist = false)
    private String customerMobile;

    /**
     * 原业务员姓名
     */
    @TableField(exist = false)
    private String oldSalesmanName;

    /**
     * 新业务员姓名
     */
    @TableField(exist = false)
    private String newSalesmanName;

    /**
     * 操作类型描述
     */
    @TableField(exist = false)
    private String operationTypeDesc;

    /**
     * 操作人姓名
     */
    @TableField(exist = false)
    private String operatorName;

    /**
     * 操作人类型描述
     */
    @TableField(exist = false)
    private String operatorTypeDesc;
}
