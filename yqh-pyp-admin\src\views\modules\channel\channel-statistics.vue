<template>
  <div class="mod-channel-statistics">
    <!-- 总体统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.channelCount || 0 }}</div>
            <div class="stats-label">渠道数量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.totalSalesmanCount || 0 }}</div>
            <div class="stats-label">业务员总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ overview.totalOrders || 0 }}</div>
            <div class="stats-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (overview.totalAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">总销售额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计选项卡 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="业务员统计" name="salesman">
        <el-form :inline="true" :model="salesmanForm">
          <el-form-item>
            <el-date-picker
              v-model="salesmanDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getSalesmanStats()">查询</el-button>
          </el-form-item>
        </el-form>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card>
              <div slot="header">业务员概况</div>
              <p>总业务员: {{ salesmanStats.totalSalesman || 0 }}</p>
              <p>活跃业务员: {{ salesmanStats.activeSalesman || 0 }}</p>
              <p>新增业务员: {{ salesmanStats.newSalesman || 0 }}</p>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card>
              <div slot="header">业绩排行</div>
              <el-table :data="salesmanStats.topPerformers || []" size="small">
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="orders" label="订单数"></el-table-column>
                <el-table-column prop="amount" label="销售额">
                  <template slot-scope="scope">
                    ¥{{ (scope.row.amount || 0).toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="订单统计" name="orders">
        <el-form :inline="true" :model="orderForm">
          <el-form-item>
            <el-date-picker
              v-model="orderDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select v-model="orderForm.orderType" placeholder="订单类型" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="活动订单" value="1"></el-option>
              <el-option label="充值订单" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getOrderStats()">查询</el-button>
          </el-form-item>
        </el-form>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">订单概况</div>
              <p>总订单数: {{ orderStats.totalOrders || 0 }}</p>
              <p>活动订单: {{ orderStats.activityOrders || 0 }}</p>
              <p>充值订单: {{ orderStats.rechargeOrders || 0 }}</p>
              <p>订单总额: ¥{{ (orderStats.totalAmount || 0).toFixed(2) }}</p>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <div slot="header">订单状态</div>
              <p>已完成: {{ orderStats.completedOrders || 0 }}</p>
              <p>进行中: {{ orderStats.processingOrders || 0 }}</p>
              <p>已取消: {{ orderStats.cancelledOrders || 0 }}</p>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="佣金统计" name="commission">
        <el-form :inline="true" :model="commissionForm">
          <el-form-item>
            <el-date-picker
              v-model="commissionDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getCommissionStats()">查询</el-button>
          </el-form-item>
        </el-form>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">佣金概况</div>
              <p>总佣金: ¥{{ (commissionStats.totalCommission || 0).toFixed(2) }}</p>
              <p>已发放: ¥{{ (commissionStats.paidCommission || 0).toFixed(2) }}</p>
              <p>待发放: ¥{{ (commissionStats.pendingCommission || 0).toFixed(2) }}</p>
              <p>佣金比例: {{ (commissionStats.commissionRate || 0).toFixed(2) }}%</p>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <div slot="header">佣金趋势</div>
              <!-- 这里可以添加图表组件 -->
              <p>本月佣金: ¥{{ (commissionStats.monthlyCommission || 0).toFixed(2) }}</p>
              <p>上月佣金: ¥{{ (commissionStats.lastMonthCommission || 0).toFixed(2) }}</p>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="趋势分析" name="trend">
        <el-form :inline="true" :model="trendForm">
          <el-form-item>
            <el-select v-model="trendForm.period" placeholder="统计周期">
              <el-option label="最近7天" value="7days"></el-option>
              <el-option label="最近30天" value="30days"></el-option>
              <el-option label="最近3个月" value="3months"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getTrendStats()">查询</el-button>
          </el-form-item>
        </el-form>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card>
              <div slot="header">趋势分析</div>
              <p>增长率: {{ (trendStats.growthRate || 0).toFixed(2) }}%</p>
              <!-- 这里可以添加趋势图表 -->
              <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
                趋势图表区域（可集成 ECharts 等图表库）
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'salesman',
      overview: {},
      
      // 业务员统计
      salesmanForm: {},
      salesmanDateRange: [],
      salesmanStats: {},
      
      // 订单统计
      orderForm: {
        orderType: ''
      },
      orderDateRange: [],
      orderStats: {},
      
      // 佣金统计
      commissionForm: {},
      commissionDateRange: [],
      commissionStats: {},
      
      // 趋势分析
      trendForm: {
        period: '30days'
      },
      trendStats: {}
    }
  },
  activated() {
    this.getOverview()
    this.getSalesmanStats()
  },
  methods: {
    // 获取总体统计
    getOverview() {
      this.$http({
        url: this.$http.adornUrl('/channel/statistics/overview'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.overview = data.overview || {}
        }
      })
    },
    
    // 标签页切换
    handleTabClick(tab) {
      switch (tab.name) {
        case 'salesman':
          this.getSalesmanStats()
          break
        case 'orders':
          this.getOrderStats()
          break
        case 'commission':
          this.getCommissionStats()
          break
        case 'trend':
          this.getTrendStats()
          break
      }
    },
    
    // 获取业务员统计
    getSalesmanStats() {
      let params = {}
      if (this.salesmanDateRange && this.salesmanDateRange.length === 2) {
        params.startDate = this.salesmanDateRange[0]
        params.endDate = this.salesmanDateRange[1]
      }
      
      this.$http({
        url: this.$http.adornUrl('/channel/statistics/salesman'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.salesmanStats = data.stats || {}
        }
      })
    },
    
    // 获取订单统计
    getOrderStats() {
      let params = {
        orderType: this.orderForm.orderType
      }
      if (this.orderDateRange && this.orderDateRange.length === 2) {
        params.startDate = this.orderDateRange[0]
        params.endDate = this.orderDateRange[1]
      }
      
      this.$http({
        url: this.$http.adornUrl('/channel/statistics/orders'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.orderStats = data.stats || {}
        }
      })
    },
    
    // 获取佣金统计
    getCommissionStats() {
      let params = {}
      if (this.commissionDateRange && this.commissionDateRange.length === 2) {
        params.startDate = this.commissionDateRange[0]
        params.endDate = this.commissionDateRange[1]
      }
      
      this.$http({
        url: this.$http.adornUrl('/channel/statistics/commission'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.commissionStats = data.stats || {}
        }
      })
    },
    
    // 获取趋势统计
    getTrendStats() {
      let params = {
        period: this.trendForm.period
      }
      
      this.$http({
        url: this.$http.adornUrl('/channel/statistics/trend'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.trendStats = data.stats || {}
        }
      })
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}
.stats-item {
  padding: 20px;
}
.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}
.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}
</style>
