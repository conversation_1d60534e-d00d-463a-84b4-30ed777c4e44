<template>
  <div class="scan-qrcode-page">
    <!-- 头部 -->
    <div class="header">
      <van-nav-bar
        title="扫码绑定业务员"
        left-text="返回"
        left-arrow
        @click-left="$router.go(-1)"
      />
    </div>

    <!-- 扫码区域 -->
    <div class="scan-container">
      <div class="scan-area">
        <div class="scan-box">
          <div class="scan-line"></div>
        </div>
        <div class="scan-tips">
          <p>将二维码放入框内，即可自动扫描</p>
        </div>
      </div>

      <!-- 手动输入 -->
      <div class="manual-input">
        <van-button
          type="primary"
          size="large"
          @click="showManualInput"
          block
        >
          手动输入邀请码
        </van-button>
      </div>

      <!-- 使用说明 -->
      <div class="instructions">
        <div class="instruction-title">使用说明</div>
        <div class="instruction-list">
          <div class="instruction-item">
            <van-icon name="info-o" />
            <span>请向您的专属业务员获取二维码或邀请码</span>
          </div>
          <div class="instruction-item">
            <van-icon name="info-o" />
            <span>绑定后可享受一对一专业服务</span>
          </div>
          <div class="instruction-item">
            <van-icon name="info-o" />
            <span>如有问题请联系客服：400-123-4567</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 手动输入弹窗 -->
    <van-dialog
      v-model="manualInputVisible"
      title="输入邀请码"
      show-cancel-button
      @confirm="bindByInviteCode"
    >
      <div class="manual-input-content">
        <van-field
          v-model="inviteCode"
          placeholder="请输入6-8位邀请码"
          maxlength="8"
          clearable
          center
        />
        <div class="input-tips">
          <p>邀请码由业务员提供，通常为6-8位字母数字组合</p>
        </div>
      </div>
    </van-dialog>

    <!-- 绑定结果弹窗 -->
    <van-dialog
      v-model="resultDialogVisible"
      :title="bindResult.success ? '绑定成功' : '绑定失败'"
      :show-cancel-button="false"
      confirm-button-text="确定"
      @confirm="handleResultConfirm"
    >
      <div class="result-content">
        <div class="result-icon">
          <van-icon
            :name="bindResult.success ? 'success' : 'fail'"
            :color="bindResult.success ? '#07c160' : '#ee0a24'"
            size="48"
          />
        </div>
        <div class="result-message">
          <p>{{ bindResult.message }}</p>
          <div v-if="bindResult.success && bindResult.salesmanInfo" class="salesman-info">
            <p><strong>业务员：</strong>{{ bindResult.salesmanInfo.name }}</p>
            <p><strong>编号：</strong>{{ bindResult.salesmanInfo.code }}</p>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'ScanQrcode',
  data() {
    return {
      manualInputVisible: false,
      inviteCode: '',
      resultDialogVisible: false,
      bindResult: {
        success: false,
        message: '',
        salesmanInfo: null
      },
      scannerInitialized: false
    }
  },
  mounted() {
    this.initScanner()
  },
  beforeDestroy() {
    this.destroyScanner()
  },
  methods: {
    // 初始化扫码器
    initScanner() {
      // 检查是否在微信环境
      if (this.isWechat()) {
        this.initWechatScanner()
      } else {
        this.initH5Scanner()
      }
    },

    // 检查是否在微信环境
    isWechat() {
      return /micromessenger/i.test(navigator.userAgent)
    },

    // 初始化微信扫码
    initWechatScanner() {
      if (window.wx) {
        // 微信JS-SDK扫码
        this.startWechatScan()
      } else {
        // 加载微信JS-SDK
        this.loadWechatSDK().then(() => {
          this.startWechatScan()
        })
      }
    },

    // 启动微信扫码
    startWechatScan() {
      window.wx.scanQRCode({
        needResult: 1,
        scanType: ["qrCode"],
        success: (res) => {
          this.handleScanResult(res.resultStr)
        },
        fail: () => {
          this.$toast('扫码失败，请重试')
        }
      })
    },

    // 初始化H5扫码器
    initH5Scanner() {
      // 这里可以集成第三方扫码库，如 qr-scanner
      // 由于需要摄像头权限，实际项目中需要处理权限申请
      this.$toast('请在微信中打开或使用手动输入功能')
    },

    // 加载微信SDK
    loadWechatSDK() {
      return new Promise((resolve, reject) => {
        if (window.wx) {
          resolve()
          return
        }

        const script = document.createElement('script')
        script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
        script.onload = () => {
          // 配置微信SDK
          this.configWechatSDK().then(resolve).catch(reject)
        }
        script.onerror = reject
        document.head.appendChild(script)
      })
    },

    // 配置微信SDK
    async configWechatSDK() {
      try {
        // 获取微信配置
        const response = await this.$fly.get('/pyp/web/wechat/config', {
          url: window.location.href
        })
        
        if (response.code ===200) {
          const config = response.config
          window.wx.config({
            debug: false,
            appId: config.appId,
            timestamp: config.timestamp,
            nonceStr: config.nonceStr,
            signature: config.signature,
            jsApiList: ['scanQRCode']
          })
          
          return new Promise((resolve, reject) => {
            window.wx.ready(resolve)
            window.wx.error(reject)
          })
        }
      } catch (error) {
        throw new Error('微信配置失败')
      }
    },

    // 处理扫码结果
    handleScanResult(result) {
      if (result) {
        this.bindByQrCode(result)
      }
    },

    // 通过二维码绑定
    async bindByQrCode(qrCodeContent) {
      try {
        const response = await this.$fly.post('/pyp/web/salesman/binding/bindByQrCode', {
          qrCodeContent
        })
        
        if (response.code ===200) {
          this.showBindResult(true, '绑定成功！', response.binding)
        } else {
          this.showBindResult(false, response.msg || '绑定失败')
        }
      } catch (error) {
        this.showBindResult(false, '网络错误，请重试')
      }
    },

    // 显示手动输入
    showManualInput() {
      this.inviteCode = ''
      this.manualInputVisible = true
    },

    // 通过邀请码绑定
    async bindByInviteCode() {
      if (!this.inviteCode.trim()) {
        this.$toast('请输入邀请码')
        return
      }

      try {
        const response = await this.$fly.post('/pyp/web/salesman/binding/bindByInviteCode', {
          inviteCode: this.inviteCode.trim()
        })
        
        this.manualInputVisible = false
        
        if (response.code ===200) {
          this.showBindResult(true, '绑定成功！', response.binding)
        } else {
          this.showBindResult(false, response.msg || '绑定失败')
        }
      } catch (error) {
        this.manualInputVisible = false
        this.showBindResult(false, '网络错误，请重试')
      }
    },

    // 显示绑定结果
    showBindResult(success, message, bindingInfo = null) {
      this.bindResult = {
        success,
        message,
        salesmanInfo: bindingInfo ? {
          name: bindingInfo.salesmanName,
          code: bindingInfo.salesmanCode
        } : null
      }
      this.resultDialogVisible = true
    },

    // 处理结果确认
    handleResultConfirm() {
      this.resultDialogVisible = false
      if (this.bindResult.success) {
        // 绑定成功，跳转到我的业务员页面
        this.$router.replace('/salesman/my-salesman')
      } else {
        // 绑定失败，重新扫码
        this.initScanner()
      }
    },

    // 销毁扫码器
    destroyScanner() {
      // 清理扫码器资源
      this.scannerInitialized = false
    }
  }
}
</script>

<style scoped>
.scan-qrcode-page {
  min-height: 100vh;
  background-color: #000;
  color: white;
}

.scan-container {
  padding: 20px;
  height: calc(100vh - 46px);
  display: flex;
  flex-direction: column;
}

.scan-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.scan-box {
  width: 250px;
  height: 250px;
  border: 2px solid #1989fa;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.scan-box::before,
.scan-box::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #1989fa;
}

.scan-box::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scan-box::after {
  top: -3px;
  right: -3px;
  border-left: none;
  border-bottom: none;
}

.scan-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1989fa, transparent);
  position: absolute;
  top: 50%;
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% {
    transform: translateY(-125px);
  }
  100% {
    transform: translateY(125px);
  }
}

.scan-tips {
  margin-top: 30px;
  text-align: center;
}

.scan-tips p {
  margin: 0;
  font-size: 16px;
  color: #ccc;
}

.manual-input {
  margin: 30px 0;
}

.instructions {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.instruction-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #fff;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  color: #ccc;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-item .van-icon {
  margin-right: 8px;
  margin-top: 2px;
  color: #1989fa;
}

.manual-input-content {
  padding: 16px;
}

.input-tips {
  margin-top: 12px;
  text-align: center;
}

.input-tips p {
  margin: 0;
  font-size: 12px;
  color: #969799;
}

.result-content {
  padding: 20px;
  text-align: center;
}

.result-icon {
  margin-bottom: 16px;
}

.result-message p {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.salesman-info {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.salesman-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #646566;
  text-align: left;
}
</style>
