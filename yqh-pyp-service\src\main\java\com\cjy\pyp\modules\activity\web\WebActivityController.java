package com.cjy.pyp.modules.activity.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.sys.service.SysUserRoleService;
import com.cjy.pyp.modules.sys.service.SysUserService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.wx.service.WxUserService;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-12-09 17:35:45
 */
@RestController
@RequestMapping("web/activity")
public class WebActivityController extends AbstractController {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private WxUserService wxUserService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserRoleService sysUserRoleService;


    @RequestMapping("/findWechatQrCode/{activityId}")
    public R findWechatQrCode(@PathVariable("activityId") Long activityId){
        ActivityEntity activity = activityService.getOne(
            new QueryWrapper<ActivityEntity>().select("wechat_qr_code").eq("id",activityId));

        return R.ok().put("result", activity.getWechatQrCode());
    }


    @RequestMapping("/update")
    public R update(@RequestBody ActivityEntity activity,@CookieValue String appid) {
        activityService.updateById(activity);
        return R.ok();
    }
    /**
     * 获取用户活动列表
     * @param appid
     * @return
     */
    @GetMapping("/userActivities")
    public R userActivities(@CookieValue String appid) {
            // 获取当前登录用户
            WxUser currentUser = wxUserService.getById(getUserId());
            // 找到用户权限，看是否是超管
            Boolean haveRole = false;
            if(currentUser != null && StringUtils.isNotEmpty(currentUser.getMobile())) {
                SysUserEntity sysUser = sysUserService.findByMobile(currentUser.getMobile());
                if (null != sysUser) {
                     List<Long> queryRoleIdList = sysUserRoleService.queryRoleIdList(sysUser.getUserId());
                     if (sysUser.getUserId().equals(1L) || queryRoleIdList.contains(1L)) {
                        haveRole = true;
                    }
                }
            }

            if (getUserId().equals(1L) || haveRole) {
                // 获取所有活动
                List<ActivityEntity> activities = activityService.findByAppid(appid);
                return R.ok().put("data", activities);
            } else {
            if (currentUser == null || currentUser.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 根据手机号查询用户的活动
            List<ActivityEntity> activities = activityService.findByUserMobile(currentUser.getMobile(), appid);

            return R.ok().put("data", activities);
            }
    }

    /**
     * 当前列表
     * @param params
     * @return
     */
    @RequestMapping("/activity/list")
    public R list(@RequestParam Map<String, Object> params,@CookieValue String appid) {
        params.put("isHistory",0);
        params.put("appid",appid);
        PageUtils page = activityService.queryWebPage(params);

        return R.ok().put("page", page);
    }
    /**
     * 当前列表
     * @param params
     * @return
     */
    @RequestMapping("/activity/historyList")
    public R historyList(@RequestParam Map<String, Object> params,@CookieValue String appid) {
        params.put("isHistory",1);
        params.put("appid",appid);
        PageUtils page = activityService.queryWebPage(params);

        return R.ok().put("page", page);
    }

}
