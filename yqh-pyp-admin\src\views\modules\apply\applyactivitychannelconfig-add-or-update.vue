<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="父通道" prop="pid">
        <el-select v-model="dataForm.pid" placeholder="父通道" filterable>
          <el-option label="无" value=""></el-option>
          <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="通道名称"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input v-model="dataForm.price" placeholder="价格"></el-input>
      </el-form-item>
      <el-form-item label="最大报名人数" prop="maxNumber">
        <el-input v-model="dataForm.maxNumber" placeholder="最大报名人数"></el-input>
      </el-form-item>
      <el-form-item label="截止日期" prop="deadline">
        <el-date-picker v-model="dataForm.deadline" style="windth: 100%" type="datetime"
          value-format="yyyy/MM/dd HH:mm:ss" placeholder="截止日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="邀请码" prop="isNeedInvite">
        <el-select v-model="dataForm.isNeedInvite">
          <el-option v-for="item in needInvite" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.isNeedInvite == 1" label="统一邀请码" prop="inviteCode">
        <el-input v-model="dataForm.inviteCode" placeholder="统一邀请码"></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.isNeedInvite == 3" label="统一邀请码(多个)" prop="inviteCode">
        <tags-editor :limit="10" v-model="dataForm.inviteCode"></tags-editor>
      </el-form-item>
      <el-form-item label="是否展示" prop="isShow">
        <el-select v-model="dataForm.isShow">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否短信验证" prop="isSms">
        <el-select v-model="dataForm.isSms">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="appid != 'wx0770d56458b33c67'" label="是否收集发票" prop="isInvoice">
        <el-select v-model="dataForm.isInvoice">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item  label="是否审核" prop="isVerify">
        <el-select v-model="dataForm.isVerify">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.isVerify" label="审核字段名称" prop="verifyName">
        <el-input v-model="dataForm.verifyName" placeholder="审核字段名称"></el-input>
      </el-form-item>
      <el-form-item label="学时(直播)" prop="hour">
        <el-input v-model="dataForm.hour" placeholder="学时(直播)">
          <template slot="append">
            分钟
          </template></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" :max="100" label="排序"></el-input-number>
      </el-form-item>
      <el-form-item v-if="dataForm.price > 0" label="微信支付" prop="isWechatPay">
        <el-select v-model="dataForm.isWechatPay">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.price > 0" label="支付宝支付" prop="isAliPay">
        <el-select v-model="dataForm.isAliPay">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.price > 0" label="银行转账" prop="isBankTransfer">
        <el-select v-model="dataForm.isBankTransfer">
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.isBankTransfer == 1" label="银行转账信息" prop="bankTransferNotify">
        <tinymce-editor ref="editor" v-model="dataForm.bankTransferNotify"></tinymce-editor>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { needInvite } from '@/data/activity.js'
import { yesOrNo } from "@/data/common.js"
export default {
  components: {
    TinymceEditor: () =>
      import("@/components/tinymce-editor"),
      tagsEditor: () => import("@/components/tags-editor"),
  },
  data() {
    return {
      channelList: [],
      appid: '',
      visible: false,
      yesOrNo,
      dataForm: {
        id: 0,
        activityId: '',
        pid: '',
        name: '',
        description: '',
        price: '',
        maxNumber: '',
        deadline: '',
        bankTransferNotify: '',
        verifyName: '',
        isShow: 1,
        paixu: 0,
        isWechatPay: 0,
        isAliPay: 0,
        isBankTransfer: 0,
        isNeedInvite: 0,
        hour: 0,
        isInvoice: 0,
        isVerify: 0,
        isSms: 1,
        inviteCode: '',
      },
      needInvite,
      dataRule: {
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '通道名称不能为空', trigger: 'blur' }
        ],
        // description: [
        //   { required: true, message: '描述不能为空', trigger: 'blur' }
        // ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        maxNumber: [
          { required: true, message: '最大报名人数不能为空', trigger: 'blur' }
        ],
        deadline: [
          { required: true, message: '截止日期不能为空', trigger: 'blur' }
        ],
        isShow: [
          { required: true, message: '是否展示不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init(activityId, id) {
      this.appid = this.$cookie.get("appid");
      this.dataForm.activityId = activityId || 0
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.applyActivityChannelConfig.activityId
              this.dataForm.name = data.applyActivityChannelConfig.name
              this.dataForm.description = data.applyActivityChannelConfig.description
              this.dataForm.price = data.applyActivityChannelConfig.price
              this.dataForm.maxNumber = data.applyActivityChannelConfig.maxNumber
              this.dataForm.deadline = data.applyActivityChannelConfig.deadline
              this.dataForm.isShow = data.applyActivityChannelConfig.isShow
              this.dataForm.paixu = data.applyActivityChannelConfig.paixu
              this.dataForm.bankTransferNotify = data.applyActivityChannelConfig.bankTransferNotify
              this.dataForm.isWechatPay = data.applyActivityChannelConfig.isWechatPay
              this.dataForm.isAliPay = data.applyActivityChannelConfig.isAliPay
              this.dataForm.isBankTransfer = data.applyActivityChannelConfig.isBankTransfer
              this.dataForm.isNeedInvite = data.applyActivityChannelConfig.isNeedInvite
              this.dataForm.inviteCode = data.applyActivityChannelConfig.inviteCode
              this.dataForm.isSms = data.applyActivityChannelConfig.isSms
              this.dataForm.isInvoice = data.applyActivityChannelConfig.isInvoice
              this.dataForm.hour = data.applyActivityChannelConfig.hour
              this.dataForm.isVerify = data.applyActivityChannelConfig.isVerify
              this.dataForm.verifyName = data.applyActivityChannelConfig.verifyName
              this.dataForm.pid = data.applyActivityChannelConfig.pid
            }
          })
        }
      })
      this.getChannelByActivityId();
    },
    getChannelByActivityId() {
      this.$http({
        url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/findByActivityIdParent/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.result;
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'name': this.dataForm.name,
              'description': this.dataForm.description,
              'price': this.dataForm.price,
              'maxNumber': this.dataForm.maxNumber,
              'deadline': this.dataForm.deadline,
              'isShow': this.dataForm.isShow,
              'bankTransferNotify': this.dataForm.bankTransferNotify,
              'isWechatPay': this.dataForm.isWechatPay,
              'isAliPay': this.dataForm.isAliPay,
              'isBankTransfer': this.dataForm.isBankTransfer,
              'paixu': this.dataForm.paixu,
              'isNeedInvite': this.dataForm.isNeedInvite,
              'isSms': this.dataForm.isSms,
              'isInvoice': this.dataForm.isInvoice,
              'inviteCode': this.dataForm.inviteCode,
              'hour': this.dataForm.hour,
              'isVerify': this.dataForm.isVerify,
              'verifyName': this.dataForm.verifyName,
              'pid': this.dataForm.pid,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
