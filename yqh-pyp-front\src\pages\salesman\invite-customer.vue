<template>
  <div class="invite-customer-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="邀请客户"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 邀请方式选择 -->
    <div class="invite-methods">
      <div class="method-card" @click="showQrCode">
        <div class="method-icon qr-icon">
          <van-icon name="qr" size="32" />
        </div>
        <div class="method-content">
          <div class="method-title">二维码邀请</div>
          <div class="method-desc">生成专属二维码，客户扫码即可绑定</div>
        </div>
        <van-icon name="arrow" />
      </div>

      <div class="method-card" @click="showInviteLink">
        <div class="method-icon link-icon">
          <van-icon name="share-o" size="32" />
        </div>
        <div class="method-content">
          <div class="method-title">邀请链接</div>
          <div class="method-desc">分享邀请链接，客户点击即可绑定</div>
        </div>
        <van-icon name="arrow" />
      </div>
    </div>

    <!-- 邀请统计 -->
    <div class="invite-stats">
      <div class="stats-title">邀请统计</div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ inviteStats.totalInvites || 0 }}</div>
          <div class="stat-label">总邀请数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ inviteStats.successInvites || 0 }}</div>
          <div class="stat-label">成功绑定</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ inviteStats.todayInvites || 0 }}</div>
          <div class="stat-label">今日邀请</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ inviteStats.conversionRate || '0%' }}</div>
          <div class="stat-label">转化率</div>
        </div>
      </div>
    </div>

    <!-- 最近邀请记录 -->
    <div class="recent-invites">
      <div class="section-title">最近邀请记录</div>
      <div class="invite-list">
        <div 
          v-for="invite in recentInvites" 
          :key="invite.id"
          class="invite-item"
        >
          <div class="invite-info">
            <div class="customer-name">{{ invite.customerName || '未知客户' }}</div>
            <div class="invite-time">{{ invite.inviteTime }}</div>
          </div>
          <div class="invite-status">
            <van-tag 
              :type="getStatusTagType(invite.status)"
              size="small"
            >
              {{ getStatusText(invite.status) }}
            </van-tag>
          </div>
        </div>
        <div v-if="recentInvites.length === 0" class="empty-state">
          <van-empty description="暂无邀请记录" />
        </div>
      </div>
    </div>

    <!-- 二维码弹窗 -->
    <van-popup v-model="qrCodeVisible" position="bottom" :style="{ height: '70%' }">
      <div class="qr-popup">
        <div class="popup-header">
          <div class="popup-title">我的邀请二维码</div>
          <van-icon name="cross" @click="qrCodeVisible = false" />
        </div>
        <div class="qr-content">
          <div class="qr-code-container">
            <canvas ref="qrCanvas" class="qr-canvas"></canvas>
          </div>
          <div class="qr-info">
            <div class="qr-title">扫码绑定业务员</div>
            <div class="qr-desc">客户扫描此二维码即可与您建立业务员关系</div>
          </div>
          <div class="qr-actions">
            <van-button type="primary" block @click="saveQrCode">保存二维码</van-button>
            <van-button block @click="shareQrCode" style="margin-top: 12px;">分享二维码</van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 邀请链接弹窗 -->
    <van-popup v-model="linkVisible" position="bottom" :style="{ height: '60%' }">
      <div class="link-popup">
        <div class="popup-header">
          <div class="popup-title">邀请链接</div>
          <van-icon name="cross" @click="linkVisible = false" />
        </div>
        <div class="link-content">
          <div class="link-container">
            <div class="link-text">{{ inviteLink }}</div>
            <van-button type="primary" size="small" @click="copyLink">复制</van-button>
          </div>
          <div class="link-info">
            <div class="link-title">分享邀请链接</div>
            <div class="link-desc">将此链接发送给客户，点击即可建立业务员关系</div>
          </div>
          <div class="link-actions">
            <van-button type="primary" block @click="shareLink">分享链接</van-button>
          </div>
        </div>
      </div>
    </van-popup>

  </div>
</template>

<script>
import QRCode from 'qrcode'

export default {
  name: 'InviteCustomer',
  data() {
    return {
      activityId: null,
      salesmanInfo: null,
      qrCodeVisible: false,
      linkVisible: false,
      inviteLink: '',
      inviteStats: {},
      recentInvites: [],
      loading: false
    }
  },
  created() {
    this.activityId = this.$route.query.activityId || localStorage.getItem('activityId')
    this.loadSalesmanInfo()
  },
  methods: {
    // 加载业务员信息
    async loadSalesmanInfo() {
      this.loading = true
      try {
        // 获取当前登录的业务员信息
        const res = await this.$fly.get('/pyp/web/salesman/checkSalesmanStatus', {
          appid: this.activityId
        })

        if (res.code === 200) {
          this.salesmanInfo = res.salesman
          this.generateInviteData()
          this.loadInviteStats()
          this.loadRecentInvites()
        } else {
          this.$toast('获取业务员信息失败')
        }
      } catch (error) {
        console.error('获取业务员信息失败:', error)
        this.$toast('获取业务员信息失败')
      } finally {
        this.loading = false
      }
    },

    // 生成邀请数据
    generateInviteData() {
      if (!this.salesmanInfo) return

      // 使用业务员ID作为邀请标识
      const salesmanId = this.salesmanInfo.id
      this.inviteLink = `${window.location.origin}/#/salesman/bind?salesmanId=${salesmanId}&activityId=${this.activityId}`
    },

    // 显示二维码
    async showQrCode() {
      this.qrCodeVisible = true
      await this.$nextTick()
      this.generateQrCode()
    },

    // 生成二维码
    async generateQrCode() {
      try {
        const canvas = this.$refs.qrCanvas
        await QRCode.toCanvas(canvas, this.inviteLink, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$toast('生成二维码失败')
      }
    },

    // 显示邀请链接
    showInviteLink() {
      this.linkVisible = true
    },

    // 复制链接
    copyLink() {
      this.copyToClipboard(this.inviteLink, '邀请链接已复制')
    },

    // 复制到剪贴板
    copyToClipboard(text, message) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$toast(message)
        })
      } else {
        // 兼容性处理
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$toast(message)
      }
    },

    // 保存二维码
    saveQrCode() {
      const canvas = this.$refs.qrCanvas
      const link = document.createElement('a')
      link.download = '邀请二维码.png'
      link.href = canvas.toDataURL()
      link.click()
      this.$toast('二维码已保存')
    },

    // 分享二维码
    shareQrCode() {
      if (navigator.share) {
        const canvas = this.$refs.qrCanvas
        canvas.toBlob(blob => {
          const file = new File([blob], '邀请二维码.png', { type: 'image/png' })
          navigator.share({
            title: '邀请二维码',
            text: '扫码绑定业务员',
            files: [file]
          })
        })
      } else {
        this.copyLink()
      }
    },

    // 分享链接
    shareLink() {
      if (navigator.share) {
        navigator.share({
          title: '邀请链接',
          text: '点击绑定业务员',
          url: this.inviteLink
        })
      } else {
        this.copyLink()
      }
    },



    // 加载邀请统计
    async loadInviteStats() {
      if (!this.salesmanInfo) return

      try {
        const res = await this.$fly.get('/pyp/salesman/wxuserbinding/customerStats', {
          salesmanId: this.salesmanInfo.id
        })

        if (res.code === 200) {
          const stats = res.stats
          this.inviteStats = {
            totalInvites: stats.totalCustomers || 0,
            successInvites: stats.activeCustomers || 0,
            todayInvites: stats.todayBindings || 0,
            conversionRate: stats.totalCustomers > 0 ?
              Math.round((stats.activeCustomers / stats.totalCustomers) * 100) + '%' : '0%'
          }
        }
      } catch (error) {
        console.error('获取邀请统计失败:', error)
      }
    },

    // 加载最近邀请记录
    async loadRecentInvites() {
      if (!this.salesmanInfo) return

      try {
        const res = await this.$fly.get('/pyp/web/salesman/getCustomerBindings')

        if (res.code === 200) {
          // 处理分页数据或直接数组数据
          const dataList = res.result
          this.recentInvites = dataList.map(item => ({
            id: item.id,
            customerName: item.wxUserName || '未知客户',
            inviteTime: item.bindingTime,
            status: item.status // 1-有效, 0-已失效, 2-已解绑
          }))
          console.log('最近邀请记录:', this.recentInvites)
        } else {
          console.log('获取邀请记录失败:', res.msg)
        }
      } catch (error) {
        console.error('获取邀请记录失败:', error)
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '已绑定',
        0: '待绑定',
        '-1': '已过期'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        1: 'success',
        0: 'warning',
        '-1': 'danger'
      }
      return typeMap[status] || 'default'
    }
  }
}
</script>

<style scoped>
.invite-customer-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.invite-methods {
  padding: 16px;
}

.method-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.method-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.method-icon.qr-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.method-icon.link-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.method-icon.code-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.method-content {
  flex: 1;
}

.method-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 14px;
  color: #969799;
}

.invite-stats {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.recent-invites {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.invite-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f7f8fa;
}

.invite-item:last-child {
  border-bottom: none;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.invite-time {
  font-size: 12px;
  color: #969799;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.qr-content,
.link-content,
.code-content {
  padding: 24px 16px;
  text-align: center;
}

.qr-code-container {
  margin-bottom: 24px;
}

.qr-canvas {
  border: 1px solid #ebedf0;
  border-radius: 8px;
}

.qr-info,
.link-info,
.code-info {
  margin-bottom: 24px;
}

.qr-title,
.link-title,
.code-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.qr-desc,
.link-desc,
.code-desc {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
}

.link-container,
.code-container {
  display: flex;
  align-items: center;
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 24px;
}

.link-text {
  flex: 1;
  font-size: 14px;
  color: #323233;
  word-break: break-all;
  margin-right: 12px;
}

.code-display {
  flex: 1;
  font-size: 20px;
  font-weight: bold;
  color: #1989fa;
  letter-spacing: 2px;
  margin-right: 12px;
}
</style>
