<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-search v-model="dataForm.name" placeholder="请输入您要搜索的专家名称" show-action shape="round">
      <div slot="action" @click="onSearch" class="search-text">搜索</div>
    </van-search>
    <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
      <!-- <van-tabs v-model="dateActive" :ellipsis="false" @click="dateOnClick">
        <van-tab title="全部日期" name=""></van-tab>
        <van-tab v-for="item in activityDateBetween" :key="item.realDate" :name="item.realDate"
          :title="item.textDate"></van-tab>
      </van-tabs> -->
      <div style="display: flex">
        <!-- <van-sidebar @change="placeOnChange" v-model="placeActive" style="width: 20%" v-if="placeList.length > 1">
          <van-sidebar-item title="全部场地" />
          <van-sidebar-item v-for="item in placeList" :key="item.id" :title="item.name" />
        </van-sidebar> -->
        <div class="data"
          style="width: 94%;margin-left: 3%;margin-top: 10px;background-color: white;border-radius: 8px;margin-bottom: 20px;">
          <div v-for="(item, index) in dataList" :key="item.id" :name="item.id"
            style="margin-bottom: 10px;padding: 0 10px;">
            <div
              :style="'display: flex;height: 50px;align-items: center;justify-content: center;color:rgba(' + topicColor[index] + ');margin-bottom: 10px'">
              <div style="font-weight: 600;font-size: 20px;margin-right: 20px;">{{ item.placeName }}</div>
              <div style="font-weight: 500;font-size: 16px;">{{ item.startTime.substring(0, 10) }}{{ item.week }}</div>
            </div>
            <div
              :style="'display: flex;height: 50px;align-items: center;justify-content: center;color:white;background-color:rgba(' + topicColor[index] + ')'">
              <div style="width: 25%;font-size: 12px;text-align: center" v-if="item.startTime">
                {{ item.startTime.substring(11, 16) }}
                <span v-if="item.endTime">-{{ item.endTime.substring(11, 16) }}</span>
              </div>
              <div style="font-weight: 600;font-size: 16px;width: 75%;text-align: center;">{{ item.name }}</div>
            </div>
            <div :style="'padding: 5px 0;font-size: 12px;border-bottom: 2px solid rgba(' + topicColor[index] + ')'">
              <div :style="'text-align: center;color:rgba(' + topicColor[index] + ')'"
                v-if="item.activitySpeakers && item.activitySpeakers.length > 0">
                <span style="font-weight: 600;">{{ item.aliasSpeakerName || '主席' }}：</span>
                <span @click="goExpertDetail(item1.id)" style="" v-for="(item1, index1) in item.activitySpeakers"
                  :key="item1.id">{{ item1.name }} <span v-if="index1 != (item.activitySpeakers.length - 1)"
                    style="margin: 0 5px;">|</span> </span>
              </div>
              <div :style="'text-align: center;color:rgba(' + topicColor[index] + ')'"
                v-if="item.activityGuests && item.activityGuests.length > 0">
                <span style="font-weight: 600;">{{ item.aliasGuestName || '主持' }}：</span>
                <span @click="goExpertDetail(item1.id)" v-for="(item1, index1) in item.activityGuests"
                  :key="item1.id">{{
                    item1.name }}<span v-if="index1 != (item.activityGuests.length - 1)" style="margin: 0 5px;">|</span>
                </span>
                <!-- 有单位 -->
                <!-- <span @click="goExpertDetail(item1.id)" v-for="(item1, index1) in item.activityGuests" :key="item1.id">{{
                item1.name }} {{ item1.unit }}<span v-if="index1 != (item.activityGuests.length - 1)" style="margin: 0 5px;">|</span>
              </span> -->
              </div>
              <div :style="'text-align: center;color:rgba(' + topicColor[index] + ')'"
                v-if="item.activityDiscuss && item.activityDiscuss.length > 0">
                <span style="font-weight: 600;">{{ item.aliasDiscussName || '讨论' }}：</span>
                <span @click="goExpertDetail(item1.id)" v-for="(item1, index1) in item.activityDiscuss"
                  :key="item1.id">{{
                    item1.name }}<span v-if="index1 != (item.activityDiscuss.length - 1)" style="margin: 0 5px;">|</span>
                </span>
                <!-- 有单位 -->
                <!-- <span @click="goExpertDetail(item1.id)" v-for="(item1, index1) in item.activityDiscuss" :key="item1.id">{{
                item1.name }} {{ item1.unit }}<span v-if="index1 != (item.activityDiscuss.length - 1)" style="margin: 0 5px;">|</span>
              </span> -->
              </div>
            </div>
            <!-- <div 
            :style="index2 % 2 == 1 ? 'display: flex;align-items: center;justify-content: center;padding: 8px 0;gap: 5px;border-bottom: 1px solid rgba('+ topicColor[index] + ',0.5);background-color:  rgba('+ topicColor[index] + ',0.15)' : 
            'display: flex;align-items: center;justify-content: center;padding: 8px 0;gap: 5px;border-bottom: 1px solid rgba('+ topicColor[index] + ',0.5)'"
              v-for="(item2,index2) in item.placeActivityTopicScheduleEntities" :key="item2.id"> -->
            <div
              :style="'display: flex;align-items: center;justify-content: center;padding: 8px 0;gap: 5px;border-bottom: 1px solid rgba(' + topicColor[index] + ',0.5);'"
              v-for="(item2, index2) in item.placeActivityTopicScheduleEntities" :key="item2.id">
              <div style="width: 25%;font-size: 12px;text-align: center" v-if="item2.startTime">
                {{ item2.startTime.substring(11, 16) }}
                <span v-if="item2.endTime">-{{ item2.endTime.substring(11, 16) }}</span>
              </div>
              <div style="font-weight: 600;font-size: 14px;width: 50%;text-align: center;">{{ item2.name }}</div>
              <div style="font-weight: 500;font-size: 12px;width: 25%;">
                <div v-if="item2.activitySpeakers && item2.activitySpeakers.length > 0">
                  <div v-for="item3 in item2.activitySpeakers" :key="item3.id" @click="goExpertDetail(item3.id)">
                    <div style="font-weight: 600;height: 20px;line-height: 20px;">{{ item3.name }}</div>
                    <!-- <div>{{ item3.unit }}</div> -->
                  </div>
                </div>
                <div v-if="item2.activityGuests && item2.activityGuests.length > 0">
                  <div v-for="item3 in item2.activityGuests" :key="item3.id" @click="goExpertDetail(item3.id)">
                    <div style="font-weight: 600;height: 20px;line-height: 20px;">{{ item3.name }}</div>
                    <!-- <div>{{ item3.unit }}</div> -->
                  </div>
                </div>
                <div v-if="item2.activityDiscuss && item2.activityDiscuss.length > 0">
                  <div v-for="item3 in item2.activityDiscuss" :key="item3.id" @click="goExpertDetail(item3.id)">
                    <div style="font-weight: 600;height: 20px;line-height: 20px;">{{ item3.name }}</div>
                    <!-- <div>{{ item3.unit }}</div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <van-collapse accordion v-model="schedulesActive">
            <van-collapse-item v-for="item in dataList" :key="item.id" :name="item.id" style="margin-bottom: 10px">
              <van-steps active="-1" direction="vertical">
                <van-step v-for="item2 in item.placeActivityTopicScheduleEntities" :key="item2.id"
                  :class="hight(item2)">
                  <div>
                    <div style="color: black">{{ item2.name }}</div>
                    <div v-if="
                      item2.activitySpeakers &&
                      item2.activitySpeakers.length > 0
                    ">
                      {{ item2.aliasSpeakerName || '主持' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activitySpeakers" :key="item3.id" round plain size="medium"
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div v-if="
                      item2.activityGuests && item2.activityGuests.length > 0
                    ">
                      {{ item2.aliasGuestName || '讲者' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityGuests" :key="item3.id" size="medium" round plain
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div v-if="
                      item2.activityDiscuss &&
                      item2.activityDiscuss.length > 0
                    ">
                      {{ item2.aliasDiscussName || '讨论' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityDiscuss" :key="item3.id" size="medium" round plain
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div>
                      {{ item2.startTime }} ~
                      {{ item2.endTime.substring(11, 19) }}
                    </div>
                  </div>
                </van-step>
              </van-steps>
            </van-collapse-item>
          </van-collapse> -->
        </div>
      </div>
    </van-list>
    <div class="bottom">
      <div class="item" @click="dateShow = true">
        <div class="left">{{ filterDate ? filterDate : '全部日期' }}</div>
        <img class="right" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240912/72d5e02f597545ff95a22fc48fb70e19.png"
          alt="">
      </div>
      <div class="item" @click="placeShow = true">
        <div class="left">{{ filterPlace ? filterPlace : '全部会场' }}</div>
        <img class="right" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240912/72d5e02f597545ff95a22fc48fb70e19.png"
          alt="">
      </div>
      <div class="item" @click="topicShow = true">
        <div class="left">{{ filterTopic ? filterTopic : '全部分论坛' }}</div>
        <img class="right" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20240912/72d5e02f597545ff95a22fc48fb70e19.png"
          alt="">
      </div>
    </div>
    <!-- 返回按钮 -->
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    <van-action-sheet title="分论坛选择" v-model="topicShow" :actions="topicList" @select="topicSelect" />
    <van-action-sheet title="会场选择" v-model="placeShow" :actions="placeList" @select="placeSelect" />
    <van-action-sheet title="日期选择" v-model="dateShow" :actions="activityDateBetween" @select="dateSelect" />
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import { topicColor } from "@/data/common"
export default {
  components: { pcheader },
  data() {
    return {
      topicColor,
      topicShow: false,
      placeShow: false,
      dateShow: false,
      filterDate: '',
      filterPlace: '',
      filterTopic: '',
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activityId: undefined,
      dataForm: {
        name: "",
        placeId: "",
        topicId: "",
        date: "",
      },
      activityInfo: {},
      loading: false,
      finished: false,
      flag: false,
      dataList: [],
      placeList: [{
        "name": "全部会场",
        "id": ""
      }],
      topicList: [{
        "name": "全部分论坛",
        "id": ""
      }],
      activityDateBetween: [{
        "name": "全部日期",
        "realDate": ""
      }],
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      placeActive: 0,
      dateActive: "",
      schedulesActive: "",
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid");
    this.placeList = [{
      "name": "全部会场",
      "id": ""
    }],
      this.topicList = [{
        "name": "全部分论坛",
        "id": ""
      }],
      this.activityDateBetween = [{
        "name": "全部日期",
        "realDate": ""
      }],
      this.getActivityInfo();
    this.getActivityDateBetween();
    this.getPlace();
    this.getTopic();
  },
  methods: {
    topicSelect(e) {
      this.topicShow = false;
      this.filterTopic = e.name;
      this.dataForm.topicId = e.id;
      this.onSearch();
    },
    placeSelect(e) {
      this.placeShow = false;
      this.filterPlace = e.name;
      this.dataForm.placeId = e.id;
      this.onSearch();
    },
    dateSelect(e) {
      this.dateShow = false;
      this.filterDate = e.name;
      this.dataForm.date = e.realDate;
      this.onSearch();
    },
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      if (!this.flag) {
        this.getActivityList();
      }
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityList();
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = this.activityInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityDateBetween() {
      this.$fly
        .get(`/pyp/activity/activity/dateBetween/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            res.result.forEach(e => {
              this.activityDateBetween.push(e);
            })
          } else {
            vant.Toast(res.msg);
            // this.activityDateBetween = [];
          }
        });
    },
    getPlace() {
      this.$fly
        .get(`/pyp/place/placeactivity/findByActivityId/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            res.result.forEach(e => {
              this.placeList.push(e);
            })
          } else {
            vant.Toast(res.msg);
            // this.placeList = [];
          }
        });
    },
    getTopic() {
      this.$fly
        .get(`/pyp/place/placeactivitytopic/findByActivityId/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            res.result.forEach(e => {
              this.topicList.push(e);
            })
          } else {
            vant.Toast(res.msg);
            // this.topicList = [];
          }
        });
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/place/placeactivitytopic/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          placeId: this.dataForm.placeId,
          topicId: this.dataForm.topicId,
          date: this.dataForm.date,
          name: this.dataForm.name,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.schedulesActive = this.dataList[0].id;
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    dateOnClick(name, title) {
      this.dataForm.date = name;
      this.onSearch();
    },
    placeOnChange(index) {
      if (index == 0) {
        this.dataForm.placeId = "";
      } else {
        this.placeList.forEach((e, index1) => {
          var realIndex = index1 + 1;
          if (realIndex == index) {
            this.dataForm.placeId = e.id;
          }
        });
      }
      this.onSearch();
    },
    goExpertDetail(v) {
      this.$router.push({
        name: "schedulesExpertDetail",
        query: { detailId: v, id: this.activityId },
      });
    },
    hight(item) {
      if (this.dataForm.name) {
        // 存在搜索才去高亮
        if (item.activityGuests && item.activityGuests.length > 0) {
          const flag = item.activityGuests.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
        if (item.activitySpeakers && item.activitySpeakers.length > 0) {
          const flag = item.activitySpeakers.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
        if (item.activityDiscuss && item.activityDiscuss.length > 0) {
          const flag = item.activityDiscuss.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
      }
    },
    cmsTurnBack() {
      if (this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/ .van-cell {
    padding: 0;
    align-items: center;
  }

  /deep/ .van-cell__right-icon {
    margin-right: 20px;
  }
}

.van-sidebar-item {
  padding: 20px 10px;
}

.show {
  background: #bae2ff !important;
}

.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #4485ff;
  display: flex;
  align-items: center;
  height: 50px;

  .item {
    width: 33%;
    display: flex;
    align-items: center;
    justify-content: center;

    .left {
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: white;
      font-weight: 500;
      width: 80%;
    }

    .right {
      width: 20px;
      height: 20px;
    }
  }
}
</style>