<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <!-- <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="考卷id" prop="examId">
      <el-input v-model="dataForm.examId" placeholder="考卷id"></el-input>
    </el-form-item>
    <el-form-item label="用户活动表id" prop="activityUserId">
      <el-input v-model="dataForm.activityUserId" placeholder="用户活动表id"></el-input>
    </el-form-item> -->
    <el-form-item label="考试状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="考试状态" filterable>
          <el-option v-for="item in examActivityUserStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <!-- <el-form-item label="得分" prop="points">
      <el-input v-model="dataForm.points" placeholder="得分"></el-input>
    </el-form-item>
    <el-form-item label="及格分数" prop="passPoints">
      <el-input v-model="dataForm.passPoints" placeholder="及格分数"></el-input>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { examActivityUserStatus } from "@/data/exam"
  export default {
    data () {
      return {
        examActivityUserStatus: examActivityUserStatus,
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          examId: '',
          activityUserId: '',
          userId: '',
          status: '',
          points: '',
          passPoints: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          examId: [
            { required: true, message: '考卷id不能为空', trigger: 'blur' }
          ],
          activityUserId: [
            { required: true, message: '用户活动表id不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '考试状态不能为空', trigger: 'blur' }
          ],
          points: [
            { required: true, message: '得分不能为空', trigger: 'blur' }
          ],
          passPoints: [
            { required: true, message: '及格分数不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (activityId,examId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.examId = examId;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examactivityuser/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.dataForm.activityId = data.examActivityUser.activityId
                this.dataForm.examId = data.examActivityUser.examId
                this.dataForm.activityUserId = data.examActivityUser.activityUserId
                this.dataForm.userId = data.examActivityUser.userId
                this.dataForm.status = data.examActivityUser.status
                this.dataForm.points = data.examActivityUser.points
                this.dataForm.passPoints = data.examActivityUser.passPoints
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examactivityuser/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'examId': this.dataForm.examId,
                'activityUserId': this.dataForm.activityUserId,
                'userId': this.dataForm.userId,
                'status': this.dataForm.status,
                'points': this.dataForm.points,
                'passPoints': this.dataForm.passPoints
              })
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
