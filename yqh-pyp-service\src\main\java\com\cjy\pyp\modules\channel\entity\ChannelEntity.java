package com.cjy.pyp.modules.channel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 渠道实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@Data
@TableName("channel")
@Accessors(chain = true)
public class ChannelEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 渠道编号
     */
    private String code;

    /**
     * 渠道描述
     */
    private String description;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactMobile;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 渠道地址
     */
    private String address;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 渠道级别
     */
    private Integer level;

    /**
     * 上级渠道ID
     */
    private Long parentId;

    /**
     * 默认佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 退款名额总数
     */
    private Integer refundQuota;

    /**
     * 已使用退款名额
     */
    private Integer refundQuotaUsed;

    /**
     * 是否启用退款名额控制：0-禁用，1-启用
     */
    private Integer refundQuotaEnabled;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    /**
     * 防重令牌
     */
    @TableField(exist = false)
    private String repeatToken;

    /**
     * 上级渠道名称（非数据库字段）
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 下级渠道数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer childrenCount;

    /**
     * 业务员数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer salesmanCount;

    /**
     * 活跃业务员数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer activeSalesmanCount;

    /**
     * 总订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer totalOrders;

    /**
     * 活动订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer activityOrders;

    /**
     * 充值订单数（非数据库字段）
     */
    @TableField(exist = false)
    private Integer rechargeOrders;

    /**
     * 销售总额（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;

    /**
     * 佣金总额（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal totalCommission;
}
