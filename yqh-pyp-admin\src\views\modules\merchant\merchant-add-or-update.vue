<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="商家名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="商家名称"></el-input>
    </el-form-item>
    <el-form-item label="简介" prop="brief">
      <el-input v-model="dataForm.brief" placeholder="简介"></el-input>
    </el-form-item>
    <el-form-item label="商家图片" prop="picUrl">
      <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                  accept=".jpg, .jpeg, .png, .gif" :on-success="backgroundSuccessHandle" :action="url">
          <img width="100px" v-if="dataForm.picUrl" :src="dataForm.picUrl" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="联系人" prop="username">
      <el-input v-model="dataForm.username" placeholder="联系人"></el-input>
    </el-form-item>
    <el-form-item label="联系方式" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="第三方链接" prop="url">
      <el-input v-model="dataForm.url" placeholder="第三方链接"></el-input>
    </el-form-item>
    <el-form-item label="排序" prop="paixu">
      <el-input v-model="dataForm.paixu" placeholder="排序"></el-input>
    </el-form-item>
    <el-form-item label="是否大牌" prop="isBig">
        <el-select v-model="dataForm.isBig" placeholder="是否大牌" filterable>
        <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="详细介绍" prop="content">
        <tinymce-editor
          ref="editor"
          v-model="dataForm.content"
        ></tinymce-editor>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
import { yesOrNo } from "@/data/common"
  export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
    data () {
      return {
        yesOrNo: yesOrNo,
        visible: false,
        url: '',
        dataForm: {
          id: 0,
          activityId: '',
          name: '',
          url: '',
          picUrl: '',
          paixu: 0,
          isBig: 0,
          brief: '',
          username: '',
          mobile: '',
          content: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '商家名称不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.dataForm.id = id || 0
        this.dataForm.activityId = activityId
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/merchant/merchant/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.merchant.activityId
                this.dataForm.name = data.merchant.name
                this.dataForm.url = data.merchant.url
                this.dataForm.picUrl = data.merchant.picUrl
                this.dataForm.paixu = data.merchant.paixu
                this.dataForm.brief = data.merchant.brief
                this.dataForm.content = data.merchant.content
                this.dataForm.username = data.merchant.username
                this.dataForm.mobile = data.merchant.mobile
                this.dataForm.isBig = data.merchant.isBig
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/merchant/merchant/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'name': this.dataForm.name,
                'url': this.dataForm.url,
                'picUrl': this.dataForm.picUrl,
                'paixu': this.dataForm.paixu,
                'brief': this.dataForm.brief,
                'mobile': this.dataForm.mobile,
                'username': this.dataForm.username,
                'isBig': this.dataForm.isBig,
                'content': this.dataForm.content
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      // 上传成功（背景）
      backgroundSuccessHandle(response, file, fileList) {
          if (response && response.code === 200) {
              this.dataForm.picUrl = response.url;
              this.$message({
                  message: '上传成功',
                  type: 'success',
              })
          } else {
              this.$message.error(response.msg);
          }
      },
    }
  }
</script>
