package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.dao.SalesmanCommissionSettlementDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity;
import com.cjy.pyp.modules.salesman.enums.SettlementStatusEnum;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionSettlementService;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务员佣金结算批次服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Service("salesmanCommissionSettlementService")
public class SalesmanCommissionSettlementServiceImpl extends ServiceImpl<SalesmanCommissionSettlementDao, SalesmanCommissionSettlementEntity> 
        implements SalesmanCommissionSettlementService {

    @Autowired
    private SalesmanCommissionRecordService commissionRecordService;

    @Override
    public List<SalesmanCommissionSettlementEntity> queryPage(Map<String, Object> params) {
        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page,limit);
        List<SalesmanCommissionSettlementEntity> list = baseMapper.queryPage(params);
        
        return list;
    }

    @Override
    public SalesmanCommissionSettlementEntity getByBatchNo(String batchNo, String appid) {
        return baseMapper.getByBatchNo(batchNo, appid);
    }

    @Override
    public Map<String, Object> getSettlementStats(Map<String, Object> params) {
        return baseMapper.getSettlementStats(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesmanCommissionSettlementEntity createSettlementBatch(List<Long> salesmanIds, String startTime, 
                                                                    String endTime, String remarks, String appid) {
        // 查询待结算的佣金记录
        Map<String, Object> params = new HashMap<>();
        params.put("salesmanIds", salesmanIds);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("appid", appid);
        
        List<SalesmanCommissionRecordEntity> unsettledRecords = commissionRecordService.getUnsettledRecords(params);
        
        if (unsettledRecords.isEmpty()) {
            throw new RuntimeException("没有找到待结算的佣金记录");
        }
        
        // 统计信息
        Set<Long> uniqueSalesmanIds = unsettledRecords.stream()
                .map(SalesmanCommissionRecordEntity::getSalesmanId)
                .collect(Collectors.toSet());
        
        BigDecimal totalAmount = unsettledRecords.stream()
                .map(SalesmanCommissionRecordEntity::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 生成批次号
        String batchNo = generateBatchNo();
        
        // 创建结算批次
        SalesmanCommissionSettlementEntity settlement = new SalesmanCommissionSettlementEntity();
        settlement.setBatchNo(batchNo);
        settlement.setSettlementDate(new Date());
        settlement.setSalesmanCount(uniqueSalesmanIds.size());
        settlement.setRecordCount(unsettledRecords.size());
        settlement.setTotalAmount(totalAmount);
        settlement.setStatus(SettlementStatusEnum.UNSETTLED.getCode());
        settlement.setSettlementType(1); // 1-手动结算
        settlement.setRemarks(remarks);
        settlement.setAppid(appid);
        
        this.save(settlement);
        
        // 更新佣金记录的结算批次号
        List<Long> recordIds = unsettledRecords.stream()
                .map(SalesmanCommissionRecordEntity::getId)
                .collect(Collectors.toList());
        
        commissionRecordService.batchUpdateSettlementStatus(recordIds, 
                SettlementStatusEnum.UNSETTLED.getCode(), batchNo, "创建结算批次");
        
        return settlement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeSettlement(String batchNo, String appid) {
        // 获取结算批次
        SalesmanCommissionSettlementEntity settlement = getByBatchNo(batchNo, appid);
        if (settlement == null) {
            throw new RuntimeException("结算批次不存在");
        }
        
        if (settlement.getStatus() != SettlementStatusEnum.UNSETTLED.getCode()) {
            throw new RuntimeException("结算批次状态不正确");
        }
        
        // 查询该批次的佣金记录
        Map<String, Object> params = new HashMap<>();
        params.put("settlementBatchNo", batchNo);
        params.put("appid", appid);
        
        List<SalesmanCommissionRecordEntity> records = commissionRecordService.getUnsettledRecords(params);
        
        if (records.isEmpty()) {
            throw new RuntimeException("没有找到待结算的佣金记录");
        }
        
        // 更新佣金记录状态为已结算
        List<Long> recordIds = records.stream()
                .map(SalesmanCommissionRecordEntity::getId)
                .collect(Collectors.toList());
        
        commissionRecordService.batchUpdateSettlementStatus(recordIds, 
                SettlementStatusEnum.SETTLED.getCode(), batchNo, "批次结算完成");
        
        // 更新结算批次状态
        settlement.setStatus(SettlementStatusEnum.SETTLED.getCode());
        settlement.setSettlementTime(new Date());
        this.updateById(settlement);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelSettlement(String batchNo, String appid) {
        // 获取结算批次
        SalesmanCommissionSettlementEntity settlement = getByBatchNo(batchNo, appid);
        if (settlement == null) {
            throw new RuntimeException("结算批次不存在");
        }
        
        if (settlement.getStatus() == SettlementStatusEnum.CANCELLED.getCode()) {
            throw new RuntimeException("结算批次已取消");
        }
        
        // 查询该批次的佣金记录
        Map<String, Object> params = new HashMap<>();
        params.put("settlementBatchNo", batchNo);
        params.put("appid", appid);
        
        List<SalesmanCommissionRecordEntity> records = commissionRecordService.list();
        records = records.stream()
                .filter(r -> batchNo.equals(r.getSettlementBatchNo()))
                .collect(Collectors.toList());
        
        if (!records.isEmpty()) {
            // 更新佣金记录状态
            List<Long> recordIds = records.stream()
                    .map(SalesmanCommissionRecordEntity::getId)
                    .collect(Collectors.toList());
            
            if (settlement.getStatus() == SettlementStatusEnum.SETTLED.getCode()) {
                // 如果已结算，恢复为未结算状态
                commissionRecordService.batchUpdateSettlementStatus(recordIds, 
                        SettlementStatusEnum.UNSETTLED.getCode(), null, "结算批次已取消");
            } else {
                // 如果未结算，清除批次号
                commissionRecordService.batchUpdateSettlementStatus(recordIds, 
                        SettlementStatusEnum.UNSETTLED.getCode(), null, "结算批次已取消");
            }
        }
        
        // 更新结算批次状态
        settlement.setStatus(SettlementStatusEnum.CANCELLED.getCode());
        settlement.setSettlementTime(new Date());
        this.updateById(settlement);
        
        return true;
    }

    @Override
    public String generateBatchNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.valueOf((int)(Math.random() * 1000));
        return "BATCH" + timestamp + String.format("%03d", Integer.parseInt(random));
    }
}
