<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <!-- <el-form-item label="活动表id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="活动表id"></el-input>
    </el-form-item> -->
      <el-form-item label="用户" prop="userId">
        <el-select v-model="dataForm.userId" placeholder="用户" filterable>
          <el-option v-for="item in sysuser" :key="item.userId" :label="item.username" :value="item.userId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="角色" prop="roleId">
        <el-select v-model="dataForm.roleId" placeholder="角色" filterable>
          <el-option v-for="item in activityRole" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">正常</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="工作板块" prop="workPlace">
        <el-input v-model="dataForm.workPlace" placeholder="工作板块"></el-input>
      </el-form-item>
      <el-form-item label="不足之处" prop="bad">
        <el-input v-model="dataForm.bad" placeholder="不足之处"></el-input>
      </el-form-item>
      <el-form-item label="亮点" prop="good">
        <el-input v-model="dataForm.good" placeholder="亮点"></el-input>
      </el-form-item>
      <el-form-item label="改进地方" prop="better">
        <el-input v-model="dataForm.better" placeholder="改进地方"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { activityRole } from "@/data/activity";
export default {
  data() {
    return {
      activityRole,
      sysuser: [],
      visible: false,
      dataForm: {
        id: 0,
        activityId: '',
        userId: '',
        roleId: '',
        workPlace: '',
        bad: '',
        good: '',
        better: '',
        status: 1
      },
      dataRule: {
        activityId: [
          { required: true, message: '活动表id不能为空', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ],
        roleId: [
          { required: true, message: '权限ID不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态，0-禁用，1-启用不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id, activityId) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityrole/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.activityRole.activityId
              this.dataForm.userId = data.activityRole.userId
              this.dataForm.roleId = data.activityRole.roleId
              this.dataForm.status = data.activityRole.status
              this.dataForm.workPlace = data.activityRole.workPlace
              this.dataForm.bad = data.activityRole.bad
              this.dataForm.good = data.activityRole.good
              this.dataForm.better = data.activityRole.better
            }
          })
        } else {
          this.dataForm.activityId = activityId
        }
      })
      this.getSysUser()
    },
    getSysUser() {
      this.$http({
        url: this.$http.adornUrl("/sys/user/findByAppid"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.sysuser = data.result;
        } else {
          this.sysuser = [];
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityrole/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'userId': this.dataForm.userId,
              'roleId': this.dataForm.roleId,
              'workPlace': this.dataForm.workPlace,
              'bad': this.dataForm.bad,
              'good': this.dataForm.good,
              'better': this.dataForm.better,
              'status': this.dataForm.status
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
