<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-radio-group v-model="dataForm.priceBankId">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button :label="item.id" v-for="item in priceBank" :key="item.id">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="摘要" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.accName" placeholder="对方户名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="timeArray" type="daterange" value-format="yyyy/MM/dd" range-separator="至"
          start-placeholder="开始日期(支付时间)" end-placeholder="结束日期(支付时间)" :picker-options="pickerOptions" />
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('price:pricetransformlog:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('price:pricetransformlog:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="success" @click="exportHandle()">导出数据</el-button>
        <el-button type="success" @click="downloadExampleHandle()">模板下载</el-button>
        <el-button type="success" @click="pricewaterimportHandle()">导入</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="payTime" header-align="center" align="center" label="付款时间">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="收入">
        <div slot-scope="scope">{{ scope.row.type == 1 ? scope.row.price : 0 }}</div>
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="支出">
        <div slot-scope="scope">{{ scope.row.type == 0 ? scope.row.price : 0 }}</div>
      </el-table-column>
      <el-table-column prop="balance" header-align="center" align="center" label="余额">
      </el-table-column>
      <el-table-column prop="priceConfigName" header-align="center" align="center" label="科目">
        <div slot-scope="scope" @click="addOrUpdateHandle(scope.row.id)">{{ scope.row.priceConfigName || '-' }}</div>
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" width="200" label="摘要">
        <div slot-scope="scope" @click="addOrUpdateHandle(scope.row.id)">{{ scope.row.name || '-' }}</div>
      </el-table-column>
      <!-- <el-table-column prop="priceBankId" header-align="center" align="center" :show-overflow-tooltip="true" label="银行账号">
      </el-table-column>
      <el-table-column prop="tranFlow" header-align="center" align="center" :show-overflow-tooltip="true" label="交易流水号">
      </el-table-column>
      <el-table-column prop="detNo" header-align="center" align="center" :show-overflow-tooltip="true" label="活存账户明细号">
      </el-table-column> -->
      <el-table-column prop="accNumber" header-align="center" align="center" :show-overflow-tooltip="true" label="对方账号">
      </el-table-column>
      <el-table-column prop="accName" header-align="center" align="center" :show-overflow-tooltip="true" label="对方户名">
      </el-table-column>
      <el-table-column prop="accBankName" header-align="center" align="center" :show-overflow-tooltip="true" label="对方账户开户行名称">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="是否比对" width="100">
        <div slot-scope="scope">
          <el-tag
            type="primary"
            :class="'tag-color-mini tag-color-' + scope.row.status"
            >{{  scope.row.status == null ? '空' : isMatch[scope.row.status].value }}</el-tag
          >
        </div>
      </el-table-column>
      <el-table-column prop="matchPrice" header-align="center" align="center" label="已比对金额">
      </el-table-column>
      <!-- <el-table-column prop="activityId" header-align="center" align="center" label="会议ID">
      </el-table-column>
      <el-table-column prop="activitySettleId" header-align="center" align="center" label="所属会议结算单">
      </el-table-column> -->
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="180" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="scope.row.status != 1" @click="activitySettle(scope.row.id)">生成结算单</el-button>
          <el-button type="text" size="small" v-if="scope.row.status != 1" @click="priceTransform(scope.row.id)">关联往来款</el-button>
          <el-button type="text" size="small" v-if="scope.row.status != 0" @click="detail(scope.row.id)">结算单明细</el-button>
          <el-button type="text" size="small" v-if="scope.row.status != 0" @click="pricewatertransformdetail(scope.row.id)">往来款明细</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <pricewatermatch v-if="pricewatermatchVisible" ref="pricewatermatch" @refreshDataList="getDataList">
    </pricewatermatch>
    <pricewatertransform v-if="pricewatertransformVisible" ref="pricewatertransform" @refreshDataList="getDataList">
    </pricewatertransform>
    <pricewaterdetail v-if="pricewaterdetailVisible" ref="pricewaterdetail" @refreshDataList="getDataList">
    </pricewaterdetail>
    <pricewatertransformdetail v-if="pricewatertransformdetailVisible" ref="pricewatertransformdetail" @refreshDataList="getDataList">
    </pricewatertransformdetail>
    <pricewaterimport v-if="pricewaterimportVisible" ref="pricewaterimport" @refreshDataList="getDataList">
    </pricewaterimport>
  </div>
</template>

<script>
import { isMatch } from "@/data/common"
import AddOrUpdate from './pricewater-add-or-update'
import pricewatermatch from './pricewater-match'
import pricewatertransform from './pricewater-transform'
import pricewaterdetail from './pricewater-detail'
import pricewatertransformdetail from './pricewater-transformdetail'
import pricewaterimport from "./pricewater-import";
export default {
  data() {
    return {
      timeArray: [],
      isMatch,
      priceBank: [],
      dataForm: {
        name: '',
        appid: '',
        accName: '',
        priceBankId: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      pricewatermatchVisible: false,
      pricewatertransformVisible: false,
      pricewaterdetailVisible: false,
      pricewatertransformdetailVisible: false,
      pricewaterimportVisible: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
    }
  },
  components: {
    AddOrUpdate,
    pricewatermatch,
    pricewatertransform,
    pricewaterdetail,
    pricewatertransformdetail,
    pricewaterimport,
  },
  activated() {
    this.getDataList()
    this.findPriceBank()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/price/pricewater/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'accName': this.dataForm.accName,
          'priceBankId': this.dataForm.priceBankId,
          'appid': this.$cookie.get('appid'),
          start: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : '',
          end: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : '',
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    activitySettle(id) {
      this.pricewatermatchVisible = true
      this.$nextTick(() => {
        this.$refs.pricewatermatch.init(id)
      })
    },
    priceTransform(id) {
      this.pricewatertransformVisible = true
      this.$nextTick(() => {
        this.$refs.pricewatertransform.init(id)
      })
    },
    detail(id) {
      this.pricewaterdetailVisible = true
      this.$nextTick(() => {
        this.$refs.pricewaterdetail.init(id)
      })
    },
    pricewatertransformdetail(id) {
      this.pricewatertransformdetailVisible = true
      this.$nextTick(() => {
        this.$refs.pricewatertransformdetail.init(id)
      })
    },
    findPriceBank() {
      this.$http({
        url: this.$http.adornUrl('/price/pricebank/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.priceBank = data.result
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/price/pricewater/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    exportHandle() {
      var url = this.$http.adornUrl("/price/pricewater/export?" + [
        "page=1",
        "limit=10000",
        "accName=" + this.dataForm.accName,
        "message=" + this.dataForm.message,
        "priceBankId=" + this.dataForm.priceBankId,
        "appid=" + this.$cookie.get("appid"),
        "start=" + ((this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : ''),
        "end=" + ((this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : ''),
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    downloadExampleHandle() {
      if (!this.dataForm.priceBankId) {
        this.$message.error("请先选择银行账户");
        return false;
      }
      var url = this.$http.adornUrl("/price/pricewater/exportExcelExample?" + [
        "priceBankId=" + this.dataForm.priceBankId,
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    pricewaterimportHandle() {
      this.pricewaterimportVisible = true
      this.$nextTick(() => {
        this.$refs.pricewaterimport.init()
      })
    },
  }
}
</script>
