package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.utils.CommissionEnumUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 业务员佣金记录查询控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/salesman/commission/record")
@Api(tags = "业务员佣金记录查询")
public class SalesmanCommissionRecordController extends AbstractController {
    
    @Autowired
    private SalesmanCommissionRecordService commissionRecordService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "佣金记录列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<SalesmanCommissionRecordEntity> page = commissionRecordService.queryPage(params);
        return R.okList( page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:commission:record:info")
    @ApiOperation(value = "佣金记录信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        SalesmanCommissionRecordEntity record = commissionRecordService.getById(id);
        return R.ok().put("record", record);
    }

    /**
     * 佣金统计
     */
    @RequestMapping("/stats")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "佣金统计", notes = "")
    public R stats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        Map<String, Object> stats = commissionRecordService.getCommissionStats(params);
        return R.ok().put("stats", stats);
    }

    /**
     * 待结算记录
     */
    @RequestMapping("/unsettled")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "待结算记录", notes = "")
    public R unsettled(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<SalesmanCommissionRecordEntity> records = commissionRecordService.getUnsettledRecords(params);
        return R.ok().put("records", records);
    }

    /**
     * 佣金汇总
     */
    @RequestMapping("/summary")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "佣金汇总", notes = "")
    public R summary(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<Map<String, Object>> summary = commissionRecordService.getCommissionSummary(params);
        return R.ok().put("summary", summary);
    }

    /**
     * 佣金趋势
     */
    @RequestMapping("/trend")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "佣金趋势", notes = "")
    public R trend(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<Map<String, Object>> trend = commissionRecordService.getCommissionTrend(params);
        return R.ok().put("trend", trend);
    }

    /**
     * 批量更新结算状态
     */
    @RequestMapping("/batchUpdateSettlement")
    @RequiresPermissions("salesman:commission:settlement:update")
    @ApiOperation(value = "批量更新结算状态", notes = "")
    public R batchUpdateSettlement(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> recordIds = (List<Long>) params.get("recordIds");
        Integer settlementStatus = (Integer) params.get("settlementStatus");
        String settlementBatchNo = (String) params.get("settlementBatchNo");
        String settlementRemarks = (String) params.get("settlementRemarks");
        
        int updateCount = commissionRecordService.batchUpdateSettlementStatus(
                recordIds, settlementStatus, settlementBatchNo, settlementRemarks);
        
        return R.ok().put("updateCount", updateCount);
    }

    /**
     * 检查业务记录是否已生成佣金
     */
    @RequestMapping("/checkExists")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "检查佣金记录是否存在", notes = "")
    public R checkExists(@RequestParam String businessType, 
                        @RequestParam Long businessId,
                        @CookieValue String appid) {
        boolean exists = commissionRecordService.existsByBusiness(businessType, businessId, appid);
        return R.ok().put("exists", exists);
    }

    /**
     * 手动生成创建活动佣金
     */
    @RequestMapping("/generateCreateActivityCommission")
    @RequiresPermissions("salesman:commission:record:generate")
    @ApiOperation(value = "生成创建活动佣金", notes = "")
    public R generateCreateActivityCommission(@RequestParam Long salesmanId,
                                             @RequestParam Long rechargeRecordId,
                                             @RequestParam java.math.BigDecimal orderAmount,
                                             @CookieValue String appid) {
        SalesmanCommissionRecordEntity record = commissionRecordService.generateCreateActivityCommission(
                salesmanId, rechargeRecordId, orderAmount, appid);
        
        if (record != null) {
            return R.ok().put("record", record);
        } else {
            return R.error("生成佣金记录失败");
        }
    }

    /**
     * 手动生成充值次数佣金
     */
    @RequestMapping("/generateRechargeCountCommission")
    @RequiresPermissions("salesman:commission:record:generate")
    @ApiOperation(value = "生成充值次数佣金", notes = "")
    public R generateRechargeCountCommission(@RequestParam Long salesmanId,
                                            @RequestParam Long rechargeRecordId,
                                            @RequestParam java.math.BigDecimal orderAmount,
                                            @CookieValue String appid) {
        SalesmanCommissionRecordEntity record = commissionRecordService.generateRechargeCountCommission(
                salesmanId, rechargeRecordId, orderAmount, appid);
        
        if (record != null) {
            return R.ok().put("record", record);
        } else {
            return R.error("生成佣金记录失败");
        }
    }

    /**
     * 手动生成用户转发佣金
     */
    @RequestMapping("/generateUserForwardCommission")
    @RequiresPermissions("salesman:commission:record:generate")
    @ApiOperation(value = "生成用户转发佣金", notes = "")
    public R generateUserForwardCommission(@RequestParam Long salesmanId,
                                          @RequestParam Long usageRecordId,
                                          @CookieValue String appid) {
        SalesmanCommissionRecordEntity record = commissionRecordService.generateUserForwardCommission(
                salesmanId, usageRecordId, appid);
        
        if (record != null) {
            return R.ok().put("record", record);
        } else {
            return R.error("生成佣金记录失败");
        }
    }

    /**
     * 获取结算状态选项
     */
    @RequestMapping("/getSettlementStatuses")
    @RequiresPermissions("salesman:commission:record:list")
    @ApiOperation(value = "获取结算状态选项", notes = "")
    public R getSettlementStatuses() {
        return R.ok().put("settlementStatuses", CommissionEnumUtils.getSettlementStatusOptions());
    }
}
