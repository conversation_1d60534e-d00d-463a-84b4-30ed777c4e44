<template>
  <div class="contact-salesman-page">
    <!-- 头部 -->
    <div class="header">
      <van-nav-bar title="联系业务员" left-text="返回" left-arrow @click-left="$router.go(-1)" />
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <div v-if="!loading">
        <!-- 业务员信息卡片 -->
        <div v-if="salesmanInfo" class="salesman-card">
          <div class="salesman-header">
            <div class="avatar">
              <van-image :src="salesmanAvatar" fit="cover" round width="80" height="80">
                <template v-slot:error>
                  <van-icon name="user-o" size="40" />
                </template>
              </van-image>
            </div>
            <div class="info">
              <div class="name">{{ salesmanInfo.salesmanName }}</div>
              <div class="code">编号：{{ salesmanInfo.salesmanCode }}</div>
              <div class="binding-time">
                绑定时间：{{ formatDate(salesmanInfo.bindingTime) }}
              </div>
            </div>
            <div class="status">
              <van-tag type="success" size="medium">专属业务员</van-tag>
            </div>
          </div>

          <!-- 联系方式列表 -->
          <div class="contact-list">
            <!-- 电话联系 -->
            <div v-if="salesmanInfo.salesmanMobile" class="contact-item" @click="makeCall">
              <div class="contact-icon">
                <van-icon name="phone-o" size="24" color="#1989fa" />
              </div>
              <div class="contact-content">
                <div class="contact-title">电话咨询</div>
                <div class="contact-desc">{{ salesmanInfo.salesmanMobile }}</div>
                <div class="contact-tips">工作时间：9:00-18:00</div>
              </div>
              <div class="contact-action">
                <van-button type="primary" size="small">拨打</van-button>
              </div>
            </div>

            <!-- 微信联系 -->
            <div class="contact-item" @click="contactWechat">
              <div class="contact-icon">
                <van-icon name="chat-o" size="24" color="#07c160" />
              </div>
              <div class="contact-content">
                <div class="contact-title">微信咨询</div>
                <div class="contact-desc">添加微信好友</div>
                <div class="contact-tips">24小时在线服务</div>
              </div>
              <div class="contact-action">
                <van-button type="success" size="small">联系</van-button>
              </div>
            </div>

            <!-- 在线客服 -->
            <div class="contact-item" @click="openOnlineService">
              <div class="contact-icon">
                <van-icon name="service-o" size="24" color="#ff976a" />
              </div>
              <div class="contact-content">
                <div class="contact-title">在线客服</div>
                <div class="contact-desc">即时在线咨询</div>
                <div class="contact-tips">快速响应，专业解答</div>
              </div>
              <div class="contact-action">
                <van-button type="warning" size="small">咨询</van-button>
              </div>
            </div>

            <!-- 邮件联系 -->
            <div class="contact-item" @click="sendEmail">
              <div class="contact-icon">
                <van-icon name="envelop-o" size="24" color="#646566" />
              </div>
              <div class="contact-content">
                <div class="contact-title">邮件咨询</div>
                <div class="contact-desc">发送邮件详细咨询</div>
                <div class="contact-tips">适合复杂问题咨询</div>
              </div>
              <div class="contact-action">
                <van-button plain size="small">发送</van-button>
              </div>
            </div>
          </div>

          <!-- 服务时间说明 -->
          <div class="service-time">
            <div class="time-title">服务时间</div>
            <div class="time-list">
              <div class="time-item">
                <van-icon name="clock-o" />
                <span>工作日：9:00 - 18:00</span>
              </div>
              <div class="time-item">
                <van-icon name="clock-o" />
                <span>周末：10:00 - 17:00</span>
              </div>
              <div class="time-item">
                <van-icon name="chat-o" />
                <span>微信咨询：24小时在线</span>
              </div>
            </div>
          </div>

          <!-- 常见问题 -->
          <div class="faq-section">
            <div class="faq-title">常见问题</div>
            <van-collapse v-model="activeNames">
              <van-collapse-item title="如何更换业务员？" name="1">
                <div class="faq-content">
                  您可以在"我的专属业务员"页面点击"解除绑定"，然后重新绑定新的业务员。
                </div>
              </van-collapse-item>
              <van-collapse-item title="业务员服务范围？" name="2">
                <div class="faq-content">
                  专属业务员为您提供产品咨询、技术支持、优惠活动通知、定制化解决方案等全方位服务。
                </div>
              </van-collapse-item>
              <van-collapse-item title="联系不上业务员怎么办？" name="3">
                <div class="faq-content">
                  如果无法联系到您的专属业务员，请拨打客服热线400-123-4567，我们会为您安排其他业务员协助。
                </div>
              </van-collapse-item>
            </van-collapse>
          </div>
        </div>

        <!-- 未绑定状态 -->
        <div v-else class="no-salesman">
          <van-empty image="https://img.yzcdn.cn/vant/custom-empty-image.png" description="您还没有绑定专属业务员">
            <van-button type="primary" @click="goToBind">立即绑定</van-button>
          </van-empty>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-state">
        <van-loading type="spinner" color="#1989fa">加载中...</van-loading>
      </div>
    </div>

    <!-- 联系方式选择弹窗 -->
    <van-action-sheet v-model="contactSheetVisible" :actions="contactActions" cancel-text="取消"
      @select="onContactSelect" />
  </div>
</template>

<script>
export default {
  name: 'ContactSalesman',
  data() {
    return {
      loading: true,
      salesmanInfo: null,
      activeNames: [],
      contactSheetVisible: false,
      contactActions: [
        { name: '复制微信号', value: 'copy' },
        { name: '添加微信好友', value: 'add' },
        { name: '发送名片', value: 'card' }
      ]
    }
  },
  computed: {
    salesmanAvatar() {
      return 'https://img.yzcdn.cn/vant/cat.jpeg'
    }
  },
  mounted() {
    this.getSalesmanContact()
  },
  methods: {
    // 获取业务员联系信息
    async getSalesmanContact() {
      try {
        this.loading = true
        const response = await this.$fly.get('/pyp/web/salesman/binding/getSalesmanContact')
        if (response.code === 200) {
          this.salesmanInfo = response
        }
      } catch (error) {
        if (error.response && error.response.data.code === 500) {
          // 未绑定业务员
          this.salesmanInfo = null
        } else {
          this.$toast('获取联系信息失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 拨打电话
    makeCall() {
      if (this.salesmanInfo && this.salesmanInfo.salesmanMobile) {
        this.$dialog.confirm({
          title: '拨打电话',
          message: `确定要拨打 ${this.salesmanInfo.salesmanMobile} 吗？`,
          confirmButtonText: '拨打',
          cancelButtonText: '取消'
        }).then(() => {
          window.location.href = `tel:${this.salesmanInfo.salesmanMobile}`
        })
      }
    },

    // 联系微信
    contactWechat() {
      this.contactSheetVisible = true
    },

    // 处理联系方式选择
    onContactSelect(action) {
      switch (action.value) {
        case 'copy':
          this.copyWechatId()
          break
        case 'add':
          this.addWechatFriend()
          break
        case 'card':
          this.sendBusinessCard()
          break
      }
    },

    // 复制微信号
    copyWechatId() {
      // 这里应该从后端获取实际的微信号
      const wechatId = 'salesman_wechat_id'
      this.copyToClipboard(wechatId)
      this.$toast.success('微信号已复制')
    },

    // 添加微信好友
    addWechatFriend() {
      this.$toast('请手动搜索微信号添加好友')
    },

    // 发送名片
    sendBusinessCard() {
      this.$toast('名片发送功能开发中')
    },

    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text)
      } else {
        // 兼容性处理
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }
    },

    // 打开在线客服
    openOnlineService() {
      // 这里可以集成在线客服系统
      this.$toast('正在连接在线客服...')
    },

    // 发送邮件
    sendEmail() {
      const email = '<EMAIL>'
      const subject = '咨询问题'
      const body = `您好，我是您的客户，有问题需要咨询。\n\n我的业务员：${this.salesmanInfo.salesmanName}\n业务员编号：${this.salesmanInfo.salesmanCode}\n\n问题描述：\n`

      window.location.href = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    },

    // 前往绑定页面
    goToBind() {
      this.$router.push('/salesman/my-salesman')
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.contact-salesman-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 16px;
}

.salesman-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.salesman-header {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  border-bottom: 1px solid #ebedf0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.avatar {
  margin-right: 16px;
}

.info {
  flex: 1;
}

.name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 6px;
}

.code {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.binding-time {
  font-size: 12px;
  opacity: 0.7;
}

.contact-list {
  padding: 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contact-item:hover {
  background-color: #f7f8fa;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  margin-right: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;
  border-radius: 50%;
}

.contact-content {
  flex: 1;
}

.contact-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.contact-desc {
  font-size: 14px;
  color: #646566;
  margin-bottom: 2px;
}

.contact-tips {
  font-size: 12px;
  color: #969799;
}

.contact-action {
  margin-left: 12px;
}

.service-time {
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
  background-color: #f7f8fa;
}

.time-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 12px;
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #646566;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-item .van-icon {
  margin-right: 8px;
  color: #1989fa;
}

.faq-section {
  padding: 20px;
}

.faq-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 16px;
}

.faq-content {
  padding: 12px 0;
  font-size: 14px;
  color: #646566;
  line-height: 1.6;
}

.no-salesman {
  text-align: center;
  padding: 60px 20px;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
