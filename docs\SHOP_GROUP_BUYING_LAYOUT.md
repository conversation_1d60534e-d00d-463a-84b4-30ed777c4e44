# 我的小店和团购券列表布局实现文档

## 功能概述

在 cms/Index 页面的九宫格下方添加了"我的小店"和"团购券列表"的新布局区域，实现了左侧我的小店卡片和右侧可滑动团购券列表的设计。

## 布局设计

### 整体结构
```
九宫格
├── 我的小店和团购券区域
│   ├── 我的小店卡片 (左侧)
│   └── 团购券列表 (右侧，可滑动)
└── 文件下载区域
```

### 显示条件
- 当 `activityInfo.showMyShop` 或 `activityInfo.showGroupBuying` 为 true 时显示整个区域
- 我的小店卡片：当 `activityInfo.showMyShop` 为 true 时显示
- 团购券列表：当 `activityInfo.showGroupBuying` 为 true 时显示

## 技术实现

### 1. HTML 结构

```vue
<!-- 我的小店和团购券区域 -->
<div class="shop-group-section" v-if="activityInfo.showMyShop || activityInfo.showGroupBuying">
  <div class="shop-group-container">
    <!-- 我的小店卡片 -->
    <div class="my-shop-card" v-if="activityInfo.showMyShop" @click="goMyShop()">
      <div class="shop-icon">
        <img src="https://pyp.yqihua.com/shop/static/icons/myshop.png" alt="我的小店">
      </div>
      <div class="shop-content">
        <div class="shop-title">我的小店</div>
        <div class="shop-desc">商户专属店铺</div>
      </div>
      <div class="shop-arrow">
        <van-icon name="arrow" />
      </div>
    </div>

    <!-- 团购券列表 -->
    <div class="group-buying-section" v-if="activityInfo.showGroupBuying">
      <div class="section-header">
        <div class="section-title">团购券</div>
        <div class="section-more" v-if="groupBuyingCoupons.length > 3" @click="showAllCoupons">
          查看全部 <van-icon name="arrow" />
        </div>
      </div>
      <div class="coupons-container" v-if="groupBuyingCoupons.length > 0">
        <van-swipe :show-indicators="false" :width="280" :loop="false">
          <van-swipe-item v-for="coupon in groupBuyingCoupons" :key="coupon.id">
            <div class="coupon-card" @click="jumpToGroupBuying(coupon)">
              <div class="coupon-image" v-if="coupon.coverImage">
                <img :src="coupon.coverImage" :alt="coupon.couponName">
              </div>
              <div class="coupon-content">
                <div class="coupon-name">{{ coupon.couponName }}</div>
                <div class="coupon-price">
                  <span class="current-price">¥{{ coupon.groupPrice }}</span>
                  <span class="original-price" v-if="coupon.originalPrice">¥{{ coupon.originalPrice }}</span>
                </div>
                <div class="coupon-platform">{{ getPlatformName(coupon.platformType) }}</div>
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="no-coupons" v-else>
        <div class="no-coupons-text">暂无团购券</div>
      </div>
    </div>
  </div>
</div>
```

### 2. 数据管理

```javascript
data() {
  return {
    // 团购券相关数据
    groupBuyingCoupons: [],
  }
}
```

### 3. 核心方法

```javascript
// 获取团购券列表
async getGroupBuyingCoupons() {
  if (!this.activityInfo.showGroupBuying) {
    return;
  }
  
  try {
    const response = await this.$http.get('/pyp/web/groupbuying/coupons', {
      params: {
        activityId: this.activityId
      }
    });

    if (response.data.code === 200) {
      this.groupBuyingCoupons = response.data.coupons || [];
    }
  } catch (error) {
    console.error('获取团购券失败:', error);
  }
},

// 获取平台名称
getPlatformName(platformType) {
  const platformNames = {
    'douyin': '抖音团购',
    'meituan': '美团团购',
    'dianping': '大众点评团购'
  };
  return platformNames[platformType] || platformType;
},

// 显示所有团购券
showAllCoupons() {
  if (this.groupBuyingCoupons.length === 0) {
    vant.Toast("暂无团购券");
    return;
  }

  // 构建选择项
  const actions = this.groupBuyingCoupons.map(coupon => ({
    name: `${coupon.couponName} - ¥${coupon.groupPrice}`,
    coupon: coupon
  }));

  // 显示选择弹窗
  vant.ActionSheet.show({
    title: '选择团购券',
    actions: actions,
    onSelect: (action) => {
      this.jumpToGroupBuying(action.coupon);
    }
  });
}
```

## 样式设计

### 1. 我的小店卡片
- **渐变背景**: 橙色渐变 (#ff6b35 到 #ff8c42)
- **圆角设计**: 12px 圆角
- **阴影效果**: 带有颜色匹配的阴影
- **交互反馈**: 点击时缩放效果
- **图标设计**: 白色圆形背景，半透明效果

### 2. 团购券列表
- **卡片式设计**: 白色背景，圆角边框
- **横向滑动**: 使用 van-swipe 组件
- **图片展示**: 120px 高度的封面图片
- **价格突出**: 橙色当前价格，删除线原价
- **平台标识**: 灰色背景的平台标签

### 3. 响应式适配
- **小屏适配**: 375px 以下屏幕的特殊样式
- **间距调整**: 不同屏幕尺寸的间距优化
- **字体缩放**: 小屏幕下的字体大小调整

## 功能特性

### 1. 我的小店功能
- **一键跳转**: 点击卡片直接跳转到小店
- **视觉突出**: 渐变背景和图标设计
- **状态管理**: 根据配置显示/隐藏

### 2. 团购券列表
- **横向滑动**: 支持左右滑动查看更多团购券
- **卡片展示**: 每个团购券独立卡片
- **封面图片**: 支持团购券封面图片展示
- **价格对比**: 显示原价和团购价对比
- **平台标识**: 显示所属平台（抖音/美团/大众点评）

### 3. 交互体验
- **查看全部**: 超过3个团购券时显示"查看全部"按钮
- **弹窗选择**: 点击"查看全部"显示所有团购券选择弹窗
- **直接跳转**: 点击团购券卡片直接跳转到对应平台
- **空状态**: 无团购券时显示友好提示

## 数据流程

### 1. 初始化流程
1. 页面加载时调用 `getGroupBuyingCoupons()`
2. 检查 `activityInfo.showGroupBuying` 配置
3. 如果开启，请求团购券数据
4. 更新 `groupBuyingCoupons` 数组
5. 页面自动渲染团购券列表

### 2. 用户交互流程
1. **点击我的小店**: 调用 `goMyShop()` 方法跳转
2. **点击团购券**: 调用 `jumpToGroupBuying(coupon)` 方法跳转
3. **查看全部**: 调用 `showAllCoupons()` 显示选择弹窗
4. **选择团购券**: 从弹窗中选择后跳转到对应平台

## 配置说明

### 管理员配置
1. 在活动管理后台开启"是否显示我的小店"
2. 在活动管理后台开启"是否显示团购券"
3. 在团购券管理中添加团购券
4. 设置团购券的封面图片、价格等信息

### 显示逻辑
- 只有开启对应开关的功能才会显示
- 我的小店和团购券可以独立显示
- 团购券列表根据实际数据动态显示

## 技术优化

### 1. 性能优化
- 团购券数据懒加载
- 图片懒加载优化
- 滑动性能优化

### 2. 用户体验
- 加载状态提示
- 错误状态处理
- 空数据友好提示

### 3. 兼容性
- 移动端适配
- 不同屏幕尺寸适配
- 浏览器兼容性处理

## 扩展功能

### 1. 可能的增强
- 团购券收藏功能
- 团购券分享功能
- 团购券搜索功能
- 个性化推荐

### 2. 数据统计
- 我的小店点击统计
- 团购券点击统计
- 用户行为分析

### 3. 营销功能
- 限时团购券
- 新用户专享
- 会员特权团购券
