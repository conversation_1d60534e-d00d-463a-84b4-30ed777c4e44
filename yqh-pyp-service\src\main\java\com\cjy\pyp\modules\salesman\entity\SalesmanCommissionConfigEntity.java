package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务员佣金配置实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Data
@TableName("salesman_commission_config")
@Accessors(chain = true)
public class SalesmanCommissionConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 佣金类型：1-创建活动佣金，2-充值次数佣金，3-用户转发佣金
     */
    private Integer commissionType;

    /**
     * 计算方式：1-固定金额，2-百分比
     */
    private Integer calculationType;

    /**
     * 佣金值（固定金额或百分比）
     */
    private BigDecimal commissionValue;

    /**
     * 最小佣金金额
     */
    private BigDecimal minAmount;

    /**
     * 最大佣金金额
     */
    private BigDecimal maxAmount;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 生效时间
     */
    private Date effectiveDate;

    /**
     * 失效时间
     */
    private Date expiryDate;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    // 关联查询字段
    /**
     * 业务员姓名
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 业务员编号
     */
    @TableField(exist = false)
    private String salesmanCode;

    /**
     * 佣金类型描述
     */
    @TableField(exist = false)
    private String commissionTypeDesc;

    /**
     * 计算方式描述
     */
    @TableField(exist = false)
    private String calculationTypeDesc;
}
