-- 渠道退款权限控制系统数据库变更
-- 版本: V1.0.9
-- 作者: cjy
-- 日期: 2025-01-31

-- 1. 为渠道表添加退款名额相关字段
ALTER TABLE `channel` 
ADD COLUMN `refund_quota` int(11) DEFAULT 0 COMMENT '退款名额总数' AFTER `commission_rate`,
ADD COLUMN `refund_quota_used` int(11) DEFAULT 0 COMMENT '已使用退款名额' AFTER `refund_quota`,
ADD COLUMN `refund_quota_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用退款名额控制：0-禁用，1-启用' AFTER `refund_quota_used`;

-- 2. 为订单表添加退款权限字段
ALTER TABLE `activity_recharge_record` 
ADD COLUMN `refund_eligible` tinyint(1) DEFAULT 0 COMMENT '是否具有退款权限：0-无权限，1-有权限' AFTER `status`,
ADD COLUMN `refund_quota_assigned_time` datetime DEFAULT NULL COMMENT '退款权限分配时间' AFTER `refund_eligible`;

-- 3. 创建退款名额使用记录表
CREATE TABLE `channel_refund_quota_record` (
  `id` bigint(20) NOT NULL COMMENT '记录ID',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_sn` varchar(100) NOT NULL COMMENT '订单号',
  `action_type` tinyint(1) NOT NULL COMMENT '操作类型：1-分配权限，2-释放权限',
  `quota_sequence` int(11) NOT NULL COMMENT '名额序号（在该渠道内的排序）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_quota_sequence` (`channel_id`, `quota_sequence`),
  CONSTRAINT `fk_channel_refund_quota_channel_id` FOREIGN KEY (`channel_id`) REFERENCES `channel` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道退款名额使用记录表';

-- 4. 为相关表添加索引优化查询性能
ALTER TABLE `activity_recharge_record` 
ADD INDEX `idx_refund_eligible` (`refund_eligible`),
ADD INDEX `idx_salesman_status_create` (`salesman_id`, `status`, `create_on`);

ALTER TABLE `channel` 
ADD INDEX `idx_refund_quota_enabled` (`refund_quota_enabled`);

-- 5. 插入默认数据：为现有渠道设置默认退款名额
-- 默认给每个渠道500个退款名额，可以后续通过管理界面调整
UPDATE `channel` SET 
    `refund_quota` = 500,
    `refund_quota_used` = 0,
    `refund_quota_enabled` = 1
WHERE `status` = 1;

-- 6. 创建存储过程：批量更新渠道内订单的退款权限
DELIMITER $$

CREATE PROCEDURE `UpdateChannelRefundPermissions`(IN channelId BIGINT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE orderId BIGINT;
    DECLARE orderSn VARCHAR(100);
    DECLARE quotaCount INT DEFAULT 0;
    DECLARE totalQuota INT;
    
    -- 获取渠道退款名额
    SELECT refund_quota INTO totalQuota FROM channel WHERE id = channelId AND refund_quota_enabled = 1;
    
    IF totalQuota IS NULL OR totalQuota <= 0 THEN
        LEAVE proc_label;
    END IF;
    
    -- 声明游标：获取该渠道内所有已支付且未退款的订单，按创建时间排序
    DECLARE order_cursor CURSOR FOR
        SELECT arr.id, arr.order_sn
        FROM activity_recharge_record arr
        INNER JOIN salesman s ON arr.salesman_id = s.id
        WHERE s.channel_id = channelId
        AND arr.status = 1  -- 已支付
        AND arr.salesman_id IS NOT NULL
        ORDER BY arr.create_on ASC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    proc_label: BEGIN
        -- 先清除该渠道所有订单的退款权限
        UPDATE activity_recharge_record arr
        INNER JOIN salesman s ON arr.salesman_id = s.id
        SET arr.refund_eligible = 0, arr.refund_quota_assigned_time = NULL
        WHERE s.channel_id = channelId;
        
        -- 清除该渠道的名额使用记录
        DELETE FROM channel_refund_quota_record WHERE channel_id = channelId;
        
        -- 重新分配退款权限
        OPEN order_cursor;
        
        read_loop: LOOP
            FETCH order_cursor INTO orderId, orderSn;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            SET quotaCount = quotaCount + 1;
            
            IF quotaCount <= totalQuota THEN
                -- 分配退款权限
                UPDATE activity_recharge_record 
                SET refund_eligible = 1, refund_quota_assigned_time = NOW()
                WHERE id = orderId;
                
                -- 记录名额使用
                INSERT INTO channel_refund_quota_record 
                (id, channel_id, order_id, order_sn, action_type, quota_sequence, create_time, remarks)
                VALUES 
                (FLOOR(RAND() * 9000000000000000000) + 1000000000000000000, channelId, orderId, orderSn, 1, quotaCount, NOW(), '批量分配退款权限');
            END IF;
        END LOOP;
        
        CLOSE order_cursor;
        
        -- 更新渠道已使用名额数
        UPDATE channel SET refund_quota_used = LEAST(quotaCount, totalQuota) WHERE id = channelId;
    END;
END$$

DELIMITER ;

-- 7. 为所有启用的渠道批量分配退款权限
-- 注意：这个操作可能耗时较长，建议在业务低峰期执行
-- 可以通过以下SQL逐个渠道执行：
-- CALL UpdateChannelRefundPermissions(渠道ID);

-- 8. 创建视图：渠道退款权限统计
CREATE OR REPLACE VIEW `v_channel_refund_quota_stats` AS
SELECT 
    c.id as channel_id,
    c.name as channel_name,
    c.code as channel_code,
    c.refund_quota as total_quota,
    c.refund_quota_used as used_quota,
    (c.refund_quota - c.refund_quota_used) as available_quota,
    CASE 
        WHEN c.refund_quota > 0 THEN ROUND((c.refund_quota_used / c.refund_quota) * 100, 2)
        ELSE 0 
    END as usage_percentage,
    c.refund_quota_enabled as quota_enabled,
    COUNT(arr.id) as total_paid_orders,
    COUNT(CASE WHEN arr.refund_eligible = 1 THEN 1 END) as eligible_orders,
    COUNT(CASE WHEN arr.status = 3 THEN 1 END) as refunded_orders
FROM channel c
LEFT JOIN salesman s ON c.id = s.channel_id
LEFT JOIN activity_recharge_record arr ON s.id = arr.salesman_id AND arr.status IN (1, 3)
WHERE c.status = 1
GROUP BY c.id, c.name, c.code, c.refund_quota, c.refund_quota_used, c.refund_quota_enabled;

-- 9. 添加注释说明
ALTER TABLE `channel` COMMENT = '渠道表，包含退款名额控制功能';
ALTER TABLE `activity_recharge_record` COMMENT = '充值记录表，包含退款权限控制字段';
