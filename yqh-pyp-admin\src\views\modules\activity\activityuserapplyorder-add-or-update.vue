<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <el-form-item label="姓名" prop="contact">
        <el-input v-model="dataForm.contact" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="channelList.length > 0" label="报名通道" prop="applyActivityChannelConfigId" :rules="[ { required: true, message: '请选择报名通道' , trigger: 'blur' } ]">
      <el-select v-model="dataForm.applyActivityChannelConfigId" placeholder="报名通道" filterable>
        <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      </el-form-item>
    <el-form-item label="订单状态" prop="status">
      <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
        <el-option v-for="item in orderStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="支付来源" prop="source">
      <el-select v-model="dataForm.source" placeholder="支付来源" filterable>
        <el-option v-for="item in sources" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
      <el-form-item label="备注" prop="remarks">
          <el-input v-model="dataForm.remarks" placeholder="备注" filterable />
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {sources,orderStatus} from '@/data/common'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          applyActivityChannelConfigId: '',
          contact: '',
          mobile: '',
          status: 1,
          source: 0,
          remarks: '',
        },
        sources,
        orderStatus,
        channelList: [],
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '订单状态不能为空', trigger: 'blur' }
          ],
          contact: [
            { required: true, message: '联系人不能为空', trigger: 'blur' }
          ],
          mobile: [
            { required: true, message: '手机不能为空', trigger: 'blur' }
          ],
          source: [
            { required: true, message: '支付来源不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.activityId = id
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.getChannelByActivityId();
          
        })
      },
      getChannelByActivityId() {
        this.$http({
              url: this.$http.adornUrl(`/apply/applyactivitychannelconfig/findByActivityId/${this.dataForm.activityId}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.channelList = data.result;
                console.log(this.channelList)
              }
            })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuserapplyorder/createOrderAdmin`),
              method: 'post',
              data: this.$http.adornData({
                'activityId': this.dataForm.activityId,
                'contact': this.dataForm.contact,
                'applyActivityChannelConfigId': this.dataForm.applyActivityChannelConfigId,
                'status': this.dataForm.status,
                'mobile': this.dataForm.mobile,
                'source': this.dataForm.source,
                'remarks': this.dataForm.remarks,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
