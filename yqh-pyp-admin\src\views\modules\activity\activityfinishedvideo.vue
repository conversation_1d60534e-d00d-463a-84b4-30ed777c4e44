<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="视频名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.platform" placeholder="选择平台" clearable>
          <el-option
            v-for="platform in platformOptions"
            :key="platform.code"
            :label="platform.name"
            :value="platform.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.mediaType" placeholder="媒体类型" clearable>
          <el-option label="视频" value="video"></el-option>
          <el-option label="图片" value="image"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activityvideo:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityvideo:save')" type="success"
          @click="showGenerateDialog = true">智能生成</el-button>
        <el-button v-if="isAuth('activity:activityvideo:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" width="200" label="视频名称">
      </el-table-column>
      <el-table-column prop="platform" header-align="center" align="center" width="100" label="平台">
        <template slot-scope="scope">
          {{ getPlatformName(scope.row.platform) }}
        </template>
      </el-table-column>
      <el-table-column prop="mediaType" header-align="center" align="center" width="100" label="类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.mediaType === 'video' ? 'primary' : 'success'">
            {{ scope.row.mediaType === 'video' ? '视频' : '图片' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="imageCount" header-align="center" align="center" width="80" label="数量">
        <template slot-scope="scope">
          {{ scope.row.mediaType === 'image' ? (scope.row.imageCount || 1) + '张' : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="fileSize" header-align="center" align="center"  label="文件大小">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column prop="duration" header-align="center" align="center" label="时长">
        <template slot-scope="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column prop="mediaUrl" header-align="center" align="center" width="120" label="内容预览">
        <template slot-scope="scope">
          <div v-if="scope.row.mediaUrl" class="media-preview">
            <el-button type="text" @click="previewContent(scope.row)" class="preview-btn">
              <i :class="scope.row.mediaType === 'video' ? 'el-icon-video-play' : 'el-icon-picture'"></i>
              预览
            </el-button>
          </div>
          <span v-else class="no-media">
            {{ scope.row.mediaType === 'video' ? '无视频' : '无图片' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="180" label="创建时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    
    <!-- 智能生成对话框 -->
    <el-dialog title="智能生成成品" :visible.sync="showGenerateDialog" width="50%" center>
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="选择平台" required>
          <el-select v-model="generateForm.platform" placeholder="请选择平台" @change="onPlatformChange">
            <el-option
              v-for="platform in platformOptions"
              :key="platform.code"
              :label="platform.name"
              :value="platform.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="媒体类型" required>
          <el-radio-group v-model="generateForm.mediaType" @change="onMediaTypeChange">
            <el-radio
              v-for="mediaType in availableMediaTypes"
              :key="mediaType.code"
              :label="mediaType.code">
              <i :class="'el-icon-' + mediaType.icon"></i>
              {{ mediaType.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图片数量" v-if="generateForm.mediaType === 'image'">
          <el-input-number v-model="generateForm.imageCount" :min="1" :max="9" :step="1"></el-input-number>
          <span style="margin-left: 10px; color: #999;">张</span>
        </el-form-item>
        <!-- <el-form-item label="生成名称">
          <el-input v-model="generateForm.name" placeholder="请输入生成内容的名称"></el-input>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showGenerateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleGenerate" :loading="generating">
          {{ generating ? '生成中...' : '开始生成' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 内容预览弹窗 -->
    <el-dialog
      :title="currentPreviewItem ? (currentPreviewItem.mediaType === 'video' ? '视频预览' : '图片预览') : '内容预览'"
      :visible.sync="previewVisible"
      width="80%"
      center
      :before-close="closePreview"
    >
      <div v-if="currentPreviewItem" class="preview-content">
        <!-- 预览头部信息 -->
        <div class="preview-header">
          <h3>{{ currentPreviewItem.name }}</h3>
          <div class="preview-tags">
            <el-tag :type="currentPreviewItem.mediaType === 'video' ? 'primary' : 'success'" size="small">
              {{ currentPreviewItem.mediaType === 'video' ? '视频' : '图片' }}
            </el-tag>
            <el-tag v-if="currentPreviewItem.platform" type="info" size="small">
              {{ getPlatformName(currentPreviewItem.platform) }}
            </el-tag>
            <el-tag v-if="currentPreviewItem.mediaType === 'image' && currentPreviewItem.imageCount" type="warning" size="small">
              {{ currentPreviewItem.imageCount }}张
            </el-tag>
          </div>
        </div>

        <el-row :gutter="20">
          <!-- 左侧：媒体内容 -->
          <el-col :span="14">
            <!-- 视频预览 -->
            <div v-if="currentPreviewItem.mediaType === 'video'" class="video-preview-container">
              <video v-if="currentPreviewItem.mediaUrl" :src="currentPreviewItem.mediaUrl" controls width="100%" height="400">
                您的浏览器不支持视频播放
              </video>
              <div v-else class="no-media-placeholder">
                <i class="el-icon-video-play" style="font-size: 48px; color: #ccc;"></i>
                <p>视频处理中...</p>
              </div>
            </div>

            <!-- 图片预览 -->
            <div v-else class="image-preview-container">
              <div v-if="previewImages.length > 0" class="image-carousel">
                <el-carousel
                  :height="'400px'"
                  indicator-position="outside"
                  :autoplay="false"
                  @change="onImageChange"
                >
                  <el-carousel-item v-for="(image, index) in previewImages" :key="index">
                    <div class="image-item">
                      <img :src="image.mediaUrl" :alt="`图片 ${index + 1}`" class="preview-image" />
                    </div>
                  </el-carousel-item>
                </el-carousel>
                <div v-if="previewImages.length > 1" class="image-counter">
                  {{ currentImageIndex + 1 }} / {{ previewImages.length }}
                </div>
              </div>
              <div v-else-if="currentPreviewItem.mediaUrl" class="single-image">
                <img :src="currentPreviewItem.mediaUrl" alt="预览图片" class="preview-image" />
              </div>
              <div v-else class="no-media-placeholder">
                <i class="el-icon-picture" style="font-size: 48px; color: #ccc;"></i>
                <p>图片处理中...</p>
              </div>
            </div>
          </el-col>

          <!-- 右侧：文案信息 -->
          <el-col :span="10">
            <div class="text-info-panel">
              <div class="panel-header">
                <h4><i class="el-icon-edit"></i> 关联文案</h4>
              </div>

              <div v-if="loadingTextInfo" class="loading-text" v-loading="true" element-loading-text="加载文案信息中...">
                <div style="height: 100px;"></div>
              </div>

              <div v-else-if="currentTextInfo" class="text-content">
                <!-- 标题 -->
                <div v-if="currentTextInfo.name" class="text-item">
                  <div class="text-label">
                    <span>标题</span>
                    <el-button size="mini" type="primary" @click="copyText(currentTextInfo.name)">
                      复制
                    </el-button>
                  </div>
                  <div class="text-value">{{ currentTextInfo.name }}</div>
                </div>

                <!-- 提示词 -->
                <div v-if="currentTextInfo.promptKeyword" class="text-item">
                  <div class="text-label">
                    <span>提示词</span>
                    <el-button size="mini" type="primary" @click="copyText(currentTextInfo.promptKeyword)">
                      复制
                    </el-button>
                  </div>
                  <div class="text-value">{{ currentTextInfo.promptKeyword }}</div>
                </div>

                <!-- 文案内容 -->
                <div v-if="currentTextInfo.content" class="text-item">
                  <div class="text-label">
                    <span>文案内容</span>
                    <el-button size="mini" type="primary" @click="copyText(currentTextInfo.content)">
                      复制
                    </el-button>
                  </div>
                  <div class="text-value content-text">{{ currentTextInfo.content }}</div>
                </div>

                <!-- 话题标签 -->
                <div v-if="currentTextInfo.topics" class="text-item">
                  <div class="text-label">
                    <span>话题标签</span>
                    <el-button size="mini" type="primary" @click="copyText(currentTextInfo.topics)">
                      复制
                    </el-button>
                  </div>
                  <div class="text-value topics-text">{{ currentTextInfo.topics }}</div>
                </div>

                <!-- 一键复制全部 -->
                <div class="copy-all-section">
                  <el-button type="info" size="small" @click="copyAllText" icon="el-icon-document-copy">
                    一键复制全部文案
                  </el-button>
                </div>
              </div>

              <div v-else class="no-text-info">
                <i class="el-icon-warning" style="font-size: 24px; color: #ccc;"></i>
                <p>暂无关联文案</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './activityvideo-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        name: '',
        platform: '',
        mediaType: '',
        appid: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activityId: '',
      previewVisible: false,
      previewVideoUrl: '',

      // 预览相关
      currentPreviewItem: null,
      previewImages: [],
      currentImageIndex: 0,
      currentTextInfo: null,
      loadingTextInfo: false,

      // 平台和生成相关
      platformOptions: [],
      availableMediaTypes: [],
      showGenerateDialog: false,
      generating: false,
      generateForm: {
        platform: 'douyin',
        mediaType: 'video',
        imageCount: 3,
        // name: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.activityId = this.$route.query.activityId || ''
    this.loadPlatformConfigs()
    this.getDataList()
  },
  methods: {
    // 加载平台配置
    loadPlatformConfigs() {
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/platforms'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.platformOptions = data.platforms || []
          this.onPlatformChange(this.generateForm.platform)
        }
      })
    },

    // 平台变化时加载媒体类型
    onPlatformChange(platform) {
      if (!platform) {
        this.availableMediaTypes = []
        return
      }

      this.$http({
        url: this.$http.adornUrl(`/activity/activityvideo/platforms/${platform}/mediaTypes`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.availableMediaTypes = data.mediaTypes || []
          // 如果当前选择的媒体类型不在支持列表中，重置为第一个
          if (this.availableMediaTypes.length > 0) {
            const supportedTypes = this.availableMediaTypes.map(t => t.code)
            if (!supportedTypes.includes(this.generateForm.mediaType)) {
              this.generateForm.mediaType = this.availableMediaTypes[0].code
            }
          }
        }
      })
    },

    // 媒体类型变化
    onMediaTypeChange(mediaType) {
      if (mediaType === 'image') {
        // 根据平台设置默认图片数量
        const platform = this.platformOptions.find(p => p.code === this.generateForm.platform)
        if (platform && platform.defaultImageCount) {
          this.generateForm.imageCount = platform.defaultImageCount
        }
      }
    },

    // 获取平台名称
    getPlatformName(platformCode) {
      const platform = this.platformOptions.find(p => p.code === platformCode)
      return platform ? platform.name : platformCode
    },

    // 处理智能生成
    handleGenerate() {
      if (!this.generateForm.platform) {
        this.$message.error('请选择平台')
        return
      }
      if (!this.generateForm.mediaType) {
        this.$message.error('请选择媒体类型')
        return
      }

      this.generating = true

      // 根据媒体类型调用不同的生成接口
      if (this.generateForm.mediaType === 'video') {
        this.generateVideo()
      } else {
        this.generateImages()
      }
    },

    // 生成视频
    generateVideo() {
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/submitVideoEdit'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: this.activityId,
          platform: this.generateForm.platform,
          mediaType: 'video',
          // name: this.generateForm.name
        })
      }).then(({ data }) => {
        this.generating = false
        if (data && data.code === 200) {
          this.$message.success('视频生成任务已提交')
          this.showGenerateDialog = false
          this.getDataList()
        } else {
          this.$message.error(data.msg || '生成失败')
        }
      }).catch(() => {
        this.generating = false
        this.$message.error('生成失败')
      })
    },

    // 生成图片
    generateImages() {
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/generateImages'),
        method: 'post',
        data: this.$http.adornData({
          activityId: this.activityId,
          platform: this.generateForm.platform,
          mediaType: 'image',
          imageCount: this.generateForm.imageCount,
          // name: this.generateForm.name
        })
      }).then(({ data }) => {
        this.generating = false
        if (data && data.code === 200) {
          this.$message.success('图片生成任务已提交')
          this.showGenerateDialog = false
          this.getDataList()
        } else {
          this.$message.error(data.msg || '生成失败')
        }
      }).catch(() => {
        this.generating = false
        this.$message.error('生成失败')
      })
    },

    generateVideoHandle() {
      // 直接调用接口
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/submitVideoEdit'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: this.activityId,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      // 成品视频固定type为1
      const type = 1

      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.activityId,
          'type': type,
          'platform': this.dataForm.platform,
          'mediaType': this.dataForm.mediaType
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        // 成品视频固定type为1
        const defaultType = 1
        this.$refs.addOrUpdate.init(id, this.activityId, defaultType)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityvideo/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 内容预览
    previewContent(item) {
      this.currentPreviewItem = item
      this.currentTextInfo = null
      this.loadingTextInfo = false
      this.previewImages = []
      this.currentImageIndex = 0

      if (item.mediaType === 'video') {
        // 视频预览
        this.previewVideoUrl = item.mediaUrl
      } else {
        // 图片预览 - 需要获取关联的图片列表
        this.loadPreviewImages(item.id)
      }

      // 加载关联的文案信息
      this.loadTextInfo(item.activityTextId)

      this.previewVisible = true
    },

    // 旧的视频预览方法（保持兼容性）
    previewVideo(url) {
      this.previewVideoUrl = url
      this.previewVisible = true
    },

    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.currentPreviewItem = null
      this.currentTextInfo = null
      this.previewImages = []
      this.currentImageIndex = 0
    },

    // 加载预览图片
    loadPreviewImages(videoId) {
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/images'),
        method: 'get',
        params: this.$http.adornParams({ videoId: videoId })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.previewImages = data.images || []
          this.currentImageIndex = 0

          if (this.previewImages.length === 0) {
            // 如果没有关联图片，使用主图片
            if (this.currentPreviewItem.mediaUrl) {
              this.previewImages = [{
                mediaUrl: this.currentPreviewItem.mediaUrl
              }]
            }
          }
        } else {
          this.$message.error('加载图片失败')
          this.previewImages = []
        }
      }).catch(() => {
        // 使用主图片作为备选
        if (this.currentPreviewItem.mediaUrl) {
          this.previewImages = [{
            mediaUrl: this.currentPreviewItem.mediaUrl
          }]
        } else {
          this.previewImages = []
        }
      })
    },

    // 图片轮播切换
    onImageChange(index) {
      this.currentImageIndex = index
    },

    // 加载文案信息
    loadTextInfo(textId) {
      if (!textId) {
        this.currentTextInfo = null
        return
      }

      this.loadingTextInfo = true

      this.$http({
        url: this.$http.adornUrl('/activity/text/info'),
        method: 'get',
        params: this.$http.adornParams({ textId: textId })
      }).then(({ data }) => {
        this.loadingTextInfo = false
        if (data && data.code === 200) {
          this.currentTextInfo = data.textInfo || null
        } else {
          this.currentTextInfo = null
          console.error('加载文案信息失败:', data.msg)
        }
      }).catch(error => {
        this.loadingTextInfo = false
        this.currentTextInfo = null
        console.error('加载文案信息失败:', error)
      })
    },

    // 复制文本
    copyText(text) {
      if (!text) {
        this.$message.warning('没有可复制的内容')
        return
      }

      // 使用现代浏览器的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('复制成功')
        }).catch(() => {
          this.fallbackCopyText(text)
        })
      } else {
        this.fallbackCopyText(text)
      }
    },

    // 备用复制方法
    fallbackCopyText(text) {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          this.$message.success('复制成功')
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
    },

    // 一键复制全部文案
    copyAllText() {
      if (!this.currentTextInfo) {
        this.$message.warning('没有可复制的文案')
        return
      }

      let allText = ''

      if (this.currentTextInfo.name) {
        allText += `标题：${this.currentTextInfo.name}\n\n`
      }

      if (this.currentTextInfo.promptKeyword) {
        allText += `提示词：${this.currentTextInfo.promptKeyword}\n\n`
      }

      if (this.currentTextInfo.content) {
        allText += `文案内容：${this.currentTextInfo.content}\n\n`
      }

      if (this.currentTextInfo.topics) {
        allText += `话题标签：${this.currentTextInfo.topics}\n\n`
      }

      if (allText.trim()) {
        this.copyText(allText.trim())
      } else {
        this.$message.warning('没有可复制的文案内容')
      }
    },
    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },
    // 格式化时长
    formatDuration(duration) {
      if (!duration) return '-'
      const minutes = Math.floor(duration / 60)
      const seconds = Math.floor(duration % 60)
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
  }
}
</script>

<style>
.media-preview {
  text-align: center;
}

.preview-btn {
  color: #409EFF;
}

.no-media {
  color: #999;
  font-size: 12px;
}

.preview-content {
  .preview-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0 0 10px 0;
      font-size: 18px;
      color: #303133;
    }

    .preview-tags {
      display: flex;
      gap: 8px;
    }
  }
}

.video-preview-container {
  text-align: center;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.image-preview-container {
  .image-carousel {
    position: relative;

    .image-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
      background: #f5f7fa;
      border-radius: 8px;

      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 4px;
      }
    }

    .image-counter {
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      z-index: 10;
    }
  }

  .single-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: #f5f7fa;
    border-radius: 8px;

    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }
}

.no-media-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f5f7fa;
  border-radius: 8px;
  color: #909399;

  p {
    margin: 10px 0 0 0;
    font-size: 14px;
  }
}

.text-info-panel {
  height: 100%;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;

  .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #f5f7fa;
    border-radius: 8px 8px 0 0;

    h4 {
      margin: 0;
      font-size: 16px;
      color: #303133;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .loading-text {
    padding: 20px;
  }

  .text-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;

    .text-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .text-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        span {
          font-size: 14px;
          font-weight: 600;
          color: #606266;
        }
      }

      .text-value {
        font-size: 14px;
        color: #303133;
        line-height: 1.6;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #ebeef5;
        word-break: break-all;

        &.content-text {
          white-space: pre-wrap;
          max-height: 150px;
          overflow-y: auto;
        }

        &.topics-text {
          color: #409eff;
          font-weight: 500;
        }
      }
    }

    .copy-all-section {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      text-align: center;
    }
  }

  .no-text-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;

    p {
      margin: 10px 0 0 0;
      font-size: 14px;
    }
  }
}
</style>
