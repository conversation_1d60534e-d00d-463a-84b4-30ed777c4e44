<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="类型" filterable>
          <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
    </el-form-item>
      <el-form-item label="banner广告" prop="name" v-if="dataForm.type == 1">
          <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                      accept=".jpg, .jpeg, .png, .gif" :on-success="backgroundSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.name" :src="dataForm.name" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
      </el-form-item>
    <el-form-item label="名称" prop="name" v-else>
      <el-input v-model="dataForm.name" placeholder="名称"></el-input>
    </el-form-item>
    <el-form-item label="链接" prop="url">
      <el-input v-model="dataForm.url" placeholder="链接"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
  export default {
    data () {
      return {
        visible: false,
        url: '',
        dataForm: {
          id: 0,
          activityId: '',
          name: '',
          type: '',
          url: ''
        },
        typeList: [
          {id: 0,name: '自定义'},
          {id: 1,name: 'banner广告'},
          {id: 2,name: '文件下载'},
        ],
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '名称不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '0-自定义，1-banner广告，2-文件下载不能为空', trigger: 'blur' }
          ],
          url: [
            { required: true, message: '链接不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.dataForm.id = id || 0
        this.dataForm.activityId = activityId
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activitybottom/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityBottom.activityId
                this.dataForm.name = data.activityBottom.name
                this.dataForm.type = data.activityBottom.type
                this.dataForm.url = data.activityBottom.url
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activitybottom/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'name': this.dataForm.name,
                'type': this.dataForm.type,
                'url': this.dataForm.url
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      // 上传成功（背景）
      backgroundSuccessHandle(response, file, fileList) {
          if (response && response.code === 200) {
              this.dataForm.name = response.url;
              this.$message({
                  message: '上传成功',
                  type: 'success',
              })
          } else {
              this.$message.error(response.msg);
          }
      },
    }
  }
</script>
