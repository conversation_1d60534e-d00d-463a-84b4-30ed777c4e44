<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="公司名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="公司名称"></el-input>
      </el-form-item>
      <el-form-item label="银行卡号" prop="account">
        <el-input v-model="dataForm.account" placeholder="银行卡号"></el-input>
      </el-form-item>
      <el-form-item label="开户行" prop="address">
        <el-input v-model="dataForm.address" placeholder="开户行"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,

        name: '',

        appid: '',

        account: '',

        address: ''
      },
      dataRule: {
        name: [
          { required: true, message: '公司名称不能为空', trigger: 'blur' }
        ],
        appid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        account: [
          { required: true, message: '银行卡号不能为空', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '开户行不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricebank/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.priceBank.name
              this.dataForm.appid = data.priceBank.appid
              this.dataForm.account = data.priceBank.account
              this.dataForm.address = data.priceBank.address
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricebank/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              appid: this.$cookie.get("appid"),
              'account': this.dataForm.account,
              'address': this.dataForm.address
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.result)
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
