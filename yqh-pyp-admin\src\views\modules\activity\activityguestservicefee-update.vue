<template>
  <el-dialog
    :title="'修改劳务费'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="劳务费" prop="serviceFee">
        <el-input v-model="dataForm.serviceFee" placeholder="劳务费"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      visible: false,
      url: "",
      loading: false,
      searchResult: [],
      dataForm: {
        id: 0,
        name: "",
        serviceFee: 0,
        isSave: false,
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        serviceFee: [
          { required: true, message: "劳务费不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.content = "";
      this.dataForm.id = id || 0;
      this.visible = true;
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.serviceFee = data.activityGuest.serviceFee;
              this.dataForm.name = data.activityGuest.name;
            }
          });
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/update`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              isSave: this.dataForm.isSave,
              serviceFee: this.dataForm.serviceFee,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
  },
};
</script>
