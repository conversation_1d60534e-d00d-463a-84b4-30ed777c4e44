<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.SalesmanCommissionRecordDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity" id="salesmanCommissionRecordMap">
        <result property="id" column="id"/>
        <result property="salesmanId" column="salesman_id"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="commissionType" column="commission_type"/>
        <result property="businessType" column="business_type"/>
        <result property="businessId" column="business_id"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="commissionAmount" column="commission_amount"/>
        <result property="calculationType" column="calculation_type"/>
        <result property="settlementStatus" column="settlement_status"/>
        <result property="settlementTime" column="settlement_time"/>
        <result property="settlementBatchNo" column="settlement_batch_no"/>
        <result property="settlementRemarks" column="settlement_remarks"/>
        <result property="businessTime" column="business_time"/>
        <result property="description" column="description"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="queryPage" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity">
        SELECT 
            r.*,
            s.name as salesmanName,
            s.code as salesmanCode,
            wu.nickname as userName,
            a.name as activityName,
            CASE r.commission_type
                WHEN 1 THEN '创建活动佣金'
                WHEN 2 THEN '充值次数佣金'
                WHEN 3 THEN '用户转发佣金'
                ELSE '未知'
            END as commissionTypeDesc,
            CASE r.calculation_type
                WHEN 1 THEN '固定金额'
                WHEN 2 THEN '百分比'
                ELSE '未知'
            END as calculationTypeDesc,
            CASE r.settlement_status
                WHEN 0 THEN '未结算'
                WHEN 1 THEN '已结算'
                WHEN 2 THEN '已取消'
                ELSE '未知'
            END as settlementStatusDesc
        FROM salesman_commission_record r
        LEFT JOIN salesman s ON r.salesman_id = s.id
        LEFT JOIN wx_user wu ON r.user_id = wu.id
        LEFT JOIN tb_activity a ON r.activity_id = a.id
        WHERE 1=1
        <if test="salesmanId != null and salesmanId != ''">
            AND r.salesman_id = #{salesmanId}
        </if>
        <if test="userId != null and userId != ''">
            AND r.user_id = #{userId}
        </if>
        <if test="activityId != null and activityId != '' ">
            AND r.activity_id = #{activityId}
        </if>
        <if test="commissionType != null and commissionType != ''">
            AND r.commission_type = #{commissionType}
        </if>
        <if test="settlementStatus != null and settlementStatus != ''">
            AND r.settlement_status = #{settlementStatus}
        </if>
        <if test="settlementBatchNo != null and settlementBatchNo != ''">
            AND r.settlement_batch_no = #{settlementBatchNo}
        </if>
        <if test="salesmanName != null and salesmanName != ''">
            AND s.name LIKE CONCAT('%', #{salesmanName}, '%')
        </if>
        <if test="startTime != null and startTime != ''">
            AND r.business_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.business_time &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND r.appid = #{appid}
        </if>
        ORDER BY r.create_on DESC
    </select>

    <select id="getCommissionStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalRecords,
            COALESCE(SUM(commission_amount), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN settlement_status = 0 THEN commission_amount ELSE 0 END), 0) as unsettledAmount,
            COALESCE(SUM(CASE WHEN settlement_status = 1 THEN commission_amount ELSE 0 END), 0) as settledAmount,
            COUNT(CASE WHEN settlement_status = 0 THEN 1 END) as unsettledCount,
            COUNT(CASE WHEN settlement_status = 1 THEN 1 END) as settledCount
        FROM salesman_commission_record 
        WHERE 1=1
        <if test="salesmanId != null and salesmanId != ''">
            AND salesman_id = #{salesmanId}
        </if>
        <if test="startTime != null and startTime != ''">
            AND business_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND business_time &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND appid = #{appid}
        </if>
    </select>

    <select id="getUnsettledRecords" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity">
        SELECT 
            r.*,
            s.name as salesmanName,
            s.code as salesmanCode
        FROM salesman_commission_record r
        LEFT JOIN salesman s ON r.salesman_id = s.id
        WHERE r.settlement_status = 0
        <if test="salesmanIds != null and salesmanIds.size() > 0">
            AND r.salesman_id IN
            <foreach collection="salesmanIds" item="salesmanId" open="(" separator="," close=")">
                #{salesmanId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            AND r.business_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.business_time &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND r.appid = #{appid}
        </if>
        ORDER BY r.business_time ASC
    </select>

    <update id="batchUpdateSettlementStatus">
        UPDATE salesman_commission_record 
        SET settlement_status = #{settlementStatus},
            settlement_batch_no = #{settlementBatchNo},
            settlement_time = #{settlementTime},
            settlement_remarks = #{settlementRemarks},
            update_on = NOW()
        WHERE id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </update>

    <select id="existsByBusiness" resultType="boolean">
        SELECT COUNT(*) > 0 FROM salesman_commission_record 
        WHERE business_type = #{businessType} 
        AND business_id = #{businessId}
        AND appid = #{appid}
    </select>

    <select id="getCommissionSummary" resultType="java.util.Map">
        SELECT 
            r.salesman_id,
            s.name as salesmanName,
            s.code as salesmanCode,
            COUNT(*) as recordCount,
            COALESCE(SUM(r.commission_amount), 0) as totalAmount,
            COALESCE(SUM(CASE WHEN r.settlement_status = 0 THEN r.commission_amount ELSE 0 END), 0) as unsettledAmount,
            COALESCE(SUM(CASE WHEN r.settlement_status = 1 THEN r.commission_amount ELSE 0 END), 0) as settledAmount
        FROM salesman_commission_record r
        LEFT JOIN salesman s ON r.salesman_id = s.id
        WHERE 1=1
        <if test="salesmanIds != null and salesmanIds != '' and salesmanIds.size() > 0">
            AND r.salesman_id IN
            <foreach collection="salesmanIds" item="salesmanId" open="(" separator="," close=")">
                #{salesmanId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            AND r.business_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.business_time &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND r.appid = #{appid}
        </if>
        GROUP BY r.salesman_id, s.name, s.code
        ORDER BY totalAmount DESC
    </select>

    <select id="getCommissionTrend" resultType="java.util.Map">
        SELECT 
            DATE(r.business_time) as businessDate,
            COUNT(*) as recordCount,
            COALESCE(SUM(r.commission_amount), 0) as totalAmount,
            COUNT(DISTINCT r.salesman_id) as salesmanCount
        FROM salesman_commission_record r
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND r.business_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.business_time &lt;= #{endTime}
        </if>
        <if test="appid != null and appid != ''">
            AND r.appid = #{appid}
        </if>
        GROUP BY DATE(r.business_time)
        ORDER BY businessDate ASC
    </select>

</mapper>
