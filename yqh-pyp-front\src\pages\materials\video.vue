<template>
  <div class="video-materials">
    <!-- 顶部背景装饰 -->
    <div class="page-header-bg"></div>

    <van-nav-bar title="素材视频" left-text="返回" left-arrow @click-left="$router.go(-1)" class="custom-nav-bar" />

    <!-- 功能区域 - 重新设计 -->
    <div class="function-section">
      <div class="section-content">
        <!-- 页面标题和描述 -->
        <div class="page-intro">
          <h2 class="page-title">素材视频管理</h2>
          <p class="page-desc">管理您的视频素材，支持批量上传和在线预览</p>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button
            type="primary"
            size="large"
            icon="plus"
            @click="showUploadDialog = true"
            class="upload-btn"
          >
            上传素材视频
          </van-button>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filter-bar">
          <van-search
            v-model="searchKeyword"
            placeholder="搜索视频名称..."
            @search="onSearch"
            @clear="onSearch"
            shape="round"
            class="search-input"
          />
          <van-button
            icon="filter-o"
            class="filter-btn"
            @click="showFilter = !showFilter"
          />
        </div>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-list">
      <!-- 统计信息 -->
      <div class="stats-bar" v-if="videoList.length > 0">
        <div class="stats-info">
          <span class="total-count">共 {{ videoList.length }} 个视频</span>
          <!-- <span class="total-size">总大小 {{ getTotalSize() }}</span> -->
        </div>
        <div class="list-actions">
          <van-button size="mini" icon="refresh" @click="onSearch" plain>刷新</van-button>
          <!-- <van-button size="mini" icon="apps-o" @click="toggleViewMode" plain>
            {{ viewMode === 'grid' ? '列表' : '网格' }}
          </van-button> -->
        </div>
      </div>

      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" class="custom-list">
        <div v-for="item in videoList" :key="item.id" class="video-card">
          <div class="card-preview" @click="previewVideo(item)">
            <div class="preview-container">
              <video v-if="item.mediaUrl" :src="item.mediaUrl" preload="metadata" :poster="item.coverUrl"
                class="preview-video" />
              <div v-else class="processing-state">
                <van-loading size="20px" color="#52c41a" />
                <span class="processing-text">处理中</span>
              </div>
              <div class="play-overlay">
                <div class="play-button">
                  <van-icon name="play" size="20" />
                </div>
              </div>
              <div class="video-duration" v-if="item.duration">
                {{ formatDuration(item.duration) }}
              </div>
              <div class="video-status">
                <van-tag size="mini" type="primary">素材</van-tag>
              </div>
            </div>
          </div>
          <div class="card-content">
            <div class="card-header">
              <h4 class="card-title">{{ item.name }}</h4>
              <van-icon name="more-o" @click="showVideoActions(item)" class="more-icon" />
            </div>
            <div class="card-meta">
              <div class="meta-row">
                <span class="meta-item">
                  <van-icon name="description" size="12" />
                  {{ formatFileSize(item.fileSize) }}
                </span>
                <span class="meta-item">
                  <van-icon name="clock-o" size="12" />
                  {{ formatDuration(item.duration) }}
                </span>
              </div>
              <div class="meta-row">
                <span class="meta-item">
                  <van-icon name="eye-o" size="12" />
                  {{ item.useCount || 0 }} 次使用
                </span>
                <!-- <van-tag v-if="item.useCount > 5" size="mini" color="#f39c12">热门</van-tag> -->
              </div>
            </div>
            <div class="card-actions">
              <van-button size="mini" type="primary" @click="previewVideo(item)" :disabled="!item.mediaUrl"
                icon="play-circle-o">
                预览
              </van-button>
              <!-- <van-button size="mini" @click="editVideo(item)" icon="edit">
                编辑
              </van-button> -->
              <van-button size="mini" type="danger" @click="deleteVideo(item)" icon="delete-o">
                删除
              </van-button>
            </div>
          </div>
        </div>
      </van-list>

      <!-- 空状态 -->
      <div v-if="!loading && videoList.length === 0" class="empty-state">
        <div class="empty-content">
          <van-icon name="video-o" size="80" class="empty-icon" />
          <h3 class="empty-title">暂无素材视频</h3>
          <p class="empty-desc">上传您的第一个素材视频，开始创作之旅</p>
          <div class="empty-actions">
            <van-button type="primary" @click="showUploadDialog = true" icon="plus">
              上传素材视频
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传素材视频弹窗 -->
    <van-dialog v-model="showUploadDialog" title="" show-cancel-button @confirm="confirmUpload"
      :confirm-button-loading="uploading" confirm-button-text="开始上传" class="custom-dialog upload-dialog" width="90%">
      <div class="upload-form">
        <div class="dialog-header">
          <div class="header-icon upload-icon">
            <van-icon name="plus" size="32" />
          </div>
          <h3 class="dialog-title">上传素材视频</h3>
          <p class="dialog-subtitle">批量上传您的视频素材文件</p>
        </div>

        <van-field v-model="uploadForm.name" label="批次名称" placeholder="请输入批次名称（可选）" class="custom-field" />

        <div class="upload-section">
          <div class="upload-header">
            <span class="upload-title">选择视频文件</span>
            <span class="upload-count">{{ fileList.length }}/5</span>
          </div>
          <van-uploader v-model="fileList" :max-count="5" :after-read="afterRead" :before-delete="beforeDelete"
            accept="video/*" :max-size="200 * 1024 * 1024" @oversize="onOversize" multiple :preview-size="80"
            upload-text="选择视频" class="custom-uploader" />
        </div>

        <div class="upload-tips">
          <div class="tip-header">
            <van-icon name="info-o" />
            <span>上传须知</span>
          </div>
          <div class="tip-grid">
            <div class="tip-item">
              <van-icon name="video-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">支持格式</span>
                <span class="tip-desc">MP4、AVI、MOV、WMV、FLV</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="description" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">文件大小</span>
                <span class="tip-desc">单个文件不超过200MB</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="apps-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">数量限制</span>
                <span class="tip-desc">最多可选择5个文件</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="star-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">推荐内容</span>
                <span class="tip-desc">高质量的视频素材</span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="uploadProgress.length > 0" class="upload-progress">
          <div class="progress-header">
            <van-icon name="clock-o" />
            <span class="progress-title">上传进度</span>
          </div>
          <div v-for="(item, index) in uploadProgress" :key="index" class="progress-item">
            <div class="progress-info">
              <span class="progress-name">{{ item.name }}</span>
              <span class="progress-status">{{ item.status }}</span>
            </div>
            <van-progress :percentage="item.progress" :color="item.color" stroke-width="6" />
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 视频预览弹窗 -->
    <van-dialog v-model="showPreviewDialog" title="" :show-confirm-button="false" show-cancel-button
      cancel-button-text="关闭" width="95%" class="custom-dialog preview-dialog">
      <div class="preview-container" v-if="previewVideoUrl">
        <div class="preview-header">
          <van-icon name="play-circle-o" size="24" />
          <span class="preview-title">素材视频预览</span>
        </div>
        <div class="video-wrapper">
          <video :src="previewVideoUrl" controls autoplay class="preview-video" controlslist="nodownload" />
        </div>
        <!-- <div class="preview-actions">
          <van-button size="small" icon="download-o">下载</van-button>
          <van-button size="small" icon="share-o">分享</van-button>
          <van-button size="small" icon="edit">编辑</van-button>
        </div> -->
      </div>
    </van-dialog>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script>
export default {
  name: 'VideoMaterials',
  data() {
    return {
      activityId: null,
      fileList: [],
      videoList: [],
      loading: false,
      finished: false,
      page: 1,
      pageSize: 10,
      searchKeyword: '',

      // 弹窗控制
      showUploadDialog: false,
      showPreviewDialog: false,
      previewVideoUrl: '',
      uploading: false,
      showFilter: false,
      viewMode: 'grid', // 'grid' 或 'list'

      // 上传表单
      uploadForm: {
        name: ''
      },

      // 上传进度
      uploadProgress: []
    }
  },
  mounted() {
    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
      }
    }

    if (!this.activityId) {
      this.$toast.fail('活动ID不能为空，请先选择活动')
      this.$router.push({ name: 'index' })
      return
    }
    this.loadVideoList()
  },
  methods: {
    onSearch() {
      this.page = 1
      this.videoList = []
      this.finished = false
      this.loadVideoList()
    },

    onLoad() {
      this.loadVideoList()
    },

    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid'
    },

    // 获取总大小
    getTotalSize() {
      const totalBytes = this.videoList.reduce((sum, item) => sum + (item.fileSize || 0), 0)
      return this.formatFileSize(totalBytes)
    },

    loadVideoList() {
      this.loading = true
      const params = {
        page: this.page,
        limit: this.pageSize,
        activityId: this.activityId,
        type: 0 // 固定为素材视频
      }

      if (this.searchKeyword) {
        params.name = this.searchKeyword
      }

      this.$fly.get('/pyp/web/activity/activityvideo/list', params).then(res => {
        this.loading = false
        if (res.code === 200) {
          const newVideos = res.page.list || []

          if (this.page === 1) {
            this.videoList = newVideos
          } else {
            this.videoList = this.videoList.concat(newVideos)
          }

          this.page++
          this.finished = this.videoList.length >= res.page.totalCount
        } else {
          this.$toast.fail(res.msg || '获取视频列表失败')
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.$toast.fail('获取视频列表失败')
        this.finished = true
      })
    },

    // 文件上传
    afterRead(file) {
      console.log('选择文件:', file)
      // 文件选择后不立即上传，等待用户点击确认
    },

    beforeDelete() {
      return new Promise((resolve) => {
        this.$dialog.confirm({
          title: '确认删除',
          message: '确定要删除这个文件吗？'
        }).then(() => {
          resolve(true)
        }).catch(() => {
          resolve(false)
        })
      })
    },

    onOversize() {
      this.$toast.fail('文件大小不能超过200MB')
    },

    async confirmUpload() {
      if (this.fileList.length === 0) {
        this.$toast.fail('请选择视频文件')
        return
      }

      this.uploading = true
      this.uploadProgress = []

      try {
        // 初始化进度
        this.fileList.forEach((file, index) => {
          this.uploadProgress.push({
            name: file.file ? file.file.name : `视频${index + 1}`,
            progress: 0,
            status: '准备上传',
            color: '#1989fa'
          })
        })

        // 批量上传文件
        const uploadPromises = this.fileList.map((file, index) => {
          return this.uploadSingleFile(file, index)
        })

        const results = await Promise.all(uploadPromises)

        // 保存上传结果到数据库
        await this.saveUploadedVideos(results)

        this.$toast.success(`成功上传 ${results.length} 个视频`)
        this.showUploadDialog = false
        this.resetUploadForm()
        this.onSearch()

      } catch (error) {
        console.error('上传失败:', error)
        this.$toast.fail('上传失败，请重试')
      } finally {
        this.uploading = false
      }
    },

    async uploadSingleFile(fileItem, index) {
      return new Promise((resolve, reject) => {
        const file = fileItem.file
        const formData = new FormData()

        // 更新进度状态
        this.uploadProgress[index].status = '上传中'
        this.uploadProgress[index].progress = 10

        formData.append('file', file)

        this.$fly.post('/pyp/web/upload', formData).then(res => {
          if (res && res.code === 200) {
            this.uploadProgress[index].progress = 100
            this.uploadProgress[index].status = '上传成功'
            this.uploadProgress[index].color = '#52c41a'

            resolve({
              url: res.result,
              name: this.uploadForm.name || `视频${index + 1}`,
              fileSize: file.size,
              duration: 0 // 视频时长需要后续处理
            })
          } else {
            throw new Error(res.msg || '上传失败')
          }
        }).catch(error => {
          this.uploadProgress[index].progress = 0
          this.uploadProgress[index].status = '上传失败'
          this.uploadProgress[index].color = '#ff4444'
          reject(error)
        })
      })
    },

    async saveUploadedVideos(uploadResults) {
      const savePromises = uploadResults.map((result) => {
        const params = {
          activityId: this.activityId,
          name: result.name,
          mediaUrl: result.url,
          fileSize: result.fileSize,
          duration: result.duration,
          type: 0, // 素材视频
          useCount: 0
        }

        return this.$fly.post('/pyp/web/activity/activityvideo/save', params)
      })

      await Promise.all(savePromises)
    },

    resetUploadForm() {
      this.uploadForm = { name: '' }
      this.fileList = []
      this.uploadProgress = []
    },

    // 预览视频
    previewVideo(item) {
      if (!item.mediaUrl) {
        this.$toast.fail('视频还在处理中，请稍后再试')
        return
      }

      this.previewVideoUrl = item.mediaUrl
      this.showPreviewDialog = true
    },

    editVideo(item) {
      this.$toast('编辑视频功能待开发')
      console.log('编辑视频:', item)
    },

    // 显示视频操作菜单
    showVideoActions(item) {
      this.$actionSheet({
        actions: [
          { name: '预览视频', callback: () => this.previewVideo(item) },
          { name: '编辑视频', callback: () => this.editVideo(item) },
          { name: '删除视频', color: '#ee0a24', callback: () => this.deleteVideo(item) }
        ]
      })
    },

    deleteVideo(item) {
      this.$dialog.confirm({
        title: '确认删除',
        message: '确定要删除这个视频吗？'
      }).then(() => {
        this.$fly.post('/pyp/web/activity/activityvideo/delete', [item.id]).then(res => {
          if (res.code === 200) {
            this.$toast.success('删除成功')
            this.onSearch()
          } else {
            this.$toast.fail(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$toast.fail('删除失败')
        })
      }).catch(() => {
        // 用户取消
      })
    },

    // 工具方法
    getTypeName(type) {
      const typeMap = {
        1: '成品视频',
        0: '素材视频'
      }
      return typeMap[type] || '未知'
    },

    getTypeTag(type) {
      const tagMap = {
        1: 'success',
        0: 'primary'
      }
      return tagMap[type] || 'default'
    },

    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    },

    formatDuration(duration) {
      if (!duration) return '00:00'
      const minutes = Math.floor(duration / 60)
      const seconds = Math.floor(duration % 60)
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  }
}
</script>

<style lang="less" scoped>
// 页面整体样式
.video-materials {
  background: #f8faff;
  min-height: 100vh;
  position: relative;
  padding-bottom: 60px;
}

.page-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  z-index: 0;
}

.safe-area-bottom {
  height: 20px;
}

// 自定义导航栏
.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

// 功能区域样式 - 重新设计
.function-section {
  background: white;
  padding: 12px 8px;
  margin-bottom: 12px;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;

  .section-content {
    .page-intro {
      text-align: center;
      margin-bottom: 24px;

      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-desc {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-bottom: 24px;

      .upload-btn {
        height: 48px;
        padding: 0 32px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border: none;
        box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        flex: 1;

        :deep(.van-search__content) {
          background: #f8faff;
          border-radius: 24px;
          border: 1px solid #e8f4ff;
        }

        :deep(.van-field__control) {
          font-size: 14px;
          color: #333;
        }

        :deep(.van-search__action) {
          display: none;
        }

        :deep(.van-field__left-icon) {
          color: #1890ff;
        }
      }

      .filter-btn {
        width: 36px;
        height: 36px;
        border-radius: 24px;
        background: #f8faff;
        border: 1px solid #e8f4ff;
        color: #1890ff;

        &:hover {
          background: #e8f4ff;
        }
      }
    }
  }
}

// 视频列表样式
.video-list {
  padding: 0 16px;
  position: relative;
  z-index: 10;

  .stats-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .stats-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .total-count {
        font-size: 14px;
        color: #333;
        font-weight: 600;
      }

      .total-size {
        font-size: 12px;
        color: #666;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 8px;
      }
    }

    .list-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .custom-list {
    .van-list__finished-text {
      color: #999;
      font-size: 13px;
      padding: 20px 0;
    }
  }
}

// 视频卡片样式
.video-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .card-preview {
    position: relative;
    cursor: pointer;

    .preview-container {
      position: relative;
      width: 100%;
      height: 200px;
      background: #f8faff;
      border-radius: 16px 16px 0 0;
      overflow: hidden;

      .preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .processing-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #52c41a;
        font-size: 12px;
        gap: 8px;
      }

      .play-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 48px;
        height: 48px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: all 0.3s ease;
      }

      .video-duration {
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
      }

      .video-status {
        position: absolute;
        top: 8px;
        left: 8px;
      }

      &:hover .play-overlay {
        opacity: 1;
      }
    }
  }

  .card-content {
    padding: 16px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0;
        line-height: 1.4;
        flex: 1;
        margin-right: 8px;
      }

      .more-icon {
        color: #999;
        cursor: pointer;
        padding: 4px;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .card-meta {
      margin-bottom: 16px;

      .meta-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #666;

          .van-icon {
            color: #999;
          }
        }
      }
    }

    .card-actions {
      display: flex;
      gap: 8px;

      .van-button {
        flex: 1;
        height: 32px;
        border-radius: 8px;
        font-size: 12px;
      }
    }
  }
}

// 视频预览样式
.video-preview {
  position: relative;
  margin-bottom: 16px;
  cursor: pointer;
  border-radius: 16px;
  overflow: hidden;

  .video-container {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: #f8faff;

    .video-element {
      width: 100%;
      height: 220px;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .no-video {
      height: 220px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);

      .processing-animation {
        margin-bottom: 12px;
      }

      .processing-text {
        margin: 0;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .play-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
      transition: all 0.3s ease;

      .play-button {
        width: 64px;
        height: 64px;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        .van-icon {
          margin-left: 2px;
        }
      }
    }

    .video-duration {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
    }

    &:hover {
      .video-element {
        transform: scale(1.02);
      }

      .play-overlay {
        opacity: 1;

        .play-button {
          transform: scale(1.1);
          background: rgba(82, 196, 26, 0.9);
        }
      }
    }
  }
}

// 视频信息样式
.video-info {
  position: relative;
  z-index: 2;

  .video-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .video-title {
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      color: #333;
      flex: 1;
      margin-right: 12px;
      line-height: 1.4;
    }

    .video-badges {
      display: flex;
      gap: 6px;
      flex-shrink: 0;
      margin-right: 8px;

      .video-tag {
        border-radius: 8px;
        font-weight: 500;
      }
    }

    .header-actions {
      .more-icon {
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.3s ease;
        color: #999;

        &:hover {
          background-color: #f0f0f0;
          color: #666;
        }
      }
    }
  }

  .video-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .meta-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666;
      font-weight: 500;

      .van-icon {
        margin-right: 4px;
        color: #999;
        font-size: 14px;
      }
    }
  }

  .video-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .van-button {
      border-radius: 12px;
      font-weight: 500;
      transition: all 0.3s ease;

      &--primary {
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
        }
      }

      &--danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }
      }

      &:not(.van-button--primary):not(.van-button--danger) {
        background: #f8faff;
        color: #52c41a;
        border: 1px solid #e6f3ff;

        &:hover {
          background: #e6f3ff;
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  padding: 60px 20px;
  text-align: center;

  .empty-content {
    background: white;
    border-radius: 20px;
    padding: 40px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

    .empty-icon {
      color: #ddd;
      margin-bottom: 20px;
    }

    .empty-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .empty-desc {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }

    .empty-actions {
      .van-button {
        border-radius: 12px;
        font-weight: 500;
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
      }
    }
  }
}

/* 弹窗样式 */
.custom-dialog :deep(.van-dialog) {
  border-radius: 20px;
  overflow: hidden;
}

 .custom-dialog  :deep(.van-dialog__content) {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0;
  }

.custom-dialog :deep(.van-dialog__footer) .van-button--primary {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border: none;
  border-radius: 12px;
}

.upload-form {
  padding: 24px;

  .dialog-header {
    text-align: center;
    margin-bottom: 24px;

    .header-icon {
      width: 64px;
      height: 64px;
      border-radius: 20px;
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      color: white;
    }

    .dialog-title {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 700;
      color: #333;
    }

    .dialog-subtitle {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }

  .upload-section {
    margin: 20px 0;

    .upload-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .upload-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .upload-count {
        font-size: 12px;
        color: #999;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 6px;
      }
    }
  }

  .upload-tips {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;

    .tip-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      color: #666;
      font-weight: 600;
      font-size: 14px;
    }

    .tip-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .tip-item {
        display: flex;
        align-items: flex-start;

        .tip-icon {
          margin-right: 8px;
          margin-top: 2px;
          color: #999;
          font-size: 14px;
        }

        .tip-content {
          .tip-title {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
          }

          .tip-desc {
            font-size: 11px;
            color: #999;
          }
        }
      }
    }
  }

  .upload-progress {
    margin-top: 20px;
    padding: 16px;
    background: #f8faff;
    border-radius: 12px;

    .progress-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      color: #52c41a;
      font-weight: 600;
      font-size: 14px;
    }

    .progress-item {
      margin-bottom: 12px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;

        .progress-name {
          font-size: 13px;
          color: #333;
          font-weight: 500;
        }

        .progress-status {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

// 预览弹窗
.preview-dialog {
  .preview-container {
    padding: 20px;

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      color: #333;
      font-weight: 600;

      .van-icon {
        margin-right: 8px;
        color: #52c41a;
      }
    }

    .video-wrapper {
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 16px;
      background: #000;

      .preview-video {
        width: 100%;
        max-height: 400px;
      }
    }

    .preview-actions {
      display: flex;
      gap: 8px;
      justify-content: center;
    }
  }
}
</style>
