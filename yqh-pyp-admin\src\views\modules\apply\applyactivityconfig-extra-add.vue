<template>
  <el-dialog :title="!dataForm.id ? '新增扩展字段配置' : '修改扩展字段配置'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type">
          <el-option v-for="item in applyTypeList" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选项数据" v-if="dataForm.type && (dataForm.type == 1 || dataForm.type == 2)" prop="selectData">
        <el-input v-model="dataForm.selectData" placeholder="选项数据，请使用“,”隔开"></el-input>
      </el-form-item>
      <!-- <el-form-item label="默认数据" prop="defaultValue">
        <el-input v-model="dataForm.defaultValue" placeholder="默认数据"></el-input>
      </el-form-item> -->
      <el-form-item label="字段名称" prop="finalName">
        <el-input v-model="dataForm.finalName" placeholder="字段最终名称"></el-input>
      </el-form-item>
      <el-form-item label="提示文字信息" prop="placeholder">
        <el-input v-model="dataForm.placeholder" placeholder="字段最终名称"></el-input>
      </el-form-item>
      <el-form-item label="是否必填" prop="required">
        <el-select v-model="dataForm.required">
          <el-option v-for="item in requiredList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" :max="100" label="排序"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
  </el-dialog>
</template>

<script>
  export default {
    data() {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          applyConfigId: '',
          applyActivityChannelConfigId: '',
          defaultValue: '',
          selectData: '',
          required: 1,
          type: 0,
          finalName: '',
          placeholder: '',
          extra: '',
          paixu: 0,
        },
        applyTypeList: [
          {id: 0, name: '填空'},
          {id: 1, name: '单选'},
          {id: 2, name: '多选'},
          {id: 3, name: '扩展',disabled: true},
          {id: 4, name: '特殊',disabled: true},
          {id: 5, name: '日期'},
        ],
        requiredList: [{
            id: 0,
            name: '否'
          },
          {
            id: 1,
            name: '是'
          },
        ],
        dataRule: {
          type: [{
            required: true,
            message: '类型不能为空',
            trigger: 'blur'
          }],
          finalName: [{
            required: true,
            message: '字段最终名称不能为空',
            trigger: 'blur'
          }],
          paixu: [{
            required: true,
            message: '排序不能为空',
            trigger: 'blur'
          }],
          selectData: [{
            required: true,
            message: '选择数据不能为空',
            trigger: 'blur'
          }],
          required: [{
            required: true,
            message: '是否必填不能为空',
            trigger: 'blur'
          }],
        }
      }
    },
    methods: {
      init(activityId, applyActivityChannelConfigId, applyConfigId, finalName, id) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.dataForm.activityId = activityId;
          this.dataForm.applyConfigId = applyConfigId;
          this.dataForm.applyActivityChannelConfigId = applyActivityChannelConfigId;
          this.dataForm.finalName = finalName;
          this.dataForm.id = id || 0
          if (this.dataForm.id) {
            // 如果已经开启配置
          } else {
            // 如果还没开启配置，先开启配置
            this.$http({
              url: this.$http.adornUrl(`/apply/applyactivityconfig/save`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'applyConfigId': this.dataForm.applyConfigId,
                'applyActivityChannelConfigId': this.dataForm.applyActivityChannelConfigId,
                'type': 3,
                'finalName': '扩展字段',
                'extra': this.dataForm.extra,
                'paixu': 20,
                'required': this.dataForm.required,
                'defaultValue': this.dataForm.defaultValue,
                'placeholder': this.dataForm.placeholder,
                'selectData': this.dataForm.selectData,
              })
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.dataForm.id = data.result.id;
                this.dataForm.type = 0;
              } else {
                this.$message.error(data.msg)
              }
            })
        
          }
        })
      },
      getApplyActivityConfigInfo() {
        this.$http({
              url: this.$http.adornUrl(`/apply/applyactivityconfig/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.applyActivityConfig.activityId
                this.dataForm.applyConfigId = data.applyActivityConfig.applyConfigId
                this.dataForm.applyActivityChannelConfigId = data.applyActivityConfig.applyActivityChannelConfigId
                this.dataForm.type = data.applyActivityConfig.type
                this.dataForm.finalName = data.applyActivityConfig.finalName
                this.dataForm.extra = data.applyActivityConfig.extra
                this.dataForm.paixu = data.applyActivityConfig.paixu
                this.dataForm.selectData = data.applyActivityConfig.selectData
                this.dataForm.defaultValue = data.applyActivityConfig.defaultValue
                this.dataForm.placeholder = data.applyActivityConfig.placeholder
                this.dataForm.required = data.applyActivityConfig.required
              }
            })
      },
      // 表单提交
      dataFormSubmit() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/apply/applyactivityconfig/configExtra`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'type': this.dataForm.type,
                'finalName': this.dataForm.finalName,
                'extra': this.dataForm.extra,
                'paixu': this.dataForm.paixu,
                'required': this.dataForm.required,
                'defaultValue': this.dataForm.defaultValue,
                'selectData': this.dataForm.selectData,
                'placeholder': this.dataForm.placeholder,
              })
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
