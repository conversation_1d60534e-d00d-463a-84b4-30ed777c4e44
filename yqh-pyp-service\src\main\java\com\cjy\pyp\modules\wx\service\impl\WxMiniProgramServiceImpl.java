package com.cjy.pyp.modules.wx.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cjy.pyp.common.utils.RedisUtils;
import com.cjy.pyp.modules.wx.form.WxMiniProgramSchemeForm;
import com.cjy.pyp.modules.wx.service.WxAccountService;
import com.cjy.pyp.modules.wx.service.WxMiniProgramService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 微信小程序服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class WxMiniProgramServiceImpl implements WxMiniProgramService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WxAccountService wxAccountService;

    @Autowired
    private RedisUtils redisUtils;

    // 缓存access token，避免频繁请求
    private static final ConcurrentHashMap<String, AccessTokenCache> tokenCache = new ConcurrentHashMap<>();
    private static final ReentrantLock tokenLock = new ReentrantLock();

    // WeChat API URLs
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String GENERATE_SCHEME_URL = "https://api.weixin.qq.com/wxa/generatescheme?access_token=%s";

    // Redis缓存相关常量
    private static final String SCHEME_CACHE_PREFIX = "scheme:";
    private static final long SCHEME_CACHE_EXPIRE = 3600; // 1小时过期

    @Override
    public String generateUrlScheme(String appid, WxMiniProgramSchemeForm form) throws WxErrorException {
        try {
            // 提取activityId用于缓存key

            // 如果有activityId，先检查Redis缓存
               String cacheKey  = SCHEME_CACHE_PREFIX + form.getActivityId().toString() + ":" + form.getPath() + ":" + form.getQuery();
                String cachedScheme = redisUtils.get(cacheKey);
                if (StringUtils.hasText(cachedScheme)) {
                    logger.info("从Redis缓存获取URL Scheme: {}", cachedScheme);
                    return cachedScheme;
                }
            

            // 获取access token
            String accessToken = getAccessToken("wxdb9c8640799c307b", "d817397006b0a818a8aff70e98040da4");

            // 构建请求参数
            JSONObject requestBody = buildSchemeRequestBody(form);

            // 调用微信API生成URL Scheme
            String response = callWeChatAPI(String.format(GENERATE_SCHEME_URL, accessToken), requestBody.toJSONString());

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("errcode") && responseJson.getInteger("errcode") != 0) {
                String errorMsg = responseJson.getString("errmsg");
                logger.error("微信API调用失败: errcode={}, errmsg={}", responseJson.getInteger("errcode"), errorMsg);
                throw new WxErrorException("微信API调用失败: " + errorMsg);
            }

            String openlink = responseJson.getString("openlink");
            if (StringUtils.hasText(openlink)) {
                // 如果有activityId，将结果缓存到Redis
                if (StringUtils.hasText(cacheKey)) {
                    redisUtils.set(cacheKey, openlink, SCHEME_CACHE_EXPIRE);
                    logger.info("URL Scheme已缓存到Redis: key={}, value={}", cacheKey, openlink);
                }

                logger.info("成功生成URL Scheme: {}", openlink);
                return openlink;
            } else {
                throw new WxErrorException("微信API返回的URL Scheme为空");
            }

        } catch (WxErrorException e) {
            throw e;
        } catch (Exception e) {
            logger.error("生成URL Scheme异常", e);
            throw new WxErrorException("生成URL Scheme异常: " + e.getMessage());
        }
    }

    @Override
    public String generateShopUrlScheme(String shopAppid, WxMiniProgramSchemeForm form) throws WxErrorException {
        try {
            // 提取activityId用于缓存key
            String cacheKey = SCHEME_CACHE_PREFIX + form.getActivityId().toString() + ":" + form.getPath() + ":" + form.getQuery();
            String cachedScheme = redisUtils.get(cacheKey);
            if (StringUtils.hasText(cachedScheme)) {
                logger.info("从Redis缓存获取小店URL Scheme: {}", cachedScheme);
                return cachedScheme;
            }

            // 注意：这里需要小店小程序的AppSecret，实际使用时需要配置
            // 目前使用通用的获取方式，实际项目中应该根据shopAppid获取对应的AppSecret
            String accessToken = getAccessToken(shopAppid, "your_shop_app_secret_here");

            // 构建请求参数
            JSONObject requestBody = buildSchemeRequestBody(form);

            // 调用微信API生成URL Scheme
            String response = callWeChatAPI(String.format(GENERATE_SCHEME_URL, accessToken), requestBody.toJSONString());

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("errcode") && responseJson.getInteger("errcode") != 0) {
                String errorMsg = responseJson.getString("errmsg");
                logger.error("微信API调用失败: errcode={}, errmsg={}", responseJson.getInteger("errcode"), errorMsg);
                throw new WxErrorException("微信API调用失败: " + errorMsg);
            }

            String openlink = responseJson.getString("openlink");
            if (StringUtils.hasText(openlink)) {
                // 将结果缓存到Redis
                redisUtils.set(cacheKey, openlink, SCHEME_CACHE_EXPIRE);
                logger.info("小店URL Scheme已缓存到Redis: key={}, value={}", cacheKey, openlink);

                logger.info("成功生成小店URL Scheme: {}", openlink);
                return openlink;
            } else {
                throw new WxErrorException("微信API返回的URL Scheme为空");
            }

        } catch (WxErrorException e) {
            throw e;
        } catch (Exception e) {
            logger.error("生成小店URL Scheme异常", e);
            throw new WxErrorException("生成小店URL Scheme异常: " + e.getMessage());
        }
    }

    /**
     * 获取微信小程序access token
     */
    private String getAccessToken(String appId, String appSecret) throws Exception {
        String cacheKey = appId + ":" + appSecret;
        AccessTokenCache cache = tokenCache.get(cacheKey);

        // 检查缓存是否有效
        if (cache != null && !cache.isExpired()) {
            return cache.getAccessToken();
        }

        tokenLock.lock();
        try {
            // 双重检查
            cache = tokenCache.get(cacheKey);
            if (cache != null && !cache.isExpired()) {
                return cache.getAccessToken();
            }

            // 请求新的access token
            String url = String.format(ACCESS_TOKEN_URL, appId, appSecret);
            String response = callWeChatAPI(url, null);

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("errcode")) {
                String errorMsg = responseJson.getString("errmsg");
                logger.error("获取access token失败: errcode={}, errmsg={}", responseJson.getInteger("errcode"), errorMsg);
                throw new WxErrorException("获取access token失败: " + errorMsg);
            }

            String accessToken = responseJson.getString("access_token");
            Integer expiresIn = responseJson.getInteger("expires_in");

            if (StringUtils.hasText(accessToken) && expiresIn != null) {
                // 缓存token，提前5分钟过期以避免边界问题
                long expireTime = System.currentTimeMillis() + (expiresIn - 10);
                tokenCache.put(cacheKey, new AccessTokenCache(accessToken, expireTime));
                logger.info("成功获取access token，有效期: {}秒", expiresIn);
                return accessToken;
            } else {
                throw new WxErrorException("获取access token响应格式异常");
            }
        } finally {
            tokenLock.unlock();
        }
    }

    /**
     * 构建生成URL Scheme的请求体
     */
    private JSONObject buildSchemeRequestBody(WxMiniProgramSchemeForm form) {
        JSONObject requestBody = new JSONObject();

        // 构建jump_wxa对象
        JSONObject jumpWxa = new JSONObject();
        if (StringUtils.hasText(form.getPath())) {
            jumpWxa.put("path", form.getPath());
        }
        if (StringUtils.hasText(form.getQuery())) {
            jumpWxa.put("query", form.getQuery());
        }
        if (StringUtils.hasText(form.getEnvVersion())) {
            jumpWxa.put("env_version", form.getEnvVersion());
        } else {
            jumpWxa.put("env_version", "release"); // 默认体验版
        }
        requestBody.put("jump_wxa", jumpWxa);

        // 设置过期时间
        if (form.getIsExpire() != null && form.getIsExpire()) {
            requestBody.put("is_expire", true);
            if (form.getExpireTime() != null) {
                requestBody.put("expire_time", form.getExpireTime());
            } else {
                // 默认1小时后过期
                requestBody.put("expire_time", System.currentTimeMillis() / 1000 + 3600);
            }
        } else {
            requestBody.put("is_expire", false);
        }

        return requestBody;
    }

    /**
     * 从查询参数中提取activityId
     */
    private String extractActivityId(String query) {
        if (!StringUtils.hasText(query)) {
            return null;
        }

        // 解析查询参数，查找activityId
        String[] params = query.split("&");
        for (String param : params) {
            if (param.startsWith("activityId=")) {
                String activityId = param.substring("activityId=".length());
                // 简单验证activityId是否为数字
                if (activityId.matches("\\d+")) {
                    return activityId;
                }
            }
        }
        return null;
    }

    /**
     * 调用微信API
     */
    private String callWeChatAPI(String apiUrl, String requestBody) throws Exception {
        URL url = new URL(apiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            // 设置请求属性
            connection.setRequestMethod(requestBody != null ? "POST" : "GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000); // 30秒读取超时
            connection.setDoOutput(requestBody != null);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            // 发送请求体
            if (requestBody != null) {
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                    os.flush();
                }
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            if (responseCode >= 200 && responseCode < 300) {
                logger.debug("微信API调用成功: {}", response.toString());
                return response.toString();
            } else {
                logger.error("微信API调用失败: HTTP {}, Response: {}", responseCode, response.toString());
                throw new Exception("微信API调用失败: HTTP " + responseCode);
            }

        } finally {
            connection.disconnect();
        }
    }


    /**
     * Access Token缓存类
     */
    private static class AccessTokenCache {
        private final String accessToken;
        private final long expireTime;

        public AccessTokenCache(String accessToken, long expireTime) {
            this.accessToken = accessToken;
            this.expireTime = expireTime;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }
}
