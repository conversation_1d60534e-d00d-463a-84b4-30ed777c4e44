package com.cjy.pyp.modules.activity.service;

import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;

/**
 * 文案预生成服务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01
 */
public interface ActivityVideoPreGenerateService {
    
    /**
     * 异步预生成文案
     * 在用户生成文案后，自动在后台预生成2条文案
     * 
     * @param originalText 原始生成的文案实体
     * @param userId 用户ID
     */
    void preGenerateVideoAsync(Long activityId, String platform, Long userId);
    
}
