<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="用户ID" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
    </el-form-item>
    <el-form-item label="联系方式" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="联系人" prop="username">
      <el-input v-model="dataForm.username" placeholder="联系人"></el-input>
    </el-form-item>
    <el-form-item label="代理产品" prop="product">
      <el-input v-model="dataForm.product" placeholder="代理产品"></el-input>
    </el-form-item>
    <el-form-item label="状态，0-等待联系，1-已联系" prop="status">
      <el-input v-model="dataForm.status" placeholder="状态，0-等待联系，1-已联系"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          userId: '',
          mobile: '',
          username: '',
          product: '',
          status: ''
        },
        dataRule: {
          userId: [
            { required: true, message: '用户ID不能为空', trigger: 'blur' }
          ],
          mobile: [
            { required: true, message: '联系方式不能为空', trigger: 'blur' }
          ],
          username: [
            { required: true, message: '联系人不能为空', trigger: 'blur' }
          ],
          product: [
            { required: true, message: '代理产品不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态，0-等待联系，1-已联系不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/proxy/proxyapply/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.userId = data.proxyApply.userId
                this.dataForm.mobile = data.proxyApply.mobile
                this.dataForm.username = data.proxyApply.username
                this.dataForm.product = data.proxyApply.product
                this.dataForm.status = data.proxyApply.status
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/proxy/proxyapply/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'userId': this.dataForm.userId,
                'mobile': this.dataForm.mobile,
                'username': this.dataForm.username,
                'product': this.dataForm.product,
                'status': this.dataForm.status
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
