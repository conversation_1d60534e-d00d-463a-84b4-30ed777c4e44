<template>
  <div class="text-materials">
    <van-nav-bar
      title="文案素材"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 功能区域 - 重新设计 -->
    <div class="function-section">
      <div class="section-content">
        <!-- 页面标题和描述 -->
        <div class="page-intro">
          <h2 class="page-title">文案素材管理</h2>
          <p class="page-desc">AI智能生成营销文案，支持多平台适配</p>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button
            type="primary"
            size="large"
            icon="magic"
            @click="showGenerateDialog = true"
            class="generate-btn"
          >
            AI文案生成
          </van-button>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filter-bar">
          <van-search
            v-model="searchKeyword"
            placeholder="搜索文案标题或内容..."
            @search="onSearch"
            @clear="onSearch"
            shape="round"
            class="search-input"
          />
          <van-dropdown-menu class="filter-dropdown">
            <van-dropdown-item v-model="filterType" :options="typeOptions" @change="onSearch">
              <template #title>
                <div class="filter-title">
                  <van-icon name="filter-o" />
                  <span>{{ getTypeName(filterType) || '全部类型' }}</span>
                </div>
              </template>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </div>
    </div>

    <!-- 文案列表 -->
    <div class="text-list">
      <!-- 统计信息 -->
      <div class="stats-bar" v-if="textList.length > 0">
        <div class="stats-info">
          <span class="total-count">共 {{ textList.length }} 条文案</span>
          <span class="total-usage">总使用 {{ getTotalUsage() }} 次</span>
        </div>
        <div class="list-actions">
          <van-button size="mini" icon="refresh" @click="onSearch" plain>刷新</van-button>
        </div>
      </div>

      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        class="custom-list"
      >
        <div
          v-for="item in textList"
          :key="item.id"
          class="text-card"
        >
          <div class="card-header">
            <div class="header-left">
              <h3 class="card-title">{{ item.name || item.title }}</h3>
              <div class="card-badges">
                <van-tag :type="getTypeTag(item.adType)" size="small">{{ getTypeName(item.adType) }}</van-tag>
                <van-tag v-if="item.useCount > 5" size="small" color="#f39c12">热门</van-tag>
              </div>
            </div>
            <div class="header-right">
              <van-icon name="more-o" @click="showActions(item)" class="more-icon" />
            </div>
          </div>

          <div class="card-content">
            <p class="content-text">{{ item.result || item.content }}</p>
          </div>

          <div class="card-meta">
            <div class="meta-info">
              <div class="meta-item">
                <van-icon name="clock-o" size="12" />
                <span>{{ formatDate(item.createOn || item.createTime) }}</span>
              </div>
              <div class="meta-item">
                <van-icon name="eye-o" size="12" />
                <span>使用 {{ item.useCount || 0 }} 次</span>
              </div>
            </div>
          </div>

          <div class="card-actions">
            <van-button size="mini" icon="copy" @click="copyText(item)" type="primary">复制</van-button>
            <van-button size="mini" icon="edit" @click="editText(item)">编辑</van-button>
            <van-button size="mini" icon="delete-o" type="danger" @click="deleteText(item)">删除</van-button>
          </div>
        </div>
      </van-list>

      <!-- 空状态 -->
      <div v-if="!loading && textList.length === 0" class="empty-state">
        <div class="empty-content">
          <van-icon name="description" size="80" class="empty-icon" />
          <h3 class="empty-title">暂无文案素材</h3>
          <p class="empty-desc">使用AI生成您的第一条营销文案</p>
          <div class="empty-actions">
            <van-button type="primary" @click="showGenerateDialog = true" icon="magic">
              AI文案生成
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- AI文案生成弹窗 -->
    <van-dialog
      v-model="showGenerateDialog"
      title=""
      show-cancel-button
      @confirm="generateContent"
      :confirm-button-loading="generating"
      confirm-button-text="生成文案"
      class="custom-dialog generate-dialog"
      width="90%"
    >
      <div class="generate-form">
        <!-- 生成中的loading遮罩 -->
        <div v-if="generating" class="generating-overlay">
          <div class="generating-content">
            <div class="generating-animation">
              <van-loading size="40px" color="#667eea" />
            </div>
            <h4 class="generating-title">AI正在生成文案...</h4>
            <p class="generating-desc">根据您选择的平台特点，智能生成专业文案</p>
            <div class="generating-steps">
              <div class="step-item active">
                <van-icon name="checked" />
                <span>分析平台特点</span>
              </div>
              <div class="step-item active">
                <van-icon name="checked" />
                <span>构建专业提示词</span>
              </div>
              <div class="step-item loading">
                <van-loading size="16px" color="#667eea" />
                <span>AI智能生成中</span>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-header">
          <div class="header-icon generate-icon">
            <van-icon name="magic" size="32" />
          </div>
          <h3 class="dialog-title">AI文案生成</h3>
          <p class="dialog-desc">智能生成营销文案，支持多平台适配</p>
          <div v-if="activityConfig.nameMode || activityConfig.defaultTitle" class="config-tips">
            <van-notice-bar
              left-icon="info-o"
              text="已加载活动默认配置"
              color="#1989fa"
              background="#ecf5ff"
              :scrollable="false"
            />
          </div>
        </div>

        <van-field
          v-model="generateForm.adTypeText"
          label="广告类型"
          placeholder="请选择广告类型"
          readonly
          @click="!generating && (showAdTypePicker = true)"
          required
          class="form-field"
          :class="{ 'field-disabled': generating }"
          right-icon="arrow-down"
        />

        <van-field
          v-model="generateForm.nameModeText"
          label="标题生成"
          placeholder="请选择标题生成方式"
          readonly
          @click="!generating && (showNameModePicker = true)"
          required
          class="form-field"
          :class="{ 'field-disabled': generating }"
          right-icon="arrow-down"
        />

        <van-field
          v-if="generateForm.nameMode === 'manual'"
          v-model="generateForm.manualTitle"
          label="自定义标题"
          :placeholder="activityConfig.defaultName ? `默认：${activityConfig.defaultName}` : '请输入标题（20字以内）'"
          maxlength="20"
          show-word-limit
          required
          class="form-field"
          :class="{ 'field-disabled': generating }"
          :disabled="generating"
        >
          <template #label>
            <span>自定义标题</span>
            <!-- <span v-if="activityConfig.defaultName" class="default-tip">（已加载默认配置）</span> -->
          </template>
        </van-field>

        <van-field
          v-model="generateForm.promptKeyword"
          label="提示词"
          :placeholder="activityConfig.defaultTitle ? `默认：${activityConfig.defaultTitle}` : '请输入产品或服务关键词，如：易企化,碰一碰AI爆店码,获客营销'"
          type="textarea"
          rows="3"
          required
          class="form-field"
          :class="{ 'field-disabled': generating }"
          :disabled="generating"
        >
          <template #label>
            <span>提示词</span>
            <!-- <span v-if="activityConfig.defaultTitle" class="default-tip">（已加载默认配置）</span> -->
          </template>
        </van-field>

        <van-field
          v-model="generateForm.userCustomInput"
          label="自定义补充"
          :placeholder="activityConfig.defaultUserInput ? `默认：${activityConfig.defaultUserInput}` : '可以在这里补充您的想法或特殊要求，比如：突出产品特色、针对特定人群、特定风格等'"
          type="textarea"
          rows="3"
          class="form-field"
          :class="{ 'field-disabled': generating }"
          :disabled="generating"
        >
          <template #label>
            <span>自定义补充</span>
            <!-- <span v-if="activityConfig.defaultUserInput" class="default-tip">（已加载默认配置）</span> -->
          </template>
        </van-field>

        <div class="generate-tips">
          <div class="tips-header">
            <van-icon name="info-o" />
            <span>生成说明</span>
          </div>
          <div class="tips-content">
            <div class="tip-item">
              <span class="tip-label">广告类型：</span>
              <span class="tip-value">默认抖音，支持多平台切换</span>
            </div>
            <div class="tip-item">
              <span class="tip-label">标题生成：</span>
              <span class="tip-value">默认自动生成，可手动输入</span>
            </div>
            <div class="tip-item">
              <span class="tip-label">提示词：</span>
              <span class="tip-value">输入产品关键词，AI会根据平台特点生成专业文案</span>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 创建/编辑文案弹窗 -->
    <van-dialog
      v-model="showCreateDialog"
      title="编辑文案"
      show-cancel-button
      @confirm="saveText"
    >
      <div class="create-form">
        <van-field
          v-model="textForm.name"
          label="标题"
          placeholder="请输入文案标题"
          required
        />
        <van-field
          v-model="textForm.title"
          label="标签"
          placeholder="请输入文案标签"
          required
        />
        <van-field
          v-model="textForm.result"
          label="内容"
          type="textarea"
          placeholder="请输入文案内容"
          rows="8"
          required
        />
      </div>
    </van-dialog>

    <!-- 广告类型选择器 -->
    <van-popup v-model="showAdTypePicker" position="bottom">
      <van-picker
        title="选择广告类型"
        :columns="adTypeColumns"
        show-toolbar
        @confirm="onAdTypeConfirm"
        @cancel="showAdTypePicker = false"
      />
    </van-popup>

    <!-- 标题生成方式选择器 -->
    <van-popup v-model="showNameModePicker" position="bottom">
      <van-picker
        title="选择标题生成方式"
        :columns="nameModeColumns"
        show-toolbar
        @confirm="onNameModeConfirm"
        @cancel="showNameModePicker = false"
      />
    </van-popup>

    <!-- 类型选择器 -->
    <van-popup v-model="showTypePicker" position="bottom">
      <van-picker
        title="选择文案类型"
        :columns="typeColumns"
        show-toolbar
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="onActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script>
export default {
  name: 'TextMaterials',
  data() {
    return {
      activityId: null,
      textList: [],
      loading: false,
      finished: false,
      page: 1,
      pageSize: 10,
      searchKeyword: '',
      filterType: '',

      // 弹窗控制
      showGenerateDialog: false,
      showCreateDialog: false,
      showAdTypePicker: false,
      showNameModePicker: false,
      showTypePicker: false,
      showActionSheet: false,
      generating: false,

      // 操作菜单相关
      actionSheetActions: [],
      currentActionItem: null,

      // 生成表单
      generateForm: {
        adType: 'douyin', // 默认抖音
        adTypeText: '抖音',
        nameMode: 'ai', // 默认自动生成
        nameModeText: '自动生成',
        manualTitle: '',
        promptKeyword: '易企化,碰一碰AI爆店码,获客营销',
        userCustomInput: '' // 用户自定义输入
      },

      // 活动默认配置
      activityConfig: {
        nameMode: 'ai',
        defaultName: '',
        defaultTitle: '',
        defaultUserInput: ''
      },

      // 编辑表单
      textForm: {
        id: null,
        name: '',
        adType: '',
        result: ''
      },

      // 选项数据
      adTypeColumns: [],
      nameModeColumns: [
        { text: '自动生成', value: 'ai' },
        { text: '手动输入', value: 'manual' }
      ],
      typeColumns: [
        { text: '抖音', value: 'douyin' },
        { text: '小红书', value: 'xiaohongshu' },
        { text: '快手', value: 'kuaishou' },
        { text: '大众点评', value: 'dianping' },
        { text: '美团点评', value: 'meituan' },
        { text: '抖音点评', value: 'douyin_review' },
        // { text: '微信朋友圈', value: 'weixin' },
        // { text: '微博', value: 'weibo' },
        // { text: 'B站', value: 'bilibili' },
        // { text: '知乎', value: 'zhihu' },
        // { text: '淘宝', value: 'taobao' },
        // { text: '京东', value: 'jingdong' },
        { text: '通用', value: 'general' }
      ],
      typeOptions: [
        { text: '全部类型', value: '' },
        { text: '抖音', value: 'douyin' },
        { text: '小红书', value: 'xiaohongshu' },
        { text: '快手', value: 'kuaishou' },
        { text: '大众点评', value: 'dianping' },
        { text: '美团点评', value: 'meituan' },
        { text: '抖音点评', value: 'douyin_review' },
        // { text: '微信朋友圈', value: 'weixin' },
        // { text: '微博', value: 'weibo' },
        // { text: 'B站', value: 'bilibili' },
        // { text: '知乎', value: 'zhihu' },
        // { text: '淘宝', value: 'taobao' },
        // { text: '京东', value: 'jingdong' },
        { text: '通用', value: 'general' }
      ]
    }
  },
  mounted() {
    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
      }
    }

    if (!this.activityId) {
      this.$toast.fail('活动ID不能为空，请先选择活动')
      this.$router.push({ name: 'index' })
      return
    }
    this.loadActivityConfig()
    this.loadAdTypeConfigs()
    this.loadTextList()
  },
  methods: {
    onLoad() {
      this.loadTextList()
    },

    onSearch() {
      this.page = 1
      this.textList = []
      this.finished = false
      this.loadTextList()
    },

    // 加载活动配置
    loadActivityConfig() {
      if (!this.activityId) {
        console.warn('活动ID为空，无法加载活动配置')
        return
      }

      this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then(res => {
        if (res.code === 200 && res.activity) {
          const activity = res.activity
          this.activityConfig = {
            nameMode: activity.nameMode || 'ai',
            defaultName: activity.defaultName || '',
            defaultTitle: activity.defaultTitle || '',
            defaultUserInput: activity.defaultUserInput || ''
          }

          // 应用活动配置到生成表单
          this.applyActivityConfig()
        } else {
          console.error('加载活动配置失败:', res.msg)
        }
      }).catch(error => {
        console.error('加载活动配置失败:', error)
      })
    },

    // 应用活动配置到生成表单
    applyActivityConfig() {
      // 设置标题生成模式
      this.generateForm.nameMode = this.activityConfig.nameMode
      this.generateForm.nameModeText = this.activityConfig.nameMode === 'ai' ? '自动生成' : '手动填写'

      // 如果是手动填写模式且有默认标题，则使用默认标题
      if (this.activityConfig.nameMode === 'manual' && this.activityConfig.defaultName) {
        this.generateForm.manualTitle = this.activityConfig.defaultName
      }

      // 如果有默认提示词，则使用默认提示词
      if (this.activityConfig.defaultTitle) {
        this.generateForm.promptKeyword = this.activityConfig.defaultTitle
      }

      // 如果有默认用户输入，则使用默认用户输入
      if (this.activityConfig.defaultUserInput) {
        this.generateForm.userCustomInput = this.activityConfig.defaultUserInput
      }

      console.log('已应用活动配置:', this.activityConfig)
    },

    // 加载广告类型配置
    loadAdTypeConfigs() {
      this.$fly.get('/pyp/web/activity/adtypeconfig/list').then(res => {
        if (res.code === 200) {
          this.adTypeColumns = res.list || []
          // 如果有配置且当前没有选择广告类型，默认选择第一个
          if (this.adTypeColumns.length > 0 && !this.generateForm.adType) {
            this.generateForm.adType = this.adTypeColumns[0].value
            this.generateForm.adTypeText = this.adTypeColumns[0].text
          }
        } else {
          console.error('加载广告类型配置失败:', res.msg)
          // 使用默认配置
          this.adTypeColumns = [
            { text: '抖音', value: 'douyin' },
            { text: '小红书', value: 'xiaohongshu' },
            { text: '通用文案', value: 'general' }
          ]
        }
      }).catch(error => {
        console.error('加载广告类型配置失败:', error)
        // 使用默认配置
        this.adTypeColumns = [
          { text: '抖音', value: 'douyin' },
          { text: '小红书', value: 'xiaohongshu' },
          { text: '通用文案', value: 'general' }
        ]
      })
    },

    // 获取总使用次数
    getTotalUsage() {
      return this.textList.reduce((sum, item) => sum + (item.useCount || 0), 0)
    },





    // 获取广告类型标签文本
    getAdTypeLabel(adType) {
      const option = this.adTypeColumns.find(item => item.value === adType)
      return option ? option.text : '通用文案'
    },

    loadTextList() {
      this.loading = true
      const params = {
        page: this.page,
        limit: this.pageSize,
        activityId: this.activityId
      }

      if (this.searchKeyword) {
        params.name = this.searchKeyword
      }

      if (this.filterType) {
        params.adType = this.filterType
      }

      this.$fly.get('/pyp/web/activity/activitytext/list', params).then(res => {
        this.loading = false
        if (res.code === 200) {
          const newTexts = res.page.list || []

          if (this.page === 1) {
            this.textList = newTexts
          } else {
            this.textList = this.textList.concat(newTexts)
          }

          this.page++
          this.finished = this.textList.length >= res.page.totalCount
        } else {
          this.$toast.fail(res.msg || '获取文案列表失败')
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.$toast.fail('获取文案列表失败')
        this.finished = true
      })
    },

    // AI生成文案
    generateContent() {
      // 验证输入
      if (!this.generateForm.adType) {
        this.$toast.fail('请先选择广告类型')
        return
      }

      if (!this.generateForm.promptKeyword || this.generateForm.promptKeyword.trim() === '') {
        this.$toast.fail('请先输入提示词')
        return
      }

      if (this.generateForm.nameMode === 'manual') {
        if (!this.generateForm.manualTitle || this.generateForm.manualTitle.trim() === '') {
          this.$toast.fail('请先输入标题')
          return
        }
        if (this.generateForm.manualTitle.length > 20) {
          this.$toast.fail('标题不能超过20个字符')
          return
        }
      }

      this.generating = true

      const params = {
        activityId: this.activityId,
        model: 'deepseek-chat',
        nameMode: this.generateForm.nameMode,
        name: this.generateForm.nameMode === 'manual' ? this.generateForm.manualTitle.trim() : null,
        query: this.generateForm.promptKeyword.trim(), // 关键词
        adType: this.generateForm.adType,
        userCustomInput: this.generateForm.userCustomInput.trim() // 用户自定义输入
      }

      this.$fly.post('/pyp/activity/activitytext/generate', params).then(res => {
        this.generating = false
        if (res.code === 200) {
          this.$toast.success('文案生成成功！')
          this.showGenerateDialog = false
          // 重置表单但保持默认值
          this.generateForm = {
            adType: 'douyin',
            adTypeText: '抖音',
            nameMode: 'ai',
            nameModeText: '自动生成',
            manualTitle: '',
            promptKeyword: '',
            userCustomInput: ''
          }
          this.onSearch() // 刷新列表
        } else {
          this.$toast.fail(res.msg || '文案生成失败，请重试')
        }
      }).catch((error) => {
        this.generating = false
        console.error('生成文案请求失败:', error)
        this.$toast.fail('网络请求失败，请检查网络连接')
      })
    },

    saveText() {
      if (!this.textForm.name || !this.textForm.result) {
        this.$toast.fail('请填写完整信息')
        return
      }

      const params = {
        ...this.textForm,
        activityId: this.activityId
      }

      const url = this.textForm.id ? '/pyp/web/activity/activitytext/update' : '/pyp/web/activity/activitytext/save'

      this.$fly.post(url, params).then(res => {
        if (res.code === 200) {
          this.$toast.success('保存成功')
          this.showCreateDialog = false
          this.textForm = { id: null, name: '', adType: '',title:'', result: '' }
          this.onSearch()
        } else {
          this.$toast.fail(res.msg || '保存失败')
        }
      }).catch(() => {
        this.$toast.fail('保存失败')
      })
    },

    editText(item) {
      this.textForm = { ...item }
      this.showCreateDialog = true
    },

    copyText(item) {
      const content = item.result || item.content
      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
          this.$toast.success('已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyText(content)
        })
      } else {
        this.fallbackCopyText(content)
      }
    },

    fallbackCopyText(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$toast.success('已复制到剪贴板')
      } catch (err) {
        this.$toast.fail('复制失败')
      }
      document.body.removeChild(textArea)
    },

    deleteText(item) {
      this.$dialog.confirm({
        title: '确认删除',
        message: '确定要删除这个文案吗？'
      }).then(() => {
        this.$fly.post('/pyp/web/activity/activitytext/delete', [item.id]).then(res => {
          if (res.code === 200) {
            this.$toast.success('删除成功')
            this.onSearch()
          } else {
            this.$toast.fail(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$toast.fail('删除失败')
        })
      }).catch(() => {
        // 用户取消
      })
    },

    // 显示操作菜单
    showActions(item) {
      this.currentActionItem = item
      this.actionSheetActions = [
        { name: '复制文案', value: 'copy' },
        { name: '编辑文案', value: 'edit' },
        { name: '删除文案', value: 'delete', color: '#ee0a24' }
      ]
      this.showActionSheet = true
    },

    // 操作菜单选择
    onActionSelect(action) {
      this.showActionSheet = false
      const item = this.currentActionItem

      switch (action.value) {
        case 'copy':
          this.copyText(item)
          break
        case 'edit':
          this.editText(item)
          break
        case 'delete':
          this.deleteText(item)
          break
      }
    },

    // 选择器确认事件
    onAdTypeConfirm(value) {
      this.generateForm.adType = value.value
      this.generateForm.adTypeText = value.text
      this.showAdTypePicker = false
    },

    onNameModeConfirm(value) {
      this.generateForm.nameMode = value.value
      this.generateForm.nameModeText = value.text
      this.showNameModePicker = false
    },

    onTypeConfirm(value) {
      this.textForm.adType = value
      this.showTypePicker = false
    },

    getTypeName(type) {
      const typeMap = {
        'douyin': '抖音',
        'xiaohongshu': '小红书',
        'weixin': '微信朋友圈',
        'weibo': '微博',
        'kuaishou': '快手',
        'dianping': '大众点评',
        'meituan': '美团点评',
        'douyin_review': '抖音点评',
        // 'bilibili': 'B站',
        // 'zhihu': '知乎',
        // 'taobao': '淘宝',
        // 'jingdong': '京东',
        'general': '通用'
      }
      return typeMap[type] || '全部'
    },

    getTypeTag(type) {
      const tagMap = {
        'douyin': 'primary',
        'xiaohongshu': 'danger',
        'weixin': 'success',
        'weibo': 'warning',
        'kuaishou': 'primary',
        'dianping': 'success',
        'meituan': 'warning',
        'douyin_review': 'primary',
        'bilibili': 'primary',
        'zhihu': 'success',
        'taobao': 'warning',
        'jingdong': 'danger',
        'general': 'default'
      }
      return tagMap[type] || 'default'
    },

    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString()
    }
  }
}
</script>

<style lang="less" scoped>
// 页面整体样式
.text-materials {
  background: #f8faff;
  min-height: 100vh;
  position: relative;
  padding-bottom: 60px;
}

// 功能区域样式 - 重新设计
.function-section {
  background: white;
  padding: 24px 16px;
  margin-bottom: 16px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;

  .section-content {
    .page-intro {
      text-align: center;
      margin-bottom: 24px;

      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-desc {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-bottom: 24px;

      .generate-btn {
        height: 40px;
        padding: 0 32px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        flex: 1;

        :deep(.van-search__content) {
          background: #f8faff;
          border-radius: 24px;
          border: 1px solid #e8f4ff;
        }

        :deep(.van-field__control) {
          font-size: 14px;
          color: #333;
        }

        :deep(.van-search__action) {
          display: none;
        }

        :deep(.van-field__left-icon) {
          color: #667eea;
        }
      }

      .filter-dropdown {
        ::v-deep .van-dropdown-menu__bar {
          height: 36px;
        }
        ::v-deep .van-dropdown-item {
          z-index: 99999;
        }
        .filter-title {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          color: #667eea;

          .van-icon {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 文案列表样式
.text-list {
  padding: 0 16px;
  position: relative;
  z-index: 9;

  .stats-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .stats-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .total-count {
        font-size: 14px;
        color: #333;
        font-weight: 600;
      }

      .total-usage {
        font-size: 12px;
        color: #666;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 8px;
      }
    }

    .list-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .custom-list {
    .van-list__finished-text {
      color: #999;
      font-size: 13px;
      padding: 20px 0;
    }
  }
}

// 文案卡片样式
.text-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px 16px 0;

    .header-left {
      flex: 1;

      .card-title {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.4;
      }

      .card-badges {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .header-right {
      margin-left: 12px;

      .more-icon {
        color: #999;
        cursor: pointer;
        padding: 4px;

        &:hover {
          color: #667eea;
        }
      }
    }
  }

  .card-content {
    padding: 0 16px 16px;

    .content-text {
      margin: 0;
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      background: #f8faff;
      padding: 12px;
      border-radius: 8px;
      border-left: 3px solid #667eea;
    }
  }

  .card-meta {
    padding: 0 16px;
    margin-bottom: 16px;

    .meta-info {
      display: flex;
      gap: 16px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #666;

        .van-icon {
          color: #999;
        }
      }
    }
  }

  .card-actions {
    padding: 0 16px 16px;
    display: flex;
    gap: 8px;

    .van-button {
      flex: 1;
      height: 32px;
      border-radius: 8px;
      font-size: 12px;
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-content {
    .empty-icon {
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .empty-title {
      font-size: 18px;
      color: #333;
      margin: 0 0 8px 0;
      font-weight: 600;
    }

    .empty-desc {
      font-size: 14px;
      color: #666;
      margin: 0 0 24px 0;
      line-height: 1.5;
    }

    .empty-actions {
      .van-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 22px;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

// 生成弹窗样式
.generate-dialog {
  :deep(.van-dialog) {
    border-radius: 20px;
    overflow: hidden;
  }
 :deep(.van-dialog__content) {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0;
  }
  .generate-form {
    position: relative;

    // 生成中的loading遮罩
    .generating-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(4px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      border-radius: 12px;

      .generating-content {
        text-align: center;
        padding: 40px 20px;

        .generating-animation {
          margin-bottom: 20px;
        }

        .generating-title {
          font-size: 18px;
          font-weight: 600;
          color: #667eea;
          margin: 0 0 8px 0;
        }

        .generating-desc {
          font-size: 14px;
          color: #666;
          margin: 0 0 24px 0;
          line-height: 1.5;
        }

        .generating-steps {
          display: flex;
          flex-direction: column;
          gap: 12px;
          align-items: flex-start;
          max-width: 200px;
          margin: 0 auto;

          .step-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;

            &.active {
              color: #52c41a;

              .van-icon {
                color: #52c41a;
              }
            }

            &.loading {
              color: #667eea;
            }
          }
        }
      }
    }

    .dialog-header {
      text-align: center;
      padding: 24px 0 20px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 20px;

      .header-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        color: white;
      }

      .dialog-title {
        font-size: 20px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
      }

      .dialog-desc {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .form-field {
      margin-bottom: 20px;

      :deep(.van-field__label) {
        font-weight: 600;
        color: #333;
      }

      &.field-disabled {
        opacity: 0.6;
        pointer-events: none;

        :deep(.van-field__control) {
          color: #999;
        }

        :deep(.van-field__label) {
          color: #999;
        }
      }
    }

    .generate-tips {
      background: #f8faff;
      border-radius: 12px;
      padding: 16px;
      margin-top: 20px;

      .tips-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #667eea;
      }

      .tips-content {
        .tip-item {
          display: flex;
          margin-bottom: 8px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .tip-label {
            color: #666;
            min-width: 60px;
          }

          .tip-value {
            color: #333;
            flex: 1;
          }
        }
      }
    }
  }
}

// 默认配置提示样式
.default-tip {
  font-size: 12px;
  color: #1989fa;
  margin-left: 4px;
}

.config-tips {
  margin-top: 12px;

  :deep(.van-notice-bar) {
    border-radius: 6px;
    font-size: 12px;
  }
}
</style>
