<template>
    <div class="site-wrapper site-page--login">
    <div class="site-content__wrapper">
      <div class="site-content">
        
        <div class="login-main">
          <img  style="width: 100px;" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250612/565465b88b2d453f9e4731e29138bd8e.png" alt="">
          <h1  class=" text-center">易企化AI爆店码</h1>
          <h3 class="login-title text-center">管理员登录</h3>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" status-icon>
            <el-form-item prop="userName">
              <el-input v-model="dataForm.userName" placeholder="帐号"></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="dataForm.password" type="password" placeholder="密码"></el-input>
            </el-form-item>
           
            <el-form-item>
              <el-button class="login-btn-submit" :loading="loading" type="primary" @click="dataFormSubmit()">登录</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUUID } from '@/utils'
export default {
    data() {
        return {
          loading: false,
            dataForm: {
                url: '',
                userName: '',
                password: '',
                uuid: '',
                captcha: ''
            },
            dataRule: {
                userName: [
                    { required: true, message: '帐号不能为空', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '密码不能为空', trigger: 'blur' }
                ],
            },
            captchaPath: ''
        }
    },
    created() {
        this.getCaptcha();
        this.url = location.href;
        console.log(this.url);
    },
    methods: {
        // 提交表单
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                  this.loading =true;
                    this.$http({
                        url: this.$http.adornUrl('/sys/login'),
                        method: 'post',
                        data: this.$http.adornData({
                            'username': this.dataForm.userName,
                            'password': this.dataForm.password,
                            'uuid': this.dataForm.uuid,
                            'captcha': this.dataForm.captcha
                        })
                    }).then(({ data }) => {
                        if (data && data.code === 200) {
                            this.$cookie.set('token', data.token)
                            this.$cookie.set('appid', 'wx6a7f38e0347e6669')
                            this.$router.replace({ name: 'home' })
                        } else {
                            this.getCaptcha()
                            this.$message.error(data.msg)
                        }
                  this.loading =false;
                    })
                }
            })
        },
        // 获取验证码
        getCaptcha() {
            this.dataForm.uuid = getUUID()
            this.captchaPath = this.$http.adornUrl(`/captcha?uuid=${this.dataForm.uuid}`)
        }
    }
}
</script>

<style lang="scss">
.site-wrapper.site-page--login {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(38, 50, 56, .6);
    overflow: hidden;
    &:before {
      position: fixed;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      content: "";
      background-size: cover;
    }
    .site-content__wrapper {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0;
      margin: 0;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: transparent;
    }
    .site-content {
     
    }
    .brand-info {
      margin: 220px 100px 0 90px;
      color: #fff;
    }
    .brand-info__text {
      margin:  0 0 22px 0;
      font-size: 48px;
      font-weight: 400;
      text-transform : uppercase;
    }
    .brand-info__intro {
      margin: 10px 0;
      font-size: 16px;
      line-height: 1.58;
      opacity: .6;
    }
    .login-main {
      padding: 50px 60px 100px;
      width: 470px;
      margin:0 auto;
      min-height: 100%;
      background-color: #fff;
    text-align: center;
    }
    .login-title {
      font-size: 16px;
    }
    .login-captcha {
      overflow: hidden;
      > img {
        width: 100%;
        cursor: pointer;
      }
    }
    .login-btn-submit {
      width: 100%;
      margin-top: 38px;
    }
  }
</style>
