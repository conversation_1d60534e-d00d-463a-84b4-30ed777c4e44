package com.cjy.pyp.modules.channel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.channel.entity.ChannelRefundQuotaRecordEntity;
import com.cjy.pyp.modules.channel.service.ChannelRefundPermissionService;
import com.cjy.pyp.modules.channel.service.ChannelRefundQuotaRecordService;
import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 渠道退款权限服务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
@Service("channelRefundPermissionService")
public class ChannelRefundPermissionServiceImpl implements ChannelRefundPermissionService {

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private ChannelRefundQuotaRecordService channelRefundQuotaRecordService;

    @Override
    public boolean hasRefundPermission(Long orderId) {
        R result = checkRefundPermissionWithDetails(orderId);
        Map<String, Object> data = (Map<String, Object>) result.get("data");
        return data != null && Boolean.TRUE.equals(data.get("hasPermission"));
    }

    @Override
    public R checkRefundPermissionWithDetails(Long orderId) {
        Map<String, Object> result = new HashMap<>();
        result.put("hasPermission", false);
        result.put("reason", "未知错误");

        try {
            // 1. 获取订单信息
            ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(orderId);
            if (order == null) {
                result.put("reason", "订单不存在");
                return R.ok().put("data", result);
            }

            // 2. 检查订单状态
            if (!Integer.valueOf(1).equals(order.getStatus())) {
                result.put("reason", "只有已支付的订单才能申请退款");
                return R.ok().put("data", result);
            }

            // 3. 检查订单是否已有退款权限标记
            if (Integer.valueOf(1).equals(order.getRefundEligible())) {
                result.put("hasPermission", true);
                result.put("reason", "订单具有退款权限");
                result.put("assignedTime", order.getRefundQuotaAssignedTime());
                return R.ok().put("data", result);
            }

            // 4. 获取订单关联的渠道
            Long channelId = getChannelIdByOrderId(orderId);
            if (channelId == null) {
                result.put("reason", "订单未关联到有效渠道，无退款权限");
                return R.ok().put("data", result);
            }

            // 5. 获取渠道信息
            ChannelEntity channel = channelService.getById(channelId);
            if (channel == null) {
                result.put("reason", "关联的渠道不存在");
                return R.ok().put("data", result);
            }

            if (!Integer.valueOf(1).equals(channel.getStatus())) {
                result.put("reason", "关联的渠道已禁用，无退款权限");
                return R.ok().put("data", result);
            }

            if (!Integer.valueOf(1).equals(channel.getRefundQuotaEnabled())) {
                result.put("reason", "渠道未启用退款名额控制，无退款权限");
                return R.ok().put("data", result);
            }

            // 6. 检查渠道退款名额
            boolean hasQuota = checkAndAssignQuotaIfEligible(channelId, orderId, channel.getRefundQuota());
            if (hasQuota) {
                result.put("hasPermission", true);
                result.put("reason", "订单在退款名额范围内，具有退款权限");
                result.put("channelId", channelId);
                result.put("channelName", channel.getName());
            } else {
                result.put("reason", "渠道退款名额已满，该订单无退款权限");
                result.put("channelId", channelId);
                result.put("channelName", channel.getName());
                result.put("totalQuota", channel.getRefundQuota());
            }

            return R.ok().put("data", result);

        } catch (Exception e) {
            result.put("reason", "权限检查失败：" + e.getMessage());
            return R.error().put("data", result);
        }
    }

    /**
     * 检查并分配退款名额（如果符合条件）
     */
    private boolean checkAndAssignQuotaIfEligible(Long channelId, Long orderId, Integer totalQuota) {
        if (totalQuota == null || totalQuota <= 0) {
            return false;
        }

        // 获取该渠道内所有已支付且未退款的订单，按创建时间排序
        List<Map<String, Object>> channelOrders = channelRefundQuotaRecordService.getChannelPaidOrdersForQuotaAssignment(channelId);

        int assignedCount = 0;
        boolean currentOrderFound = false;
        int currentOrderPosition = 0;

        for (Map<String, Object> orderInfo : channelOrders) {
            Long orderIdInList = (Long) orderInfo.get("order_id");
            Integer orderStatus = (Integer) orderInfo.get("status");

            // 跳过已退款的订单（释放名额）
            if (Integer.valueOf(3).equals(orderStatus)) {
                continue;
            }

            assignedCount++;

            if (orderIdInList.equals(orderId)) {
                currentOrderFound = true;
                currentOrderPosition = assignedCount;
            }

            // 如果当前订单在名额范围内，返回true
            if (currentOrderFound && currentOrderPosition <= totalQuota) {
                return true;
            }

            // 如果已经超出名额且还没找到当前订单，说明当前订单肯定不在范围内
            if (assignedCount > totalQuota && !currentOrderFound) {
                return false;
            }
        }

        return currentOrderFound && currentOrderPosition <= totalQuota;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R assignRefundPermission(Long orderId) {
        try {
            // 检查权限
            R checkResult = checkRefundPermissionWithDetails(orderId);
            Map<String, Object> checkData = (Map<String, Object>) checkResult.get("data");

            if (!Boolean.TRUE.equals(checkData.get("hasPermission"))) {
                return R.error((String) checkData.get("reason"));
            }

            // 获取订单和渠道信息
            ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(orderId);
            Long channelId = (Long) checkData.get("channelId");

            // 更新订单退款权限标记
            order.setRefundEligible(1);
            order.setRefundQuotaAssignedTime(new Date());
            activityRechargeRecordService.updateById(order);

            // 计算序号
            Integer quotaSequence = calculateQuotaSequence(channelId, orderId);

            // 记录权限分配
            channelRefundQuotaRecordService.recordQuotaAssignment(
                channelId, orderId, order.getOrderSn(), quotaSequence, "系统自动分配退款权限"
            );

            return R.ok("退款权限分配成功").put("quotaSequence", quotaSequence);

        } catch (Exception e) {
            return R.error("退款权限分配失败：" + e.getMessage());
        }
    }

    /**
     * 计算订单在渠道内的退款权限序号
     */
    private Integer calculateQuotaSequence(Long channelId, Long orderId) {
        List<Map<String, Object>> channelOrders = channelRefundQuotaRecordService.getChannelPaidOrdersForQuotaAssignment(channelId);

        int sequence = 0;
        for (Map<String, Object> orderInfo : channelOrders) {
            Long orderIdInList = (Long) orderInfo.get("order_id");
            Integer orderStatus = (Integer) orderInfo.get("status");

            // 跳过已退款的订单
            if (Integer.valueOf(3).equals(orderStatus)) {
                continue;
            }

            sequence++;

            if (orderIdInList.equals(orderId)) {
                return sequence;
            }
        }

        return sequence;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R releaseRefundPermission(Long orderId) {
        try {
            ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(orderId);
            if (order == null) {
                return R.error("订单不存在");
            }

            Long channelId = getChannelIdByOrderId(orderId);
            if (channelId == null) {
                return R.error("订单未关联到渠道");
            }

            // 更新订单退款权限标记
            order.setRefundEligible(0);
            order.setRefundQuotaAssignedTime(null);
            activityRechargeRecordService.updateById(order);

            // 记录权限释放
            channelRefundQuotaRecordService.recordQuotaRelease(
                channelId, orderId, order.getOrderSn(), "退款完成，释放退款权限"
            );

            return R.ok("退款权限释放成功");

        } catch (Exception e) {
            return R.error("退款权限释放失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getChannelRefundQuotaUsage(Long channelId) {
        return channelRefundQuotaRecordService.getChannelRefundQuotaStats(channelId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R batchUpdateRefundPermissions(Long channelId) {
        try {
            ChannelEntity channel = channelService.getById(channelId);
            if (channel == null) {
                return R.error("渠道不存在");
            }

            if (!Integer.valueOf(1).equals(channel.getRefundQuotaEnabled())) {
                return R.error("渠道未启用退款名额控制");
            }

            // 清除现有权限分配
            channelRefundQuotaRecordService.deleteByChannelId(channelId);

            // 获取渠道内所有已支付订单
            List<Map<String, Object>> channelOrders = channelRefundQuotaRecordService.getChannelPaidOrdersForQuotaAssignment(channelId);

            int totalQuota = channel.getRefundQuota() != null ? channel.getRefundQuota() : 0;
            int assignedCount = 0;

            for (Map<String, Object> orderInfo : channelOrders) {
                Long orderIdInList = (Long) orderInfo.get("order_id");
                Integer orderStatus = (Integer) orderInfo.get("status");

                // 跳过已退款的订单
                if (Integer.valueOf(3).equals(orderStatus)) {
                    continue;
                }

                assignedCount++;

                // 更新订单权限标记
                ActivityRechargeRecordEntity order = new ActivityRechargeRecordEntity();
                order.setId(orderIdInList);

                if (assignedCount <= totalQuota) {
                    order.setRefundEligible(1);
                    order.setRefundQuotaAssignedTime(new Date());

                    // 记录权限分配
                    String orderSn = (String) orderInfo.get("order_sn");
                    channelRefundQuotaRecordService.recordQuotaAssignment(
                        channelId, orderIdInList, orderSn, assignedCount, "批量重新分配退款权限"
                    );
                } else {
                    order.setRefundEligible(0);
                    order.setRefundQuotaAssignedTime(null);
                }

                activityRechargeRecordService.updateById(order);
            }

            // 更新渠道已使用名额
            channel.setRefundQuotaUsed(Math.min(assignedCount, totalQuota));
            channelService.updateById(channel);

            return R.ok("批量更新退款权限成功")
                .put("totalOrders", channelOrders.size())
                .put("assignedCount", Math.min(assignedCount, totalQuota))
                .put("totalQuota", totalQuota);

        } catch (Exception e) {
            return R.error("批量更新退款权限失败：" + e.getMessage());
        }
    }

    @Override
    public Long getChannelIdByOrderId(Long orderId) {
        ActivityRechargeRecordEntity order = activityRechargeRecordService.getById(orderId);
        if (order == null || order.getSalesmanId() == null) {
            return null;
        }

        SalesmanEntity salesman = salesmanService.getById(order.getSalesmanId());
        return salesman != null ? salesman.getChannelId() : null;
    }

    @Override
    public Integer getOrderRefundQuotaSequence(Long orderId) {
        Long channelId = getChannelIdByOrderId(orderId);
        if (channelId == null) {
            return null;
        }
        return channelRefundQuotaRecordService.getOrderQuotaSequence(channelId, orderId);
    }

    @Override
    public boolean isRefundQuotaEnabled(Long channelId) {
        ChannelEntity channel = channelService.getById(channelId);
        return channel != null && Integer.valueOf(1).equals(channel.getRefundQuotaEnabled());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateChannelRefundQuota(Long channelId, Integer refundQuota, Boolean enabled) {
        try {
            ChannelEntity channel = channelService.getById(channelId);
            if (channel == null) {
                return R.error("渠道不存在");
            }

            channel.setRefundQuota(refundQuota);
            channel.setRefundQuotaEnabled(enabled ? 1 : 0);
            channelService.updateById(channel);

            // 如果启用了退款名额控制，重新分配权限
            if (enabled && refundQuota != null && refundQuota > 0) {
                batchUpdateRefundPermissions(channelId);
            }

            return R.ok("渠道退款名额设置更新成功");

        } catch (Exception e) {
            return R.error("渠道退款名额设置更新失败：" + e.getMessage());
        }
    }

    @Override
    public String getRefundPermissionDeniedReason(Long orderId) {
        R result = checkRefundPermissionWithDetails(orderId);
        Map<String, Object> data = (Map<String, Object>) result.get("data");
        return data != null ? (String) data.get("reason") : "未知原因";
    }
}
