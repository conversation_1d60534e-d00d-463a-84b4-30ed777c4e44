<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-card style="background: white" :thumb="guestInfo && guestInfo.avatar
        ? guestInfo.avatar
        : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'
      ">
      <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary" plain>{{
          guestInfo.unit }}</van-tag>
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning" plain>{{
          guestInfo.duties }}</van-tag>
      </div>
    </van-card>
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">接送确认填写</div>
    </div>
    <van-cell-group inset>

      <van-cell center title="是否需要接站/接机">
        <template #right-icon>
          <van-switch v-model="guestInfo.isLinkStart" size="24" />
        </template>
      </van-cell>
      
      <van-field
        v-if="guestInfo.isLinkStart"
        v-model="guestInfo.linkStart"
        name="接站/接机地点"
        label="接站/接机地点"
        placeholder="请输入接站/接机地点"
        :rules="[{ required: true, message: '请输入接站/接机地点' }]"
      />

      <van-cell center title="是否需要送站/送机">
        <template #right-icon>
          <van-switch v-model="guestInfo.isLinkEnd" size="24" />
        </template>
      </van-cell>

      <van-field
        v-if="guestInfo.isLinkEnd" 
        v-model="guestInfo.linkEnd"
        name="送站/送机地点"
        label="送站/送机地点"
        placeholder="请输入送站/送机地点"
        :rules="[{ required: true, message: '请输入送站/送机地点' }]"
      />
    </van-cell-group>
    <div style="margin: 16px">
      <van-button round block type="info" @click="submit" :loading="loading" loading-text="提交中">核对无误，请点击确认</van-button>
    </div>
    <div style="margin: 16px">
      <van-button round block type="primary" @click="
        $router.replace({
          path: '/schedules/expertIndex',
          query: { detailId: id },
        })
        ">返回上一页面</van-button>
    </div>
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: { pcheader },
  data() {
    return {
      activityConfig: {},
      loading: false,
      signVisible: false,
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activeName: ["1", "2", "3"],
      activityId: undefined,
      id: undefined,
      guestInfo: {
        isLinkStart: false,
        isLinkEnd: false,
        linkStart: '',
        linkEnd: ''
      },
      activityInfo: {},
    };
  },
  mounted() {
    // this.activityId = this.$route.query.id;
    this.id = this.$route.query.detailId;
    this.openid = this.$cookie.get("openid");
    this.getActivityList();
  },
  methods: {
    preImage(v) {
      vant.ImagePreview({
        images: [v], // 图片集合
        closeable: true, // 关闭按钮
      });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = "接送确认填写-" + this.guestInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name +
                "-接送确认填写-" +
                this.activityInfo.name,
                this.activityInfo.shareUrl
                  ? this.activityInfo.shareUrl
                  : this.activityInfo.mobileBanner.split(
                    ","
                  )[0],
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name +
                "-接送确认填写-" +
                this.activityInfo.name,
                this.activityInfo.shareUrl
                  ? this.activityInfo.shareUrl
                  : this.activityInfo.mobileBanner.split(
                    ","
                  )[0],
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
        .then((res) => {
          if (res.code == 200) {
            this.guestInfo = res.result;
            this.guestInfo.isLinkStart = this.guestInfo.isLinkStart ? true : false;
            this.guestInfo.isLinkEnd = this.guestInfo.isLinkEnd ? true : false;
            this.activityId = res.result.activityId;
            this.getActivityInfo();
            this.getActivityConfig();
          } else {
            vant.Toast(res.msg);
            this.guestInfo = {};
          }
        });
    },
    getActivityConfig() {
      this.$fly
        .get(`/pyp/web/activity/activityConfig/check`, {
          activityId: this.activityId,
          guestId: this.guestInfo.id,
        })
        .then((res) => {
          if (res.code == 200) {
            this.activityConfig = res.result;
            if (!this.activityConfig.linkStart) {
              this.guestInfo.linkStart = this.activityConfig.linkStart;
            }
            if (!this.activityConfig.linkEnd) {
              this.guestInfo.linkEnd = this.activityConfig.linkEnd;
            }
          } else {
            vant.Toast(res.msg);
            this.activityConfig = {};
          }
        });
    },
    showTask() {
      this.$router.push({
        name: "schedulesExpertDetail",
        query: { detailId: this.id, id: this.activityId },
      });
    },
    cmsTurnBack() {
      if (this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({
          name: "cmsIndex",
          query: { id: this.activityInfo.id },
        });
      }
    },
    submit() {
      if (this.guestInfo.isLinkStart && !this.guestInfo.linkStart) {
        vant.Toast("请输入接站地点");
        return false;
      }
      if (this.guestInfo.isLinkEnd && !this.guestInfo.linkEnd) {
        vant.Toast("请输入送站地点");
        return false;
      }
      this.guestInfo.isLinkStart = this.guestInfo.isLinkStart ? 1 : 0;
      this.guestInfo.isLinkEnd = this.guestInfo.isLinkEnd ? 1 : 0;
      this.guestInfo.isLink = 1;
      this.guestInfo.isLinkTime = date.formatDate.format(
        new Date(),
        "yyyy/MM/dd hh:mm:ss"
      );
      this.loading = true;
      // 保存
      this.$fly
        .post(
          "/pyp/web/activity/activityguest/updateInfo",
          this.guestInfo
        )
        .then((res) => {
          this.loading = false;
          if (res && res.code === 200) {
            vant.Dialog.confirm({
              title: "更新成功",
              message: "点击确定，返回继续完善其他信息",
            })
              .then(() => {
                this.$router.replace({
                  path: "/schedules/expertIndex",
                  query: { detailId: this.id },
                });
              })
              .catch(() => {
                this.getActivityList();
              });
            // 绑定手机
          } else {
            vant.Toast(res.msg);
          }
        });
    },
  },
};
</script>
<style lang="less" scoped>
.transparent {
  /deep/.van-collapse-item__content {
    background: transparent;
  }
}

.content {
  /deep/ img {
    max-width: 100%;
    height: auto;
  }
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.van-cell__title {
  flex: none;
  box-sizing: border-box;
  width: 9em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
  line-height: 33px;
}
/deep/ .van-field__label {
  width: 8em;
}

.van-cell__value {
  text-align: left;
  display: flex;
  align-items: center;
}

.sign {
  position: fixed;
  top: 0;
  background: white;
  height: 100%;

  width: 100%;

  .button {
    text-align: center;
    width: 30%;
    height: 40px;
    line-height: 40px;
    background: #4485ff;
    border-radius: 20px;
    text-align: center;
    color: white;
  }
}

.sign-btns {
  display: flex;
  justify-content: space-between;

  #clear,
  #clear1,
  #save {
    display: inline-block;
    padding: 5px 10px;
    width: 76px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #eee;
    background: #e1e1e1;
    border-radius: 10px;
    text-align: center;
    margin: 20px auto;
    cursor: pointer;
  }
}
</style>
