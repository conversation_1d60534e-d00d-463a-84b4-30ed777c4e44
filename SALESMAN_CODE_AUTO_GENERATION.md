# 业务员编号自动生成功能

## 功能概述

为业务员管理系统添加了编号自动生成功能，支持在创建和编辑业务员时自动生成唯一的业务员编号。

## 编号生成规则

### 格式规范
```
YW + YYYYMMDD + XXXX
```

- **YW**: 固定前缀，表示"业务员"
- **YYYYMMDD**: 当前日期（年月日）
- **XXXX**: 4位随机数字（0000-9999）

### 示例
- `YW202507160001` - 2025年7月16日生成的第一个编号
- `YW202507165678` - 2025年7月16日生成的另一个编号

## 功能特性

### 1. 智能重复检测
- 生成编号后自动检查数据库中是否已存在
- 如果重复，自动重新生成新的编号
- 编辑时排除当前记录，避免误判

### 2. 用户友好界面
- 在编号输入框右侧添加"自动生成"按钮
- 生成过程中显示"生成中"状态
- 生成成功后显示成功提示

### 3. 容错处理
- 网络请求失败时仍使用生成的编号
- 避免因网络问题影响用户操作

## 实现细节

### 前端实现

#### 1. 界面组件
```vue
<el-form-item label="编号" prop="code">
  <el-input v-model="dataForm.code" placeholder="业务员编号">
    <el-button slot="append" @click="generateCode" :loading="generating">
      {{ generating ? '生成中' : '自动生成' }}
    </el-button>
  </el-input>
</el-form-item>
```

#### 2. 生成逻辑
```javascript
generateCode() {
  this.generating = true
  
  // 生成规则：YW + 当前日期(YYYYMMDD) + 4位随机数
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0')
  
  const generatedCode = `YW${year}${month}${day}${random}`
  
  // 检查编号是否重复并处理结果
  // ...
}
```

### 后端实现

#### 1. 控制器接口
```java
@RequestMapping("/checkCode")
@RequiresPermissions("salesman:salesman:list")
@ApiOperation(value = "检查业务员编号", notes = "")
public R checkCode(@RequestParam String code, 
                   @RequestParam(required = false) Long excludeId,
                   @CookieValue String appid) {
    boolean exists = salesmanService.existsByCode(code, appid, excludeId);
    return R.ok().put("exists", exists);
}
```

#### 2. 服务层实现
```java
@Override
public boolean existsByCode(String code, String appid, Long excludeId) {
    QueryWrapper<SalesmanEntity> wrapper = new QueryWrapper<SalesmanEntity>()
        .eq("code", code)
        .eq("appid", appid);
    
    if (excludeId != null) {
        wrapper.ne("id", excludeId);
    }
    
    return this.count(wrapper) > 0;
}
```

## 应用场景

### 1. 普通业务员管理
- 路径：`/salesman/salesman-add-or-update.vue`
- API：`/salesman/salesman/checkCode`
- 适用于系统管理员创建业务员

### 2. 渠道业务员管理
- 路径：`/channel/channel-salesman-add-or-update.vue`
- API：`/channel/salesman/checkCode`
- 适用于渠道管理员创建业务员

## 使用流程

### 创建业务员时
1. 打开业务员创建/编辑表单
2. 点击编号输入框右侧的"自动生成"按钮
3. 系统自动生成编号并检查唯一性
4. 生成成功后编号自动填入输入框
5. 继续填写其他信息并保存

### 编辑业务员时
1. 打开业务员编辑表单
2. 如需更换编号，点击"自动生成"按钮
3. 系统生成新编号（排除当前记录）
4. 保存更新

## 技术优势

### 1. 唯一性保证
- 基于时间戳和随机数的组合
- 数据库层面的重复检测
- 自动重试机制

### 2. 用户体验
- 一键生成，操作简便
- 实时状态反馈
- 容错处理，避免操作中断

### 3. 系统集成
- 与现有业务员管理系统无缝集成
- 支持普通管理和渠道管理两种场景
- 遵循现有的权限控制机制

## 扩展建议

### 1. 编号规则配置化
- 支持在系统配置中自定义编号前缀
- 支持不同渠道使用不同的编号规则
- 支持编号长度和格式的配置

### 2. 批量生成
- 支持批量创建业务员时的编号生成
- 支持编号预分配和保留机制

### 3. 编号回收
- 删除业务员时的编号回收机制
- 编号使用统计和分析

## 注意事项

1. **时区问题**：编号中的日期基于客户端时区，建议统一使用服务器时区
2. **并发处理**：高并发情况下可能出现编号冲突，已通过重试机制解决
3. **数据迁移**：现有业务员数据不受影响，新功能仅对新创建的记录生效
4. **权限控制**：编号检查接口遵循现有的权限控制机制

## 总结

业务员编号自动生成功能提供了便捷、可靠的编号管理方案，既保证了编号的唯一性，又提升了用户操作体验。通过合理的技术架构和容错机制，确保了功能的稳定性和可用性。
