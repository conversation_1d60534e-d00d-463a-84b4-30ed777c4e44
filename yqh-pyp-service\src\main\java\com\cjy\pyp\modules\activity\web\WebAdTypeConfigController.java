package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.AdTypeConfigEntity;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 广告类型配置Web接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("web/activity/adtypeconfig")
public class WebAdTypeConfigController extends AbstractController {
    
    @Autowired
    private AdTypeConfigService adTypeConfigService;

    /**
     * 获取所有启用的广告类型配置（供前端选择器使用）
     */
    @RequestMapping("/list")
    public R list() {
        try {
            List<AdTypeConfigEntity> configs = adTypeConfigService.getEnabledConfigs();
            
            // 转换为前端需要的格式
            List<Object> options = configs.stream().map(config -> {
                return new Object() {
                    public final String text = config.getTypeName();
                    public final String value = config.getTypeCode();
                    public final String platform = config.getPlatform();
                    public final String contentType = config.getContentType();
                };
            }).collect(Collectors.toList());
            
            return R.ok().put("list", options);
        } catch (Exception e) {
            return R.error("获取广告类型配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据类型编码获取配置详情
     */
    @RequestMapping("/info")
    public R info(String typeCode) {
        try {
            AdTypeConfigEntity config = adTypeConfigService.getByTypeCode(typeCode);
            if (config == null) {
                return R.error("未找到对应的广告类型配置");
            }
            return R.ok().put("config", config);
        } catch (Exception e) {
            return R.error("获取广告类型配置详情失败: " + e.getMessage());
        }
    }
}
