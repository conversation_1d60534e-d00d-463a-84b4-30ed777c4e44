<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="排序，数值越小越靠前" prop="orderBy">
      <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
    </el-form-item>
    <el-form-item label="嘉宾ID" prop="activityGuestId">
      <el-input v-model="dataForm.activityGuestId" placeholder="嘉宾ID"></el-input>
    </el-form-item>
    <el-form-item label="主题ID" prop="placeActivityTopicScheduleId">
      <el-input v-model="dataForm.placeActivityTopicScheduleId" placeholder="主题ID"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          orderBy: '',
          activityGuestId: '',
          placeActivityTopicScheduleId: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          orderBy: [
            { required: true, message: '排序，数值越小越靠前不能为空', trigger: 'blur' }
          ],
          activityGuestId: [
            { required: true, message: '嘉宾ID不能为空', trigger: 'blur' }
          ],
          placeActivityTopicScheduleId: [
            { required: true, message: '主题ID不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicscheduleguest/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.placeActivityTopicScheduleGuest.activityId
                this.dataForm.createOn = data.placeActivityTopicScheduleGuest.createOn
                this.dataForm.createBy = data.placeActivityTopicScheduleGuest.createBy
                this.dataForm.updateOn = data.placeActivityTopicScheduleGuest.updateOn
                this.dataForm.updateBy = data.placeActivityTopicScheduleGuest.updateBy
                this.dataForm.orderBy = data.placeActivityTopicScheduleGuest.orderBy
                this.dataForm.activityGuestId = data.placeActivityTopicScheduleGuest.activityGuestId
                this.dataForm.placeActivityTopicScheduleId = data.placeActivityTopicScheduleGuest.placeActivityTopicScheduleId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivitytopicscheduleguest/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'orderBy': this.dataForm.orderBy,
                'activityGuestId': this.dataForm.activityGuestId,
                'placeActivityTopicScheduleId': this.dataForm.placeActivityTopicScheduleId
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
