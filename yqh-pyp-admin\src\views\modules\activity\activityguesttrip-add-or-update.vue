<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : (type == 1 ? '改签' : '修改')" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行程类型" prop="inType">
              <el-select v-model="dataForm.inType" placeholder="行程类型" filterable @change="inTypeChange">
                <el-option v-for="item in guestGoType" :key="item.key" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="行程日期" prop="inDate">
              <el-date-picker v-model="dataForm.inDate" style="width: 100%" type="date" value-format="yyyy/MM/dd"
                placeholder="开始日期"></el-date-picker>
            </el-form-item></el-col>
          <el-col :span="10">
            <el-form-item label="行程出发地点" prop="inStartPlace">
              <el-autocomplete style="width: 100%;" v-model="dataForm.inStartPlace" :fetch-suggestions="search"
                label="name" placeholder="请输入内容" @select="selectResult" :loading="loading">
                <div slot-scope="scope">
                  <span style="float: left">{{ dataForm.inType == 0 ? scope.item.airportName : scope.item.stationName
                    }}</span>
                  <!-- <span style="float: right; color: #8492a6; font-size: 13px">{{ dataForm.type == 0 ?  scope.item.airportName : scope.item.stationName }}</span> -->
                </div>
              </el-autocomplete>
            </el-form-item></el-col>
          <el-col :span="10">
            <el-form-item label="行程到达地点" prop="inEndPlace">
              <el-autocomplete style="width: 100%;" v-model="dataForm.inEndPlace" :fetch-suggestions="endsearch"
                label="name" placeholder="请输入内容" @select="endselectResult" :loading="endloading">
                <div slot-scope="scope">
                  <span style="float: left">{{ dataForm.inType == 0 ? scope.item.airportName : scope.item.stationName
                    }}</span>
                  <!-- <span style="float: right; color: #8492a6; font-size: 13px">{{ dataForm.type == 0 ?  scope.item.airportName : scope.item.stationName }}</span> -->
                </div>
              </el-autocomplete>
            </el-form-item></el-col>
          <el-col :span="2" style="text-align: center;">

            <el-button type="primary" @click="searchReturnResultPlane()">查询</el-button>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行程时间" prop="inDate">
              <el-date-picker v-model="times" @change="dateChange" type="datetimerange"
                value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="出发日期"
                end-placeholder="到达日期"></el-date-picker>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="行程航班/火车号" prop="inNumber">
              <el-input v-model="dataForm.inNumber" placeholder="行程航班/火车号"></el-input>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select v-model="dataForm.type" placeholder="类型" filterable>
                <el-option v-for="item in tripType" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item></el-col>
          <!-- <el-col :span="12">
            <el-form-item label="是否购买" prop="isBuy">
              <el-select v-model="dataForm.isBuy" placeholder="是否购买" filterable>
                <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input v-model="dataForm.price" placeholder="价格"></el-input>
            </el-form-item></el-col> -->
            
          <!-- 改签信息 -->
          <template v-if="type == 1">
            <el-col :span="24">
              <div style="margin: 10px 0; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <h3>改签信息</h3>
              </div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="改签日期" prop="chaDate">
                <el-date-picker v-model="dataForm.chaDate" style="width: 100%" type="date" value-format="yyyy/MM/dd"
                  placeholder="改签日期"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="改签出发地点" prop="chaStartPlace">
                <el-autocomplete style="width: 100%;" v-model="dataForm.chaStartPlace" :fetch-suggestions="chaSearch"
                  label="name" placeholder="请输入内容" @select="chaSelectResult" :loading="chaLoading">
                  <div slot-scope="scope">
                    <span style="float: left">{{ dataForm.inType == 0 ? scope.item.airportName : scope.item.stationName }}</span>
                  </div>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="改签到达地点" prop="chaEndPlace">
                <el-autocomplete style="width: 100%;" v-model="dataForm.chaEndPlace" :fetch-suggestions="chaEndSearch"
                  label="name" placeholder="请输入内容" @select="chaEndSelectResult" :loading="chaEndLoading">
                  <div slot-scope="scope">
                    <span style="float: left">{{ dataForm.inType == 0 ? scope.item.airportName : scope.item.stationName }}</span>
                  </div>
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="2" style="text-align: center;">
              <el-button type="primary" @click="searchChaReturnResultPlane()">查询</el-button>
            </el-col>
            <el-col :span="12">
              <el-form-item label="改签时间" prop="chaTimes">
                <el-date-picker v-model="chaTimes" @change="chaDateChange" type="datetimerange"
                  value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="出发日期"
                  end-placeholder="到达日期"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="改签航班/火车号" prop="chaNumber">
                <el-input v-model="dataForm.chaNumber" placeholder="改签航班/火车号"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="改签价格" prop="chaPrice">
                <el-input v-model="dataForm.chaPrice" placeholder="改签价格"></el-input>
              </el-form-item>
            </el-col> -->
          </template>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
    <planeselect v-if="planeselectVisible" ref="planeselect" @select="handleFlightSelect"></planeselect>
    <trainselect v-if="trainselectVisible" ref="trainselect" @select="handleTrainSelect"></trainselect>
  </div>
</template>

<script>
import planeselect from "./components/plane-select"
import trainselect from "./components/train-select"
import { yesOrNo } from "@/data/common"
import { guestGoType, tripType } from '@/data/activity'
import { format } from "@/utils/date.js";
export default {
  components: {
    planeselect,
    trainselect,
  },
  data() {
    return {
      type: 0, // 0-新增，1-改签
      planeselectVisible: false,
      trainselectVisible: false,
      loading: false,
      endloading: false,
      searchResult: [],
      endsearchResult: [],
      times: [],
      yesOrNo,
      guestGoType,
      tripType,
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        inType: 0,
        inDate: '',
        inNumber: '',
        inEndPlace: '',
        inStartPlace: '',
        inStartDate: '',
        inEndDate: '',
        activityGuestId: '',
        activityId: '',
        startCityCode: '',
        endCityCode: '',
        inStartCity: '',
        inEndCity: '',
        inStartTerminal: '',
        inEndTerminal: '',
        isShareFlight: '',
        realFlightNo: '',
        cabinCode: '',
        cabinBookPara: '',
        isBuy: 0,
        type: 0,
        price: 0,
        // 改签字段
        chaDate: '',
        chaNumber: '',
        chaEndPlace: '',
        chaStartPlace: '',
        chaStartDate: '',
        chaEndDate: '',
        chaPrice: 0,
        chaStartCityCode: '',
        chaEndCityCode: '',
        chaStartCity: '',
        chaEndCity: '',
        chaStartTerminal: '',
        chaEndTerminal: ''
      },
      dataRule: {
        inType: [
          { required: true, message: '行程类型（0-飞机，1-火车，2-其他）不能为空', trigger: 'blur' }
        ],
        inDate: [
          { required: true, message: '行程日期不能为空', trigger: 'blur' }
        ],
        inNumber: [
          { required: true, message: '行程航班号不能为空', trigger: 'blur' }
        ],
        activityGuestId: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        isBuy: [
          { required: true, message: '是否购买不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleFlightSelect(flight) {
      console.log('已选择航班:', flight)
      if (flight) {
        this.dataForm.inNumber = flight.flightNo;
        this.dataForm.inStartPlace = flight.fromAirportName;
        this.dataForm.inEndPlace = flight.toAirportName;
        this.dataForm.inStartTerminal = flight.fromTerminal;
        this.dataForm.inEndTerminal = flight.toTerminal;
        this.dataForm.isShareFlight = flight.isShareFlight ? 1 : 0;
        this.dataForm.realFlightNo = flight.realFlightNo;
        this.dataForm.cabinCode = flight.cabinCode;
        this.dataForm.cabinBookPara = flight.cabinBookPara;
        this.dataForm.startCityCode = flight.fromAirportCode;
        this.dataForm.endCityCode = flight.toAirportCode;
        this.dataForm.price = flight.price;
        this.times = [flight.fromDateTime.replaceAll("-", "/"), flight.toDateTime.replaceAll("-", "/")];
        console.log(this.times)
        this.dataForm.inStartDate = flight.fromDateTime.replaceAll("-", "/");
        this.dataForm.inEndDate = flight.toDateTime.replaceAll("-", "/");
      }
      // 处理选择的航班数据
    },
    handleTrainSelect(flight) {
      console.log('已选择火车:', flight)
      if (flight) {
        this.dataForm.inNumber = flight.trainCode;
        this.dataForm.realFlightNo = flight.trainNo;
        this.dataForm.inStartPlace = flight.fromStation;
        this.dataForm.inEndPlace = flight.toStation;
        this.dataForm.cabinCode = flight.cabinCode;
        this.dataForm.cabinBookPara = flight.cabinBookPara;
        this.dataForm.price = flight.price;
        this.times = [flight.fromDateTime.replaceAll("-", "/") + ":00", flight.toDateTime.replaceAll("-", "/") + ":00"];
        this.dataForm.inStartDate = flight.fromDateTime.replaceAll("-", "/") + ":00";
        this.dataForm.inEndDate = flight.toDateTime.replaceAll("-", "/") + ":00";
      }
      // 处理选择的航班数据
    },
    inTypeChange(v) {
      this.dataForm.inStartPlace = '';
      this.dataForm.inEndPlace = '';
      this.dataForm.inStartTerminal = '';
      this.dataForm.inEndTerminal = '';
      this.dataForm.isShareFlight = '';
      this.dataForm.realFlightNo = '';
      this.dataForm.cabinCode = '';
      this.dataForm.cabinBookPara = '';
      this.searchResult = [];
      this.endsearchResult = [];
    },
    dateChange(v) {
      this.dataForm.inStartDate = v[0];
      this.dataForm.inEndDate = v[1];
    },
    // 添加改签相关方法
    chaDateChange(v) {
      if (v) {
        this.dataForm.chaStartDate = v[0];
        this.dataForm.chaEndDate = v[1];
      }
    },
    
    chaSearch(query, cb) {
      if (query !== '') {
        this.chaLoading = true;
        this.chaSearchResult = [];
        var url = this.dataForm.inType == 0 ? "/config/configairport/findByName" : "/config/configtrainstation/findByName";
        this.$http({
          url: this.$http.adornUrl(url),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.chaLoading = false;
          if (data && data.code === 200) {
            this.chaSearchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.chaSearchResult);
            }, 100 * Math.random());
          } else {
            this.$message.error(data.msg)
          }
        });
      } else {
        this.chaLoading = false;
      }
    },
    
    chaEndSearch(query, cb) {
      if (query !== '') {
        this.chaEndLoading = true;
        this.chaEndSearchResult = [];
        var url = this.dataForm.inType == 0 ? "/config/configairport/findByName" : "/config/configtrainstation/findByName";
        this.$http({
          url: this.$http.adornUrl(url),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.chaEndLoading = false;
          if (data && data.code === 200) {
            this.chaEndSearchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.chaEndSearchResult);
            }, 100 * Math.random());
          } else {
            this.$message.error(data.msg)
          }
        });
      } else {
        this.chaEndLoading = false;
      }
    },
    
    chaSelectResult(v) {
      if (this.dataForm.inType == 0) {
        // 飞机
        this.dataForm.chaStartPlace = v.airportName;
        this.dataForm.chaStartCity = v.cityName;
        this.dataForm.chaStartCityCode = v.cityCode;
      } else {
        this.dataForm.chaStartPlace = v.stationName;
        this.dataForm.chaStartCity = v.cityName;
        this.dataForm.chaStartCityCode = v.stationCode;
      }
    },
    
    chaEndSelectResult(v) {
      if (this.dataForm.inType == 0) {
        // 飞机
        this.dataForm.chaEndPlace = v.airportName;
        this.dataForm.chaEndCity = v.cityName;
        this.dataForm.chaEndCityCode = v.cityCode;
      } else {
        this.dataForm.chaEndPlace = v.stationName;
        this.dataForm.chaEndCity = v.cityName;
        this.dataForm.chaEndCityCode = v.stationCode;
      }
    },
    
    searchChaReturnResultPlane() {
      if (!this.dataForm.chaStartPlace) {
        this.$message.error("请输入改签出发地");
        return false;
      }
      if (!this.dataForm.chaEndPlace) {
        this.$message.error("请输入改签目的地");
        return false;
      }
      if (!this.dataForm.chaDate) {
        this.$message.error("请输入改签日期");
        return false;
      }
      
      if(this.dataForm.inType == 0) {
        this.planeselectVisible = true;
        this.$nextTick(() => {
          this.$refs.planeselect.init(this.dataForm.chaStartCityCode, this.dataForm.chaEndCityCode, this.dataForm.chaDate, (flight) => {
            this.handleChaFlightSelect(flight);
          })
        })
      } else {
        this.trainselectVisible = true;
        this.$nextTick(() => {
          this.$refs.trainselect.init(this.dataForm.chaStartPlace, this.dataForm.chaEndPlace, this.dataForm.chaDate, (train) => {
            this.handleChaTrainSelect(train);
          })
        })
      }
    },
    
    handleChaFlightSelect(flight) {
      console.log('已选择改签航班:', flight)
      if (flight) {
        this.dataForm.chaNumber = flight.flightNo;
        this.dataForm.chaStartPlace = flight.fromAirportName;
        this.dataForm.chaEndPlace = flight.toAirportName;
        this.dataForm.chaStartTerminal = flight.fromTerminal;
        this.dataForm.chaEndTerminal = flight.toTerminal;
        this.dataForm.chaStartCityCode = flight.fromAirportCode;
        this.dataForm.chaEndCityCode = flight.toAirportCode;
        this.dataForm.chaPrice = flight.price;
        this.dataForm.cabinCode = flight.cabinCode;
        this.dataForm.cabinBookPara = flight.cabinBookPara;
        this.chaTimes = [flight.fromDateTime.replaceAll("-", "/"), flight.toDateTime.replaceAll("-", "/")];
        this.dataForm.chaStartDate = flight.fromDateTime.replaceAll("-", "/");
        this.dataForm.chaEndDate = flight.toDateTime.replaceAll("-", "/");
      }
    },
    
    handleChaTrainSelect(train) {
      console.log('已选择改签火车:', train)
      if (train) {
        this.dataForm.chaNumber = train.trainCode;
        this.dataForm.chaStartPlace = train.fromStation;
        this.dataForm.chaEndPlace = train.toStation;
        this.dataForm.chaPrice = train.price;
        this.dataForm.cabinCode = train.cabinCode;
        this.dataForm.cabinBookPara = train.cabinBookPara;
        this.chaTimes = [train.fromDateTime.replaceAll("-", "/") + ":00", train.toDateTime.replaceAll("-", "/") + ":00"];
        this.dataForm.chaStartDate = train.fromDateTime.replaceAll("-", "/") + ":00";
        this.dataForm.chaEndDate = train.toDateTime.replaceAll("-", "/") + ":00";
      }
    },
    init(id, activityId, activityGuestId,type) {
      this.getToken();
      this.type = type || 0;
      this.dataForm.id = id || 0
      this.dataForm.activityGuestId = activityGuestId || 0
      this.dataForm.activityId = activityId || 0
      this.times = [];
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityguesttrip/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.inType = data.activityGuestTrip.inType
              this.dataForm.inDate = data.activityGuestTrip.inDate
              this.dataForm.inNumber = data.activityGuestTrip.inNumber
              this.dataForm.inEndPlace = data.activityGuestTrip.inEndPlace
              this.dataForm.inStartPlace = data.activityGuestTrip.inStartPlace
              this.dataForm.inEndDate = data.activityGuestTrip.inEndDate
              this.dataForm.inStartDate = data.activityGuestTrip.inStartDate
              this.dataForm.activityGuestId = data.activityGuestTrip.activityGuestId
              this.dataForm.activityId = data.activityGuestTrip.activityId
              this.dataForm.isBuy = data.activityGuestTrip.isBuy
              this.dataForm.price = data.activityGuestTrip.price
              this.dataForm.type = data.activityGuestTrip.type
              this.dataForm.startCityCode = data.activityGuestTrip.startCityCode
              this.dataForm.endCityCode = data.activityGuestTrip.endCityCode
              this.dataForm.inStartCity = data.activityGuestTrip.inStartCity
              this.dataForm.inEndCity = data.activityGuestTrip.inEndCity
              this.dataForm.inStartTerminal = data.activityGuestTrip.inStartTerminal
              this.dataForm.inEndTerminal = data.activityGuestTrip.inEndTerminal
              this.dataForm.isShareFlight = data.activityGuestTrip.isShareFlight
              this.dataForm.realFlightNo = data.activityGuestTrip.realFlightNo
              this.dataForm.cabinCode = data.activityGuestTrip.cabinCode
              this.dataForm.cabinBookPara = data.activityGuestTrip.cabinBookPara
              // 初始化改签相关字段
              if (this.type == 1 && data.activityGuestTrip.chaDate) {
                this.dataForm.chaDate = data.activityGuestTrip.chaDate
                this.dataForm.chaNumber = data.activityGuestTrip.chaNumber
                this.dataForm.chaEndPlace = data.activityGuestTrip.chaEndPlace
                this.dataForm.chaStartPlace = data.activityGuestTrip.chaStartPlace
                this.dataForm.chaStartDate = data.activityGuestTrip.chaStartDate
                this.dataForm.chaEndDate = data.activityGuestTrip.chaEndDate
                this.dataForm.chaPrice = data.activityGuestTrip.chaPrice
                this.dataForm.chaStartCityCode = data.activityGuestTrip.chaStartCityCode
                this.dataForm.chaEndCityCode = data.activityGuestTrip.chaEndCityCode
                this.dataForm.chaStartCity = data.activityGuestTrip.chaStartCity
                this.dataForm.chaEndCity = data.activityGuestTrip.chaEndCity
                this.dataForm.chaStartTerminal = data.activityGuestTrip.chaStartTerminal
                this.dataForm.chaEndTerminal = data.activityGuestTrip.chaEndTerminal
                
                if (this.dataForm.chaStartDate && this.dataForm.chaEndDate) {
                  this.chaTimes = [this.dataForm.chaStartDate, this.dataForm.chaEndDate];
                }
              }
              
              if (this.dataForm.inStartDate && this.dataForm.inEndDate) {
                this.times = [this.dataForm.inStartDate, this.dataForm.inEndDate];
              }
            }
          })
        } else {

          this.dataForm.inDate = format(new Date(), "yyyy/MM/dd");
        }
      })
    },
    selectResult(v) {
      if (this.dataForm.inType == 0) {
        // 飞机
        this.dataForm.inStartPlace = v.airportName;
        this.dataForm.inStartCity = v.cityName;
        this.dataForm.startCityCode = v.cityCode;
      } else {
        this.dataForm.inStartPlace = v.stationName;
        this.dataForm.inStartCity = v.cityName;
        this.dataForm.startCityCode = v.stationCode;

      }
    },
    endselectResult(v) {
      if (this.dataForm.inType == 0) {
        // 飞机
        this.dataForm.inEndPlace = v.airportName;
        this.dataForm.inEndCity = v.cityName;
        this.dataForm.endCityCode = v.cityCode;
      } else {
        this.dataForm.inEndPlace = v.stationName;
        this.dataForm.inEndCity = v.cityName;
        this.dataForm.endCityCode = v.stationCode;

      }
    },
    search(query, cb) {
      if (query !== '') {
        this.loading = true;
        this.searchResult = [];
        var url = this.dataForm.inType == 0 ? "/config/configairport/findByName" : "/config/configtrainstation/findByName";
        this.$http({
          url: this.$http.adornUrl(url),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.loading = false;
          if (data && data.code === 200) {
            this.searchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.searchResult);
            }, 100 * Math.random());
            // }
          } else {
            this.$message.error(data.msg)
          }
        });
      } else {
        this.loading = false;
      }
    },
    endsearch(query, cb) {
      if (query !== '') {
        this.endloading = true;
        this.endsearchResult = [];
        var url = this.dataForm.inType == 0 ? "/config/configairport/findByName" : "/config/configtrainstation/findByName";
        this.$http({
          url: this.$http.adornUrl(url),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.loading = false;
          if (data && data.code === 200) {
            this.endsearchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.endsearchResult);
            }, 100 * Math.random());
            // }
          } else {
            this.$message.error(data.msg)
          }
        });
      } else {
        this.endloading = false;
      }
    },
    searchReturnResultPlane() {
      if (!this.dataForm.inStartPlace) {
        this.$message.error("请输入出发地");
        return false;
      }
      if (!this.dataForm.inEndPlace) {
        this.$message.error("请输入目的地");
        return false;
      }
      if (!this.dataForm.inDate) {
        this.$message.error("请输入日期");
        return false;
      }
      if(this.dataForm.inType == 0) {

        this.planeselectVisible = true;
      this.$nextTick(() => {
        this.$refs.planeselect.init(this.dataForm.startCityCode, this.dataForm.endCityCode, this.dataForm.inDate)
      })
      } else {

        this.trainselectVisible = true;
      this.$nextTick(() => {
        this.$refs.trainselect.init(this.dataForm.inStartPlace, this.dataForm.inEndPlace, this.dataForm.inDate)
      })
      }

    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let submitData = {
            'repeatToken': this.dataForm.repeatToken,
            'id': this.dataForm.id || undefined,
            'inType': this.dataForm.inType,
            'inDate': this.dataForm.inDate,
            'inNumber': this.dataForm.inNumber,
            'inEndPlace': this.dataForm.inEndPlace,
            'inStartPlace': this.dataForm.inStartPlace,
            'inEndDate': this.dataForm.inEndDate,
            'inStartDate': this.dataForm.inStartDate,
            'activityGuestId': this.dataForm.activityGuestId,
            'activityId': this.dataForm.activityId,
            'isBuy': this.dataForm.isBuy,
            'type': this.dataForm.type,
            'startCityCode': this.dataForm.startCityCode,
            'endCityCode': this.dataForm.endCityCode,
            'inStartCity': this.dataForm.inStartCity,
            'inEndCity': this.dataForm.inEndCity,
            'inStartTerminal': this.dataForm.inStartTerminal,
            'inEndTerminal': this.dataForm.inEndTerminal,
            'isShareFlight': this.dataForm.isShareFlight,
            'realFlightNo': this.dataForm.realFlightNo,
            'cabinCode': this.dataForm.cabinCode,
            'cabinBookPara': this.dataForm.cabinBookPara,
            'price': this.dataForm.price
          };
          
          // 如果是改签，添加改签相关字段
          if (this.type == 1) {
            submitData = {
              ...submitData,
              'chaDate': this.dataForm.chaDate,
              'chaNumber': this.dataForm.chaNumber,
              'chaEndPlace': this.dataForm.chaEndPlace,
              'chaStartPlace': this.dataForm.chaStartPlace,
              'chaStartDate': this.dataForm.chaStartDate,
              'chaEndDate': this.dataForm.chaEndDate,
              'chaPrice': this.dataForm.chaPrice,
              'chaStartCityCode': this.dataForm.chaStartCityCode,
              'chaEndCityCode': this.dataForm.chaEndCityCode,
              'chaStartCity': this.dataForm.chaStartCity,
              'chaEndCity': this.dataForm.chaEndCity,
              'chaStartTerminal': this.dataForm.chaStartTerminal,
              'chaEndTerminal': this.dataForm.chaEndTerminal
            };
          }
          var url = this.type == 1 ? '/panhe/plane/cha' : `/activity/activityguesttrip/${!this.dataForm.id ? 'save' : 'update'}`;
          this.$http({
            url: this.$http.adornUrl(url),
            method: 'post',
            data: this.$http.adornData(submitData)
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
