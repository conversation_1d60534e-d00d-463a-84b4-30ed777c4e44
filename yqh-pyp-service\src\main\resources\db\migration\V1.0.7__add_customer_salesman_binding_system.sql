-- 客户与业务员绑定系统数据库变更
-- 版本：V1.0.7
-- 日期：2025-07-17

-- 1. 创建微信用户业务员绑定表
CREATE TABLE `wx_user_salesman_binding` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `wx_user_id` bigint(20) NOT NULL COMMENT '微信用户ID（对应wx_user.id）',
  `salesman_id` bigint(20) NOT NULL COMMENT '业务员ID',
  `binding_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '绑定方式：1-二维码扫描，2-邀请链接，3-手动绑定，4-系统分配',
  `binding_source` varchar(100) DEFAULT NULL COMMENT '绑定来源（二维码ID、邀请码等）',
  `binding_time` datetime NOT NULL COMMENT '绑定时间',
  `effective_time` datetime DEFAULT NULL COMMENT '生效时间',
  `expiry_time` datetime DEFAULT NULL COMMENT '失效时间（NULL表示永久有效）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-已失效，1-有效，2-已解绑',
  `priority` int(11) DEFAULT '1' COMMENT '优先级（数字越大优先级越高，用于多业务员绑定时的选择）',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_wxuser_salesman_active` (`wx_user_id`, `salesman_id`, `appid`, `status`),
  KEY `idx_wxuser_binding` (`wx_user_id`, `appid`),
  KEY `idx_salesman_binding` (`salesman_id`, `appid`),
  KEY `idx_binding_time` (`binding_time`),
  KEY `idx_status_expiry` (`status`, `expiry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户业务员绑定表';

-- 2. 为业务员表添加二维码相关字段（如果还没有的话）
-- ALTER TABLE `salesman` ADD COLUMN `qr_code_url` varchar(255) DEFAULT NULL COMMENT '业务员专属二维码URL' AFTER `appid`;
-- ALTER TABLE `salesman` ADD COLUMN `invite_code` varchar(32) DEFAULT NULL COMMENT '业务员邀请码' AFTER `qr_code_url`;

-- 3. 创建微信用户业务员绑定变更记录表
CREATE TABLE `wx_user_salesman_binding_log` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `wx_user_id` bigint(20) NOT NULL COMMENT '微信用户ID',
  `old_salesman_id` bigint(20) DEFAULT NULL COMMENT '原业务员ID',
  `new_salesman_id` bigint(20) DEFAULT NULL COMMENT '新业务员ID',
  `operation_type` tinyint(4) NOT NULL COMMENT '操作类型：1-新增绑定，2-更换业务员，3-解除绑定，4-系统自动解绑',
  `operation_reason` varchar(255) DEFAULT NULL COMMENT '操作原因',
  `binding_source` varchar(100) DEFAULT NULL COMMENT '绑定来源',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(4) DEFAULT NULL COMMENT '操作人类型：1-客户自己，2-业务员，3-管理员，4-系统',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_wxuser_log` (`wx_user_id`, `appid`),
  KEY `idx_salesman_log` (`old_salesman_id`, `new_salesman_id`),
  KEY `idx_operation_time` (`operation_type`, `create_on`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户业务员绑定变更记录表';

-- 4. 为活动充值记录表添加业务员字段
ALTER TABLE `activity_recharge_record`
ADD COLUMN `salesman_id` bigint(20) DEFAULT NULL COMMENT '关联业务员ID' AFTER `appid`,
ADD INDEX `idx_salesman_recharge` (`salesman_id`, `appid`);

-- 5. 创建微信用户业务员绑定统计表（用于缓存统计数据）
CREATE TABLE `wx_user_salesman_binding_stats` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `salesman_id` bigint(20) NOT NULL COMMENT '业务员ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_wx_users` int(11) DEFAULT '0' COMMENT '总微信用户数',
  `active_wx_users` int(11) DEFAULT '0' COMMENT '活跃微信用户数（当日有操作）',
  `new_wx_users` int(11) DEFAULT '0' COMMENT '新增微信用户数',
  `lost_wx_users` int(11) DEFAULT '0' COMMENT '流失微信用户数',
  `total_orders` int(11) DEFAULT '0' COMMENT '总订单数',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '总销售额',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '总佣金',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_salesman_stat_date` (`salesman_id`, `stat_date`, `appid`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_salesman_stats` (`salesman_id`, `appid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户业务员绑定统计表';

-- 6. 创建索引优化查询性能
ALTER TABLE `wx_user_salesman_binding` ADD INDEX `idx_wxuser_active` (`wx_user_id`, `status`, `appid`);
ALTER TABLE `wx_user_salesman_binding` ADD INDEX `idx_salesman_active` (`salesman_id`, `status`, `appid`);
ALTER TABLE `wx_user_salesman_binding` ADD INDEX `idx_binding_source` (`binding_source`, `binding_type`);

-- 7. 创建视图，方便查询有效绑定关系
CREATE VIEW `v_wx_user_salesman_active_binding` AS
SELECT
  wsb.id,
  wsb.wx_user_id,
  wsb.salesman_id,
  wsb.binding_type,
  wsb.binding_source,
  wsb.binding_time,
  wsb.priority,
  wsb.appid,
  s.name as salesman_name,
  s.code as salesman_code,
  s.mobile as salesman_mobile,
  wu.nickname as wx_user_name,
  wu.mobile as wx_user_mobile,
  wu.openid as wx_user_openid
FROM `wx_user_salesman_binding` wsb
LEFT JOIN `salesman` s ON wsb.salesman_id = s.id
LEFT JOIN `wx_user` wu ON wsb.wx_user_id = wu.id
WHERE wsb.status = 1
  AND (wsb.expiry_time IS NULL OR wsb.expiry_time > NOW())
  AND s.status = 1;
