package com.cjy.pyp.modules.activity.task;

import com.cjy.pyp.modules.activity.service.ActivityExpirationService;
import com.cjy.pyp.modules.activity.service.ActivityExpirationNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 活动过期检查定时任务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Slf4j
@Component
public class ActivityExpirationTask {

    @Autowired
    private ActivityExpirationService activityExpirationService;

    @Autowired
    private ActivityExpirationNotifyService activityExpirationNotifyService;

    /**
     * 每小时检查一次活动过期状态
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkActivityExpiration() {
        log.info("Starting scheduled activity expiration check");
        try {
            activityExpirationService.batchUpdateExpirationStatus();
        } catch (Exception e) {
            log.error("Failed to execute activity expiration check", e);
        }
    }

    /**
     * 每天上午9点发送过期提醒（7天内过期）
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendExpirationReminders() {
        log.info("Starting to send expiration reminders");
        try {
            activityExpirationNotifyService.batchSendExpirationReminders(7);
            log.info("Batch send expiration reminders completed");
        } catch (Exception e) {
            log.error("Failed to send expiration reminders", e);
        }
    }

    /**
     * 每天上午10点发送过期通知
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void sendExpirationNotifications() {
        log.info("Starting to send expiration notifications");
        try {
            activityExpirationNotifyService.batchSendExpirationNotifications();
            log.info("Batch send expiration notifications completed");
        } catch (Exception e) {
            log.error("Failed to send expiration notifications", e);
        }
    }
}
