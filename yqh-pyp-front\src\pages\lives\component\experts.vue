<template>
  <div >
    <van-search
      v-model="dataForm.name"
      placeholder="请输入您要搜索的嘉宾关键词"
      show-action
      shape="round"
    >
      <div slot="action" @click="onSearch" class="search-text">搜索</div>
    </van-search>

  <div v-if="activityInfo.isFirstChar == 1" style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">按照专家姓氏首字母排序</div>
        </div>
    <van-list
      class="data"
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <van-card
        v-for="item in dataList"
        :key="item.id"
        @click="goExpertDetail(item.id)"
        style="background: white; margin-top: 10px"
        :thumb="item.avatar ? item.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'"
      >
        <div slot="title" style="font-size: 18px">{{ item.name }}</div>
        <div
          slot="desc"
          style="padding-top: 10px; font-size: 14px; color: grey"
        >
          <van-tag
            style="margin: 5px 10px 5px 0px"
            v-if="item.unit"
            size="medium"
            
            type="primary"
            plain
            >{{ item.unit }}</van-tag
          >
          <van-tag
            style="margin: 5px 10px 5px 0px"
            v-if="item.duties"
            size="medium"
            
            type="warning"
            plain
            >{{ item.duties }}</van-tag
          >
        </div>
      </van-card>
    </van-list>
  </div>
</template>

<script>
import date from "@/js/date.js";
export default {
  data() {
    return {
      openid: undefined,
      activityId: undefined,
      dataForm: {
        name: "",
        date: "",
      },
      loading: false,
      finished: false,
      flag: false,
      dataList: [],
      placeList: [],
      activityDateBetween: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      placeActive: 0,
      dateActive: "",
      schedulesActive: "",
    };
  },
  props: ["activityId"],
  mounted() {
    // this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid")
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityInfo();
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            document.title = this.activityInfo.name;
            this.getActivityList();
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "嘉宾列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "嘉宾列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/activity/activityguest/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          name: this.dataForm.name,
          isFirstChar: this.activityInfo.isFirstChar,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    goExpertDetail(v) {
      this.$router.push({
        name: "schedulesSimpleExpertDetail",
        query: { detailId: v, id: this.activityId },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/.van-card__thumb img {
    border-radius: 100%;
  }
}
</style>