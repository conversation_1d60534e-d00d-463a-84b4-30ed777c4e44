<template>
  <div class="image-materials">
    <van-nav-bar title="图片素材" left-text="返回" left-arrow @click-left="$router.go(-1)" />

    <!-- 功能区域 - 重新设计 -->
    <div class="function-section">
      <div class="section-content">
        <!-- 页面标题和描述 -->
        <div class="page-intro">
          <h2 class="page-title">图片素材管理</h2>
          <p class="page-desc">管理您的图片素材，支持批量上传和在线预览</p>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button type="primary" size="large" icon="plus" @click="showUploadDialog = true" class="upload-btn">
            上传图片素材
          </van-button>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filter-bar">
          <van-search v-model="searchKeyword" placeholder="搜索图片名称..." @search="onSearch" @clear="onSearch" shape="round"
            class="search-input" />
          <van-button icon="filter-o" class="filter-btn" @click="showFilter = !showFilter" />
        </div>
      </div>
    </div>

    <!-- 图片列表 -->
    <div class="image-list">
      <!-- 统计信息 -->
      <div class="stats-bar" v-if="imageList.length > 0">
        <div class="stats-info">
          <span class="total-count">共 {{ imageList.length }} 张图片</span>
          <!-- <span class="total-size">总大小 {{ getTotalSize() }}</span> -->
        </div>
        <div class="list-actions">
          <van-button size="mini" icon="refresh" @click="onSearch" plain>刷新</van-button>
          <!-- <van-button size="mini" icon="apps-o" @click="toggleViewMode" plain>
            {{ viewMode === 'grid' ? '列表' : '网格' }}
          </van-button> -->
        </div>
      </div>

      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" class="custom-list">
        <div class="image-grid">
          <div v-for="item in imageList" :key="item.id" class="image-card">
            <div class="card-preview" @click="previewImage(item)">
              <div class="preview-container">
                <van-image :src="item.mediaUrl || item.url" fit="cover" width="100%" height="200px" loading-icon="photo"
                  error-icon="photo-fail" class="preview-image" />
                <div class="image-overlay">
                  <div class="overlay-actions">
                    <van-button size="mini" icon="eye-o" @click.stop="previewImage(item)" round>预览</van-button>
                    <van-button size="mini" icon="more-o" @click.stop="showImageActions(item)" round>更多</van-button>
                  </div>
                </div>
                <div class="image-status">
                  <van-tag size="mini" type="primary">素材</van-tag>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-header">
                <h4 class="card-title">{{ item.name }}</h4>
                <van-icon name="more-o" @click="showImageActions(item)" class="more-icon" />
              </div>
              <div class="card-meta">
                <div class="meta-row">
                  <span class="meta-item">
                    <van-icon name="folder-o" size="12" />
                    {{ formatFileSize(item.fileSize) }}
                  </span>
                  <span class="meta-item">
                    <van-icon name="eye-o" size="12" />
                    {{ item.useCount || 0 }} 次使用
                  </span>
                </div>
              </div>
              <div class="card-actions">
                <van-button size="mini" type="primary" @click="previewImage(item)" icon="eye-o">
                  预览
                </van-button>
                <!-- <van-button size="mini" @click="editImage(item)" icon="edit">
                  编辑
                </van-button> -->
                <van-button size="mini" type="danger" @click="deleteImage(item)" icon="delete-o">
                  删除
                </van-button>
              </div>
            </div>
          </div>
        </div>
      </van-list>

      <!-- 空状态 -->
      <div v-if="!loading && imageList.length === 0" class="empty-state">
        <div class="empty-content">
          <van-icon name="photo-o" size="80" class="empty-icon" />
          <h3 class="empty-title">暂无图片素材</h3>
          <p class="empty-desc">上传您的第一张图片素材，开始创作之旅</p>
          <div class="empty-actions">
            <van-button type="primary" @click="showUploadDialog = true" icon="plus">
              上传图片素材
            </van-button>
          </div>
        </div>
      </div>
    </div>



    <!-- 上传图片弹窗 -->
    <van-dialog v-model="showUploadDialog" title="" show-cancel-button @confirm="confirmUpload"
      :confirm-button-loading="uploading" confirm-button-text="开始上传" class="custom-dialog upload-dialog" width="90%">
      <div class="upload-form">
        <div class="dialog-header">
          <div class="header-icon upload-icon">
            <van-icon name="plus" size="32" />
          </div>
          <h3 class="dialog-title">上传图片素材</h3>
          <p class="dialog-desc">批量上传您的图片素材，支持多种格式</p>
        </div>

        <van-field v-model="uploadForm.name" label="批次名称" placeholder="请输入批次名称（可选）" class="form-field" />

        <div class="upload-section">
          <div class="upload-header">
            <span class="upload-title">选择图片文件</span>
            <span class="upload-count">{{ fileList.length }}/20</span>
          </div>
          <van-uploader v-model="fileList" :max-count="20" :after-read="afterRead" :before-delete="beforeDelete"
            accept="image/*" :max-size="10 * 1024 * 1024" @oversize="onOversize" multiple :preview-size="80"
            upload-text="选择图片" class="custom-uploader" />
        </div>

        <div class="upload-tips">
          <div class="tips-header">
            <van-icon name="info-o" />
            <span>上传说明</span>
          </div>
          <div class="tips-content">
            <div class="tip-item">
              <span class="tip-label">支持格式：</span>
              <span class="tip-value">JPG、PNG、GIF、WEBP</span>
            </div>
            <div class="tip-item">
              <span class="tip-label">文件大小：</span>
              <span class="tip-value">单个文件不超过10MB</span>
            </div>
            <div class="tip-item">
              <span class="tip-label">数量限制：</span>
              <span class="tip-value">最多可选择20个文件</span>
            </div>
          </div>
        </div>

        <div v-if="uploadProgress.length > 0" class="upload-progress">
          <div class="progress-header">
            <van-icon name="clock-o" />
            <span>上传进度</span>
          </div>
          <div v-for="(item, index) in uploadProgress" :key="index" class="progress-item">
            <div class="progress-info">
              <span class="progress-name">{{ item.name }}</span>
              <span class="progress-status">{{ item.status }}</span>
            </div>
            <van-progress :percentage="item.progress" :color="item.color" stroke-width="6" />
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 图片预览 -->
    <van-image-preview v-model="showPreview" :images="previewImages" :start-position="previewIndex" />
  </div>
</template>

<script>
export default {
  name: 'ImageMaterials',
  data() {
    return {
      activityId: null,
      fileList: [],
      imageList: [],
      loading: false,
      finished: false,
      page: 1,
      pageSize: 20,
      searchKeyword: '',

      // 弹窗控制
      showUploadDialog: false,
      showPreview: false,
      uploading: false,
      previewImages: [],
      previewIndex: 0,
      showFilter: false,
      viewMode: 'grid', // 'grid' 或 'list'

      // 上传表单
      uploadForm: {
        name: ''
      },

      // 上传进度
      uploadProgress: []
    }
  },
  mounted() {
    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
      }
    }

    if (!this.activityId) {
      this.$toast.fail('活动ID不能为空，请先选择活动')
      this.$router.push({ name: 'index' })
      return
    }
    this.loadImageList()
  },
  methods: {
    onSearch() {
      this.page = 1
      this.imageList = []
      this.finished = false
      this.loadImageList()
    },

    onLoad() {
      this.loadImageList()
    },

    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid'
    },

    // 获取总大小
    getTotalSize() {
      const totalBytes = this.imageList.reduce((sum, item) => sum + (item.fileSize || 0), 0)
      return this.formatFileSize(totalBytes)
    },

    // 编辑图片
    editImage(item) {
      // 编辑图片功能
      this.$toast('编辑功能开发中')
    },

    // 删除图片
    deleteImage(item) {
      this.$dialog.confirm({
        title: '确认删除',
        message: `确定要删除图片"${item.name}"吗？`,
      }).then(() => {
        this.$fly.delete(`/pyp/web/activity/activityimage/${item.id}`).then(res => {
          if (res.code === 200) {
            this.$toast.success('删除成功')
            this.onSearch()
          } else {
            this.$toast.fail(res.message || '删除失败')
          }
        }).catch(err => {
          this.$toast.fail('删除失败')
        })
      }).catch(() => {
        // 取消删除
      })
    },

    loadImageList() {
      this.loading = true
      const params = {
        page: this.page,
        limit: this.pageSize,
        activityId: this.activityId
      }

      if (this.searchKeyword) {
        params.name = this.searchKeyword
      }

      this.$fly.get('/pyp/web/activity/activityimage/list', params).then(res => {
        this.loading = false
        if (res.code === 200) {
          const newImages = res.page.list || []

          if (this.page === 1) {
            this.imageList = newImages
          } else {
            this.imageList = this.imageList.concat(newImages)
          }

          this.page++
          this.finished = this.imageList.length >= res.page.totalCount
        } else {
          this.$toast.fail(res.msg || '获取图片列表失败')
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.$toast.fail('获取图片列表失败')
        this.finished = true
      })
    },

    // 文件上传
    afterRead(file) {
      console.log('选择文件:', file)
      // 文件选择后不立即上传，等待用户点击确认
    },

    beforeDelete(file, detail) {
      return new Promise((resolve) => {
        this.$dialog.confirm({
          title: '确认删除',
          message: '确定要删除这个文件吗？'
        }).then(() => {
          resolve(true)
        }).catch(() => {
          resolve(false)
        })
      })
    },

    onOversize() {
      this.$toast.fail('文件大小不能超过10MB')
    },

    async confirmUpload() {
      if (this.fileList.length === 0) {
        this.$toast.fail('请选择图片文件')
        return
      }

      this.uploading = true
      this.uploadProgress = []

      try {
        // 初始化进度
        this.fileList.forEach((file, index) => {
          this.uploadProgress.push({
            name: file.file ? file.file.name : `图片${index + 1}`,
            progress: 0,
            status: '准备上传',
            color: '#1989fa'
          })
        })

        // 批量上传文件
        const uploadPromises = this.fileList.map((file, index) => {
          return this.uploadSingleFile(file, index)
        })

        const results = await Promise.all(uploadPromises)

        // 保存上传结果到数据库
        await this.saveUploadedImages(results)

        this.$toast.success(`成功上传 ${results.length} 个图片`)
        this.showUploadDialog = false
        this.resetUploadForm()
        this.onSearch()

      } catch (error) {
        console.error('上传失败:', error)
        this.$toast.fail('上传失败，请重试')
      } finally {
        this.uploading = false
      }
    },

    async uploadSingleFile(fileItem, index) {
      return new Promise((resolve, reject) => {
        const file = fileItem.file
        const formData = new FormData()

        // 更新进度状态
        this.uploadProgress[index].status = '上传中'
        this.uploadProgress[index].progress = 10

        // 判断是否为图片文件，进行压缩
        if (file.type.startsWith('image/')) {
          // 使用Compressor进行图片压缩
          // new Compressor(file, {
          //   quality: 0.8,
          //   maxWidth: 1920,
          //   maxHeight: 1080,
          //   success: (compressedFile) => {
          formData.append('file', file)
          this.performUpload(formData, index, resolve, reject)
          //   },
          //   error: (error) => {
          //     console.error('压缩失败:', error)
          //     formData.append('file', file)
          //     this.performUpload(formData, index, resolve, reject)
          //   }
          // })
        } else {
          formData.append('file', file)
          this.performUpload(formData, index, resolve, reject)
        }
      })
    },

    performUpload(formData, index, resolve, reject) {
      this.$fly.post('/pyp/web/upload', formData).then(res => {
        if (res && res.code === 200) {
          this.uploadProgress[index].progress = 100
          this.uploadProgress[index].status = '上传成功'
          this.uploadProgress[index].color = '#52c41a'

          resolve({
            url: res.result,
            name: this.uploadForm.name || `图片${index + 1}`,
            fileSize: formData.get('file').size
          })
        } else {
          throw new Error(res.msg || '上传失败')
        }
      }).catch(error => {
        this.uploadProgress[index].progress = 0
        this.uploadProgress[index].status = '上传失败'
        this.uploadProgress[index].color = '#ff4444'
        reject(error)
      })
    },

    async saveUploadedImages(uploadResults) {
      const savePromises = uploadResults.map((result, index) => {
        const params = {
          activityId: this.activityId,
          name: result.name,
          mediaUrl: result.url,
          fileSize: result.fileSize,
          useCount: 0
        }

        return this.$fly.post('/pyp/web/activity/activityimage/save', params)
      })

      await Promise.all(savePromises)
    },

    resetUploadForm() {
      this.uploadForm = { name: '' }
      this.fileList = []
      this.uploadProgress = []
    },



    previewImage(item) {
      this.previewImages = this.imageList.map(img => img.mediaUrl || img.url)
      this.previewIndex = this.imageList.findIndex(img => img.id === item.id)
      this.showPreview = true
    },

    // 显示图片操作菜单
    showImageActions(item) {
      this.$actionSheet({
        actions: [
          { name: '预览图片', callback: () => this.previewImage(item) },
          { name: '编辑图片', callback: () => this.editImage(item) },
          { name: '删除图片', color: '#ee0a24', callback: () => this.deleteImage(item) }
        ]
      })
    },

    editImage(item) {
      this.$toast('编辑图片功能待开发')
      console.log('编辑图片:', item)
    },

    deleteImage(item) {
      this.$dialog.confirm({
        title: '确认删除',
        message: '确定要删除这张图片吗？'
      }).then(() => {
        this.$fly.post('/pyp/web/activity/activityimage/delete', [item.id]).then(res => {
          if (res.code === 200) {
            this.$toast.success('删除成功')
            this.onSearch()
          } else {
            this.$toast.fail(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$toast.fail('删除失败')
        })
      }).catch(() => {
        // 用户取消
      })
    },

    // 工具方法
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    }
  }
}
</script>

<style lang="less" scoped>
// 页面整体样式
.image-materials {
  background: #f8faff;
  min-height: 100vh;
  position: relative;
  padding-bottom: 60px;
}

// 功能区域样式 - 重新设计
.function-section {
  background: white;
  padding: 24px 16px;
  margin-bottom: 16px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;

  .section-content {
    .page-intro {
      text-align: center;
      margin-bottom: 24px;

      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-desc {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-bottom: 24px;

      .upload-btn {
        height: 48px;
        padding: 0 32px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border: none;
        box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        flex: 1;

        :deep(.van-search__content) {
          background: #f8faff;
          border-radius: 24px;
          border: 1px solid #e8f4ff;
          // padding: 8px 16px;
        }

        :deep(.van-field__control) {
          font-size: 14px;
          color: #333;
        }

        :deep(.van-search__action) {
          display: none;
        }

        :deep(.van-field__left-icon) {
          color: #1890ff;
        }
      }

      .filter-btn {
        width: 36px;
        height: 36px;
        border-radius: 24px;
        background: #f8faff;
        border: 1px solid #e8f4ff;
        color: #1890ff;

        &:hover {
          background: #e8f4ff;
        }
      }
    }
  }

}

// 图片列表样式
.image-list {
  padding: 0 16px;
  position: relative;
  z-index: 10;

  .stats-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .stats-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .total-count {
        font-size: 14px;
        color: #333;
        font-weight: 600;
      }

      .total-size {
        font-size: 12px;
        color: #666;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 8px;
      }
    }

    .list-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .custom-list {
    .van-list__finished-text {
      color: #999;
      font-size: 13px;
      padding: 20px 0;
    }
  }

  .image-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

// 图片卡片样式
.image-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .card-preview {
    position: relative;
    cursor: pointer;

    .preview-container {
      position: relative;
      width: 100%;
      height: 200px;
      background: #f8faff;
      border-radius: 16px 16px 0 0;
      overflow: hidden;

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;

        .overlay-actions {
          display: flex;
          gap: 8px;
        }
      }

      .image-status {
        position: absolute;
        top: 8px;
        left: 8px;
      }

      &:hover .image-overlay {
        opacity: 1;
      }
    }
  }

  .card-content {
    padding: 16px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0;
        line-height: 1.4;
        flex: 1;
        margin-right: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .more-icon {
        color: #999;
        cursor: pointer;
        padding: 4px;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .card-meta {
      margin-bottom: 16px;

      .meta-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #666;

          .van-icon {
            color: #999;
          }
        }
      }
    }

    .card-actions {
      display: flex;
      gap: 8px;

      .van-button {
        flex: 1;
        height: 32px;
        border-radius: 8px;
        font-size: 12px;
      }
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-content {
    .empty-icon {
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .empty-title {
      font-size: 18px;
      color: #333;
      margin: 0 0 8px 0;
      font-weight: 600;
    }

    .empty-desc {
      font-size: 14px;
      color: #666;
      margin: 0 0 24px 0;
      line-height: 1.5;
    }

    .empty-actions {
      .van-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 22px;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

// 上传弹窗样式
.upload-dialog {
  :deep(.van-dialog) {
    border-radius: 20px;
    overflow: hidden;
  }

  :deep(.van-dialog__content) {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0;
  }

  .upload-form {
    padding: 10px;

    .dialog-header {
      text-align: center;
      padding: 24px 0 20px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 20px;

      .header-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        color: white;
      }

      .dialog-title {
        font-size: 20px;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 8px 0;
      }

      .dialog-desc {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .form-field {
      margin-bottom: 20px;

      :deep(.van-field__label) {
        font-weight: 600;
        color: #333;
      }
    }

    .upload-section {
      margin-bottom: 20px;

      .upload-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .upload-title {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .upload-count {
          font-size: 12px;
          color: #666;
          background: #f0f0f0;
          padding: 4px 8px;
          border-radius: 8px;
        }
      }

      .custom-uploader {
        :deep(.van-uploader__wrapper) {
          gap: 8px;
        }

        :deep(.van-uploader__upload) {
          border: 2px dashed #d9d9d9;
          border-radius: 12px;
          background: #fafafa;
          transition: all 0.3s ease;

          &:hover {
            border-color: #52c41a;
            background: #f6ffed;
          }
        }
      }
    }

    .upload-tips {
      background: #f8faff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 20px;

      .tips-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #1890ff;
      }

      .tips-content {
        .tip-item {
          display: flex;
          margin-bottom: 8px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .tip-label {
            color: #666;
            min-width: 60px;
          }

          .tip-value {
            color: #333;
            flex: 1;
          }
        }
      }
    }

    .upload-progress {
      background: #f8faff;
      border-radius: 12px;
      padding: 16px;

      .progress-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #1890ff;
      }

      .progress-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .progress-name {
            font-size: 12px;
            color: #333;
            flex: 1;
            margin-right: 8px;
          }

          .progress-status {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
}
</style>
