<template>
  <div class="mod-config">
    <el-header height="115px" style="padding: 0">
      <div class="statistical-data-card-box">
        <el-card class="statistical-data-box-card" style="flex: 3;height: 110px;">
          <div slot="header" class="clearfix statistical-data-box-card-title">
            <span class="statistical-data-box-card-title-text">收入统计</span>
          </div>
          <div class="statistical-data-box-card-body clearfix">
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">收入总额</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[1].price | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">已收</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[1].arrivePrice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">未收</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[1].price -analysis[1].arrivePrice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">总开票总额</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[1].invoice | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">已开票</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[1].arriveInvoice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">未开票</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[1].invoice -analysis[1].arriveInvoice) | SumFormat }}</span>
            </span>
          </div>
        </el-card>
        <el-card class="statistical-data-box-card" style="flex: 3;height: 110px;">
          <div slot="header" class="clearfix statistical-data-box-card-title">
            <span class="statistical-data-box-card-title-text">支出统计</span>
          </div>
          <div class="statistical-data-box-card-body clearfix">
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">支出总额</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[0].price | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">已付</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[0].arrivePrice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">未付</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[0].price -analysis[0].arrivePrice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">总收票总额</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[0].invoice | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">已收票</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[0].arriveInvoice) | SumFormat }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">未收票</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[0].invoice -analysis[0].arriveInvoice) | SumFormat }}</span>
            </span>
          </div>
        </el-card>
        <el-card class="statistical-data-box-card" style="flex: 3;height: 110px;">
          <div slot="header" class="clearfix statistical-data-box-card-title">
            <span class="statistical-data-box-card-title-text">利润统计</span>
          </div>
          <div class="statistical-data-box-card-body clearfix">
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">客户付</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[0].clientPrice | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">总收入</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ analysis[1].price | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">余额</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" :style="( analysis[1].price  -analysis[0].clientPrice ) > 0 ? 'color:#4485ff' : 'color:red' ">{{ ( analysis[1].price  -analysis[0].clientPrice ) | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">总支出</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[0].price) | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">服务费</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ (analysis[1].servicePrice) | SumFormat
              }}</span>
            </span>
            <span class="card-body-title-box" style="flex: 3;">
              <span class="statistical-data-box-card-body-name">利润</span>
              <div style="width: 10px;height: 12px"></div>
              <span class="statistical-data-box-card-body-key" style="color:#4485ff">{{ ( analysis[0].clientPrice - analysis[0].price + analysis[1].servicePrice) | SumFormat }}</span>
            </span>
          </div>
        </el-card>
      </div>
    </el-header>
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="关键词" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.priceStatus" placeholder="收/付款状态" filterable>
          <el-option label="全部(收/付款状态)" value=""></el-option>
          <el-option v-for="item in contractPriceStatus" :key="item.key" :label="item.value"
            :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.isInvoice" placeholder="是否发票" filterable>
          <el-option label="全部(是否发票)" value=""></el-option>
          <el-option v-for="item in isInvoice" :key="item.key" :label="item.value"
            :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.invoiceStatus" placeholder="发票状态" filterable>
          <el-option label="全部(发票状态)" value=""></el-option>
          <el-option v-for="item in contractInvoiceStatus" :key="item.key" :label="item.value"
            :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="dataForm.priceConfigId" placeholder="科目" filterable>
          <el-option label="全部(科目)" value=""></el-option>
          <el-option v-for="item in priceConfig" :key="item.id" :label="item.name"
            :value="item.name"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="dataForm.priceBankId" placeholder="银行" filterable>
          <el-option label="全部(银行)" value=""></el-option>
          <el-option v-for="item in priceBank" :key="item.id" :label="item.name"
            :value="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activitysettle:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activitysettle:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
          <el-button @click="exportHandle()" type="success">导出</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/activity/activitysettle/importExcel?appid=' + appid + '&activityId=' + dataForm.activityId" :name="'结算导入'"></Upload>
        </el-button>  
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <!-- <el-table-column prop="priceConfigName" header-align="center" align="center" label="类型">
      </el-table-column> -->
      <el-table-column prop="settleType" header-align="center" align="center" label="类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + scope.row.settleType">{{ scope.row.settleType == null ? '空' :
            settleType[scope.row.settleType].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="客户/供应商">
        <div slot-scope="scope">
          <div v-if="scope.row.type == 0">{{ scope.row.supplierName }}</div>
          <div v-else>{{ scope.row.clientName }}</div>
        </div>
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="摘要">
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="收款or付款">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + scope.row.type">{{ scope.row.type == null ? '空' :
            contractTypeSimple[scope.row.type].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="应收/应付金额">
      </el-table-column>
      <el-table-column prop="clientPrice" header-align="center" align="center" label="客户付">
        <div slot-scope="scope">
          <div v-if="scope.row.type == 0" @click="activitysettleupdateclientprice(scope.row.id)">
            {{ scope.row.clientPrice}}</div>
            <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="payTime" header-align="center" align="center" label="预计支付时间">
      </el-table-column>
      <el-table-column prop="priceStatus" header-align="center" align="center" label="收/付款状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + scope.row.priceStatus">{{ scope.row.priceStatus
            == null ? '空' : (scope.row.type == 0 ?
              contractPriceStatus[scope.row.priceStatus].value : contractPriceStatus[scope.row.priceStatus].value1)
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="arrivePrice" header-align="center" align="center" label="已收/已付金额">
      </el-table-column>
      <el-table-column prop="isInvoice" header-align="center" align="center" label="是否发票">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + scope.row.isInvoice">{{ scope.row.isInvoice == null ? '空' :
            isInvoice[scope.row.isInvoice].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="invoice" header-align="center" align="center" label="开票金额">
        <div slot-scope="scope">
          {{ scope.row.isInvoice == 1 ? scope.row.invoice : '-' }}
        </div>
      </el-table-column>
      <el-table-column prop="arriveInvoice" header-align="center" align="center" label="已开票金额">
        <div slot-scope="scope">
          {{ scope.row.isInvoice == 1 ? scope.row.arriveInvoice : '-' }}
        </div>
      </el-table-column>
      <el-table-column prop="invoiceStatus" header-align="center" align="center" label="开票状态">
        <div slot-scope="scope">
          <el-tag v-if="scope.row.isInvoice == 1" type="primary" :class="'tag-color-mini tag-color-' + scope.row.invoiceStatus">{{ scope.row.invoiceStatus == null ? '空' :
            contractInvoiceStatus[scope.row.invoiceStatus].value }}</el-tag>
            <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="invoiceType" header-align="center" align="center" label="发票类型">
        <div slot-scope="scope">
          <el-tag v-if="scope.row.isInvoice == 1" type="primary" :class="'tag-color-mini tag-color-' + scope.row.invoiceType">{{ scope.row.invoiceType == null ? '空' :
            contractInvoiceType[scope.row.invoiceType].value }}</el-tag>
            <div v-else>-</div>
        </div>
      </el-table-column>
      <!-- <el-table-column prop="refundPrice" header-align="center" align="center" label="退款金额">
      </el-table-column>
      <el-table-column prop="redInvoice" header-align="center" align="center" label="已红冲金额">
      </el-table-column> -->
      <el-table-column prop="invoiceBillType" header-align="center" align="center" label="开票类型">
        <div slot-scope="scope">
          <el-tag v-if="scope.row.isInvoice == 1" type="primary" :class="'tag-color-mini tag-color-' + scope.row.invoiceBillType">{{ scope.row.invoiceBillType == null ? '空' :
            contractInvoiceBillType[scope.row.invoiceBillType].value }}</el-tag>
            <div v-else>-</div>
        </div>
      </el-table-column>
      <el-table-column prop="remarks" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="250" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.priceStatus != 0 && scope.row.priceStatus != 3" type="text" size="small"
            style="color: green" @click="activitysettlepricedetail(scope.row.id)">{{scope.row.type == 0 ? '付款明细' : '收款明细'}}</el-button>
          <el-button type="text" size="small" v-if="(scope.row.priceStatus == 1 || scope.row.priceStatus == 0) && isAuth('activity:activitysettle:pay')"  @click="activitysettlepay(scope.row.id)">{{scope.row.type == 0 ? '付款' : '收款'}}</el-button>
          <el-button v-if="scope.row.invoiceStatus != 0 && scope.row.invoiceStatus != 3" type="text" size="small"
            style="color: green" @click="activitysettleinvoicedetail(scope.row.id)">{{scope.row.type == 0 ? '收票明细' : '开票明细'}}</el-button>
          <el-button type="text" size="small" v-if="(scope.row.invoiceStatus == 1 || scope.row.invoiceStatus == 0)  && isAuth('activity:activitysettle:invoice')"  @click="activitysettleinvoice(scope.row.id)">{{scope.row.type == 0 ? '收票' : '开票'}}</el-button>
          <el-button type="text" size="small" v-if="isAuth('activity:activitysettle:update')" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" v-if="isAuth('activity:activitysettle:delete')" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination> -->
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <activitysettlepricedetail v-if="activitysettlepricedetailVisible" ref="activitysettlepricedetail" @refreshDataList="getDataList"></activitysettlepricedetail>
    <activitysettlepay v-if="activitysettlepayVisible" ref="activitysettlepay" @refreshDataList="getDataList"></activitysettlepay>
    <activitysettleinvoicedetail v-if="activitysettleinvoicedetailVisible" ref="activitysettleinvoicedetail" @refreshDataList="getDataList"></activitysettleinvoicedetail>
    <activitysettleinvoice v-if="activitysettleinvoiceVisible" ref="activitysettleinvoice" @refreshDataList="getDataList"></activitysettleinvoice>
    <activitysettleupdateclientprice v-if="activitysettleupdateclientpriceVisible" ref="activitysettleupdateclientprice" @refreshDataList="getDataList"></activitysettleupdateclientprice>
  </div>
</template>

<script>
import {
  contractPriceStatus,
  contractTypeSimple,
  isInvoice,
  contractInvoiceType,
  contractInvoiceStatus,
  contractInvoiceSellBuy,
  contractInvoiceBillType,
} from "@/data/price";
import {
  settleType,
} from "@/data/activity";
import AddOrUpdate from './activitysettle-add-or-update'
import activitysettlepricedetail from './activitysettle-pricedetail'
import activitysettleinvoicedetail from './activitysettle-invoicedetail'
import activitysettlepay from './activitysettle-pay'
import activitysettleinvoice from './activitysettle-invoice'
import activitysettleupdateclientprice from './activitysettle-updateclientprice'
export default {
  data() {
    return {
      analysis: [],
      priceConfig: [],
      priceBank: [],
      isInvoice,
      settleType,
      contractInvoiceType,
      contractInvoiceStatus,
      contractInvoiceSellBuy,
      contractInvoiceBillType,
      contractPriceStatus,
      contractTypeSimple,
      dataForm: {
        name: '',
        appid: '',
        activityId: '',
        priceStatus: '',
        priceConfigId: '',
        type: '',
        invoiceStatus: '',
        isInvoice: '',
        priceBankId: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activitysettlepricedetailVisible: false,
      activitysettlepayVisible: false,
      activitysettleinvoicedetailVisible: false,
      activitysettleinvoiceVisible: false,
      activitysettleupdateclientpriceVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    activitysettlepricedetail,
    activitysettlepay,
    activitysettleinvoicedetail,
    activitysettleinvoice,
    activitysettleupdateclientprice,
    Upload: () => import('@/components/upload'),
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList()
    this.getPriceConfig();
    this.getPriceBank();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activitysettle/export?" + [
        "token=" + this.$cookie.get('token'),
        "name=" + this.dataForm.name,
        "priceConfigId=" + this.dataForm.priceConfigId,
        "activityId=" + this.dataForm.activityId,
        "priceStatus=" + this.dataForm.priceStatus,
        "invoiceStatus=" + this.dataForm.invoiceStatus,
        "isInvoice=" + this.dataForm.isInvoice,
        "type=" + this.dataForm.type,
        "appid=" + this.$cookie.get('appid'),
        "priceBankId=" + this.dataForm.priceBankId
      ].join('&'));
      window.open(url);
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activitysettle/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.dataForm.activityId,
          'priceConfigId': this.dataForm.priceConfigId,
          'priceBankId': this.dataForm.priceBankId,
          'priceStatus': this.dataForm.priceStatus,
          'invoiceStatus': this.dataForm.invoiceStatus,
          'isInvoice': this.dataForm.isInvoice,
          'type': this.dataForm.type,
          'appid': this.$cookie.get('appid'),
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
      this.$http({
        url: this.$http.adornUrl('/activity/activitysettle/analysis'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.dataForm.activityId,
          'priceConfigId': this.dataForm.priceConfigId,
          'priceBankId': this.dataForm.priceBankId,
          'priceStatus': this.dataForm.priceStatus,
          'invoiceStatus': this.dataForm.invoiceStatus,
          'isInvoice': this.dataForm.isInvoice,
          'type': this.dataForm.type,
          'appid': this.$cookie.get('appid'),
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.analysis = data.result
        } else {
          this.analysis = []
        }
      })
    },
    getPriceConfig() {
      this.$http({
        url: this.$http.adornUrl("/price/priceconfig/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceConfig = data.result;
          }
        })
    },
    getPriceBank() {
      this.$http({
        url: this.$http.adornUrl("/price/pricebank/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceBank = data.result;
          }
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.dataForm.activityId)
      })
    },
    activitysettlepricedetail(id) {
      this.activitysettlepricedetailVisible = true
      this.$nextTick(() => {
        this.$refs.activitysettlepricedetail.init(id)
      })
    },
    activitysettlepay(id) {
      this.activitysettlepayVisible = true
      this.$nextTick(() => {
        this.$refs.activitysettlepay.init(id)
      })
    },
    activitysettleinvoicedetail(id) {
      this.activitysettleinvoicedetailVisible = true
      this.$nextTick(() => {
        this.$refs.activitysettleinvoicedetail.init(id)
      })
    },
    activitysettleinvoice(id) {
      this.activitysettleinvoiceVisible = true
      this.$nextTick(() => {
        this.$refs.activitysettleinvoice.init(id)
      })
    },
    activitysettleupdateclientprice(id) {
      this.activitysettleupdateclientpriceVisible = true
      this.$nextTick(() => {
        this.$refs.activitysettleupdateclientprice.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activitysettle/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
  font-size: 12px;
  margin: 0 5px;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.statistical-data-box-card /deep/ .el-card__header {
  border: 0;
  padding: 0;
}

.statistical-data-box-card /deep/ .el-card__body {
  padding: 3px 20px;
}
.statistical-data-box-card-body-key{
  font-size: 0.8vmax;
  font-family: DIN Alternate;
  font-weight: bold;
  color: #333333;
}
.statistical-data-box-card-body-name{
  font-size: 0.7vmax;
  font-family: PingFang SC;
  font-weight: 400;
  color: #666666;
}
</style>