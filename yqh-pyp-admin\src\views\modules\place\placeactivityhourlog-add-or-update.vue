<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="ip地址" prop="ipAddr">
      <el-input v-model="dataForm.ipAddr" placeholder="ip地址"></el-input>
    </el-form-item>
    <el-form-item label="设备" prop="device">
      <el-input v-model="dataForm.device" placeholder="设备"></el-input>
    </el-form-item>
    <el-form-item label="mac地址" prop="macAddr">
      <el-input v-model="dataForm.macAddr" placeholder="mac地址"></el-input>
    </el-form-item>
    <el-form-item label="浏览时长" prop="count">
      <el-input v-model="dataForm.count" placeholder="浏览时长"></el-input>
    </el-form-item>
    <el-form-item label="房间id" prop="placeId">
      <el-input v-model="dataForm.placeId" placeholder="房间id"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          ipAddr: '',
          device: '',
          macAddr: '',
          count: '',
          placeId: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          ipAddr: [
            { required: true, message: 'ip地址不能为空', trigger: 'blur' }
          ],
          device: [
            { required: true, message: '设备不能为空', trigger: 'blur' }
          ],
          macAddr: [
            { required: true, message: 'mac地址不能为空', trigger: 'blur' }
          ],
          count: [
            { required: true, message: '浏览时长不能为空', trigger: 'blur' }
          ],
          placeId: [
            { required: true, message: '房间id不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivityhourlog/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.placeActivityHourLog.activityId
                this.dataForm.ipAddr = data.placeActivityHourLog.ipAddr
                this.dataForm.device = data.placeActivityHourLog.device
                this.dataForm.macAddr = data.placeActivityHourLog.macAddr
                this.dataForm.createOn = data.placeActivityHourLog.createOn
                this.dataForm.createBy = data.placeActivityHourLog.createBy
                this.dataForm.updateOn = data.placeActivityHourLog.updateOn
                this.dataForm.updateBy = data.placeActivityHourLog.updateBy
                this.dataForm.count = data.placeActivityHourLog.count
                this.dataForm.placeId = data.placeActivityHourLog.placeId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/place/placeactivityhourlog/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'ipAddr': this.dataForm.ipAddr,
                'device': this.dataForm.device,
                'macAddr': this.dataForm.macAddr,
                'count': this.dataForm.count,
                'placeId': this.dataForm.placeId
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
