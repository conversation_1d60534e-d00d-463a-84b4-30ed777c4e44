<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input v-model="dataForm.paixu" placeholder="排序"></el-input>
      </el-form-item>
      <!-- <el-form-item label="背景颜色" prop="color">
        <el-input v-model="dataForm.color" placeholder="背景颜色"></el-input>
      </el-form-item> -->
      <el-form-item label="详细介绍" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input v-model="dataForm.longitude" placeholder="经度"></el-input>
          </el-form-item></el-col>
        <el-col :span="12"><el-form-item label="纬度" prop="latitude">
            <el-input v-model="dataForm.latitude" placeholder="纬度"></el-input>
          </el-form-item></el-col>
      </el-row>
      <a style="color:red;margin-left:50px" target="_blank"
        href="https://lbs.qq.com/tool/getpoint/index.html">腾讯地图坐标拾取工具</a>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        title: '',
        activityId: '',
        paixu: '',
        longitude: '',
        latitude: '',
        color: '',
        content: ''
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '活动表id不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        longitude: [
          { required: true, message: '经度不能为空', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '纬度不能为空', trigger: 'blur' }
        ],
        color: [
          { required: true, message: '背景颜色不能为空', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '内容不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
  methods: {
    init(activityId, id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.dataForm.activityId = activityId
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitynav/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.title = data.activityNav.title
              this.dataForm.activityId = data.activityNav.activityId
              this.dataForm.paixu = data.activityNav.paixu
              this.dataForm.longitude = data.activityNav.longitude
              this.dataForm.latitude = data.activityNav.latitude
              this.dataForm.color = data.activityNav.color
              this.dataForm.content = data.activityNav.content
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitynav/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'title': this.dataForm.title,
              'activityId': this.dataForm.activityId,
              'paixu': this.dataForm.paixu,
              'longitude': this.dataForm.longitude,
              'latitude': this.dataForm.latitude,
              'color': this.dataForm.color,
              'content': this.dataForm.content
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    },
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    // 上传成功（背景）
    backgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.picUrl = response.url;
        this.$message({
          message: '上传成功',
          type: 'success',
        })
      } else {
        this.$message.error(response.msg);
      }
    },

  }
}
</script>
