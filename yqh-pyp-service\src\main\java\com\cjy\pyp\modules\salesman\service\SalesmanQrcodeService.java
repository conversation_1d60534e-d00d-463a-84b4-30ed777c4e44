package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanQrcodeEntity;

import java.util.List;
import java.util.Map;

/**
 * 业务员二维码记录服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
public interface SalesmanQrcodeService extends IService<SalesmanQrcodeEntity> {

    /**
     * 分页查询二维码记录列表
     * @param params 查询参数
     * @return 分页结果
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 为业务员生成二维码
     * @param salesmanId 业务员ID
     * @param activityId 活动ID（可选）
     * @param appid 应用ID
     * @return 二维码记录
     */
    SalesmanQrcodeEntity generateQrcode(Long salesmanId, Long activityId, String appid);

    /**
     * 根据业务员ID查询二维码列表
     * @param salesmanId 业务员ID
     * @return 二维码列表
     */
    List<SalesmanQrcodeEntity> findBySalesmanId(Long salesmanId);

    /**
     * 根据二维码内容查询二维码记录
     * @param qrcodeContent 二维码内容
     * @return 二维码记录
     */
    SalesmanQrcodeEntity findByQrcodeContent(String qrcodeContent);

    /**
     * 增加扫码次数
     * @param qrcodeId 二维码ID
     */
    void increaseScanCount(Long qrcodeId);

    /**
     * 增加订单统计
     * @param qrcodeId 二维码ID
     * @param orderAmount 订单金额
     */
    void increaseOrderStats(Long qrcodeId, java.math.BigDecimal orderAmount);

    /**
     * 根据应用ID查询二维码列表
     * @param appid 应用ID
     * @return 二维码列表
     */
    List<SalesmanQrcodeEntity> findByAppid(String appid);
}
