<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="考卷id" prop="examId">
      <el-input v-model="dataForm.examId" placeholder="考卷id"></el-input>
    </el-form-item>
    <el-form-item label="问题id" prop="questionId">
      <el-input v-model="dataForm.questionId" placeholder="问题id"></el-input>
    </el-form-item>
    <el-form-item label="答案" prop="optionId">
      <el-input v-model="dataForm.optionId" placeholder="答案"></el-input>
    </el-form-item>
    <el-form-item label="用户活动表id" prop="activityUserId">
      <el-input v-model="dataForm.activityUserId" placeholder="用户活动表id"></el-input>
    </el-form-item>
    <el-form-item label="用户表id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户表id"></el-input>
    </el-form-item>
    <el-form-item label="正确答案" prop="realOptionId">
      <el-input v-model="dataForm.realOptionId" placeholder="正确答案"></el-input>
    </el-form-item>
    <el-form-item label="答案状态，0-不正确，1-正确" prop="status">
      <el-input v-model="dataForm.status" placeholder="答案状态，0-不正确，1-正确"></el-input>
    </el-form-item>
    <el-form-item label="用户考试id" prop="examActivityUserId">
      <el-input v-model="dataForm.examActivityUserId" placeholder="用户考试id"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          examId: '',
          questionId: '',
          optionId: '',
          activityUserId: '',
          userId: '',
          realOptionId: '',
          status: '',
          examActivityUserId: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          examId: [
            { required: true, message: '考卷id不能为空', trigger: 'blur' }
          ],
          questionId: [
            { required: true, message: '问题id不能为空', trigger: 'blur' }
          ],
          optionId: [
            { required: true, message: '答案不能为空', trigger: 'blur' }
          ],
          activityUserId: [
            { required: true, message: '用户活动表id不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户表id不能为空', trigger: 'blur' }
          ],
          realOptionId: [
            { required: true, message: '正确答案不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '答案状态，0-不正确，1-正确不能为空', trigger: 'blur' }
          ],
          examActivityUserId: [
            { required: true, message: '用户考试id不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examactivityuseroption/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.dataForm.activityId = data.examActivityUserOption.activityId
                this.dataForm.examId = data.examActivityUserOption.examId
                this.dataForm.questionId = data.examActivityUserOption.questionId
                this.dataForm.optionId = data.examActivityUserOption.optionId
                this.dataForm.activityUserId = data.examActivityUserOption.activityUserId
                this.dataForm.userId = data.examActivityUserOption.userId
                this.dataForm.realOptionId = data.examActivityUserOption.realOptionId
                this.dataForm.status = data.examActivityUserOption.status
                this.dataForm.examActivityUserId = data.examActivityUserOption.examActivityUserId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examactivityuseroption/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'examId': this.dataForm.examId,
                'questionId': this.dataForm.questionId,
                'optionId': this.dataForm.optionId,
                'activityUserId': this.dataForm.activityUserId,
                'userId': this.dataForm.userId,
                'realOptionId': this.dataForm.realOptionId,
                'status': this.dataForm.status,
                'examActivityUserId': this.dataForm.examActivityUserId
              })
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
