<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="ip地址" prop="ipAddr">
      <el-input v-model="dataForm.ipAddr" placeholder="ip地址"></el-input>
    </el-form-item>
    <el-form-item label="设备" prop="device">
      <el-input v-model="dataForm.device" placeholder="设备"></el-input>
    </el-form-item>
    <el-form-item label="mac地址" prop="macAddr">
      <el-input v-model="dataForm.macAddr" placeholder="mac地址"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="修改时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="修改时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          ipAddr: '',
          device: '',
          macAddr: '',
          createTime: '',
          updateTime: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          ipAddr: [
            { required: true, message: 'ip地址不能为空', trigger: 'blur' }
          ],
          device: [
            { required: true, message: '设备不能为空', trigger: 'blur' }
          ],
          macAddr: [
            { required: true, message: 'mac地址不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '修改时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityviewlog/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityViewLog.activityId
                this.dataForm.ipAddr = data.activityViewLog.ipAddr
                this.dataForm.device = data.activityViewLog.device
                this.dataForm.macAddr = data.activityViewLog.macAddr
                this.dataForm.createTime = data.activityViewLog.createTime
                this.dataForm.updateTime = data.activityViewLog.updateTime
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityviewlog/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'ipAddr': this.dataForm.ipAddr,
                'device': this.dataForm.device,
                'macAddr': this.dataForm.macAddr,
                'createTime': this.dataForm.createTime,
                'updateTime': this.dataForm.updateTime
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
