<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('apply:applyactivitychannelconfig:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('apply:applyactivitychannelconfig:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="名称">
      </el-table-column>
      <el-table-column prop="pname" header-align="center" align="center" label="父通道">
      </el-table-column>
      <el-table-column prop="description" header-align="center" align="center" label="描述">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="价格">
      </el-table-column>
      <el-table-column prop="maxNumber" header-align="center" align="center" label="最大报名人数">
      </el-table-column>
      <el-table-column prop="deadline" header-align="center" align="center" label="截止日期">
      </el-table-column>
      <el-table-column prop="isShow" header-align="center" align="center" label="是否展示">
        <div slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.isShow == 0">否</el-tag>
          <el-tag type="success" v-else>是</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="isSms" header-align="center" align="center" label="是否短信验证">
        <div slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.isSms == 0">否</el-tag>
          <el-tag type="success" v-else>是</el-tag>
        </div>
      </el-table-column>
      <el-table-column v-if="appid != 'wx0770d56458b33c67'" prop="isInvoice" header-align="center" align="center"
        label="是否收集发票">
        <div slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.isInvoice == 0">否</el-tag>
          <el-tag type="success" v-else>是</el-tag>
        </div>
      </el-table-column>
      <el-table-column  prop="isVerify" header-align="center" align="center"
        label="是否审核">
        <div slot-scope="scope">
          <el-tag type="danger" v-if="scope.row.isVerify == 0">否</el-tag>
          <el-tag type="success" v-else>是</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="paixu" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column prop="pvCount"   width="150px" header-align="center" align="center" label="报名通道(点击跳转复制)">
        <div slot-scope="scope" v-if="!scope.row.pid" @click="openUrl(wxAccount.baseUrl + 'apply/index?id='+dataForm.activityId+'&channelId=' + scope.row.id)">
          <vue-qrcode :options="{ width: 120 }" :value="wxAccount.baseUrl + 'apply/index?id='+dataForm.activityId+'&channelId=' + scope.row.id"></vue-qrcode>
        </div>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isNeedInvite == 2" type="text" size="small"
            @click="invitecode(scope.row.id)">邀请码</el-button>
          <el-button type="text" size="small" @click="applyActivityConfig(scope.row.id)">字段配置</el-button>
          <el-button type="text" size="small" v-if="isAuth('apply:applyactivitychannelconfig:copy')"
            @click="copy(scope.row.id)">复制</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './applyactivitychannelconfig-add-or-update'
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  data() {
    return {
      wxAccount: {},
      appid: '',
      dataForm: {
        key: '',
        activityId: undefined
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    VueQrcode,
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList()
    this.getAccountInfo()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/apply/applyactivitychannelconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
          'activityId': this.dataForm.activityId,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
        }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
      })
    },
    // 字段配置
    applyActivityConfig(v) {
      this.$router.push({
        name: 'applyActivityConfig',
        query: {
          'channelId': v
        }
      }
      )
    },
    invitecode(v) {
      this.$router.push({
        name: 'applyinvitecode',
        query: {
          'channelId': v
        }
      }
      )
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/apply/applyactivitychannelconfig/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    copy(id) {
      this.$confirm(`确定复制该报名通道?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/apply/applyactivitychannelconfig/copy'),
          method: 'get',
          params: this.$http.adornParams({ id: id })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
      openUrl(v) {
        window.open(v)
      }
  }
}
</script>
