<template>
  <div class="page">
    <img style="width: 100%;" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230324/9dc5404a275c42ac9a07f42ccd9cc245.png"
      alt="">
    <div class="content" v-if="userInfo.status == 1">
      <div style="height: 90px; width: 100%; text-align: center; padding-top:10px">
        <div style="
            font-size: 18px;
            font-weight: bold;
            padding: 5px 0px;
          ">
          {{ activityInfo.name }}
        </div>
        <div style="
            font-size: 16px;
            font-weight: bold;
            padding: 5px 0px;
          ">
          {{ activityInfo.startTime.substring(0, 11) }} -
          {{ activityInfo.endTime.substring(5, 11) }}
        </div>
      </div>
      <div style="padding: 10px 0px">{{ userInfo.contact }}的参会二维码</div>
      <vue-qrcode :options="{ width: 240 }" :value="this.userInfo.id"></vue-qrcode>
    </div>
    <div class="content" v-else>
      <van-button round type="info" class="bottom-button"
        @click="$router.push({ name: 'applyIndex', query: { id: activityId } })">点击报名，生成专属参会二维码</van-button>
    </div>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  components: {
    VueQrcode
  },
  data() {
    return {
      activityId: undefined,
      userInfo: {},
      activityInfo: {},
    };
  },
  mounted() {
    document.title = "我的参会二维码";
    this.activityId = this.$route.query.id;
    this.getActivityInfo();
    this.getUserActivityInfo();
  },
  methods: {
    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/web/activity/activityuserapplyorder/applyUserInfo/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.activityUserEntity;
            this.applyActivityConfigList = res.applyActivityConfigEntities;
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityInfo() {
      this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then(res => {
        if (res.code == 200) {
          this.activityInfo = res.activity
        } else {
          this.activityInfo = {}
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #f6f6f6;
}

.content {
  text-align: center;
  padding: 20px 0;
  width: 90%;
  margin-left: 5%;
  background: white;
  border-radius: 20px;
  position: fixed;
  top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
}
</style>