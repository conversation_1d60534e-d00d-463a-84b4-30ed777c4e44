<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="考卷id" prop="examId">
      <el-input v-model="dataForm.examId" placeholder="考卷id"></el-input>
    </el-form-item>
    <el-form-item label="选项名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="选项名称"></el-input>
    </el-form-item>
    <el-form-item label="问题id" prop="questionId">
      <el-input v-model="dataForm.questionId" placeholder="问题id"></el-input>
    </el-form-item>
    <el-form-item label="选项，A,B,C,D" prop="option">
      <el-input v-model="dataForm.option" placeholder="选项，A,B,C,D"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          examId: '',
          name: '',
          questionId: '',
          option: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          examId: [
            { required: true, message: '考卷id不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '选项名称不能为空', trigger: 'blur' }
          ],
          questionId: [
            { required: true, message: '问题id不能为空', trigger: 'blur' }
          ],
          option: [
            { required: true, message: '选项不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (activityId,id) {
        this.dataForm.activityId = activityId;
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examquestionoption/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.dataForm.activityId = data.examQuestionOption.activityId
                this.dataForm.examId = data.examQuestionOption.examId
                this.dataForm.name = data.examQuestionOption.name
                this.dataForm.questionId = data.examQuestionOption.questionId
                this.dataForm.option = data.examQuestionOption.option
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/exam/examquestionoption/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'examId': this.dataForm.examId,
                'name': this.dataForm.name,
                'questionId': this.dataForm.questionId,
                'option': this.dataForm.option
              })
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
