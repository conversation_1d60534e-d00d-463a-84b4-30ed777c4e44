<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activityguesttrip:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityguesttrip:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column prop="inType" header-align="center" align="center" label="来程类型（0-飞机，1-火车，2-其他）">
      </el-table-column>
      <el-table-column prop="inDate" header-align="center" align="center" label="来程日期">
      </el-table-column>
      <el-table-column prop="inNumber" header-align="center" align="center" label="来程航班号">
      </el-table-column>
      <el-table-column prop="inEndPlace" header-align="center" align="center" label="来程到达点">
      </el-table-column>
      <el-table-column prop="inEndDate" header-align="center" align="center" label="来程日期">
      </el-table-column>
      <el-table-column prop="inStartDate" header-align="center" align="center" label="来程日期">
      </el-table-column>
      <el-table-column prop="activityGuestId" header-align="center" align="center" label="">
      </el-table-column>
      <el-table-column prop="activityId" header-align="center" align="center" label="会议id">
      </el-table-column>
      <el-table-column prop="isBuy" header-align="center" align="center" label="是否购买">
        <div>

        </div>
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="价格">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <activityguesttripupdatestatus v-if="activityguesttripupdatestatusVisible" ref="activityguesttripupdatestatus" @refreshDataList="getDataList"></activityguesttripupdatestatus>
  </div>
</template>

<script>
import AddOrUpdate from './activityguesttrip-add-or-update'
import activityguesttripupdatestatus from './activityguesttrip-updatestatus'
export default {
  data() {
    return {
      dataForm: {
        name: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activityguesttripupdatestatusVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    activityguesttripupdatestatus,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityguesttrip/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    activityguesttripupdatestatusHandle(id) {
      this.activityguesttripupdatestatusVisible = true
      this.$nextTick(() => {
        this.$refs.activityguesttripupdatestatus.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguesttrip/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
