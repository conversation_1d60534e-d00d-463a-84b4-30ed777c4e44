package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.channel.utils.ChannelPermissionUtils;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.sys.service.SysUserService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.sys.service.SysUserRoleService;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 渠道管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/channel/channel")
@Api(tags = "渠道管理")
public class ChannelController extends AbstractController {
    
    @Autowired
    private ChannelService channelService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private ChannelPermissionUtils channelPermissionUtils;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("channel:channel:list")
    @ApiOperation(value = "渠道列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        // 应用渠道权限过滤
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        List<ChannelEntity> channelList = channelService.queryPageWithStats(params);
        
        return R.okList(channelList);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("channel:channel:info")
    @ApiOperation(value = "渠道信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        ChannelEntity channel = channelService.getById(id);
        return R.ok().put("channel", channel);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("channel:channel:save")
    @SysLog("保存渠道")
    @ApiOperation(value = "保存渠道", notes = "")
    public R save(@RequestBody ChannelEntity channel, @CookieValue String appid) {
        ValidatorUtils.validateEntity(channel);
        
        // 设置应用ID
        channel.setAppid(appid);
        
        // 检查渠道编号是否重复
        if (channelService.existsByCode(channel.getCode(), appid, null)) {
            return R.error("渠道编号已存在");
        }
        
        // 计算层级
        channel.setLevel(channelService.calculateLevel(channel.getParentId()));

        channelService.save(channel);

        // 如果有上级，更新上级的层级信息
        if (channel.getParentId() != null) {
            channelService.updateLevel(channel.getParentId());
        }

        // 自动创建渠道管理员账号
        createChannelAdmin(channel, appid);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("channel:channel:update")
    @SysLog("修改渠道")
    @ApiOperation(value = "修改渠道", notes = "")
    public R update(@RequestBody ChannelEntity channel, @CookieValue String appid) {
        ValidatorUtils.validateEntity(channel);

        // 获取原渠道信息
        ChannelEntity oldChannel = channelService.getById(channel.getId());
        if (oldChannel == null) {
            return R.error("渠道不存在");
        }

        // 检查渠道编号是否重复
        if (channelService.existsByCode(channel.getCode(), appid, channel.getId())) {
            return R.error("渠道编号已存在");
        }

        // 重新计算层级
        channel.setLevel(channelService.calculateLevel(channel.getParentId()));

        channelService.updateById(channel);

        // 更新层级信息
        channelService.updateLevel(channel.getId());
        if (channel.getParentId() != null) {
            channelService.updateLevel(channel.getParentId());
        }

        // 处理管理员账号
        handleChannelAdminUpdate(oldChannel, channel, appid);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("channel:channel:delete")
    @SysLog("删除渠道")
    @ApiOperation(value = "删除渠道", notes = "")
    public R delete(@RequestBody Long[] ids) {
        // 删除渠道前处理相关管理员账号
        for (Long channelId : ids) {
            handleChannelAdminDelete(channelId);
        }

        channelService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 获取渠道选择列表
     */
    @RequestMapping("/select")
    @RequiresPermissions("channel:channel:list")
    @ApiOperation(value = "渠道选择列表", notes = "")
    public R select(@CookieValue String appid) {
        List<ChannelEntity> channelList = channelService.findByAppid(appid);
        return R.ok().put("channelList", channelList);
    }

    /**
     * 获取渠道统计信息
     */
    @RequestMapping("/stats")
    @RequiresPermissions("channel:channel:list")
    @ApiOperation(value = "渠道统计信息", notes = "")
    public R stats( @CookieValue String appid) {
        Map<String, Object> stats;
        // 应用渠道权限过滤
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        
        if (!CollectionUtils.isEmpty(accessibleChannelIds) ) {

            stats = channelService.getStatsByChannelIds(accessibleChannelIds, appid);
        } else {
            stats = channelService.getOverallStatsByAppid(appid);
        }
        return R.ok().put("stats", stats);
    }

    /**
     * 自动创建渠道管理员账号
     * @param channel 渠道信息
     * @param appid 应用ID
     */
    private void createChannelAdmin(ChannelEntity channel, String appid) {
        try {
            // 生成用户名：channel_ + 渠道编号
            String username = "channel_" + channel.getCode();

            // 检查用户名是否已存在
            SysUserEntity existingUser = sysUserService.queryByUserName(username);
            if (existingUser != null) {
                return; // 用户已存在，不重复创建
            }

            // 创建渠道管理员账号
            SysUserEntity channelAdmin = new SysUserEntity();
            channelAdmin.setUsername(username);
            channelAdmin.setEmail(channel.getContactEmail());
            channelAdmin.setMobile(channel.getContactMobile());
            channelAdmin.setStatus(1); // 启用状态
            channelAdmin.setAppid(appid);
            channelAdmin.setChannelId(channel.getId());

            // 设置默认密码 123456
            String salt = RandomStringUtils.randomAlphanumeric(20);
            channelAdmin.setPassword(new Sha256Hash("123456", salt).toHex());
            channelAdmin.setSalt(salt);

            // 保存用户
            sysUserService.save(channelAdmin);

            // 分配渠道管理员角色
            Long channelAdminRoleId = 1000000000000000001L; // 渠道管理员角色ID
            sysUserRoleService.saveOrUpdate(channelAdmin.getUserId(),
                java.util.Arrays.asList(channelAdminRoleId));

        } catch (Exception e) {
            // 创建管理员失败不影响渠道创建，只记录日志
            e.printStackTrace();
        }
    }

    /**
     * 处理渠道管理员账号更新
     * @param oldChannel 原渠道信息
     * @param newChannel 新渠道信息
     * @param appid 应用ID
     */
    private void handleChannelAdminUpdate(ChannelEntity oldChannel, ChannelEntity newChannel, String appid) {
        try {
            String oldUsername = "channel_" + oldChannel.getCode();
            String newUsername = "channel_" + newChannel.getCode();

            // 查找现有的管理员账号
            SysUserEntity existingAdmin = sysUserService.queryByUserName(oldUsername);

            if (existingAdmin != null) {
                // 如果渠道编号改变了，需要更新用户名
                if (!oldChannel.getCode().equals(newChannel.getCode())) {
                    // 检查新用户名是否已存在
                    SysUserEntity conflictUser = sysUserService.queryByUserName(newUsername);
                    if (conflictUser != null && !conflictUser.getUserId().equals(existingAdmin.getUserId())) {
                        // 新用户名已被占用，保持原用户名不变
                        return;
                    }

                    // 更新用户名
                    existingAdmin.setUsername(newUsername);
                }

                // 更新联系信息
                existingAdmin.setEmail(newChannel.getContactEmail());
                existingAdmin.setMobile(newChannel.getContactMobile());
                existingAdmin.setChannelId(newChannel.getId());

                sysUserService.updateById(existingAdmin);

            } else {
                // 没有找到管理员账号，检查是否需要创建
                // 查找是否有其他管理员关联到这个渠道
                List<SysUserEntity> channelAdmins = findAdminsByChannelId(newChannel.getId());

                if (channelAdmins.isEmpty()) {
                    // 没有管理员，创建新的管理员账号
                    createChannelAdmin(newChannel, appid);
                }
            }

        } catch (Exception e) {
            // 管理员账号更新失败不影响渠道更新，只记录日志
            e.printStackTrace();
        }
    }

    /**
     * 查找指定渠道的管理员账号
     * @param channelId 渠道ID
     * @return 管理员列表
     */
    private List<SysUserEntity> findAdminsByChannelId(Long channelId) {
        try {
            // 查询渠道管理员角色的用户
            Map<String, Object> params = new java.util.HashMap<>();
            params.put("channelId", channelId);
            params.put("roleId", 1000000000000000001L); // 渠道管理员角色ID
            params.put("page", "1");
            params.put("limit", "100");

            return sysUserService.queryPageByRole(params);
        } catch (Exception e) {
            e.printStackTrace();
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 处理渠道删除时的管理员账号
     * @param channelId 渠道ID
     */
    private void handleChannelAdminDelete(Long channelId) {
        try {
            // 查找该渠道的所有管理员
            List<SysUserEntity> channelAdmins = findAdminsByChannelId(channelId);

            for (SysUserEntity admin : channelAdmins) {
                // 可以选择删除管理员账号或者将其channel_id设为null
                // 这里选择将channel_id设为null，保留账号但取消渠道关联
                admin.setChannelId(null);
                sysUserService.updateById(admin);

                // 如果要完全删除管理员账号，可以使用下面的代码：
                // sysUserService.removeById(admin.getUserId());
            }

        } catch (Exception e) {
            // 管理员账号处理失败不影响渠道删除，只记录日志
            e.printStackTrace();
        }
    }

    /**
     * 渠道客户列表
     */
    @RequestMapping("/customerList")
    @RequiresPermissions("channel:channel:list")
    @ApiOperation(value = "渠道客户列表", notes = "获取渠道下的客户列表")
    public R customerList(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);

        // 应用渠道权限过滤
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }

        List<WxUser>  page = channelService.queryCustomerPage(params);
        return R.okList(page);
    }

    /**
     * 渠道客户统计
     */
    @RequestMapping("/customerStats")
    @RequiresPermissions("channel:channel:list")
    @ApiOperation(value = "渠道客户统计", notes = "获取渠道客户统计数据")
    public R customerStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);

        // 应用渠道权限过滤
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }

        Map<String, Object> stats = channelService.getCustomerStats(params);
        return R.ok().put("stats", stats);
    }

    /**
     * 批量更新客户渠道归属
     */
    @RequestMapping("/updateCustomerChannel")
    @RequiresPermissions("channel:channel:update")
    @ApiOperation(value = "批量更新客户渠道归属", notes = "批量更新微信用户的渠道归属")
    public R updateCustomerChannel(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        try {
            channelService.batchUpdateCustomerChannel(appid);
            return R.ok().put("message", "批量更新客户渠道归属成功");
        } catch (Exception e) {
            return R.error("批量更新失败：" + e.getMessage());
        }
    }

}
