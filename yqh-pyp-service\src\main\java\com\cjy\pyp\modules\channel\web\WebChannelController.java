package com.cjy.pyp.modules.channel.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.channel.service.ChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 渠道Web接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("web/channel")
@Api(tags = "渠道Web接口")
public class WebChannelController {

    @Autowired
    private ChannelService channelService;

    /**
     * 获取渠道信息
     */
    @RequestMapping("/info/{id}")
    @ApiOperation(value = "获取渠道信息", notes = "根据渠道ID获取渠道详细信息")
    public R info(@PathVariable("id") Long id) {
        try {
            ChannelEntity channel = channelService.getById(id);
            if (channel == null) {
                return R.error("渠道不存在");
            }
            
            // 检查渠道状态
            if (channel.getStatus() != 1) {
                return R.error("渠道已禁用");
            }
            
            return R.ok().put("result", channel);
        } catch (Exception e) {
            return R.error("获取渠道信息失败：" + e.getMessage());
        }
    }
}
