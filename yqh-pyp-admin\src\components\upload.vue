<template>
    <div @click="selectFile">
        <input type="file" ref="fileInput" v-show="false" @change="onFileChange" />
        <div>{{uploading?infoText:'导入数据'}}</div>
    </div>
</template>

<script>
export default {
    name: "oss-uploader",
    data() {
        return {
            uploading: false,
            infoText: "上传中...",
            cosConfig: []
        }
    },
    props: {
        url: {
            type: String,
            required: true,
            default: ""
        },
        name: {
            type: String,
            default: ""
        },
    },
    mounted() {
    },
    methods: {
        selectFile() {//选择文件
            if (!this.uploading) {
                this.$refs.fileInput.click();
            }
        },
        onFileChange() {
            let file = this.$refs.fileInput.files[0];
            this.uploading = true;
            let formData = new FormData();
            formData.append("file", file)
            this.$http({
                url: this.$http.adornUrl(this.url),
                responseType: 'arraybuffer',
                config: {
                withCredentials: false
                },
                method: 'post',
                data: formData
            }).then(({ data }) => {
                console.log(data);
                if (data.byteLength < 50) {
                    this.$message.success("导入成功");
                    this.$emit('uploaded');
                } else {
                    this.$confirm(`存在导入失败数据，点击确定下载`, '提示', {
                        confirmButtonText: '确定',
                        showClose: false,
                        type: 'error'
                    }).then(() => {
                        const blob = new Blob([data]);
                        const fileName = this.name + "导入失败文件.xlsx";
                        const linkNode = document.createElement("a");
                        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                        linkNode.style.display = "none";
                        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                        document.body.appendChild(linkNode);
                        linkNode.click(); //模拟在按钮上的一次鼠标单击
                        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                        document.body.removeChild(linkNode);
                    })
                }
                this.uploading = false;
            })
        }
    }
}
</script>
