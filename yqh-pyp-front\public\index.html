<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>加载中...</title>
    <!-- <link rel="stylesheet" href="https://unpkg.com/vant@2.12.44/lib/index.css" /> -->
    <link rel="stylesheet" href="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/vant-index.min.css" />
    <!-- 动画效果 -->
    <!-- <link rel="stylesheet" href="https://unpkg.com/animate.css@4.1.1/animate.min.css" /> -->
    <link rel="stylesheet" href="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/animate.min.css" />
    
  </head>
  <style>
    html, body, #app {
      margin: 0;
      padding: 0;
      background-color: #f6f6f6;
      height: 100%;
    }
    @media screen and (min-width: 960px) {
      body {
        /* width: 400px; */
        margin: auto auto;
      }
    }

    .app-loading {
      margin: 2rem;
      text-align: center;
      font-size: 0.8rem;
      color: #959595;
    }
  </style>
  <body>
    <!-- <script src="https://cdn.jsdelivr.net/npm/eruda"></script>
    <script>eruda.init();</script> -->
    <noscript>
      <strong>JavaScript已被禁用，页面内容无法加载</strong>
    </noscript>
    <div id="app">
      <div class="app-loading">加载中...</div>
    </div>
    <!-- vue -->
    <script src="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/vue.min.js"></script>
    <script src="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/vue-router.min.js"></script>
    <script src="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/vuex.min.js"></script>
    <!-- <script src="https://unpkg.com/vue@2.6.11/dist/vue.min.js"></script> -->
    <!-- <script src="https://unpkg.com/vue-router@3.4.0/dist/vue-router.min.js"></script> -->
    <!-- <script src="https://unpkg.com/vuex@3.0.1/dist/vuex.min.js"></script> -->
    
    <!-- <script src="https://unpkg.com/flyio@0.6.14/dist/fly.min.js"></script> -->
    <script src="https://mpjoy.oss-cn-beijing.aliyuncs.com/fly.min.js"></script>
    <!-- vant -->
    <!-- <script src="https://unpkg.com/vant@2.12.44/lib/vant.min.js"></script> -->
    <script src="https://mpjoy.oss-cn-beijing.aliyuncs.com/static/vant.min.js"></script>
    <!-- weixin -->
    <script src=https://res.wx.qq.com/open/js/jweixin-1.6.0.js async></script>
    <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=E4IBZ-4DCL4-FKYUM-DH2AF-FRBX3-QLFGO"></script>
    <!-- 二维码 -->
    <!-- <script src="https://unpkg.com/qrcode@1.5.0/build/qrcode.js"></script>
    <script src="https://unpkg.com/@chenfengyuan/vue-qrcode@2"></script> -->
    <!-- 其他库 -->
    <!-- https://unpkg.zhimg.com/ -->
    <script src="https://fe-static.xhscdn.com/biz-static/goten/xhs-1.0.1.js"></script>

  </body>
</html>