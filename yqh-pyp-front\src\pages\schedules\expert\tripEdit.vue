<template>
    <div :class="isMobilePhone ? '' : 'pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <!-- <van-nav-bar title="行程编辑" left-arrow @click-left="goBack" /> -->

        <!-- 个人信息确认部分 -->
        <!-- 个人信息确认部分 -->
        <div class="user-info-confirm">
            <div class="info-header">
                <van-icon name="user-circle-o" size="22" color="#1989fa" />
                <span>个人信息确认</span>
            </div>

            <div class="info-content">
                <div class="info-item">
                    <div class="info-label">姓名</div>
                    <div class="info-value">{{ guestInfo.name }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">联系方式</div>
                    <van-field v-model="guestInfo.mobile" placeholder="请输入联系方式" class="custom-field" :border="false"
                        required :rules="[{ required: true, message: '请填写联系方式' }]" />
                </div>

                <div class="info-item">
                    <div class="info-label">身份证类型</div>
                    <div class="info-value clickable" @click="idCardTypeShow = true">
                        {{ guestInfo.idCardType || '请选择身份证类型' }}
                        <van-icon name="arrow" size="16" color="#999" />
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-label">身份证号</div>
                    <van-field v-model="guestInfo.idCard" placeholder="请输入身份证号" class="custom-field" :border="false"
                        required :rules="[{ required: true, message: '请填写身份证号' }]" />
                </div>
            </div>

            <div v-if="!isInfoConfirm" class="info-footer">
                <van-button type="primary" block round @click="confirmUserInfo"
                    :loading="confirmLoading">确认信息</van-button>
            </div>

            <van-action-sheet v-model="idCardTypeShow" :actions="idCardType" @select="idCardTypeSelect" />
        </div>

        <!-- 原有的旅行类型选择 -->
        <div class="travel-type-selector">
            <!-- ... 原有代码保持不变 ... -->
            <div class="tool-selector">
                <span>您选择的出行工具</span>
                <div class="tool-buttons">
                    <button :class="['tool-btn', tripForm.inType == 0 ? 'active' : '']"
                        @click="selectTravelType(0)">飞机</button>
                    <button :class="['tool-btn', tripForm.inType == 1 ? 'active' : '']"
                        @click="selectTravelType(1)">火车</button>
                    <!-- <button class="tool-btn">本地</button> -->
                </div>
            </div>

            <div class="travel-info-input">
                <div class="input-row">
                    <button :class="['input-btn', showNewUI ? 'active' : '']" @click="toggleInputMode(2)">查询</button>
                    <button :class="['input-btn', !showNewUI ? 'active' : '']" @click="toggleInputMode(0)">录入</button>
                </div>
            </div>

            <!-- <div class="trip-type">
                <button :class="['trip-btn', tripForm.type == 0 ? 'active' : '']" @click="selectTripType(0)">单程</button>
                <button :class="['trip-btn', tripForm.type == 1 ? 'active' : '']" @click="selectTripType(1)">往返</button>
            </div> -->

            <!-- 查询模式UI -->
            <div v-if="showNewUI">
                <!-- 修改城市选择部分 -->
                <div class="location-selector">
                    <div class="location from">
                        <van-field v-model="tripForm.inStartPlace" placeholder="请输入出发地" @focus="showStartSearch = true"
                            readonly />
                    </div>
                    <div class="location-arrow">
                        <van-icon name="arrow" />
                    </div>
                    <div class="location to">
                        <van-field v-model="tripForm.inEndPlace" placeholder="请输入目的地" @focus="showEndSearch = true"
                            readonly />
                    </div>
                </div>

                <div class="date-selector" @click="dateShow = true">
                    <div class="date">
                        {{ formatDisplayDate(tripForm.inDate) || '01月17日 周五' }}
                    </div>
                    <van-icon name="calendar-o" />
                </div>

                <div class="search-button" @click="showDetailForm">
                    <span>{{ tripForm.inType == 0 ? '搜索机票' : '搜索火车票' }}</span>
                </div>

                <div class="promotion-info">
                    * 出票规则：{{ tripForm.inType == 0 ? ticketType[activityConfig.planeTicketType].value
                        : tripForm.inType == 1 ? ticketType[activityConfig.trainTicketType].value : '' }}
                </div>
                <div class="go-back-button" @click="goBack">
                    <span>返回上一页面</span>
                </div>
            </div>

            <!-- 原有表单，在录入模式或点击搜索后显示 -->
            <van-cell-group v-else>
                <van-field name="radio" label="填写方式">
                    <template #input>
                        <van-radio-group v-model="tripForm.doType" direction="horizontal">
                            <van-radio v-for="item in doType" v-show="item.show" :name="item.key" :key="item.key">{{
                                item.name
                                }}</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-cell v-model="tripForm.inTypeName" :required="true" title="行程类型" is-link
                    @click="guestGoTypeShow = true" />
                <!-- <van-cell v-model="tripForm.typeName" :required="true" title="购票类型" is-link
                    @click="tripTypeShow = true" /> -->
                <div v-if="tripForm.doType == 0">
                    <van-cell title="上传行程图片">
                        <van-uploader v-if="!tripForm.image" :after-read="afterRead" name="image"
                            :before-read="beforeRead" accept="*">
                            <template>
                                <van-icon name="description" size="40px" style="margin-left: 5px" />
                                <div style="font-size: 12px">点击上传文件</div>
                            </template>
                        </van-uploader>
                        <div v-if="tripForm.image" class="file-info">
                            <template v-if="isImage(tripForm.image)">
                                <van-image height="40px" @click.stop="preImage(tripForm.image)" :src="tripForm.image"
                                    fit="contain" />
                            </template>
                            <template v-else>
                                <van-icon name="description" size="20px" />
                                <span class="file-text">已上传文件</span>
                            </template>
                            <van-icon name="cross" @click.stop="removeFile" class="remove-icon" />
                        </div>
                    </van-cell>
                </div>
                <div v-else>
                    <van-field v-model="tripForm.inNumber" name="行程航班/火车号" label="行程航班/火车号" placeholder="行程航班/火车号"
                        required :rules="[{ required: true, message: '行程航班/火车号' }]">
                    </van-field>
                    <van-cell v-model="tripForm.inDate" :required="true" title="行程日期" is-link
                        @click="dateShow = true" />
                    <van-cell v-show="tripForm.inDate" v-model="tripForm.inStartDate" :required="true" title="出发时间"
                        is-link @click="startShow = true" />
                    <van-cell v-show="tripForm.inDate" v-model="tripForm.inEndDate" :required="true" title="到达时间"
                        is-link @click="endShow = true" />
                    <div style="font-size: 12px;padding: 5px 20px;color: red;">
                        站点精确到航站楼或动车站，而非城市
                    </div>
                    <van-field v-model="tripForm.inStartPlace" name="行程出发站点" label="行程出发站点"
                        placeholder="行程出发站点"></van-field>
                    <van-field v-model="tripForm.inEndPlace" name="行程到达站点" label="行程到达站点"
                        placeholder="行程到达站点"></van-field>
                </div>

                <div style="margin: 16px;display: flex;gap: 10px" v-if="showForm">
                    <van-button round block type="info" @click="goBack">取消</van-button>
                    <van-button round color="#DD5C5F" block type="info" @click="submit" :loading="loading"
                        loading-text="提交中">提交</van-button>
                </div>
            </van-cell-group>

            <van-popup v-model="dateShow" position="bottom" :style="{ height: '45%' }">
                <van-datetime-picker v-model="currentDate" type="date" title="选择日期" :min-date="minDate"
                    :max-date="maxDate" :formatter="formatter" @cancel="dateShow = false" @confirm="dateSelect" />
            </van-popup>
            <van-popup v-model="startShow" position="bottom" :style="{ height: '45%' }">
                <van-datetime-picker v-model="start" type="time" title="选择出发时间" :formatter="formatter"
                    @cancel="startShow = false" @confirm="startSelect" />
            </van-popup>
            <van-popup v-model="endShow" position="bottom" :style="{ height: '45%' }">
                <van-datetime-picker v-model="end" type="time" title="选择到达时间" :formatter="formatter"
                    @cancel="endShow = false" @confirm="endSelect" :min-hour="minHour" />
            </van-popup>
            <van-action-sheet v-model="guestGoTypeShow" :actions="guestGoType" @select="guestGoTypeSelect" />
            <van-action-sheet v-model="tripTypeShow" :actions="tripType" @select="tripTypeSelect" />

            <!-- 修改搜索弹窗结构 -->
            <van-popup v-model="showStartSearch" position="bottom" :style="{ height: '70%' }" round>
                <div class="search-header">
                    <van-search v-model="startSearchText" placeholder="请输入城市名称或机场/车站名称" @input="searchStartPlaces"
                        shape="round" background="#f7f8fa" autofocus />
                    <van-icon name="cross" @click="showStartSearch = false" class="close-icon" />
                </div>
                <div class="search-results">
                    <div v-if="startSearchResults.length === 0 && startSearchText && !startLoading"
                        class="empty-result">
                        <van-empty description="暂无搜索结果" />
                    </div>
                    <div v-for="(item, index) in startSearchResults" :key="index" class="search-item"
                        @click="selectStartPlace(item)">
                        <div class="item-main">{{ tripForm.inType == 0 ? item.airportName : item.stationName }}</div>
                        <div class="item-sub">{{ item.cityName }}</div>
                    </div>
                    <div v-if="startLoading" class="loading-more">
                        <van-loading type="spinner" size="24px" color="#1989fa">搜索中...</van-loading>
                    </div>
                </div>
            </van-popup>

            <van-popup v-model="showEndSearch" position="bottom" :style="{ height: '70%' }" round>
                <div class="search-header">
                    <van-search v-model="endSearchText" placeholder="请输入城市名称或机场/车站名称" @input="searchEndPlaces"
                        shape="round" background="#f7f8fa" autofocus />
                    <van-icon name="cross" @click="showEndSearch = false" class="close-icon" />
                </div>
                <div class="search-results">
                    <div v-if="endSearchResults.length === 0 && endSearchText && !endLoading" class="empty-result">
                        <van-empty description="暂无搜索结果" />
                    </div>
                    <div v-for="(item, index) in endSearchResults" :key="index" class="search-item"
                        @click="selectEndPlace(item)">
                        <div class="item-main">{{ tripForm.inType == 0 ? item.airportName : item.stationName }}</div>
                        <div class="item-sub">{{ item.cityName }}</div>
                    </div>
                    <div v-if="endLoading" class="loading-more">
                        <van-loading type="spinner" size="24px" color="#1989fa">搜索中...</van-loading>
                    </div>
                </div>
            </van-popup>
        </div>
    </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone, isIdCard, isMobile } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
import { guestGoType, tripType, doType, idCardType, ticketType } from "@/data/common";
import Compressor from 'compressorjs';

export default {
    components: { pcheader },
    data() {
        return {
            activityConfig: {},
            isInfoConfirm: false,
            idCardType,
            ticketType,
            idCardTypeShow: false,
            guestGoType,
            doType,
            tripType,
            loading: false,
            tripTypeShow: false,
            dateShow: false,
            guestGoTypeShow: false,
            selectingStartPlace: true, // 标记是选择出发地还是目的地
            showForm: false, // 控制是否显示详细表单
            showNewUI: true, // 控制显示新UI还是原始表单
            isMobilePhone: isMobilePhone(),
            openid: undefined,
            activityId: undefined,
            id: undefined,
            guestInfo: {},
            tripForm: {
                id: '',
                inType: 0,
                inTypeName: '飞机',
                type: 0,
                typeName: '',
                inDate: '',
                inNumber: '',
                inEndPlace: '',
                inStartPlace: '',
                inEndDate: '',
                inStartDate: '',
                activityGuestId: '',
                activityId: '',
                isBuy: 0,
                price: 0,
                doType: 0,
                image: '',
            },
            minDate: new Date(),
            maxDate: new Date(2030, 10, 1),
            currentDate: new Date(),
            startShow: false,
            start: '',
            endShow: false,
            end: '',
            minHour: '',
            showStartSearch: false,
            showEndSearch: false,
            startSearchText: '',
            endSearchText: '',
            startSearchResults: [],
            endSearchResults: [],
            startLoading: false,
            endLoading: false,
            startTimeout: null,
            endTimeout: null,
        };
    },
    mounted() {
        this.id = this.$route.query.detailId;
        this.tripForm.activityGuestId = this.$route.query.detailId;
        this.openid = this.$cookie.get("openid");
        // 设置默认日期为当天
        this.tripForm.inDate = date.formatDate.format(
            new Date(),
            "yyyy/MM/dd hh:mm:ss"
        );
        // 如果是编辑模式，获取行程信息
        if (this.$route.query.tripId) {
            this.getTripInfo(this.$route.query.tripId);
        } else {
            // 新增模式，需要获取活动信息
            this.getActivityList();
            // 设置默认值
            this.tripForm.inTypeName = this.guestGoType[0].name;
            this.tripForm.typeName = this.tripType[0].name;
        }
    },
    methods: {

        getActivityConfig() {
            this.$fly
                .get(`/pyp/web/activity/activityConfig/check`, {
                    activityId: this.activityId,
                    guestId: this.guestInfo.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.activityConfig = res.result;
                    } else {
                        vant.Toast(res.msg);
                        this.activityConfig = {};
                    }
                });
        },
        // 新增的个人信息确认方法
        confirmUserInfo() {
            // 验证必填项
            if (!this.guestInfo.mobile) {
                vant.Toast('请填写联系方式');
                return;
            }
            if (!isMobile(this.guestInfo.mobile)) {
                vant.Toast("请输入正确的手机号");
                return false;
            }
            if (!this.guestInfo.idCardType) {
                vant.Toast('请选择身份证类型');
                return;
            }
            if (!this.guestInfo.idCard) {
                vant.Toast('请填写身份证');
                return;
            }
            // 身份证校验
            if (!isIdCard(this.guestInfo.idCard)) {
                vant.Toast("请输入正确的身份证");
                return;
            }

            this.confirmLoading = true;
            // 调用接口更新个人信息
            this.$fly
                .post("/pyp/web/activity/activityguest/updateInfo", {
                    id: this.guestInfo.id,
                    mobile: this.guestInfo.mobile,
                    idCardType: this.guestInfo.idCardType,
                    idCard: this.guestInfo.idCard,
                })
                .then((res) => {
                    this.confirmLoading = false;
                    if (res && res.code === 200) {
                        vant.Toast("信息确认成功");
                        this.isInfoConfirm = true;
                    } else {
                        vant.Toast(res.msg || "确认失败，请重试");
                    }
                })
                .catch(() => {
                    this.confirmLoading = false;
                    vant.Toast("网络错误，请重试");
                });
        },

        // 身份证类型选择
        idCardTypeSelect(v) {
            this.idCardTypeShow = false;
            this.guestInfo.idCardType = v.name;
        },
        toggleInputMode(isNewUI) {
            this.tripForm.doType = isNewUI;
            this.showNewUI = isNewUI;
            if (!isNewUI) {
                // 切换到录入模式时直接显示表单
                this.showForm = true;
            }
        },
        formatDisplayDate(dateStr) {
            if (!dateStr) return '';
            const dateObj = new Date(dateStr);
            const month = dateObj.getMonth() + 1;
            const day = dateObj.getDate();
            const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][dateObj.getDay()];
            return `${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日 ${weekDay}`;
        },
        selectTravelType(type) {
            this.tripForm.inType = type;
            this.tripForm.inTypeName = this.guestGoType[type].name;
        },
        selectTripType(type) {
            this.tripForm.type = type;
            this.tripForm.typeName = this.tripType[type].name;
        },
        // 在 methods 中添加或修改 showDetailForm 方法
        showDetailForm() {
            if (!this.guestInfo.mobile) {
                vant.Toast("请输入联系方式");
                return false;
            }
            if (!isMobile(this.guestInfo.mobile)) {
                vant.Toast("请输入正确的手机号");
                return false;
            }
            if (!this.guestInfo.idCardType) {
                vant.Toast("请选择身份证类型");
                return false;
            }
            if (!this.guestInfo.idCard) {
                vant.Toast("请输入身份证号");
                return false;
            }
            // 验证必填项
            if (!this.tripForm.inStartPlace) {
                vant.Toast('请选择出发地');
                return;
            }
            if (!this.tripForm.inEndPlace) {
                vant.Toast('请选择目的地');
                return;
            }
            if (!this.tripForm.inDate) {
                vant.Toast('请选择日期');
                return;
            }
            this.tripForm.activityId = this.activityId;
            this.tripForm.activityGuestId = this.id;

            // 将表单数据序列化为JSON字符串
            const tripInfo = JSON.stringify(this.tripForm);

            // 根据选择的交通工具类型跳转到不同的选择页面
            if (this.tripForm.inType == 0) {
                // 飞机
                this.$router.push({
                    path: '/schedules/expert/components/plane-select',
                    query: { tripInfo }
                });
            } else {
                // 火车
                this.$router.push({
                    path: '/schedules/expert/components/train-select',
                    query: { tripInfo }
                });
            }
        },
        goBack() {
            this.$router.back();
        },
        getTripInfo(tripId) {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getTripById/${tripId}`)
                .then((res) => {
                    if (res.code == 200) {
                        this.tripForm = res.result;
                        this.tripForm.inTypeName = this.guestGoType[this.tripForm.inType].name;
                        this.tripForm.typeName = this.tripType[this.tripForm.type].name;
                        this.getActivityList();
                    } else {
                        vant.Toast(res.msg);
                    }
                });
        },
        isImage(fileName) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            const fileExtension = fileName.split('.').pop().toLowerCase();
            return imageExtensions.includes(fileExtension);
        },
        preImage(v) {
            vant.ImagePreview({
                images: [v], // 图片集合
                closeable: true, // 关闭按钮
            });
        },
        startSelect(e) {
            this.startShow = false;
            this.minHour = this.start.substring(0, 2);
            this.tripForm.inStartDate = this.tripForm.inDate.substring(0, 10) + ' ' + e + ':00';
        },
        endSelect(e) {
            this.endShow = false;
            this.tripForm.inEndDate = this.tripForm.inDate.substring(0, 10) + ' ' + e + ':00';
        }, dateSelect(e) {
            this.dateShow = false;
            this.tripForm.inDate = date.formatDate.format(
                e,
                "yyyy/MM/dd hh:mm:ss"
            );

            // 如果出发时间和到达时间未设置，设置默认值
            if (!this.tripForm.inStartDate) {
                this.tripForm.inStartDate = this.tripForm.inDate.substring(0, 10) + ' 08:00:00';
            }
            if (!this.tripForm.inEndDate) {
                this.tripForm.inEndDate = this.tripForm.inDate.substring(0, 10) + ' 10:00:00';
            }
        },
        formatter(type, val) {
            if (type === 'year') {
                return `${val}年`;
            } else if (type === 'month') {
                return `${val}月`;
            } else if (type === 'day') {
                return `${val}日`;
            } else if (type === 'hour') {
                return `${val}时`;
            } else if (type === 'minute') {
                return `${val}分`;
            }
            return val;
        },
        getActivityList() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
                .then((res) => {
                    if (res.code == 200) {
                        this.guestInfo = res.result;
                        this.activityId = res.result.activityId;
                        this.tripForm.activityId = res.result.activityId;
                        this.getActivityConfig();
                    } else {
                        vant.Toast(res.msg);
                        this.guestInfo = {};
                    }
                });
        },
        guestGoTypeSelect(v) {
            this.guestGoTypeShow = false;
            this.tripForm.inType = v.key;
            this.tripForm.inTypeName = v.name;
        },
        tripTypeSelect(v) {
            this.tripTypeShow = false;
            this.tripForm.type = v.key;
            this.tripForm.typeName = v.name;
        },
        afterRead(e, name) {
            let filedName = name.name;
            e.status = "uploading";
            e.message = "上传中...";
            let formData = new FormData();
            let file = e.file;
            var that = this;
            if (file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/jpg') {
                formData.append("file", file);
                that.$fly.post("/pyp/web/upload", formData).then((res) => {
                    if (res && res.code === 200) {
                        that.$set(that.tripForm, filedName, res.result);
                    }
                });
            } else {
                new Compressor(file, {
                    quality: 0.7,
                    success(result) {
                        formData.append("file", new window.File([result], file.name, {type: file.type}));

                        that.$fly.post("/pyp/web/upload", formData).then((res) => {
                            if (res && res.code === 200) {
                                that.$set(that.tripForm, filedName, res.result);
                            }
                        });
                    }
                });
            }
        },
        beforeRead(file) {
            return true;
        },
        removeFile() {
            this.tripForm.image = '';
        },

        // 搜索出发地
        searchStartPlaces() {
            if (this.startSearchText === '') {
                this.startSearchResults = [];
                return;
            }

            this.startLoading = true;
            clearTimeout(this.startTimeout);

            this.startTimeout = setTimeout(() => {
                const url = this.tripForm.inType == 0
                    ? "/pyp/web/config/configairport/findByName"
                    : "/pyp/web/config/configtrainstation/findByName";

                this.$fly.get(url, {
                    name: this.startSearchText
                }).then(res => {
                    this.startLoading = false;
                    if (res && res.code === 200) {
                        this.startSearchResults = res.result;
                    } else {
                        vant.Toast(res.msg || '搜索失败');
                        this.startSearchResults = [];
                    }
                }).catch(() => {
                    this.startLoading = false;
                    vant.Toast('网络错误，请重试');
                });
            }, 300);
        },

        // 搜索目的地
        searchEndPlaces() {
            if (this.endSearchText === '') {
                this.endSearchResults = [];
                return;
            }

            this.endLoading = true;
            clearTimeout(this.endTimeout);

            this.endTimeout = setTimeout(() => {
                const url = this.tripForm.inType == 0
                    ? "/pyp/web/config/configairport/findByName"
                    : "/pyp/web/config/configtrainstation/findByName";

                this.$fly.get(url, {
                    name: this.endSearchText
                }).then(res => {
                    this.endLoading = false;
                    if (res && res.code === 200) {
                        this.endSearchResults = res.result;
                    } else {
                        vant.Toast(res.msg || '搜索失败');
                        this.endSearchResults = [];
                    }
                }).catch(() => {
                    this.endLoading = false;
                    vant.Toast('网络错误，请重试');
                });
            }, 300);
        },

        // 选择出发地
        selectStartPlace(item) {
            if (this.tripForm.inType == 0) {
                // 飞机
                this.tripForm.inStartPlace = item.airportName;
                this.tripForm.inStartCity = item.cityName;
                this.tripForm.startCityCode = item.cityCode;
            } else {
                // 火车
                this.tripForm.inStartPlace = item.stationName;
                this.tripForm.inStartCity = item.cityName;
                this.tripForm.startCityCode = item.stationCode;
            }
            this.showStartSearch = false;
        },

        // 选择目的地
        selectEndPlace(item) {
            if (this.tripForm.inType == 0) {
                // 飞机
                this.tripForm.inEndPlace = item.airportName;
                this.tripForm.inEndCity = item.cityName;
                this.tripForm.endCityCode = item.cityCode;
            } else {
                // 火车
                this.tripForm.inEndPlace = item.stationName;
                this.tripForm.inEndCity = item.cityName;
                this.tripForm.endCityCode = item.stationCode;
            }
            this.showEndSearch = false;
        },

        // 修改出行工具类型时重置地点
        selectTravelType(type) {
            this.tripForm.inType = type;
            this.tripForm.inTypeName = this.guestGoType[type].name;
            // 重置地点信息
            this.tripForm.inStartPlace = '';
            this.tripForm.inEndPlace = '';
            this.tripForm.inStartCity = '';
            this.tripForm.inEndCity = '';
            this.tripForm.startCityCode = '';
            this.tripForm.endCityCode = '';
        },

        submit() {
            if (!this.guestInfo.mobile) {
                vant.Toast("请输入联系方式");
                return false;
            }
            if (!isMobile(this.guestInfo.mobile)) {
                vant.Toast("请输入正确的手机号");
                return false;
            }
            if (!this.guestInfo.idCardType) {
                vant.Toast("请选择身份证类型");
                return false;
            }
            if (!this.guestInfo.idCard) {
                vant.Toast("请输入身份证号");
                return false;
            }
            if (this.tripForm.doType == 0) {
                if (!this.tripForm.image) {
                    vant.Toast("请上传行程图片");
                    return false;
                }
            } else {
                if (this.tripForm.inType === '') {
                    vant.Toast("选择类型");
                    return false;
                }
                if (this.tripForm.type === '') {
                    vant.Toast("选择类型");
                    return false;
                }
                if (!this.tripForm.inDate || !this.tripForm.inStartDate || !this.tripForm.inEndDate) {
                    vant.Toast("选择日期");
                    return false;
                }
                if (!this.tripForm.inNumber) {
                    vant.Toast("请输入航班号/火车号");
                    return false;
                }
            }
            this.loading = true;
            let url = this.tripForm.id ? "/pyp/web/activity/activityguest/updateTrip" : "/pyp/web/activity/activityguest/saveTrip";
            // 保存
            this.$fly
                .post(
                    url,
                    this.tripForm
                )
                .then((res) => {
                    this.loading = false;
                    if (res && res.code === 200) {
                        vant.Toast("提交成功");
                        this.$router.back();
                    } else {
                        vant.Toast(res.msg);
                    }
                });
        }
    },
};
</script>

<style lang="less" scoped>
.file-info {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    margin-top: 10px;
}

.file-text {
    margin-left: 5px;
    flex: 1;
}

.remove-icon {
    margin-left: 10px;
    cursor: pointer;
    transition: color 0.3s;
}

.remove-icon:hover {
    color: #ff4d4f;
}

.van-cell__title {
    flex: none;
    box-sizing: border-box;
    width: 6.2em;
    margin-right: 12px;
    color: #646566;
    text-align: left;
    word-wrap: break-word;
    line-height: 33px;
}

.van-cell__value {
    text-align: left;
    display: flex;
    align-items: center;
}

/* 新增的样式 */
.travel-type-selector {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin: 10px;
}

.tool-selector {
    margin-bottom: 15px;
}

.tool-selector span {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.tool-buttons {
    display: flex;
    gap: 10px;
}

.tool-btn {
    flex: 1;
    padding: 8px 0;
    border: 1px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
    font-size: 14px;
}

.tool-btn.active {
    background-color: #1989fa;
    color: white;
    border-color: #1989fa;
}

.travel-info-input {
    margin-bottom: 15px;
}

.input-row {
    display: flex;
    gap: 10px;
}

.input-btn {
    flex: 1;
    padding: 8px 0;
    border: 1px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
    font-size: 14px;
}

.input-btn.active {
    background-color: #1989fa;
    color: white;
    border-color: #1989fa;
}

.trip-type {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.trip-btn {
    flex: 1;
    padding: 8px 0;
    border: 1px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
    font-size: 14px;
}

.trip-btn.active {
    background-color: #1989fa;
    color: white;
    border-color: #1989fa;
}

.location-selector {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.location {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.city {
    font-size: 18px;
    font-weight: bold;
}

.location-arrow {
    padding: 0 15px;
}

.date-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.date {
    font-size: 16px;
}

.search-button {
    background-color: #1989fa;
    color: white;
    text-align: center;
    padding: 12px 0;
    border-radius: 4px;
    font-size: 16px;
    margin-bottom: 10px;
}

.go-back-button {
    background-color: #ff6b00;
    color: white;
    text-align: center;
    padding: 12px 0;
    border-radius: 4px;
    font-size: 16px;
    margin-bottom: 10px;
}

.promotion-info {
    font-size: 12px;
    color: #ff6b00;
    text-align: left;
}

/* 修改搜索弹窗相关样式 */
.search-header {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
}

.search-header .van-search {
    flex: 1;
    margin-right: 10px;
}

.close-icon {
    padding: 0 10px;
    font-size: 20px;
    color: #666;
}

.search-results {
    // height: calc(100% - 64px);
    overflow-y: auto;
    padding-bottom: 20px;
}

/* 新增搜索结果项样式 */
.search-item {
    padding: 15px;
    position: relative;
    background-color: #fff;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
}

.search-item:active {
    background-color: #f2f3f5;
}

.item-main {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.item-sub {
    font-size: 14px;
    color: #999;
}

.loading-more {
    text-align: center;
    padding: 15px 0;
    color: #666;
}

/* 修改输入框样式 */
.location .van-field {
    padding: 0;
    background-color: transparent;
}

.location .van-field__control {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.location .van-field__control::placeholder {
    color: #999;
    font-weight: normal;
}

/* 个人信息确认样式 */
.user-info-confirm {
    background-color: #fff;
    margin: 10px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.info-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: linear-gradient(to right, #f8f8f8, #ffffff);
    border-bottom: 1px solid #f0f0f0;
}

.info-header span {
    margin-left: 10px;
    font-size: 17px;
    font-weight: 600;
    color: #333;
}

.info-content {
    padding: 10px 0;
}

.info-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    width: 90px;
    color: #666;
    font-size: 15px;
}

.info-value {
    flex: 1;
    font-size: 15px;
    color: #333;
}

.clickable {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
}

.custom-field {
    flex: 1;
    padding: 0;
}

.custom-field /deep/ .van-field__control {
    color: #333;
    font-size: 15px;
}

.info-footer {
    padding: 16px;
    background-color: #f9f9f9;
}

/* 确保原有样式和新增样式不冲突 */
.travel-type-selector {
    margin-top: 15px;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin: 10px;
}
</style>