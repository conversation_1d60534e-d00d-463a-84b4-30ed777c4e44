package com.cjy.pyp.modules.activity.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cjy.pyp.common.exception.RRException;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityNotifyEntity;
import com.cjy.pyp.modules.activity.service.ActivityNotifyService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动通知 Web 端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/web/activity/activitynotify")
public class WebActivityNotifyController extends AbstractController {

    @Autowired
    private ActivityNotifyService activityNotifyService;

    /**
     * 获取未读通知数量
     */
    @RequestMapping("/noReadCount")
    public R noReadCount(@RequestParam("activityId") Long activityId) {
        int count = activityNotifyService.count(
            new QueryWrapper<ActivityNotifyEntity>()
                .eq("activity_id", activityId)
                .eq("`read`", 0)
        );
        return R.ok().put("result", count);
    }

    /**
     * 获取通知列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        // 验证必要参数
        if (!params.containsKey("activityId")) {
            return R.error("活动ID不能为空");
        }

        // 支持按类型过滤
        if (params.containsKey("types")) {
            String types = params.get("types").toString();
            params.put("typeList", Arrays.asList(types.split(",")));
        }

        List<ActivityNotifyEntity> notifications = activityNotifyService.queryPage(params);
        return R.okList(notifications);
    }

    /**
     * 标记通知为已读
     */
    @RequestMapping("/read")
    public R read(@RequestParam("id") String id) {
        // 批量操作
        List<Long> ids = StringUtils.isNotEmpty(id) ? 
            Arrays.stream(id.split(",")).map(Long::parseLong).collect(Collectors.toList()) : 
            new ArrayList<>();
        
        if (ids.isEmpty()) {
            throw new RRException("id不能为空");
        }

        activityNotifyService.update(
            new UpdateWrapper<ActivityNotifyEntity>()
                .in("id", ids)
                .set("`read`", 1)
        );
        
        return R.ok();
    }

    /**
     * 标记活动的所有通知为已读
     */
    @RequestMapping("/readAll")
    public R readAll(@RequestParam("activityId") Long activityId) {
        activityNotifyService.update(
            new UpdateWrapper<ActivityNotifyEntity>()
                .eq("activity_id", activityId)
                .eq("`read`", 0)
                .set("`read`", 1)
        );
        
        return R.ok();
    }

    /**
     * 获取通知详情
     */
    @RequestMapping("/info")
    public R info(@RequestParam("id") Long id) {
        ActivityNotifyEntity notify = activityNotifyService.getById(id);
        if (notify == null) {
            return R.error("通知不存在");
        }
        
        // 自动标记为已读
        if (notify.getRead() == 0) {
            notify.setRead(1);
            activityNotifyService.updateById(notify);
        }
        
        return R.ok().put("notify", notify);
    }

    /**
     * 删除通知
     */
    @RequestMapping("/delete")
    public R delete(@RequestParam("id") String id) {
        List<Long> ids = StringUtils.isNotEmpty(id) ? 
            Arrays.stream(id.split(",")).map(Long::parseLong).collect(Collectors.toList()) : 
            new ArrayList<>();
        
        if (ids.isEmpty()) {
            throw new RRException("id不能为空");
        }

        activityNotifyService.removeByIds(ids);
        return R.ok();
    }

    /**
     * 获取通知统计信息
     */
    @RequestMapping("/statistics")
    public R statistics(@RequestParam("activityId") Long activityId) {
        // 总通知数
        int totalCount = activityNotifyService.count(
            new QueryWrapper<ActivityNotifyEntity>().eq("activity_id", activityId)
        );

        // 未读通知数
        int unreadCount = activityNotifyService.count(
            new QueryWrapper<ActivityNotifyEntity>()
                .eq("activity_id", activityId)
                .eq("`read`", 0)
        );

        // 已读通知数
        int readCount = totalCount - unreadCount;

        return R.ok()
            .put("totalCount", totalCount)
            .put("unreadCount", unreadCount)
            .put("readCount", readCount);
    }
}
