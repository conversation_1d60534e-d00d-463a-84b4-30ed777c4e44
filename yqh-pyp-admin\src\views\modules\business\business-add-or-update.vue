<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="医企秀名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="医企秀名称"></el-input>
    </el-form-item>
    <el-form-item label="资讯类型" prop="businessTypeId">
      <el-select v-model="dataForm.businessTypeId">
        <el-option v-for="item in businessType" :key="item.id"  :label="item.name" :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="第三方链接" prop="url">
      <el-input v-model="dataForm.url" placeholder="第三方链接"></el-input>
    </el-form-item>
    <el-form-item label="联系人" prop="username">
      <el-input v-model="dataForm.username" placeholder="联系人"></el-input>
    </el-form-item>
    <el-form-item label="联系方式" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="图片" prop="picUrl">
        <el-upload
        :before-upload="checkFileSize" 
          class="avatar-uploader"
          list-type="picture-card"
          :show-file-list="false"
          accept=".jpg, .jpeg, .png, .gif"
          :on-success="appSuccessHandle"
          :action="url"
        >
          <img
            width="100px"
            v-if="dataForm.picUrl"
            :src="dataForm.picUrl"
            class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
    </el-form-item>
    <el-form-item label="简介" prop="brief">
      <el-input v-model="dataForm.brief" placeholder="简介"></el-input>
    </el-form-item>
    <el-form-item label="详细介绍" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
    </el-form-item>
    <el-form-item label="排序" prop="paixu">
      <el-input v-model="dataForm.paixu" placeholder="排序"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
  export default {
    components: {
      TinymceEditor: () =>
        import ("@/components/tinymce-editor"),
      OssUploader: () =>
        import ('../oss/oss-uploader')
    },
    data () {
      return {
        url: "",
        businessType: [],
        visible: false,
        dataForm: {
          id: 0,
          name: '',
          businessTypeId: '',
          paixu: 0,
          picUrl: '',
          brief: '',
          content: '',
          username: '',
          url: '',
          mobile: ''
        },
        dataRule: {
          name: [
            { required: true, message: '医企秀名称不能为空', trigger: 'blur' }
          ],
          businessTypeId: [
            { required: true, message: '医企秀名称不能为空', trigger: 'blur' }
          ],
          paixu: [
            { required: true, message: '排序不能为空', trigger: 'blur' }
          ],
          content: [
            { required: true, message: '详细介绍不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/business/business/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.name = data.business.name
                this.dataForm.businessTypeId = data.business.businessTypeId
                this.dataForm.paixu = data.business.paixu
                this.dataForm.picUrl = data.business.picUrl
                this.dataForm.brief = data.business.brief
                this.dataForm.content = data.business.content
                this.dataForm.username = data.business.username
                this.dataForm.mobile = data.business.mobile
                this.dataForm.url = data.business.url
              }
            })
          }
        })
        this.findByAppid();
      },
      findByAppid() {
        this.$http({
              url: this.$http.adornUrl(`/business/businesstype/findByAppid`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.businessType = data.result
              }
            })
      },
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      beforeUploadHandle(file) {
        if (
          file.type !== "image/jpg" &&
          file.type !== "image/jpeg" &&
          file.type !== "image/png" &&
          file.type !== "image/gif"
        ) {
          this.$message.error("只支持jpg、png、gif格式的图片！");
          return false;
        }
      },
      // app公众号轮播图上传成功
      appSuccessHandle(response, file, fileList) {
        if (response && response.code === 200) {
          this.dataForm.picUrl = response.url;
        } else {
          this.$message.error(response.msg);
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/business/business/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'businessTypeId': this.dataForm.businessTypeId,
                'paixu': this.dataForm.paixu,
                'picUrl': this.dataForm.picUrl,
                'brief': this.dataForm.brief,
                'content': this.dataForm.content,
                'username': this.dataForm.username,
                'url': this.dataForm.url,
                'mobile': this.dataForm.mobile
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
