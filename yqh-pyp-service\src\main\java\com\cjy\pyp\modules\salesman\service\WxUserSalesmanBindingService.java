package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;

import java.util.List;
import java.util.Map;

/**
 * 微信用户业务员绑定服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public interface WxUserSalesmanBindingService extends IService<WxUserSalesmanBindingEntity> {

    /**
     * 分页查询绑定关系列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<WxUserSalesmanBindingEntity> queryPage(Map<String, Object> params);

    /**
     * 根据微信用户ID查询有效的业务员绑定
     * @param wxUserId 微信用户ID
     * @param appid 应用ID
     * @return 有效的绑定关系
     */
    WxUserSalesmanBindingEntity getActiveBindingByWxUser(Long wxUserId, String appid);

    /**
     * 根据业务员ID查询绑定的微信用户列表
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 绑定的微信用户列表
     */
    List<WxUserSalesmanBindingEntity> getBindingsBySlesman(Long salesmanId, String appid);

    /**
     * 创建绑定关系
     * @param wxUserId 微信用户ID
     * @param salesmanId 业务员ID
     * @param bindingType 绑定方式
     * @param bindingSource 绑定来源
     * @param appid 应用ID
     * @return 绑定关系
     */
    WxUserSalesmanBindingEntity createBinding(Long wxUserId, Long salesmanId, Integer bindingType, 
                                             String bindingSource, String appid);

    /**
     * 解除绑定关系
     * @param wxUserId 微信用户ID
     * @param salesmanId 业务员ID
     * @param reason 解绑原因
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean unbindSalesman(Long wxUserId, Long salesmanId, String reason, String appid);

    /**
     * 更换业务员
     * @param wxUserId 微信用户ID
     * @param oldSalesmanId 原业务员ID
     * @param newSalesmanId 新业务员ID
     * @param reason 更换原因
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean changeSalesman(Long wxUserId, Long oldSalesmanId, Long newSalesmanId, String reason, String appid);

    /**
     * 通过二维码绑定业务员
     * @param wxUserId 微信用户ID
     * @param qrCodeContent 二维码内容
     * @param appid 应用ID
     * @return 绑定关系
     */
    WxUserSalesmanBindingEntity bindByQrCode(Long wxUserId, String qrCodeContent, String appid);

    /**
     * 通过邀请码绑定业务员
     * @param wxUserId 微信用户ID
     * @param inviteCode 邀请码
     * @param appid 应用ID
     * @return 绑定关系
     */
    WxUserSalesmanBindingEntity bindByInviteCode(Long wxUserId, String inviteCode, String appid);

    /**
     * 批量查询微信用户的业务员绑定
     * @param wxUserIds 微信用户ID列表
     * @param appid 应用ID
     * @return 绑定关系映射（wxUserId -> 绑定关系）
     */
    Map<Long, WxUserSalesmanBindingEntity> getBatchActiveBindingsByWxUsers(List<Long> wxUserIds, String appid);

    /**
     * 根据微信用户ID查询关联的业务员ID
     * @param wxUserId 微信用户ID
     * @param appid 应用ID
     * @return 业务员ID，如果没有绑定则返回null
     */
    Long getSalesmanIdByWxUser(Long wxUserId, String appid);

    /**
     * 检查绑定关系是否存在
     * @param wxUserId 微信用户ID
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 是否存在
     */
    boolean existsBinding(Long wxUserId, Long salesmanId, String appid);

    /**
     * 统计业务员的绑定客户数量
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 绑定客户数量
     */
    Integer countBindingsBySlesman(Long salesmanId, String appid);

    /**
     * 查询即将过期的绑定关系
     * @param days 提前天数
     * @param appid 应用ID
     * @return 即将过期的绑定关系列表
     */
    List<WxUserSalesmanBindingEntity> getExpiringBindings(Integer days, String appid);

    /**
     * 自动失效过期的绑定关系
     * @param appid 应用ID
     * @return 影响的记录数
     */
    Integer expireOverdueBindings(String appid);

    /**
     * 验证绑定参数
     * @param wxUserId 微信用户ID
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 验证结果消息，null表示验证通过
     */
    String validateBindingParams(Long wxUserId, Long salesmanId, String appid);

    /**
     * 获取绑定统计数据
     * @param appid 应用ID
     * @return 统计数据
     */
    Map<String, Object> getBindingStats(String appid);

    /**
     * 获取绑定历史记录
     * @param params 查询参数
     * @return 分页结果
     */
    List<WxUserSalesmanBindingLogEntity> getBindingHistory(Map<String, Object> params);

    /**
     * 获取业务员客户统计数据
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 统计数据
     */
    Map<String, Object> getCustomerStats(Long salesmanId, String appid);

    /**
     * 验证邀请码并返回业务员信息
     * @param inviteCode 邀请码
     * @param appid 应用ID
     * @return 业务员信息
     */
    Map<String, Object> verifyInviteCode(String inviteCode, String appid);

    /**
     * 验证业务员ID并返回业务员信息
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 业务员信息
     */
    Map<String, Object> verifySalesmanId(Long salesmanId, String appid);

    /**
     * 客户通过邀请码绑定业务员
     * @param wxUserId 微信用户ID
     * @param inviteCode 邀请码
     * @param bindingMethod 绑定方式
     * @param appid 应用ID
     * @return 绑定记录
     */
    WxUserSalesmanBindingEntity bindCustomerBySalesman(Long wxUserId, String inviteCode,
                                                      String bindingMethod, String appid);
}
