# 业务员邀请功能实现

## 功能概述

在业务员管理页面（`salesman/salesman`）新增了"邀请业务员"功能，支持通过渠道邀请新业务员加入。

## 功能特性

### 1. 智能渠道选择
- **单渠道场景**：如果系统中只有一个渠道，点击"邀请业务员"按钮后直接生成该渠道的邀请二维码
- **多渠道场景**：如果系统中有多个渠道，会弹出渠道选择对话框，管理员可以选择特定渠道生成邀请二维码

### 2. 邀请方式
- **二维码邀请**：生成包含渠道信息的二维码，可以复制图片分享
- **链接邀请**：生成邀请链接，可以直接复制分享

### 3. 邀请链接格式
```
https://yqihua.com/p_front/#/salesman/register?channelId={渠道ID}
```

## 界面修改

### 1. 按钮添加
在业务员管理页面的操作按钮区域添加了"邀请业务员"按钮：
```html
<el-button type="warning" @click="inviteSalesmanHandle()">邀请业务员</el-button>
```

### 2. 邀请弹窗
新增邀请业务员弹窗，包含：
- 渠道选择下拉框（多渠道时显示）
- 邀请二维码显示区域
- 邀请链接输入框（带复制按钮）
- 操作按钮（复制二维码、确定）

## 核心逻辑

### 1. 邀请流程
```javascript
inviteSalesmanHandle() {
  if (this.channelList.length === 0) {
    // 没有可用渠道
    this.$message.error('没有可用的渠道')
    return
  }

  if (this.channelList.length === 1) {
    // 单渠道：直接生成二维码
    this.selectedChannelId = this.channelList[0].id
    this.generateInviteQrcode()
  } else {
    // 多渠道：显示选择弹窗
    this.selectedChannelId = null
    this.inviteQrcodeUrl = ''
  }
  
  this.inviteDialogVisible = true
}
```

### 2. 二维码生成
- 使用 `qrcode` 库生成二维码
- 二维码内容为邀请链接
- 在canvas上绘制二维码和相关文字信息
- 包含渠道名称等描述信息

### 3. 复制功能
- **复制链接**：支持现代剪贴板API和备用方法
- **复制二维码**：将canvas转换为图片并复制到剪贴板

## 数据流

### 1. 渠道数据获取
```javascript
getChannelList() {
  this.$http({
    url: this.$http.adornUrl('/channel/channel/select'),
    method: 'get',
    params: this.$http.adornParams()
  }).then(({ data }) => {
    if (data && data.code === 200) {
      this.channelList = data.channelList || []
    }
  })
}
```

### 2. 邀请链接生成
```javascript
generateInviteQrcode() {
  if (!this.selectedChannelId) return
  
  const baseUrl = 'https://yqihua.com/p_front/#/salesman/register'
  this.inviteQrcodeUrl = `${baseUrl}?channelId=${this.selectedChannelId}`
  
  this.$nextTick(() => {
    this.generateInviteQrcodeCanvas()
  })
}
```

## 用户体验

### 1. 操作流程
1. 管理员在业务员管理页面点击"邀请业务员"按钮
2. 系统自动判断渠道数量：
   - 单渠道：直接显示邀请二维码
   - 多渠道：显示渠道选择界面
3. 选择渠道后自动生成邀请二维码和链接
4. 管理员可以复制二维码图片或邀请链接进行分享

### 2. 响应式设计
- 弹窗居中显示
- 二维码清晰可扫描
- 操作按钮布局合理
- 支持键盘和鼠标操作

## 技术实现

### 1. 新增数据属性
```javascript
data() {
  return {
    // ... 其他属性
    inviteDialogVisible: false,    // 邀请弹窗显示状态
    selectedChannelId: null,       // 选中的渠道ID
    inviteQrcodeUrl: '',          // 邀请链接
  }
}
```

### 2. 监听器
```javascript
watch: {
  selectedChannelId(newVal) {
    if (newVal) {
      this.generateInviteQrcode()
    } else {
      this.inviteQrcodeUrl = ''
    }
  }
}
```

### 3. 核心方法
- `inviteSalesmanHandle()` - 处理邀请按钮点击
- `generateInviteQrcode()` - 生成邀请链接
- `generateInviteQrcodeCanvas()` - 生成二维码canvas
- `copyInviteLink()` - 复制邀请链接
- `copyInviteQrcodeImage()` - 复制二维码图片

## 注意事项

1. **权限控制**：确保只有有权限的管理员可以邀请业务员
2. **渠道验证**：生成邀请链接前验证渠道的有效性
3. **链接安全**：邀请链接应该包含必要的验证参数
4. **用户反馈**：提供清晰的操作反馈和错误提示
5. **兼容性**：复制功能提供了现代API和备用方案

## 后续扩展

1. **邀请统计**：可以添加邀请成功率统计
2. **邀请记录**：记录邀请历史和状态
3. **批量邀请**：支持批量生成邀请链接
4. **自定义模板**：支持自定义邀请二维码样式
5. **有效期控制**：为邀请链接设置有效期
