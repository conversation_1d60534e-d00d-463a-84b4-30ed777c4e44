<template>
  <div>

    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">酒店房型</div>
    </div>
    <van-radio-group v-model="dataFrom.hotelActivityRoomId">
      <van-cell-group>
        <van-cell v-for="item in dataList" :key="item.id" :title="item.name" clickable>
          <template #right-icon>
            <van-radio :name="item.id" @click="chooseRoom(item)" />
          </template>
          <div slot="label" v-if="item.price > 0">
            <div>￥{{ item.price }}元/间<span v-if="item.roomRemarks">({{ item.roomRemarks }})</span></div>
            <div v-if="item.bedStatus == 1 && item.bedNumber > 1">
              ￥{{ item.bedPrice }}元/床位<span v-if="item.bedRemarks">({{ item.bedRemarks }})</span>
            </div>
          </div>
        </van-cell>
      </van-cell-group>
    </van-radio-group>
    <van-cell-group v-show="dataFrom.hotelActivityRoomId" inset style="margin-top: 20px">
      <van-cell :required="true" title="入住时间" :value="dataFrom.inDate | dateFilter" @click="showInDate">
      </van-cell>
      <van-cell :required="true" title="退房时间" :value="dataFrom.outDate | dateFilter" @click="showOutDate">
      </van-cell>
      <van-cell  :required="true" v-if="indexRoom.bedStatus == 1 && indexRoom.bedNumber > 1" title="入住类型" :value="dataFrom.roomTypeName"
        @click="showType">
      </van-cell>
      <div style="color: red;font-size: 12px;padding-left: 10px" v-if="indexRoom.roomRemarks || indexRoom.bedRemarks">{{ (indexRoom.roomRemarks || '') + (indexRoom.bedRemarks || '') }}</div>
        <van-field v-model="dataFrom.contact" label="联系人" :required="true" placeholder="请输入联系人" :rules="[
          {
            required: true,
            message: '请输入联系人',
          },
        ]" />
        <van-field v-model="dataFrom.mobile" label="联系方式" :required="true" placeholder="请输入联系方式" :rules="[
          {
            required: true,
            message: '请输入联系方式',
          },
        ]" />
      <div v-if="appid != 'wx0770d56458b33c67'">
        <van-field v-model="dataFrom.number" @input="numberChange" type="number" label="数量" :required="true"
          placeholder="请输入数量" :rules="[
            {
              required: true,
              message: '请输入数量',
            },
          ]" />
        <van-field v-model="dataFrom.email" label="邮箱" :required="true" placeholder="请输入邮箱" :rules="[
          {
            required: true,
            message: '请输入邮箱',
          },
        ]" />
      </div>
    </van-cell-group>
    <!-- 房间类型选择 -->
    <van-action-sheet v-model="typeShow" :actions="roomType"
      @select="typeSelect" />
    <!-- 入住时间选择 -->
    <van-action-sheet v-model="inDateShow">
      <van-datetime-picker v-model="inDate" type="date" title="选择入住时间" :min-date="minDate" :max-date="maxDate"
        @confirm="inDateSelect" />
    </van-action-sheet>
    <!-- 退房时间选择 -->
    <van-action-sheet v-model="outDateShow">
      <van-datetime-picker v-model="outDate" type="date" title="选择退房时间" :min-date="minDate" :max-date="maxDate"
        @confirm="outDateSelect" />
    </van-action-sheet>
    <div class="bottom" v-show="dataFrom.hotelActivityRoomId">
      <div style="width: 40%;text-align: center;font-size: 18px">总价：<span style="color: red">￥{{ dataFrom.price }}元</span>
      </div>
      <div style="width: 60%;text-align: center"><van-button :loading="loading" style="width: 90%" round block
          type="info" @click="submit">提交订单</van-button></div>
    </div>
  </div>
</template>

<script>
import date from "@/js/date.js";
import { roomType, roomTypeFjsd } from "@/data/room";
export default {
  data() {
    return {
      userInfo: {},
      loading: false,
      appid: '',
      openid: undefined,
      dataFrom: {
        activityId: "",
        hotelId: "",
        hotelActivityId: "",
        hotelActivityRoomId: "",
        roomType: "",
        roomTypeName: "",
        price: 0,
        dayNumber: 0,
        number: 1,
        inDate: "",
        outDate: "",
        contact: "",
        mobile: "",
        email: "",
        orderToken: "",
      },
      roomType,
      roomTypeFjsd,
      minDate: "",
      maxDate: "",
      inDate: "",
      outDate: "",
      typeShow: false,
      inDateShow: false,
      outDateShow: false,
      indexRoom: {},
      dataList: [],
    };
  },
  filters: {
    dateFilter(val) {
      return !val ? '' : date.formatDate.format(
        new Date(val),
        "yyyy/MM/dd"
      );
    },
  },
  mounted() {
    this.appid = this.$cookie.get("appid");
    document.title = "选择房型";
    this.dataFrom.hotelActivityId = this.$route.query.id;
    this.dataFrom.activityId = this.$route.query.activityId;
    this.openid = this.$cookie.get("openid")
    this.rebuildUrl();
    this.getToken();
    this.getActivityRoomList();
    this.getUserActivityInfo();
  },
  methods: {
    getActivityRoomList() {
      this.$fly
        .get("/pyp/web/hotel/hotelactivityroom/list", {
          hotelActivityId: this.dataFrom.hotelActivityId,
          status: 1,
        })
        .then((res) => {
          if (res.code == 200) {
            this.dataList = res.result;
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
          }
        });
    },
    getToken() {
      this.$fly
        .get("/pyp/web/hotel/hotelactivity/createToken")
        .then((res) => {
          if (res.code == 200) {
            this.dataFrom.orderToken = res.result;
          }
        });
    },
    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activityuser/findByUserIdAndActivityId`, {
          activityId: this.dataFrom.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.result;
            this.dataFrom.contact = res.result.contact;
            this.dataFrom.mobile = res.result.mobile;
            this.dataFrom.email = res.result.email;
          }
        });
    },
    chooseRoom(v) {
      this.indexRoom = v;
      this.dataFrom.hotelActivityRoomId = v.id;
      this.dataFrom.hotelId = v.hotelId;
      // this.dataFrom.inDate = v.inDate;
      // this.dataFrom.outDate = v.outDate;
      this.minDate = new Date(v.inDate);
      this.maxDate = new Date(v.outDate);
      // this.inDate = new Date(v.inDate);
      // this.outDate = new Date(v.outDate);
      if (v.bedNumber == 1) {
        // 只有一个床位，自动赋值
        this.$set(this.dataFrom, "roomType", 0);
      } else {
        // 判断用户性别
        this.$set(this.dataFrom, "roomType", '');
      }
      // 重新计算价格
      this.countPrice();
    },
    showType() {
      this.typeShow = true;
    },
    typeSelect(v) {
      this.dataFrom.roomType = v.id;
      this.dataFrom.roomTypeName = v.name;
      this.typeShow = false;
      // 重新计算价格
      this.countPrice();
    },
    numberChange() {
      this.countPrice();
    },
    showInDate() {
      this.inDateShow = true;
    },
    showOutDate() {
      this.outDateShow = true;
    },
    inDateSelect() {
      this.dataFrom.inDate = date.formatDate.format(
        new Date(this.inDate),
        "yyyy/MM/dd hh:mm:ss"
      );
      this.inDateShow = false;
      // 重新计算价格
      this.countPrice();
    },
    outDateSelect() {
      this.dataFrom.outDate = date.formatDate.format(
        new Date(this.outDate),
        "yyyy/MM/dd hh:mm:ss"
      );
      this.outDateShow = false;
      // 重新计算价格
      this.countPrice();
    },
    countPrice() {
      if (this.outDate && this.inDate && this.dataFrom.roomType != null && this.dataFrom.roomType !== '') {
        var times = this.outDate.getTime() / 1000 - this.inDate.getTime() / 1000;
        this.dataFrom.dayNumber = parseInt(times / 60 / 60 / 24); //相差天数
        if (this.dataFrom.roomType == 0) {
          // 购买整间按整间价格算
          this.dataFrom.price =
            this.dataFrom.dayNumber * this.indexRoom.price * this.dataFrom.number;
        } else {
          // 购买床位按床位价格算
          this.dataFrom.price =
            this.dataFrom.dayNumber *
            this.indexRoom.bedPrice *
            this.dataFrom.number;
        }
      }
    },
    submit() {
      if (!this.dataFrom.number || this.dataFrom.number <= 0) {
        vant.Toast("请输入正确房间数量");
        return false;
      }
      if (this.dataFrom.roomType != 1 && this.dataFrom.roomType !== 0 && this.dataFrom.roomType != 2) {
        vant.Toast("请选择房型");
        return false;
      }
      if (!this.dataFrom.inDate) {
        vant.Toast("请选择入住时间");
        return false;
      }
      if (!this.dataFrom.outDate) {
        vant.Toast("请选择退房时间");
        return false;
      }
      if (!this.dataFrom.contact) {
        vant.Toast("请输入联系人");
        return false;
      }
      if (!this.dataFrom.mobile) {
        vant.Toast("请输入联系方式");
        return false;
      }
      if (!this.dataFrom.email && this.appid != 'wx0770d56458b33c67') {
        vant.Toast("请输入联系人邮箱");
        return false;
      }
      // 计算金额
      this.countPrice();
      // 校验时间
      if (this.dataFrom.dayNumber <= 0) {
        vant.Toast("退房时间必须大于入住时间");
        return false;
      }
      this.loading = true;
      var that = this;
      this.$fly
        .post("/pyp/web/hotel/hotelactivity/createOrder", this.dataFrom)
        .then((res) => {
          if (res.code == 200) {
            if (res.isNeedPay && !res.turnSuccess) {
              // 如果需要支付，调用支付接口，并且设置了微信支付，没有设置银行支付的前提下
              this.$fly
                .get(
                  "/pyp/web/hotel/hotelactivity/pay",
                  { orderId: res.result }
                )
                .then((res1) => {
                  if (res1 && res1.code === 200) {
                    WeixinJSBridge.invoke(
                      'getBrandWCPayRequest', {
                      "appId": res1.result.appId,
                      "timeStamp": res1.result.timeStamp,
                      "nonceStr": res1.result.nonceStr,
                      "package": res1.result.packageValue,
                      "signType": res1.result.signType,
                      "paySign": res1.result.paySign
                    },
                      function (res2) {
                        console.log("开始支付")
                        if (res2.err_msg == "get_brand_wcpay_request:ok") {
                          var baseUrl = window.location.href.split("#")[0];
                          location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + res.result; //支付成功跳转到详情页
                        } else if (res2.err_msg == "get_brand_wcpay_request:cancel") {
                          var baseUrl = window.location.href.split("#")[0];
                          location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + res.result;
                        } else {
                          var baseUrl = window.location.href.split("#")[0];
                          location.href = baseUrl + '#/hotel/success?id=' + that.activityId + "&orderId=" + res.result;
                        }
                      });
                  } else {
                    vant.Toast(res1.msg);

                  }
                })

            } else {
              vant.Toast("酒店预订提交成功");
              this.$router.push({
                name: "hotelSuccess",
                query: {
                  orderId: res.result,
                  id: this.activityId,
                }
              })
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
          }
          this.loading = false;
        });

    },
    rebuildUrl() {
      let {
        href,
        protocol,
        host,
        pathname,
        search,
        hash
      } = window.location
      console.log(window.location)
      search = search || '?'
      let newHref = `${protocol}//${host}${pathname}${search}${hash}`
      console.log(newHref)
      if (newHref !== href) {
        window.location.replace(newHref)
      }
    },
  },
};
</script>
<style lang="less" scoped>
.bottom {
  height: 60px;
  width: 100%;
  display: flex;
  position: fixed;
  bottom: 0px;
  background: white;
  justify-content: center;
  align-items: center;
}
</style>