<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="收费类型id" prop="channelId">
      <el-input v-model="dataForm.channelId" placeholder="收费类型id"></el-input>
    </el-form-item>
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="邀请码" prop="inviteCode">
      <el-input v-model="dataForm.inviteCode" placeholder="邀请码"></el-input>
    </el-form-item>
    <el-form-item label="是否使用 0-未使用 1-已使用" prop="isUse">
      <el-input v-model="dataForm.isUse" placeholder="是否使用 0-未使用 1-已使用"></el-input>
    </el-form-item>
    <el-form-item label="使用时间" prop="useTime">
      <el-input v-model="dataForm.useTime" placeholder="使用时间"></el-input>
    </el-form-item>
    <el-form-item label="使用人" prop="useBy">
      <el-input v-model="dataForm.useBy" placeholder="使用人"></el-input>
    </el-form-item>
    <el-form-item label="使用时间" prop="useByName">
      <el-input v-model="dataForm.useByName" placeholder="使用时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          channelId: '',
          activityId: '',
          inviteCode: '',
          isUse: '',
          useTime: '',
          useBy: '',
          useByName: '',
        },
        dataRule: {
          channelId: [
            { required: true, message: '收费类型id不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          inviteCode: [
            { required: true, message: '邀请码不能为空', trigger: 'blur' }
          ],
          isUse: [
            { required: true, message: '是否使用不能为空', trigger: 'blur' }
          ],
          useTime: [
            { required: true, message: '使用时间不能为空', trigger: 'blur' }
          ],
          useBy: [
            { required: true, message: '使用人不能为空', trigger: 'blur' }
          ],
          useByName: [
            { required: true, message: '使用时间不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/apply/applyinvitecode/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.dataForm.channelId = data.applyInviteCode.channelId
                this.dataForm.activityId = data.applyInviteCode.activityId
                this.dataForm.inviteCode = data.applyInviteCode.inviteCode
                this.dataForm.isUse = data.applyInviteCode.isUse
                this.dataForm.useTime = data.applyInviteCode.useTime
                this.dataForm.useBy = data.applyInviteCode.useBy
                this.dataForm.useByName = data.applyInviteCode.useByName
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/apply/applyinvitecode/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'channelId': this.dataForm.channelId,
                'activityId': this.dataForm.activityId,
                'inviteCode': this.dataForm.inviteCode,
                'isUse': this.dataForm.isUse,
                'useTime': this.dataForm.useTime,
                'useBy': this.dataForm.useBy,
                'useByName': this.dataForm.useByName,
              })
            }).then(({data}) => {
              if (data && data.code ===200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
