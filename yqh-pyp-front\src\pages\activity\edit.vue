<template>
  <div class="activity-edit-page">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="修改活动"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      class="custom-nav-bar"
    >
      <!-- <template #right>
        <van-button
          type="primary"
          size="small"
          :loading="saving"
          @click="saveActivity"
          class="save-button"
        >
          保存
        </van-button>
      </template> -->
    </van-nav-bar>

    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <div class="progress-steps">
        <div class="step active">
          <div class="step-icon">
            <van-icon name="info-o" />
          </div>
          <span>基本信息</span>
        </div>
        <div class="step-line"></div>
        <div class="step active">
          <div class="step-icon">
            <van-icon name="photo-o" />
          </div>
          <span>图片设置</span>
        </div>
        <div class="step-line"></div>
        <div class="step active">
          <div class="step-icon">
            <van-icon name="setting-o" />
          </div>
          <span>平台配置</span>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <van-form @submit="onSubmit" ref="form" style="margin: 10px;">
        <!-- 基本信息 -->
        <div class="form-section" id="basic-info">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="info-o" />
              </div>
              <div class="title-content">
                <h3>基本信息</h3>
                <p>设置活动的基础信息</p>
              </div>
            </div>
            <div class="section-badge">
              <van-tag type="primary" size="medium">必填</van-tag>
            </div>
          </div>

          <div class="section-content">
            <van-field
              v-model="formData.name"
              name="name"
              label="活动名称"
              placeholder="请输入活动名称"
              :rules="[{ required: true, message: '请输入活动名称' }]"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="edit" />
              </template>
            </van-field>
          </div>
        </div>

        <!-- 标题生成配置 -->
        <div class="form-section" id="title-config">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="magic" />
              </div>
              <div class="title-content">
                <h3>标题生成配置</h3>
                <p>配置AI生成标题的相关参数</p>
              </div>
            </div>
            <div class="section-badge">
              <van-tag type="warning" size="medium">可选</van-tag>
            </div>
          </div>

          <div class="section-content">
            <!-- 标题生成模式 -->
            <van-field
              name="nameMode"
              label="标题生成模式"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="setting-o" />
              </template>
              <template #input>
                <van-radio-group v-model="formData.nameMode" direction="horizontal">
                  <van-radio name="ai" class="custom-radio">
                    <template #icon="props">
                      <div class="radio-icon" :class="{ active: props.checked }">
                        <van-icon name="magic" />
                      </div>
                    </template>
                    AI生成
                  </van-radio>
                  <van-radio name="manual" class="custom-radio">
                    <template #icon="props">
                      <div class="radio-icon" :class="{ active: props.checked }">
                        <van-icon name="edit" />
                      </div>
                    </template>
                    手动填写
                  </van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <!-- 默认标题 -->
            <van-field
              v-model="formData.defaultName"
              name="defaultName"
              label="默认标题"
              placeholder="请输入默认标题"
              maxlength="50"
              show-word-limit
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="label-o" />
              </template>
            </van-field>

            <!-- 默认提示词 -->
            <van-field
              v-model="formData.defaultTitle"
              name="defaultTitle"
              label="默认提示词"
              placeholder="请输入默认提示词"
              maxlength="100"
              show-word-limit
              type="textarea"
              rows="2"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="comment-o" />
              </template>
            </van-field>

            <!-- 默认用户输入 -->
            <van-field
              v-model="formData.defaultUserInput"
              name="defaultUserInput"
              label="默认用户输入"
              placeholder="设置用户自定义补充字段的默认值，如：请在这里补充您的特殊要求或想法"
              maxlength="200"
              show-word-limit
              type="textarea"
              rows="2"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="edit" />
              </template>
            </van-field>

            <!-- 配置说明 -->
            <div class="config-tips">
              <van-notice-bar
                left-icon="info-o"
                text="AI生成模式：系统将根据默认提示词自动生成标题；手动填写模式：使用默认标题作为固定标题"
                color="#1989fa"
                background="#ecf5ff"
                :scrollable="false"
              />
            </div>
          </div>
        </div>

        <!-- 图片设置 -->
        <div class="form-section" id="image-settings">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="photo-o" />
              </div>
              <div class="title-content">
                <h3>图片设置</h3>
                <p>上传活动相关的图片素材</p>
              </div>
            </div>
            <div class="section-badge">
              <!-- <van-tag type="warning" size="medium">可选</van-tag> -->
            </div>
          </div>

          <div class="section-content">
            <div class="image-grid">
              <!-- Logo -->
              <div class="image-item">
                <div class="image-header">
                  <van-icon name="user-circle-o" />
                  <span>Logo</span>
                </div>
                <van-field name="logo" class="image-field">
                  <template #input>
                    <div class="image-upload-field">
                      <van-uploader
                        v-model="logoFileList"
                        :max-count="1"
                        :after-read="(file) => afterRead(file, 'logo')"
                        :before-delete="() => beforeDelete('logo')"
                        class="custom-uploader"
                      >
                        <div class="upload-area">
                          <van-icon name="plus" />
                          <span>选择Logo</span>
                        </div>
                      </van-uploader>
                      <div class="upload-tip">建议尺寸：200*200，大小100kb以下</div>
                    </div>
                  </template>
                </van-field>
              </div>

              <!-- 主图片 -->
              <div class="image-item">
                <div class="image-header">
                  <van-icon name="photo" />
                  <span>主图片</span>
                </div>
                <van-field name="mobileBanner" class="image-field">
                  <template #input>
                    <div class="image-upload-field">
                      <van-uploader
                        v-model="mobileBannerFileList"
                        :max-count="9"
                        multiple
                        :after-read="(file) => afterRead(file, 'mobileBanner')"
                        :before-delete="(file) => beforeDelete('mobileBanner', file)"
                        class="custom-uploader"
                      >
                        <div class="upload-area">
                          <van-icon name="plus" />
                          <span>选择图片</span>
                        </div>
                      </van-uploader>
                      <div class="upload-tip">建议尺寸：1920*1080，大小2mb以下</div>
                    </div>
                  </template>
                </van-field>
              </div>

              <!-- 背景图 -->
              <!-- <div class="image-item">
                <div class="image-header">
                  <van-icon name="apps-o" />
                  <span>背景图</span>
                </div>
                <van-field name="background" class="image-field">
                  <template #input>
                    <div class="image-upload-field">
                      <van-uploader
                        v-model="backgroundFileList"
                        :max-count="1"
                        :after-read="(file) => afterRead(file, 'background')"
                        :before-delete="() => beforeDelete('background')"
                        class="custom-uploader"
                      >
                        <div class="upload-area">
                          <van-icon name="plus" />
                          <span>选择背景图</span>
                        </div>
                      </van-uploader>
                      <div class="upload-tip">建议尺寸：1080*1920，大小1mb以下</div>
                    </div>
                  </template>
                </van-field>
              </div> -->

              <!-- 分享图 -->
              <!-- <div class="image-item">
                <div class="image-header">
                  <van-icon name="share-o" />
                  <span>分享图</span>
                </div>
                <van-field name="shareUrl" class="image-field">
                  <template #input>
                    <div class="image-upload-field">
                      <van-uploader
                        v-model="shareUrlFileList"
                        :max-count="1"
                        :after-read="(file) => afterRead(file, 'shareUrl')"
                        :before-delete="() => beforeDelete('shareUrl')"
                        class="custom-uploader"
                      >
                        <div class="upload-area">
                          <van-icon name="plus" />
                          <span>选择分享图</span>
                        </div>
                      </van-uploader>
                      <div class="upload-tip">建议尺寸：400*400，大小300kb以下</div>
                    </div>
                  </template>
                </van-field>
              </div> -->

              <!-- 公众号图片 -->
              <div class="image-item">
                <div class="image-header">
                  <van-icon name="wechat" color="#07c160" />
                  <span>公众号图片</span>
                </div>
                <van-field name="wechatQrCode" class="image-field">
                  <template #input>
                    <div class="image-upload-field">
                      <van-uploader
                        v-model="wechatQrCodeFileList"
                        :max-count="1"
                        :after-read="(file) => afterRead(file, 'wechatQrCode')"
                        :before-delete="() => beforeDelete('wechatQrCode')"
                        class="custom-uploader"
                      >
                        <div class="upload-area">
                          <van-icon name="plus" />
                          <span>选择公众号图片</span>
                        </div>
                      </van-uploader>
                      <div class="upload-tip">建议尺寸：430*430，大小500kb以下</div>
                    </div>
                  </template>
                </van-field>
              </div>
            </div>
          </div>
        </div>

        <!-- 平台设置 -->
        <div class="form-section" id="platform-settings">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="setting-o" />
              </div>
              <div class="title-content">
                <h3>平台设置</h3>
                <p>配置各平台相关信息</p>
              </div>
            </div>
            <div class="section-badge">
              <!-- <van-tag type="success" size="medium">推荐</van-tag> -->
            </div>
          </div>

          <div class="section-content">
            <!-- 平台类型设置 -->
            <div class="setting-group">
              <div class="group-title">
                <van-icon name="apps-o" />
                <span>平台类型</span>
              </div>
              <div class="type-grid">
                <div class="type-item">
                  <van-field name="douyinType" label="抖音类型" class="custom-field">
                    <template #input>
                      <van-radio-group v-model="formData.douyinType" direction="horizontal" class="custom-radio-group">
                        <van-radio :name="0" class="custom-radio">视频</van-radio>
                        <van-radio :name="1" class="custom-radio">图文</van-radio>
                      </van-radio-group>
                    </template>
                  </van-field>
                </div>
                <div class="type-item">
                  <van-field name="xiaohongshuType" label="小红书类型" class="custom-field">
                    <template #input>
                      <van-radio-group v-model="formData.xiaohongshuType" direction="horizontal" class="custom-radio-group">
                        <van-radio :name="0" class="custom-radio">视频</van-radio>
                        <van-radio :name="1" class="custom-radio">图文</van-radio>
                      </van-radio-group>
                    </template>
                  </van-field>
                </div>
              </div>
            </div>

            <!-- 平台信息 -->
            <div class="setting-group">
              <div class="group-title">
                <van-icon name="link-o" />
                <span>平台信息</span>
              </div>
              <div class="platform-fields">
                <van-field
                  v-model="formData.douyinPoi"
                  name="douyinPoi"
                  label="抖音POI"
                  placeholder="请输入抖音POI"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="location-o" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.douyindianping"
                  name="douyindianping"
                  label="抖音点评"
                  placeholder="请输入抖音点评"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="star-o" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.meituan"
                  name="meituan"
                  label="美团"
                  placeholder="请输入美团"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="shop-o" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.dazhongdianping"
                  name="dazhongdianping"
                  label="大众点评"
                  placeholder="请输入大众点评"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="comment-o" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.qiyeweixin"
                  name="qiyeweixin"
                  label="企业微信"
                  placeholder="请输入企业微信"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="chat-o" />
                  </template>
                </van-field>

                <!-- WiFi配置 -->
                <van-field
                  v-model="formData.wifiAccount"
                  name="wifiAccount"
                  label="WiFi账号"
                  placeholder="请输入WiFi账号"
                  class="custom-field"
                >
                  <template #left-icon>
                    <div class="wifi-icon" style="color: #5352ed;">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
                      </svg>
                    </div>
                  </template>
                </van-field>

                <van-field
                  v-model="formData.wifiPassword"
                  name="wifiPassword"
                  label="WiFi密码"
                  placeholder="请输入WiFi密码"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="lock" color="#5352ed" />
                  </template>
                </van-field>

                <!-- 携程配置 -->
                <van-field
                  v-model="formData.ctripConfig"
                  name="ctripConfig"
                  label="携程首页配置"
                  placeholder="请输入携程首页配置信息"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="location-o" color="#0066cc" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.ctripReviewConfig"
                  name="ctripReviewConfig"
                  label="携程点评配置"
                  placeholder="请输入携程点评配置信息"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="star-o" color="#0066cc" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.ctripNotesConfig"
                  name="ctripNotesConfig"
                  label="携程笔记配置"
                  placeholder="请输入携程笔记配置信息"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="notes-o" color="#0066cc" />
                  </template>
                </van-field>

                <!-- <van-field
                  v-model="formData.mobile"
                  name="mobile"
                  label="联系手机"
                  placeholder="请输入联系手机号"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="phone-o" color="#52c41a" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.adminAccount"
                  name="adminAccount"
                  label="管理员账号"
                  placeholder="请输入管理员账号"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="manager-o" color="#722ed1" />
                  </template>
                </van-field> -->

                <van-field
                  v-model="formData.zhuyeDouyin"
                  name="zhuyeDouyin"
                  label="抖音主页"
                  placeholder="请输入抖音主页"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="user-o" />
                  </template>
                </van-field>

                <van-field
                  v-model="formData.zhuyeKuaishou"
                  name="zhuyeKuaishou"
                  label="快手主页"
                  placeholder="请输入快手主页"
                  class="custom-field"
                >
                  <template #left-icon>
                    <van-icon name="user-circle-o" />
                  </template>
                </van-field>
              </div>
            </div>
          </div>
        </div>

        <!-- 显示设置 -->
        <div class="form-section" id="display-settings">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="eye-o" />
              </div>
              <div class="title-content">
                <h3>显示设置</h3>
                <p>控制各功能模块的显示状态</p>
              </div>
            </div>
            <div class="section-badge">
              <!-- <van-tag type="default" size="medium">可选</van-tag> -->
            </div>
          </div>

          <div class="section-content">
            <div class="switch-grid">
              <!-- 平台显示开关 -->
              <div class="switch-group">
                <div class="group-title">
                  <van-icon name="tv-o" />
                  <span>平台显示</span>
                </div>
                <div class="switch-items">
                  <div class="switch-item">
                    <van-field name="showDouyin" label="显示抖音" class="switch-field">
                      <template #left-icon>
                        <van-icon name="video-o" color="#ff6b6b" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showDouyin" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showXiaohongshu" label="显示小红书" class="switch-field">
                      <template #left-icon>
                        <van-icon name="photo-o" color="#ff4757" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showXiaohongshu" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showKuaishou" label="显示快手" class="switch-field">
                      <template #left-icon>
                        <van-icon name="play-circle-o" color="#ffa502" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showKuaishou" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showShipinhao" label="显示视频号" class="switch-field">
                      <template #left-icon>
                        <van-icon name="video" color="#2ed573" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showShipinhao" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>
                </div>
              </div>

              <!-- 功能显示开关 -->
              <div class="switch-group">
                <div class="group-title">
                  <van-icon name="setting-o" />
                  <span>功能显示</span>
                </div>
                <div class="switch-items">
                  <div class="switch-item">
                    <van-field name="showDouyindianping" label="显示抖音点评" class="switch-field">
                      <template #left-icon>
                        <van-icon name="star-o" color="#ff6b6b" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showDouyindianping" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showDazhongdianping" label="显示大众点评" class="switch-field">
                      <template #left-icon>
                        <van-icon name="comment-o" color="#ffa502" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showDazhongdianping" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showMeituandianping" label="显示美团点评" class="switch-field">
                      <template #left-icon>
                        <van-icon name="shop-o" color="#2ed573" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showMeituandianping" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showQiyeweixin" label="显示企业微信" class="switch-field">
                      <template #left-icon>
                        <van-icon name="chat-o" color="#3742fa" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showQiyeweixin" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showMiniProgram" label="显示微信小程序" class="switch-field">
                      <template #left-icon>
                        <van-icon name="wechat" color="#07c160" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showMiniProgram" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showWifi" label="显示WIFI" class="switch-field">
                      <template #left-icon>
                        <div class="wifi-icon" style="color: #5352ed;">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
                          </svg>
                        </div>
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showWifi" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showGuanzhukuaishou" label="显示关注快手" class="switch-field">
                      <template #left-icon>
                        <van-icon name="like-o" color="#ff9ff3" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showGuanzhukuaishou" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showGuanzhudouyin" label="显示关注抖音" class="switch-field">
                      <template #left-icon>
                        <van-icon name="good-job-o" color="#ff3838" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showGuanzhudouyin" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showShipindianzan" label="显示视频点赞" class="switch-field">
                      <template #left-icon>
                        <van-icon name="thumb-circle-o" color="#ff6348" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showShipindianzan" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showCtrip" label="显示携程首页" class="switch-field">
                      <template #left-icon>
                        <van-icon name="location-o" color="#0066cc" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showCtrip" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showCtripReview" label="显示携程点评详情" class="switch-field">
                      <template #left-icon>
                        <van-icon name="star-o" color="#0066cc" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showCtripReview" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showCtripNotes" label="显示携程笔记" class="switch-field">
                      <template #left-icon>
                        <van-icon name="notes-o" color="#0066cc" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showCtripNotes" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showGroupBuying" label="显示团购" class="switch-field">
                      <template #left-icon>
                        <van-icon name="shopping-cart-o" color="#ff9500" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showGroupBuying" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showWechatQr" label="显示微信公众号二维码" class="switch-field">
                      <template #left-icon>
                        <van-icon name="qr" color="#07c160" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showWechatQr" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>

                  <div class="switch-item">
                    <van-field name="showMyShop" label="显示我的小店" class="switch-field">
                      <template #left-icon>
                        <van-icon name="shop-o" color="#ff9500" />
                      </template>
                      <template #input>
                        <van-switch v-model="formData.showMyShop" :active-value="1" :inactive-value="0" class="custom-switch" />
                      </template>
                    </van-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 我的小店配置 -->
        <div class="form-section" id="shop-settings" v-if="formData.showMyShop">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="shop-o" />
              </div>
              <div class="title-content">
                <h3>我的小店配置</h3>
                <p>配置商户自己的网页或小程序跳转</p>
              </div>
            </div>
            <div class="section-badge">
              <van-tag type="warning" size="medium">必填</van-tag>
            </div>
          </div>

          <div class="section-content">
            <!-- 小店类型选择 -->
            <van-field name="shopType" label="小店类型" class="custom-field">
              <template #left-icon>
                <van-icon name="apps-o" />
              </template>
              <template #input>
                <van-radio-group v-model="formData.shopType" direction="horizontal">
                  <van-radio :name="0">网页</van-radio>
                  <van-radio :name="1">小程序</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <!-- 网页URL配置 -->
            <van-field
              v-if="formData.shopType === 0"
              v-model="formData.shopUrl"
              name="shopUrl"
              label="小店网页URL"
              placeholder="请输入小店网页URL，如：https://www.example.com/shop"
              :rules="formData.showMyShop && formData.shopType === 0 ? [{ required: true, message: '请输入小店网页URL' }] : []"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="link-o" />
              </template>
            </van-field>

            <!-- 小程序配置 -->
            <template v-if="formData.shopType === 1">
              <van-field
                v-model="formData.shopAppid"
                name="shopAppid"
                label="小程序AppID"
                placeholder="请输入小程序AppID"
                :rules="formData.showMyShop && formData.shopType === 1 ? [{ required: true, message: '请输入小程序AppID' }] : []"
                class="custom-field"
              >
                <template #left-icon>
                  <van-icon name="wechat" />
                </template>
              </van-field>

              <van-field
                v-model="formData.shopPagePath"
                name="shopPagePath"
                label="小程序页面路径"
                placeholder="请输入页面路径，如：pages/shop/index"
                :rules="formData.showMyShop && formData.shopType === 1 ? [{ required: true, message: '请输入小程序页面路径' }] : []"
                class="custom-field"
              >
                <template #left-icon>
                  <van-icon name="location-o" />
                </template>
              </van-field>
            </template>

            <!-- 小店描述 -->
            <van-field
              v-model="formData.shopDescription"
              name="shopDescription"
              label="小店描述"
              placeholder="请输入小店描述，如：商户专属店铺"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="description" />
              </template>
            </van-field>
          </div>
        </div>

        <!-- 音乐设置 -->
        <!-- <div class="form-section" id="music-settings">
          <div class="section-header">
            <div class="section-title">
              <div class="title-icon">
                <van-icon name="music-o" />
              </div>
              <div class="title-content">
                <h3>背景音乐</h3>
                <p>为活动选择合适的背景音乐</p>
              </div>
            </div>
            <div class="section-badge">
              <van-tag type="default" size="medium">可选</van-tag>
            </div>
          </div>

          <div class="section-content">
            <div class="music-setting-compact">
              <van-field name="musicUrl" class="music-field">
                <template #label>
                  <div class="music-label">
                    <van-icon name="music-o" />
                    <span>背景音乐</span>
                  </div>
                </template>
                <template #input>
                  <van-picker
                    v-model="formData.musicUrl"
                    :columns="musicOptions"
                    @confirm="onMusicConfirm"
                  >
                    <template #default>
                      <div class="music-selector-compact">
                        <span class="music-name">{{ getMusicName(formData.musicUrl) || '点击选择音乐' }}</span>
                        <van-icon name="arrow-down" class="arrow-icon" />
                      </div>
                    </template>
                  </van-picker>
                </template>
              </van-field>

              <div v-if="formData.musicUrl" class="music-preview">
                <van-button
                  size="small"
                  type="primary"
                  plain
                  @click="playMusic"
                  class="preview-button"
                >
                  <van-icon name="play-circle-o" />
                  预览
                </van-button>
                <van-button
                  size="small"
                  @click="clearMusic"
                  class="clear-button"
                >
                  <van-icon name="clear" />
                  清除
                </van-button>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 底部保存按钮 -->
        <div class="form-footer">
          <div class="footer-content">
            <div class="footer-info">
              <van-icon name="info-o" />
              <span>修改后将立即生效</span>
            </div>
            <div class="footer-buttons">
              <van-button
                size="large"
                @click="goBack"
                class="cancel-button"
              >
                取消
              </van-button>
              <van-button
                type="primary"
                size="large"
                :loading="saving"
                @click="saveActivity"
                class="save-button"
              >
                <van-icon name="success" v-if="!saving" />
                保存修改
              </van-button>
            </div>
          </div>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityEdit',
  data() {
    return {
      saving: false,
      activityId: null,
      musicList: [],
      formData: {
        name: '',
        logo: '',
        mobileBanner: '',
        background: '',
        shareUrl: '',
        musicUrl: '',
        douyinType: 0,
        xiaohongshuType: 1,
        douyinPoi: '',
        douyindianping: '',
        meituan: '',
        dazhongdianping: '',
        qiyeweixin: '',
        zhuyeDouyin: '',
        zhuyeKuaishou: '',
        wifiAccount: '',
        wifiPassword: '',
        wechatQrCode: '',
        showDouyin: 1,
        showXiaohongshu: 1,
        showKuaishou: 1,
        showShipinhao: 1,
        showDouyindianping: 1,
        showDazhongdianping: 1,
        showMeituandianping: 1,
        showQiyeweixin: 1,
        showMiniProgram: 1,
        showWifi: 1,
        showGuanzhukuaishou: 1,
        showGuanzhudouyin: 1,
        showShipindianzan: 1,
        nameMode: 'ai',
        defaultName: '',
        defaultTitle: '',
        defaultUserInput: '',
        showCtrip: 0,
        ctripConfig: '',
        showCtripReview: 0,
        showCtripNotes: 0,
        ctripReviewConfig: '',
        ctripNotesConfig: '',
        showWechatQr: 0,
        mobile: '',
        adminAccount: '',
        expirationTime: null,
        shopType: 0,
        shopUrl: '',
        shopAppid: '',
        shopPagePath: '',
        shopDescription: '商户专属店铺',
        showMyShop: 0,
        showGroupBuying: 0,
      },
      // 文件列表
      logoFileList: [],
      mobileBannerFileList: [],
      backgroundFileList: [],
      shareUrlFileList: [],
      wechatQrCodeFileList: [],
    };
  },
  computed: {
    musicOptions() {
      return [
        { text: '无', value: '' },
        ...this.musicList.map(item => ({
          text: item.name,
          value: item.url
        }))
      ];
    }
  },
  mounted() {
    this.activityId = this.$route.query.id;
    if (this.activityId) {
      this.loadActivityData();
    }
    this.loadMusicList();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载活动数据
    loadActivityData() {
      this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then((res) => {
        if (res.code === 200) {
          const activity = res.activity;
          this.formData = {
            name: activity.name || '',
            logo: activity.logo || '',
            mobileBanner: activity.mobileBanner || '',
            background: activity.background || '',
            shareUrl: activity.shareUrl || '',
            musicUrl: activity.musicUrl || '',
            douyinType: activity.douyinType || 0,
            xiaohongshuType: activity.xiaohongshuType || 1,
            douyinPoi: activity.douyinPoi || '',
            douyindianping: activity.douyindianping || '',
            meituan: activity.meituan || '',
            dazhongdianping: activity.dazhongdianping || '',
            qiyeweixin: activity.qiyeweixin || '',
            wifiAccount: activity.wifiAccount || '',
            wifiPassword: activity.wifiPassword || '',
            wechatQrCode: activity.wechatQrCode || '',
            zhuyeDouyin: activity.zhuyeDouyin || '',
            zhuyeKuaishou: activity.zhuyeKuaishou || '',
            showDouyin: activity.showDouyin !== undefined ? activity.showDouyin : 1,
            showXiaohongshu: activity.showXiaohongshu !== undefined ? activity.showXiaohongshu : 1,
            showKuaishou: activity.showKuaishou !== undefined ? activity.showKuaishou : 1,
            showShipinhao: activity.showShipinhao !== undefined ? activity.showShipinhao : 1,
            showDouyindianping: activity.showDouyindianping !== undefined ? activity.showDouyindianping : 1,
            showDazhongdianping: activity.showDazhongdianping !== undefined ? activity.showDazhongdianping : 1,
            showMeituandianping: activity.showMeituandianping !== undefined ? activity.showMeituandianping : 1,
            showQiyeweixin: activity.showQiyeweixin !== undefined ? activity.showQiyeweixin : 1,
            showWifi: activity.showWifi !== undefined ? activity.showWifi : 1,
            showGuanzhukuaishou: activity.showGuanzhukuaishou !== undefined ? activity.showGuanzhukuaishou : 1,
            showGuanzhudouyin: activity.showGuanzhudouyin !== undefined ? activity.showGuanzhudouyin : 1,
            showShipindianzan: activity.showShipindianzan !== undefined ? activity.showShipindianzan : 1,
            nameMode: activity.nameMode || 'ai',
            defaultName: activity.defaultName || '',
            defaultTitle: activity.defaultTitle || '',
            defaultUserInput: activity.defaultUserInput || '',
            showCtrip: activity.showCtrip !== undefined ? activity.showCtrip : 0,
            ctripConfig: activity.ctripConfig || '',
            showCtripReview: activity.showCtripReview !== undefined ? activity.showCtripReview : 0,
            showCtripNotes: activity.showCtripNotes !== undefined ? activity.showCtripNotes : 0,
            ctripReviewConfig: activity.ctripReviewConfig || '',
            ctripNotesConfig: activity.ctripNotesConfig || '',
            showWechatQr: activity.showWechatQr !== undefined ? activity.showWechatQr : 0,
            mobile: activity.mobile || '',
            adminAccount: activity.adminAccount || '',
            expirationTime: activity.expirationTime || null,
            shopDescription: activity.shopDescription || '商户专属店铺',
            showGroupBuying: activity.showGroupBuying !== undefined ? activity.showGroupBuying : 0,
          };

          // 初始化文件列表
          this.initFileList();
        } else {
          this.$toast.fail(res.msg || '加载活动数据失败');
        }
      }).catch((error) => {
        console.error('加载活动数据失败:', error);
        this.$toast.fail('加载活动数据失败');
      });
    },

    // 加载音乐列表
    loadMusicList() {
      this.$fly.get('/pyp/sys/sysmusic/findAll').then((res) => {
        if (res.code === 200) {
          this.musicList = res.result || [];
        }
      }).catch((error) => {
        console.error('加载音乐列表失败:', error);
      });
    },

    // 初始化文件列表
    initFileList() {
      // Logo
      if (this.formData.logo) {
        this.logoFileList = [{
          url: this.formData.logo,
          isImage: true
        }];
      }

      // 主图片
      if (this.formData.mobileBanner) {
        this.mobileBannerFileList = this.formData.mobileBanner.split(',').map(url => ({
          url: url.trim(),
          isImage: true
        })).filter(item => item.url);
      }

      // 背景图
      if (this.formData.background) {
        this.backgroundFileList = [{
          url: this.formData.background,
          isImage: true
        }];
      }

      // 分享图
      if (this.formData.shareUrl) {
        this.shareUrlFileList = [{
          url: this.formData.shareUrl,
          isImage: true
        }];
      }

      // 公众号图片
      if (this.formData.wechatQrCode) {
        this.wechatQrCodeFileList = [{
          url: this.formData.wechatQrCode,
          isImage: true
        }];
      }
    },

    // 文件上传后处理
    afterRead(file, field) {
      const formData = new FormData();
      formData.append('file', file.file);

      this.$fly.post('/pyp/web/upload', formData).then((res) => {
        if (res.code === 200) {
          const url = res.result;

          switch (field) {
            case 'logo':
              this.formData.logo = url;
              break;
            case 'mobileBanner':
              const currentUrls = this.formData.mobileBanner ? this.formData.mobileBanner.split(',') : [];
              currentUrls.push(url);
              this.formData.mobileBanner = currentUrls.join(',');
              break;
            case 'background':
              this.formData.background = url;
              break;
            case 'shareUrl':
              this.formData.shareUrl = url;
              break;
            case 'wechatQrCode':
              this.formData.wechatQrCode = url;
              break;
          }

          file.url = url;
          this.$toast.success('上传成功');
        } else {
          this.$toast.fail(res.msg || '上传失败');
        }
      }).catch((error) => {
        console.error('上传失败:', error);
        this.$toast.fail('上传失败');
      });
    },

    // 删除文件前处理
    beforeDelete(field, file) {
      switch (field) {
        case 'logo':
          this.formData.logo = '';
          break;
        case 'mobileBanner':
          if (file && file.url) {
            const urls = this.formData.mobileBanner.split(',');
            const index = urls.indexOf(file.url);
            if (index > -1) {
              urls.splice(index, 1);
              this.formData.mobileBanner = urls.join(',');
            }
          }
          break;
        case 'background':
          this.formData.background = '';
          break;
        case 'shareUrl':
          this.formData.shareUrl = '';
          break;
        case 'wechatQrCode':
          this.formData.wechatQrCode = '';
          break;
      }
      return true;
    },

    // 音乐选择确认
    onMusicConfirm(value) {
      this.formData.musicUrl = value;
    },

    // 获取音乐名称
    getMusicName(url) {
      if (!url) return '';
      const music = this.musicList.find(item => item.url === url);
      return music ? music.name : '未知音乐';
    },

    // 预览音乐
    playMusic() {
      if (this.formData.musicUrl) {
        // 创建音频元素进行预览
        const audio = new Audio(this.formData.musicUrl);
        audio.play().catch(() => {
          this.$toast.fail('音乐预览失败');
        });
        this.$toast.success('开始播放预览');
      }
    },

    // 清除音乐
    clearMusic() {
      this.formData.musicUrl = '';
      this.$toast.success('已清除背景音乐');
    },

    // 过期时间确认
    onExpirationTimeConfirm(value) {
      this.formData.expirationTime = value;
    },

    // 格式化过期时间显示
    formatExpirationTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 表单提交
    onSubmit() {
      this.saveActivity();
    },

    // 保存活动
    saveActivity() {
      // 验证表单
      this.$refs.form.validate().then(() => {
        this.saving = true;

        this.$fly.post('/pyp/web/activity/update', {
          id: this.activityId,
          ...this.formData
        }).then((res) => {
          this.saving = false;
          if (res.code === 200) {
            this.$toast.success('保存成功');
            setTimeout(() => {
              this.goBack();
            }, 1000);
          } else {
            this.$toast.fail(res.msg || '保存失败');
          }
        }).catch((error) => {
          this.saving = false;
          console.error('保存失败:', error);
          this.$toast.fail('保存失败');
        });
      }).catch(() => {
        // 表单验证失败
        this.$toast.fail('请检查表单填写');
      });
    }
  }
};
</script>

<style lang="less" scoped>
.activity-edit-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
}

.custom-nav-bar {
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;


  .save-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 1);
    }
  }
}

.progress-indicator {
  background: white;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .progress-steps {
    display: flex;
    align-items: center;
    justify-content: center;

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      opacity: 0.5;
      transition: all 0.3s ease;

      &.active {
        opacity: 1;
      }

      .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      span {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }
    }

    .step-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      margin: 0 20px;
      border-radius: 1px;
    }
  }
}

.form-container {
  padding-bottom: 120px;
}

.form-section {
  background: white;
  margin-bottom: 12px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;

    .section-title {
      display: flex;
      align-items: center;
      gap: 15px;

      .title-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
      }

      .title-content {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }

        p {
          margin: 0;
          font-size: 13px;
          color: #666;
        }
      }
    }

    .section-badge {
      .van-tag {
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 12px;
      }
    }
  }

  .section-content {
    padding: 20px;
  }
}

// 图片上传相关样式
.image-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.image-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;

  .image-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
    font-weight: 500;
    color: #333;

    .van-icon {
      color: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .image-field {
    padding: 16px;
    background: transparent;
  }
}

.image-upload-field {
  width: 100%;

  .custom-uploader {
    .upload-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 30px 20px;
      border: 2px dashed #d1d5db;
      border-radius: 8px;
      background: white;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: #667eea;
        background: #f8fafc;
      }

      .van-icon {
        font-size: 24px;
        color: #9ca3af;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      span {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }

  .upload-tip {
    margin-top: 12px;
    font-size: 12px;
    color: #9ca3af;
    line-height: 1.4;
    text-align: center;
    padding: 8px 12px;
    background: #f1f5f9;
    border-radius: 6px;
  }
}

// 设置组相关样式
.setting-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .group-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #374151;

    .van-icon {
      color: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}



.type-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.type-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.platform-fields {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1px;
  background: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;

  .custom-field {
    background: white;
    margin: 0;
  }
}

// 开关组相关样式
.switch-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .group-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #374151;

    .van-icon {
      color: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .switch-items {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1px;
    background: #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
  }

  .switch-item {
    background: white;

    .switch-field {
      margin: 0;
      padding: 8px 10px;

      :deep(.van-field__label) {
        color: #374151;
        font-weight: 500;
      }

      :deep(.van-field__left-icon) {
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.custom-switch {
  :deep(.van-switch) {
    &--on {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }
}

.form-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e2e8f0;
  z-index: 100;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

  .footer-content {
    padding: 8px 10px;

    .footer-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 13px;
      color: #6b7280;

      .van-icon {
        color: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .footer-buttons {
      display: flex;
      gap: 12px;

      .cancel-button {
        flex: 1;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        color: #6b7280;

        &:hover {
          background: #f1f5f9;
        }
      }

      .save-button {
        flex: 2;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

        .van-icon {
          margin-right: 6px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}

// 通用字段样式
.custom-field {
  :deep(.van-field__label) {
    width: 90px;
    flex: none;
    color: #374151;
    font-weight: 500;
  }

  :deep(.van-field__left-icon) {
    margin-right: 8px;
    color: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.van-field__control) {
    color: #374151;
  }

  :deep(.van-field__body) {
    border-radius: 8px;
  }
}

// 覆盖vant样式
:deep(.van-uploader__upload) {
  margin: 0;
  width: 100%;
}

:deep(.van-uploader__preview) {
  margin: 8px 8px 0 0;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.van-uploader__preview-image) {
  border-radius: 8px;
}

:deep(.van-radio-group--horizontal) {
  display: flex;
  gap: 8px;
  padding: 6px 8x;

  .van-radio {
    margin: 0;
    flex: 1;

    .van-radio__label {
      color: #374151;
      font-weight: 500;
    }

    &--checked {
      .van-radio__icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
      }
    }
  }
}

:deep(.van-picker) {
  .van-button {
    border: none;
    background: transparent;
    color: inherit;
    padding: 0;

    &:active {
      background: transparent;
    }
  }
}
.van-cell {
  align-items: center;
}
:deep(.van-field) {
  padding: 8px 10px;

  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

:deep(.van-nav-bar) {
  .van-icon {
    color: white;
  }
}

// WiFi图标样式
.wifi-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;

  svg {
    width: 16px;
    height: 16px;
  }
}

// 标题生成配置样式
.custom-radio {
  margin-right: 24px;

  .radio-icon {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    .van-icon {
      font-size: 12px;
      color: #9ca3af;
      transition: color 0.3s ease;
    }

    &.active {
      border-color: #667eea;
      background: #667eea;

      .van-icon {
        color: white;
      }
    }
  }
}

.config-tips {
  margin-top: 16px;

  :deep(.van-notice-bar) {
    border-radius: 8px;
    font-size: 12px;
    line-height: 1.4;
  }
}

// 音乐设置相关样式
.music-setting-compact {
  .music-field {
    margin: 0;
    padding: 0;
    background: #f8fafc;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e2e8f0;

    :deep(.van-field__body) {
      padding: 8px 10px;
    }

    .music-label {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #374151;
      font-weight: 500;
      min-width: 90px;

      .van-icon {
        color: #667eea;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .music-selector-compact {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 40px;

    &:hover {
      background: #f9fafb;
      border-color: #667eea;
    }

    .music-name {
      flex: 1;
      color: #374151;
      font-size: 14px;

      &:empty::before {
        content: '点击选择音乐';
        color: #9ca3af;
      }
    }

    .arrow-icon {
      color: #9ca3af;
      font-size: 14px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .music-preview {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding: 12px;
    background: #f1f5f9;
    border-radius: 8px;

    .preview-button {
      flex: 1;
      border-color: #667eea;
      color: #667eea;

      .van-icon {
        margin-right: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        background: #667eea;
        color: white;
      }
    }

    .clear-button {
      flex: 1;
      background: #fee2e2;
      border-color: #fecaca;
      color: #dc2626;

      .van-icon {
        margin-right: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        background: #fecaca;
      }
    }
  }
}

// 全局图标垂直居中
:deep(.van-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

// 动画效果
.form-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }
.form-section:nth-child(4) { animation-delay: 0.4s; }

// 日期时间选择器样式
.datetime-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    background: #f8fafc;
  }

  .datetime-text {
    flex: 1;
    font-size: 14px;
    color: #333;
  }

  .arrow-icon {
    color: #9ca3af;
    font-size: 16px;
    transition: transform 0.3s ease;
  }
}
</style>

