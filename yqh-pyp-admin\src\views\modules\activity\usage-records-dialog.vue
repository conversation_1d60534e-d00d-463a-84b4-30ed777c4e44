<template>
  <div>
    <el-dialog title="使用记录" :visible.sync="visible" width="80%" :close-on-click-modal="false">

    <!-- 统计信息 -->
    <div class="summary-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ activityInfo.allCount || 0 }}</div>
              <div class="summary-label">总次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ activityInfo.useCount || 0 }}</div>
              <div class="summary-label">已使用</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-value">{{ (activityInfo.allCount || 0) - (activityInfo.useCount || 0) }}</div>
              <div class="summary-label">剩余次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="summary-value" style="color: #e6a23c;">{{ getOverallUsagePercentage() }}%</div>
              <div class="summary-label">总使用率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 记录类型切换 -->
    <div class="record-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="充值记录" name="recharge">
          <!-- 充值记录筛选器 -->
          <div class="filter-section" style="margin-bottom: 15px;">
            <el-form :inline="true" size="small">
              <el-form-item label="订单状态:">
                <el-select v-model="rechargeStatusFilter" placeholder="全部状态" clearable @change="handleRechargeStatusFilter" style="width: 150px;">
                  <el-option label="全部状态" value=""></el-option>
                  <el-option label="待支付" value="0"></el-option>
                  <el-option label="已支付" value="1"></el-option>
                  <el-option label="已取消" value="2"></el-option>
                  <el-option label="已退款" value="3"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="充值类型:">
                <el-select v-model="rechargeTypeFilter" placeholder="全部类型" clearable @change="handleRechargeTypeFilter" style="width: 150px;">
                  <el-option label="全部类型" value=""></el-option>
                  <el-option label="套餐充值" value="1"></el-option>
                  <el-option label="自定义充值" value="2"></el-option>
                  <el-option label="系统赠送" value="3"></el-option>
                  <el-option label="创建活动套餐" value="4"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="rechargeList" border v-loading="rechargeLoading" style="width: 100%;">
            <el-table-column prop="orderSn" header-align="center" align="center" label="订单号">
            </el-table-column>
            <el-table-column prop="rechargeType" header-align="center" align="center" label="充值类型">
              <template slot-scope="scope">
                <el-tag class="status-tag" :type="getRechargeTypeTag(scope.row.rechargeType).type">
                  {{ getRechargeTypeTag(scope.row.rechargeType).text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="countValue" header-align="center" align="center" label="充值次数" width="100">
              <template slot-scope="scope">
                <span class="count-display" style="color: #409EFF;">{{ scope.row.countValue || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="usedCount" header-align="center" align="center" label="已使用次数" width="120">
              <template slot-scope="scope">
                <span class="count-display" style="color: #f56c6c;">{{ scope.row.usedCount || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="center" label="剩余可用次数" width="120">
              <template slot-scope="scope">
                <span class="count-display" style="color: #67c23a;">
                  {{ (scope.row.countValue || 0) - (scope.row.usedCount || 0) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="center" label="使用率" width="100">
              <template slot-scope="scope">
                <el-progress
                  :percentage="getUsagePercentage(scope.row)"
                  :color="getUsageColor(scope.row)"
                  :stroke-width="8"
                  :show-text="false">
                </el-progress>
                <div style="margin-top: 2px; font-size: 12px; color: #666;">
                  {{ getUsagePercentage(scope.row) }}%
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="payAmount" header-align="center" align="center" label="充值金额">
              <template slot-scope="scope">
                ¥{{ scope.row.amount }}
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="activityName"
              header-align="center"
              align="center"
              label="活动名称">
              <template slot-scope="scope">
                {{ scope.row.activityName || '-' }}
              </template>
            </el-table-column> -->
            <el-table-column prop="status" header-align="center" align="center" label="状态">
              <template slot-scope="scope">
                <el-tag class="status-tag" :type="getRechargeStatusTag(scope.row.status).type">
                  {{ getRechargeStatusTag(scope.row.status).text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createOn" header-align="center" align="center" width="180" label="充值时间">
            </el-table-column>
            <el-table-column header-align="center" align="center" width="120" label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="updateRechargeStatus(scope.row)">修改状态</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 充值记录分页 -->
          <el-pagination @size-change="rechargeSizeChangeHandle" @current-change="rechargeCurrentChangeHandle"
            :current-page="rechargePageIndex" :page-sizes="[10, 20, 50, 100]" :page-size="rechargePageSize"
            :total="rechargeTotalPage" layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-tab-pane>

        <el-tab-pane label="使用记录" name="usage">
          <!-- 使用记录筛选器 -->
          <div class="filter-section" style="margin-bottom: 15px;">
            <el-form :inline="true" size="small">
              <el-form-item label="使用类型:">
                <el-select v-model="usageTypeFilter" placeholder="全部类型" clearable @change="handleUsageTypeFilter" style="width: 150px;">
                  <el-option label="全部类型" value=""></el-option>
                  <el-option label="生成文案" value="1"></el-option>
                  <el-option label="生成视频" value="2"></el-option>
                  <el-option label="转发" value="3"></el-option>
                  <el-option label="生成图文成品" value="4"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="usageList" border v-loading="usageLoading" style="width: 100%;">
            <el-table-column prop="usageType" header-align="center" align="center" label="使用类型">
              <template slot-scope="scope">
                <el-tag class="status-tag" :type="getUsageTypeTag(scope.row.usageType).type">
                  {{ getUsageTypeTag(scope.row.usageType).text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="usageCount" header-align="center" align="center" label="使用次数">
              <template slot-scope="scope">
                <span class="count-display" style="color: #f56c6c;">{{ scope.row.usageCount || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" header-align="center" align="center" label="使用描述" show-overflow-tooltip>
            </el-table-column>
            <!-- <el-table-column
              prop="relatedId"
              header-align="center"
              align="center"
              label="关联ID">
            </el-table-column> -->
            <el-table-column prop="createOn" header-align="center" align="center" width="180" label="使用时间">
            </el-table-column>
            <el-table-column header-align="center" align="center" width="120" label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="deleteUsageRecord(scope.row)" style="color: #f56c6c;">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 使用记录分页 -->
          <el-pagination @size-change="usageSizeChangeHandle" @current-change="usageCurrentChangeHandle"
            :current-page="usagePageIndex" :page-sizes="[10, 20, 50, 100]" :page-size="usagePageSize"
            :total="usageTotalPage" layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>

  <!-- 修改订单状态对话框 -->
  <el-dialog title="修改订单状态" :visible.sync="statusDialogVisible" width="500px" :close-on-click-modal="false">
    <el-form :model="statusForm" :rules="statusRules" ref="statusForm" label-width="100px">
      <el-form-item label="订单号:">
        <span>{{ statusForm.orderSn }}</span>
      </el-form-item>
      <el-form-item label="当前状态:">
        <el-tag :type="getRechargeStatusTag(statusForm.currentStatus).type">
          {{ getRechargeStatusTag(statusForm.currentStatus).text }}
        </el-tag>
      </el-form-item>
      <el-form-item label="新状态:" prop="newStatus">
        <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%;">
          <el-option label="待支付" :value="0"></el-option>
          <el-option label="已支付" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
          <el-option label="已退款" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="statusForm.remarks" type="textarea" :rows="3" placeholder="请输入修改原因或备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="statusDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmUpdateStatus" :loading="statusUpdateLoading">确定</el-button>
    </span>
  </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    activityId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      activityInfo: {},
      visible: false,
      activeTab: 'recharge',
      // 充值记录
      rechargeList: [],
      rechargePageIndex: 1,
      rechargePageSize: 10,
      rechargeTotalPage: 0,
      rechargeLoading: false,
      // 充值记录筛选
      rechargeStatusFilter: '',
      rechargeTypeFilter: '',
      // 使用记录
      usageList: [],
      usagePageIndex: 1,
      usagePageSize: 10,
      usageTotalPage: 0,
      usageLoading: false,
      // 使用记录筛选
      usageTypeFilter: '',
      // 充值类型映射
      rechargeTypeMap: {
        1: { text: '套餐充值', type: 'primary' },
        2: { text: '自定义充值', type: 'success' },
        3: { text: '系统赠送', type: 'warning' },
        4: { text: '创建活动套餐', type: 'info' }
      },
      // 充值状态映射
      rechargeStatusMap: {
        0: { text: '待支付', type: 'warning' },
        1: { text: '已支付', type: 'success' },
        2: { text: '已取消', type: 'danger' },
        3: { text: '已退款', type: 'info' },
        4: { text: '退款中', type: 'info' }
      },
      // 使用类型映射
      usageTypeMap: {
        1: { text: '生成文案', type: 'primary' },
        2: { text: '生成视频', type: 'success' },
        4: { text: '生成图文成品', type: 'success' },
        3: { text: '转发', type: 'info' }
      },
      // 状态修改对话框
      statusDialogVisible: false,
      statusUpdateLoading: false,
      statusForm: {
        id: null,
        orderSn: '',
        currentStatus: null,
        newStatus: null,
        remarks: ''
      },
      statusRules: {
        newStatus: [
          { required: true, message: '请选择新状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.getActivity();
        this.getRechargeRecords()
      })
    },

    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
        }
      });
    },
    // 切换标签页
    handleTabClick(tab) {
      if (tab.name === 'recharge') {
        this.getRechargeRecords()
      } else if (tab.name === 'usage') {
        this.getUsageRecords()
      }
    },

    // 获取充值记录
    getRechargeRecords() {
      this.rechargeLoading = true
      const params = {
        page: this.rechargePageIndex,
        limit: this.rechargePageSize,
        activityId: this.activityId
      }

      // 添加筛选条件
      if (this.rechargeStatusFilter !== '') {
        params.status = this.rechargeStatusFilter
      }
      if (this.rechargeTypeFilter !== '') {
        params.rechargeType = this.rechargeTypeFilter
      }

      this.$http({
        url: this.$http.adornUrl('/activity/rechargerecord/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.rechargeList = data.page.list
          this.rechargeTotalPage = data.page.totalCount
        } else {
          this.rechargeList = []
          this.rechargeTotalPage = 0
        }
        this.rechargeLoading = false
      }).catch(() => {
        this.rechargeLoading = false
      })
    },

    // 获取使用记录
    getUsageRecords() {
      this.usageLoading = true
      const params = {
        page: this.usagePageIndex,
        limit: this.usagePageSize,
        activityId: this.activityId
      }

      // 添加筛选条件
      if (this.usageTypeFilter !== '') {
        params.usageType = this.usageTypeFilter
      }

      this.$http({
        url: this.$http.adornUrl('/activity/activityrechargeusage/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.usageList = data.page.list
          this.usageTotalPage = data.page.totalCount
        } else {
          this.usageList = []
          this.usageTotalPage = 0
        }
        this.usageLoading = false
      }).catch(() => {
        this.usageLoading = false
      })
    },

    // 充值记录分页
    rechargeSizeChangeHandle(val) {
      this.rechargePageSize = val
      this.rechargePageIndex = 1
      this.getRechargeRecords()
    },
    rechargeCurrentChangeHandle(val) {
      this.rechargePageIndex = val
      this.getRechargeRecords()
    },

    // 使用记录分页
    usageSizeChangeHandle(val) {
      this.usagePageSize = val
      this.usagePageIndex = 1
      this.getUsageRecords()
    },
    usageCurrentChangeHandle(val) {
      this.usagePageIndex = val
      this.getUsageRecords()
    },

    // 获取充值类型标签
    getRechargeTypeTag(type) {
      return this.rechargeTypeMap[type] || { text: '未知', type: 'info' }
    },

    // 获取充值状态标签
    getRechargeStatusTag(status) {
      return this.rechargeStatusMap[status] || { text: '未知', type: 'info' }
    },

    // 获取使用类型标签
    getUsageTypeTag(type) {
      return this.usageTypeMap[type] || { text: '未知', type: 'info' }
    },

    // 处理充值状态筛选
    handleRechargeStatusFilter() {
      this.rechargePageIndex = 1
      this.getRechargeRecords()
    },

    // 处理充值类型筛选
    handleRechargeTypeFilter() {
      this.rechargePageIndex = 1
      this.getRechargeRecords()
    },

    // 处理使用类型筛选
    handleUsageTypeFilter() {
      this.usagePageIndex = 1
      this.getUsageRecords()
    },

    // 计算使用率百分比
    getUsagePercentage(row) {
      const total = row.countValue || 0
      const used = row.usedCount || 0
      if (total === 0) return 0
      return Math.round((used / total) * 100)
    },

    // 获取使用率颜色
    getUsageColor(row) {
      const percentage = this.getUsagePercentage(row)
      if (percentage >= 90) return '#f56c6c'  // 红色
      if (percentage >= 70) return '#e6a23c'  // 橙色
      if (percentage >= 50) return '#409eff'  // 蓝色
      return '#67c23a'  // 绿色
    },

    // 计算总使用率
    getOverallUsagePercentage() {
      const total = this.activityInfo.allCount || 0
      const used = this.activityInfo.useCount || 0
      if (total === 0) return 0
      return Math.round((used / total) * 100)
    },


    // 修改充值记录状态
    updateRechargeStatus(row) {
      this.statusForm = {
        id: row.id,
        orderSn: row.orderSn,
        currentStatus: row.status,
        newStatus: null,
        remarks: ''
      }
      this.statusDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.statusForm) {
          this.$refs.statusForm.clearValidate()
        }
      })
    },

    // 确认修改状态
    confirmUpdateStatus() {
      if (!this.$refs.statusForm) {
        this.$message.error('表单未准备就绪，请重试')
        return
      }
      this.$refs.statusForm.validate((valid) => {
        if (!valid) {
          return false
        }

        if (this.statusForm.currentStatus === this.statusForm.newStatus) {
          this.$message.warning('新状态与当前状态相同')
          return false
        }

        this.statusUpdateLoading = true
        this.$http({
          url: this.$http.adornUrl('/activity/rechargerecord/updateStatus'),
          method: 'post',
          data: this.$http.adornData({
            id: this.statusForm.id,
            status: this.statusForm.newStatus,
            remarks: this.statusForm.remarks
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message.success(data.msg || '状态修改成功')
            this.statusDialogVisible = false
            this.getActivity();
            this.getRechargeRecords() // 刷新列表
          } else {
            this.$message.error(data.msg || '状态修改失败')
          }
          this.statusUpdateLoading = false
        }).catch(() => {
          this.$message.error('状态修改失败')
          this.statusUpdateLoading = false
        })
      })
    },

    // 删除使用记录
    deleteUsageRecord(row) {
      this.$confirm('确定要删除这条使用记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityrechargeusage/delete'),
          method: 'post',
          data: this.$http.adornData([row.id])
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message.success('删除成功')
            this.getUsageRecords() // 刷新列表
          } else {
            this.$message.error(data.msg || '删除失败')
          }
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    }
  }
}
</script>

<style scoped>
.summary-info {
  margin-bottom: 20px;
}

.summary-card {
  text-align: center;
}

.summary-item {
  padding: 10px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.record-tabs {
  margin-top: 20px;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

.filter-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.filter-section .el-form-item {
  margin-bottom: 0;
}

/* 表格样式优化 */
.el-table .cell {
  padding: 0 8px;
}

/* 使用率进度条样式 */
.el-progress-bar__outer {
  border-radius: 4px;
}

.el-progress-bar__inner {
  border-radius: 4px;
}

/* 数字显示样式 */
.count-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

/* 状态标签样式 */
.status-tag {
  font-weight: bold;
}
</style>
