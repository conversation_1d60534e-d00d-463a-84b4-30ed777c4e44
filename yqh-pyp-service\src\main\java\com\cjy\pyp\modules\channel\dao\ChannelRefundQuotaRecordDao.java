package com.cjy.pyp.modules.channel.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.channel.entity.ChannelRefundQuotaRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 渠道退款名额使用记录DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
@Mapper
public interface ChannelRefundQuotaRecordDao extends BaseMapper<ChannelRefundQuotaRecordEntity> {
    
    /**
     * 分页查询退款名额使用记录
     */
    List<ChannelRefundQuotaRecordEntity> queryPage(Map<String, Object> params);

    /**
     * 查询退款名额使用记录总数
     */
    int queryPageCount(Map<String, Object> params);
    
    /**
     * 获取渠道当前已分配的退款权限数量
     */
    Integer getChannelAssignedQuotaCount(@Param("channelId") Long channelId);
    
    /**
     * 获取渠道内订单的退款权限排序
     */
    Integer getOrderQuotaSequence(@Param("channelId") Long channelId, @Param("orderId") Long orderId);
    
    /**
     * 获取渠道内按时间排序的已支付订单列表（用于权限分配）
     */
    List<Map<String, Object>> getChannelPaidOrdersForQuotaAssignment(@Param("channelId") Long channelId);
    
    /**
     * 批量插入退款名额使用记录
     */
    int batchInsert(@Param("records") List<ChannelRefundQuotaRecordEntity> records);
    
    /**
     * 删除渠道的所有退款名额记录（重新分配时使用）
     */
    int deleteByChannelId(@Param("channelId") Long channelId);
    
    /**
     * 获取渠道退款名额统计信息
     */
    Map<String, Object> getChannelRefundQuotaStats(@Param("channelId") Long channelId);
    
    /**
     * 查询渠道内具有退款权限的订单列表
     */
    List<Map<String, Object>> getChannelEligibleOrders(@Param("channelId") Long channelId);
}
