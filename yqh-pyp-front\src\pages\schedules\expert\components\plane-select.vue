<template>
    <div class="plane-select">
        <!-- <van-nav-bar title="选择航班" left-arrow @click-left="goBack" /> -->

        <!-- 日期导航 -->
        <div class="date-nav">
            <van-button plain size="small" @click="switchDay(-1)" :disabled="isLoading">前一天</van-button>
            <div class="current-date">{{ inDate }}</div>
            <van-button plain size="small" @click="switchDay(1)" :disabled="isLoading">后一天</van-button>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-overlay">
            <van-loading type="spinner" color="#1989fa" />
            <div class="loading-text">航班信息加载中...</div>
        </div>

        <!-- 航班列表 -->
        <template v-if="!isLoading && !showCabins">
            <div v-if="flights.length > 0" class="flight-list">
                <div v-for="flight in flights" :key="flight.flightNo" class="flight-item" @click="showFlightCabins(flight)">
                    <div class="time-row">
                        <div class="time-info">
                            <span class="departure-time">{{ flight.fromDateTime }}</span>
                            <span class="airport-name">{{ flight.fromAirportName }}</span>
                        </div>
                        <div class="flight-duration">
                            <div class="duration-line"></div>
                            <span class="duration-text">{{ flight.flyDuration }}时</span>
                        </div>
                        <div class="time-info text-right">
                            <span class="arrival-time">{{ flight.toDateTime }}</span>
                            <span class="airport-name">{{ flight.toAirportName }}</span>
                        </div>
                    </div>

                    <div class="flight-detail">
                        <div class="flight-number">
                            <span>{{ flight.flightNo }}</span>
                            <span class="company-name">{{ flight.airlineCompany }}</span>
                        </div>
                        <div class="price-preview">
                            <!-- <span class="price-from">¥{{ getLowestPrice(flight) }}</span> -->
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <van-empty v-else description="暂无航班信息" />
        </template>

        <!-- 舱位选择 -->
        <template v-if="!isLoading && showCabins">
            <div class="cabin-header">
                <van-icon name="arrow-left" @click="backToFlightList" class="back-icon" />
                <div class="selected-flight-info">
                    <div class="time-row">
                        <div class="time-info">
                            <span class="departure-time">{{ currentFlight.fromDateTime }}</span>
                            <span class="airport-name">{{ currentFlight.fromAirportName }}</span>
                        </div>
                        <div class="flight-duration">
                            <div class="duration-line"></div>
                            <span class="duration-text">{{ currentFlight.flyDuration }}时</span>
                        </div>
                        <div class="time-info text-right">
                            <span class="arrival-time">{{ currentFlight.toDateTime }}</span>
                            <span class="airport-name">{{ currentFlight.toAirportName }}</span>
                        </div>
                    </div>
                    <div class="flight-number">
                        <span>{{ currentFlight.flightNo }}</span>
                        <span class="company-name">{{ currentFlight.airlineCompany }}</span>
                    </div>
                </div>
            </div>
<div class="cabin-list">
    <div v-for="item in currentFlight.cabins" :key="item.cabinBookPara" 
        class="cabin-item" 
        :class="{ 'selected-item': selectedFlightItem.cabinBookPara === item.cabinBookPara }"
        @click="selectFlightItem(item)">
        <div class="cabin-info">
            <div class="cabin-left">
                <span class="cabin-name">{{ item.cabinName }}</span>
                <span class="cabin-desc" v-if="item.cabinDesc">{{ item.cabinDesc }}</span>
            </div>
            <div class="cabin-right">
                <!-- <span class="original-price">原价：¥{{ item.cabinPrice.adultFarePrice }}</span>
                <span class="sale-price">售价：¥{{ item.cabinPrice.adultSalePrice }}</span> -->
            </div>
        </div>
    </div>
</div>
        </template>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
            <van-button plain block @click="goBack">取消</van-button>
            <van-button type="primary" block @click="confirmSelection" 
                :disabled="!selectedFlightItem.cabinBookPara">确定</van-button>
        </div>
    </div>
</template>

<script>
import date from "@/js/date.js";

export default {
    name: 'PlaneSelect',
    data() {
        return {
            isLoading: false,
            inDate: '',
            startCityCode: '',
            endCityCode: '',
            flights: [],
            showCabins: false,
            currentFlight: {},
            selectedFlightItem: {},
            tripInfo: {}
        };
    },
    mounted() {
        // 获取路由参数
        this.tripInfo = JSON.parse(this.$route.query.tripInfo || '{}');
        this.inDate = this.tripInfo.inDate ? date.formatDate.format(new Date(this.tripInfo.inDate), "yyyy/MM/dd") : date.formatDate.format(new Date(), "yyyy/MM/dd");
        this.startCityCode = this.tripInfo.startCityCode || '';
        this.endCityCode = this.tripInfo.endCityCode || '';

        // 加载航班信息
        this.loadFlights();
    },
    methods: {
        goBack() {
            if (this.showCabins) {
                this.backToFlightList();
            } else {
                this.$router.back();
            }
        },

        // 切换日期
        switchDay(offset) {
            const currentDate = new Date(this.inDate);
            currentDate.setDate(currentDate.getDate() + offset);
            this.inDate = date.formatDate.format(currentDate, "yyyy/MM/dd");
            this.loadFlights();
            this.showCabins = false;
        },

        // 加载航班信息
        loadFlights() {
            if (!this.startCityCode || !this.endCityCode) {
                vant.Toast('请选择出发地和目的地');
                return;
            }

            this.isLoading = true;
            this.flights = [];

            this.$fly.get('/pyp/panhe/searchPlane', {
                fromCity: this.startCityCode,
                toCity: this.endCityCode,
                fromDate: this.inDate
            }).then(res => {
                this.isLoading = false;
                if (res && res.code === 200) {
                    this.flights = res.result || [];
                } else {
                    vant.Toast(res.msg || '获取航班信息失败');
                }
            }).catch(() => {
                this.isLoading = false;
                vant.Toast('网络错误，请重试');
            });
        },

        // 获取最低价格
        getLowestPrice(flight) {
            if (!flight.cabins || flight.cabins.length === 0) {
                return '暂无';
            }
            
            let lowestPrice = Number.MAX_VALUE;
            flight.cabins.forEach(cabin => {
                if (cabin.cabinPrice && cabin.cabinPrice.adultSalePrice < lowestPrice) {
                    lowestPrice = cabin.cabinPrice.adultSalePrice;
                }
            });
            
            return lowestPrice === Number.MAX_VALUE ? '暂无' : lowestPrice;
        },

        // 显示航班舱位
        showFlightCabins(flight) {
            this.currentFlight = flight;
            this.showCabins = true;
            this.selectedFlightItem = {};
        },

        // 返回航班列表
        backToFlightList() {
            this.showCabins = false;
            this.currentFlight = {};
            this.selectedFlightItem = {};
        },

        // 选择舱位
        selectFlightItem(item) {
            this.selectedFlightItem = item;
        },

        // 确认选择
        confirmSelection() {
            if (!this.selectedFlightItem.cabinBookPara) {
                vant.Toast('请选择舱位');
                return;
            }

            // 构建行程信息
            const tripData = {
                ...this.tripInfo,
                inNumber: this.currentFlight.flightNo,
                startCityCode: this.currentFlight.fromAirportCode,
                endCityCode: this.currentFlight.toAirportCode,
                inStartPlace: this.currentFlight.fromAirportName,
                inStartTerminal: this.currentFlight.fromTerminal,
                inEndTerminal: this.currentFlight.toTerminal,
                inEndPlace: this.currentFlight.toAirportName,
                inStartDate: (this.currentFlight.fromDateTime + ':00').replaceAll("-", "/"),
                inEndDate:  (this.currentFlight.toDateTime + ':00').replaceAll("-", "/"),
                cabinBookPara: this.selectedFlightItem.cabinBookPara,
                cabinCode: this.selectedFlightItem.cabinCode,
                price: this.selectedFlightItem.cabinPrice.adultSalePrice,
                isBuy: 0,
                type: 0, // 单程
                typeName: '单程'
            };

            // 保存行程信息
            this.saveTrip(tripData);
        },

        // 保存行程信息
        saveTrip(tripData) {
            this.isLoading = true;
            const url = tripData.id ? "/pyp/web/activity/activityguest/updateTrip" : "/pyp/web/activity/activityguest/saveTrip";

            this.$fly.post(url, tripData).then(res => {
                this.isLoading = false;
                if (res && res.code === 200) {
                    vant.Toast('保存成功');
                    // 返回行程列表页面
                    this.$router.replace({
                        path: '/schedules/expertTrip',
                        query: { detailId: tripData.activityGuestId }
                    });
                } else {
                    vant.Toast(res.msg || '保存失败');
                }
            }).catch(() => {
                this.isLoading = false;
                vant.Toast('网络错误，请重试');
            });
        }
    }
};
</script>

<style lang="less" scoped>
.plane-select {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.date-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #fff;
    margin-bottom: 10px;
}

.current-date {
    font-size: 16px;
    font-weight: bold;
}

.loading-overlay {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-text {
    margin-top: 10px;
    color: #999;
}

.flight-list {
    padding: 0 10px;
    flex: 1;
    overflow-y: auto;
}

.flight-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.time-info {
    display: flex;
    flex-direction: column;
    width: 30%;
}

.text-right {
    text-align: right;
}

.departure-time, .arrival-time {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.airport-name {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.flight-duration {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 40%;
}

.duration-line {
    width: 100%;
    height: 1px;
    background-color: #ddd;
    position: relative;
}

.duration-line:before, .duration-line:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #ddd;
    top: -2.5px;
}

.duration-line:before {
    left: 0;
}

.duration-line:after {
    right: 0;
}

.duration-text {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.flight-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f5f5f5;
    padding-top: 10px;
}

.flight-number {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.company-name {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
}

.price-preview {
    display: flex;
    align-items: center;
}

.price-from {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
    margin-right: 5px;
}

/* 舱位选择样式 */
.cabin-header {
    background-color: #fff;
    padding: 15px;
    position: relative;
    margin-bottom: 10px;
}

.back-icon {
    position: absolute;
    left: 15px;
    top: 15px;
    font-size: 20px;
    color: #333;
}

.selected-flight-info {
    padding-left: 30px;
}

.cabin-list {
    padding: 0 10px;
    flex: 1;
    overflow-y: auto;
}
.cabin-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.cabin-item.selected-item {
    background-color: #e6f7ff;
    border: 1px solid #1989fa;
}

.cabin-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cabin-left {
    display: flex;
    flex-direction: column;
}

.cabin-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.cabin-desc {
    font-size: 12px;
    color: #999;
}

.cabin-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    margin-bottom: 2px;
}

.sale-price {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
}

.bottom-buttons {
    display: flex;
    padding: 10px;
    gap: 10px;
    background-color: #fff;
    border-top: 1px solid #eee;
    position: sticky;
    bottom: 0;
    z-index: 10;
}
</style>
</style>