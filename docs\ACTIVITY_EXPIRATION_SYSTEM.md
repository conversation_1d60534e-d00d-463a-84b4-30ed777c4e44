# 活动过期和续费系统

## 概述

活动过期和续费系统为平台提供了完整的活动生命周期管理功能，包括过期时间设置、过期状态监控、续费套餐管理和自动化提醒等功能。

## 系统架构

### 数据库设计

#### 1. tb_activity 表新增字段
- `expiration_time` (datetime): 活动过期时间，NULL表示永不过期
- `is_expired` (tinyint): 是否已过期，0-未过期，1-已过期

#### 2. activity_recharge_package 表新增字段
- `renewal_days` (int): 续费天数，仅续费套餐使用

#### 3. activity_recharge_record 表新增字段
- `renewal_days` (int): 续费天数
- `original_expiration_time` (datetime): 续费前的原过期时间
- `new_expiration_time` (datetime): 续费后的新过期时间

#### 4. 新增套餐类型
- `package_type = 3`: 活动续费套餐

#### 5. 新增通知类型
- `type = 7`: 活动即将过期提醒
- `type = 8`: 活动已过期通知
- `type = 9`: 活动续费成功通知

### 后端服务

#### 核心服务类
- `ActivityExpirationService`: 活动过期管理核心服务
- `ActivityExpirationServiceImpl`: 服务实现类
- `ActivityExpirationNotifyService`: 活动过期通知服务
- `ActivityExpirationNotifyServiceImpl`: 通知服务实现类
- `ActivityExpirationTask`: 定时任务处理类

#### 主要功能
1. **过期状态检查**: 检查活动是否过期或即将过期
2. **续费订单管理**: 创建和处理续费订单
3. **过期时间管理**: 设置和延长活动过期时间
4. **批量状态更新**: 定时批量更新活动过期状态
5. **通知提醒**: 通过 activity_notify 表发送过期提醒和通知
6. **通知管理**: 支持未读通知统计、批量标记已读等功能

### API 接口

#### 管理端接口 (`/pyp/activity/expiration/`)
- `GET /status/{activityId}`: 获取活动过期状态
- `POST /setExpirationTime`: 设置活动过期时间
- `POST /extend`: 延长活动有效期
- `POST /updateStatus/{activityId}`: 更新活动过期状态
- `POST /batchUpdate`: 批量更新所有活动过期状态
- `GET /expiringSoon`: 获取即将过期的活动列表
- `GET /expired`: 获取已过期的活动列表
- `POST /sendReminder/{activityId}`: 发送过期提醒
- `POST /sendNotification/{activityId}`: 发送过期通知

#### 前端移动端接口 (`/pyp/web/activity/expiration/`)
- `GET /status/{activityId}`: 获取活动过期状态
- `GET /userActivitiesStatus`: 获取用户所有活动的过期状态
- `GET /checkExpired/{activityId}`: 检查活动是否过期
- `GET /renewalPackages`: 获取续费套餐列表
- `POST /createRenewalOrder`: 创建续费订单
- `POST /paymentSuccess`: 处理续费支付成功回调

### 前端界面

#### 管理端功能
1. **活动列表页面**: 显示活动过期状态和剩余时间
2. **过期管理弹窗**: 设置过期时间和延长有效期
3. **续费套餐管理**: 创建和管理续费套餐
4. **过期监控**: 查看即将过期和已过期的活动

#### 移动端功能
1. **过期状态显示**: 在活动选择器下方显示当前活动的过期状态
2. **续费提醒**: 对过期或即将过期的活动显示续费按钮
3. **续费弹窗**: 选择续费套餐并创建订单
4. **支付流程**: 跳转到支付页面完成续费
5. **通知中心**: 显示过期相关通知，支持未读数量提示
6. **通知管理**: 支持查看、标记已读、分类筛选等功能

## 使用指南

### 管理员操作

#### 1. 设置活动过期时间
```javascript
// 在活动编辑页面设置过期时间
{
  "expirationTime": "2025-12-31 23:59:59" // 或 null 表示永不过期
}
```

#### 2. 创建续费套餐
```javascript
{
  "name": "活动续费30天",
  "description": "为活动延长30天使用期限",
  "packageType": 3,
  "renewalDays": 30,
  "price": 29.90,
  "originalPrice": 39.90,
  "status": 1
}
```

#### 3. 批量更新过期状态
定时任务每小时自动执行，也可手动触发：
```bash
POST /pyp/activity/expiration/batchUpdate
```

### 用户操作

#### 1. 查看活动过期状态
用户在首页可以看到当前活动的过期状态：
- 绿色：正常使用中
- 黄色：即将过期（7天内）
- 红色：已过期

#### 2. 续费操作
1. 点击"续费"按钮打开续费弹窗
2. 选择合适的续费套餐
3. 确认订单信息
4. 跳转到支付页面
5. 支付成功后活动有效期自动延长

## 定时任务

### 过期状态检查
- **频率**: 每小时执行一次
- **功能**: 批量更新所有活动的过期状态
- **Cron表达式**: `0 0 * * * ?`

### 过期提醒
- **频率**: 每天上午9点执行
- **功能**: 发送7天内即将过期的活动提醒
- **Cron表达式**: `0 0 9 * * ?`

### 过期通知
- **频率**: 每天上午10点执行
- **功能**: 发送已过期活动的通知
- **Cron表达式**: `0 0 10 * * ?`

## 技术实现细节

### 过期时间计算
```java
// 延长活动有效期
Date currentExpiration = activity.getExpirationTime();
Date newExpiration;

if (currentExpiration == null || new Date().after(currentExpiration)) {
    // 如果没有过期时间或已过期，从现在开始计算
    newExpiration = new Date(System.currentTimeMillis() + days * 24L * 60 * 60 * 1000);
} else {
    // 从当前过期时间开始计算
    newExpiration = new Date(currentExpiration.getTime() + days * 24L * 60 * 60 * 1000);
}
```

### 状态判断逻辑
```java
// 判断是否即将过期（7天内）
public boolean isExpiringSoon(Long activityId) {
    ActivityEntity activity = activityService.getById(activityId);
    if (activity == null || activity.getExpirationTime() == null) {
        return false;
    }
    
    Date now = new Date();
    Date expirationTime = activity.getExpirationTime();
    long diffInMillis = expirationTime.getTime() - now.getTime();
    long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);
    
    return diffInDays <= 7 && diffInDays >= 0;
}
```

### 防重复提交
使用Redis实现防重复提交机制：
```java
// 原子性操作验证和删除令牌
Long result = stringRedisTemplate.execute(
    new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
    Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + userId), 
    repeatToken
);
if (result == 0L) {
    throw new RRException("不能重复提交");
}
```

## 测试

### 单元测试
- `ActivityExpirationServiceTest`: 服务层单元测试
- `ActivityExpirationControllerTest`: 控制器单元测试
- `WebActivityExpirationControllerTest`: Web控制器单元测试

### 集成测试
- `ActivityExpirationIntegrationTest`: 完整流程集成测试

### 测试覆盖
- 过期状态检查和更新
- 续费订单创建和支付
- 过期时间设置和延长
- 批量状态更新
- API接口功能

## 部署说明

### 数据库迁移
执行数据库迁移脚本：
```sql
-- 执行 V1.0.5__add_activity_expiration_system.sql
```

### 配置检查
确保以下配置正确：
1. 定时任务已启用
2. Redis连接正常
3. 支付回调地址配置

### 监控指标
建议监控以下指标：
- 即将过期活动数量
- 已过期活动数量
- 续费订单成功率
- 定时任务执行状态

## 常见问题

### Q: 如何处理已过期的活动？
A: 已过期的活动会自动标记为过期状态，用户可以通过续费恢复使用。系统不会自动删除过期活动。

### Q: 续费后的过期时间如何计算？
A: 如果活动未过期，从当前过期时间开始延长；如果已过期，从当前时间开始计算新的过期时间。

### Q: 定时任务失败怎么办？
A: 定时任务有完整的日志记录，可以通过日志查看失败原因。也可以手动触发批量更新接口。

### Q: 如何自定义过期提醒？
A: 可以修改 `ActivityExpirationTask` 中的提醒逻辑，或者实现自定义的通知服务。
