<template>
  <el-dialog :title="dataForm.type == 0 ? '收票' : '开票'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="dataForm.type == 0 ? '收票金额' : '开票金额'" prop="price">
            <el-input v-model="dataForm.price" :placeholder="dataForm.type == 0 ? '收票金额' : '开票金额'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="dataForm.type == 0 ? '收票时间' : '开票时间'" prop="payTime">
            <el-date-picker style="width: 100%" v-model="dataForm.payTime" type="datetime"
              :placeholder="dataForm.type == 0 ? '收票时间' : '开票时间'" value-format="yyyy/MM/dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="dataForm.type == 0 ? '收票类型' : '开票类型'" prop="invoiceType">
            <el-select v-model="dataForm.invoiceType" :placeholder="dataForm.type == 0 ? '收票类型' : '开票类型'" filterable>
              <el-option v-for="item in contractInvoiceType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="dataForm.type == 0 ? '发票类型' : '发票类型'" prop="invoiceBillType">
            <el-select v-model="dataForm.invoiceBillType" :placeholder="dataForm.type == 0 ? '发票类型' : '发票类型'" filterable>
              <el-option v-for="item in contractInvoiceBillType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'发票号码'" prop="number">
            <el-input v-model="dataForm.number" :placeholder="'发票号码'"></el-input>
          </el-form-item>
          
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'备注'" prop="remarks">
            <el-input v-model="dataForm.remarks" :placeholder="'备注'"></el-input>
          </el-form-item>
          
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  contractInvoiceType,
  contractInvoiceBillType,
} from "@/data/price";
import { format } from "@/utils/date.js";
import { yesOrNo } from "@/data/common";
export default {
  data() {
    return {
  contractInvoiceType,
  contractInvoiceBillType,
      yesOrNo,
      companyBank: [],
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        price: 0,
        type: '',
        priceBankId: '',
        payTime: '',
        number: '',
        invoiceType: 0,
        invoiceBillType: 0,
        remarks: '',
      },
      dataRule: {
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        priceBankId: [
          { required: true, message: '收票账户id不能为空', trigger: 'blur' }
        ],
        payTime: [
          { required: true, message: '收票时间不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitysettle/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.price = data.activitySettle.invoice - data.activitySettle.arriveInvoice
              this.dataForm.type = data.activitySettle.type
              this.dataForm.appid = data.activitySettle.appid
              this.dataForm.invoiceType = data.activitySettle.invoiceType
              this.dataForm.invoiceBillType = data.activitySettle.invoiceBillType
              this.dataForm.number = data.activitySettle.number
              this.dataForm.remarks = data.activitySettle.remarks
            }
          })
        } else {
          this.dataForm.appid = this.$cookie.get("appid");
        }
      })
      this.findPriceBank();
      this.getToken();
      this.dataForm.payTime = format(new Date(), "yyyy/MM/dd hh:mm:ss");
    },
    findPriceBank() {
      this.$http({
        url: this.$http.adornUrl('/price/pricebank/findAll'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.priceBank = data.result
          if (this.priceBank.length > 0) {
            this.dataForm.priceBankId = this.priceBank[0].id
          }
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activitysettle/invoice`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'price': this.dataForm.price,
              'invoiceType': this.dataForm.invoiceType,
              'invoiceBillType': this.dataForm.invoiceBillType,
              'number': this.dataForm.number,
              
              'payTime': this.dataForm.payTime,
              'remarks': this.dataForm.remarks,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
