-- 业务员佣金抽成系统数据库变更

-- 1. 创建业务员佣金配置表
CREATE TABLE `salesman_commission_config` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `salesman_id` bigint(20) NOT NULL COMMENT '业务员ID',
  `commission_type` tinyint(4) NOT NULL COMMENT '佣金类型：1-创建活动佣金，2-充值次数佣金，3-用户转发佣金',
  `calculation_type` tinyint(4) NOT NULL COMMENT '计算方式：1-固定金额，2-百分比',
  `commission_value` decimal(10,4) NOT NULL COMMENT '佣金值（固定金额或百分比）',
  `min_amount` decimal(10,2) DEFAULT NULL COMMENT '最小佣金金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大佣金金额',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `effective_date` datetime DEFAULT NULL COMMENT '生效时间',
  `expiry_date` datetime DEFAULT NULL COMMENT '失效时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_salesman_id` (`salesman_id`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_status` (`status`),
  KEY `idx_appid` (`appid`),
  KEY `idx_effective_date` (`effective_date`),
  UNIQUE KEY `uk_salesman_commission_type` (`salesman_id`, `commission_type`, `appid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务员佣金配置表';

-- 2. 创建业务员佣金记录表
CREATE TABLE `salesman_commission_record` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `salesman_id` bigint(20) NOT NULL COMMENT '业务员ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动ID',
  `commission_type` tinyint(4) NOT NULL COMMENT '佣金类型：1-创建活动佣金，2-充值次数佣金，3-用户转发佣金',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型标识',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID（充值记录ID或使用记录ID）',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额（仅类型1和2有值）',
  `commission_rate` decimal(10,4) DEFAULT NULL COMMENT '佣金比例',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `calculation_type` tinyint(4) NOT NULL COMMENT '计算方式：1-固定金额，2-百分比',
  `settlement_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算，1-已结算，2-已取消',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settlement_batch_no` varchar(100) DEFAULT NULL COMMENT '结算批次号',
  `settlement_remarks` varchar(500) DEFAULT NULL COMMENT '结算备注',
  `business_time` datetime NOT NULL COMMENT '业务发生时间',
  `description` varchar(500) DEFAULT NULL COMMENT '佣金描述',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_salesman_id` (`salesman_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_settlement_batch_no` (`settlement_batch_no`),
  KEY `idx_business_time` (`business_time`),
  KEY `idx_appid` (`appid`),
  KEY `idx_create_on` (`create_on`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务员佣金记录表';

-- 3. 创建佣金结算批次表
CREATE TABLE `salesman_commission_settlement` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `batch_no` varchar(100) NOT NULL COMMENT '结算批次号',
  `settlement_date` date NOT NULL COMMENT '结算日期',
  `salesman_count` int(11) NOT NULL DEFAULT '0' COMMENT '结算业务员数量',
  `record_count` int(11) NOT NULL DEFAULT '0' COMMENT '结算记录数量',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '结算总金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已取消',
  `settlement_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '结算类型：1-手动结算，2-自动结算',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算完成时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '结算备注',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_settlement_date` (`settlement_date`),
  KEY `idx_status` (`status`),
  KEY `idx_appid` (`appid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务员佣金结算批次表';

-- 4. 插入默认的佣金配置数据（示例）
INSERT INTO `salesman_commission_config` (
    `id`, `salesman_id`, `commission_type`, `calculation_type`, `commission_value`, 
    `min_amount`, `max_amount`, `status`, `effective_date`, `appid`, 
    `create_on`, `create_by`
) VALUES 
-- 示例：为业务员ID为1的设置默认佣金配置
(1, 1, 1, 2, 0.1000, 1.00, 100.00, 1, NOW(), 'default', NOW(), 1),  -- 创建活动佣金：10%
(2, 1, 2, 2, 0.0500, 0.50, 50.00, 1, NOW(), 'default', NOW(), 1),   -- 充值次数佣金：5%
(3, 1, 3, 1, 1.0000, NULL, NULL, 1, NOW(), 'default', NOW(), 1);    -- 用户转发佣金：固定1元

-- 5. 添加索引优化查询性能
ALTER TABLE `salesman_commission_record` ADD INDEX `idx_salesman_settlement` (`salesman_id`, `settlement_status`);
ALTER TABLE `salesman_commission_record` ADD INDEX `idx_business_settlement` (`business_type`, `business_id`, `settlement_status`);

-- 6. 为现有的salesman_order表添加佣金记录关联字段（如果需要）
ALTER TABLE `salesman_order` ADD COLUMN `commission_record_id` bigint(20) DEFAULT NULL COMMENT '佣金记录ID';
ALTER TABLE `salesman_order` ADD INDEX `idx_commission_record_id` (`commission_record_id`);
