package com.cjy.pyp.modules.activity.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI模型配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@Mapper
public interface AiModelConfigDao extends BaseMapper<AiModelConfigEntity> {
    
    /**
     * 根据模型编码查询配置
     */
    AiModelConfigEntity selectByModelCode(@Param("modelCode") String modelCode);
    
    /**
     * 查询启用的模型配置列表
     */
    List<AiModelConfigEntity> selectEnabledModels();
    
    /**
     * 根据服务提供商查询模型列表
     */
    List<AiModelConfigEntity> selectByProvider(@Param("provider") String provider);
    
    /**
     * 获取默认模型配置
     */
    AiModelConfigEntity selectDefaultModel();
    
    /**
     * 更新默认模型（先清除所有默认标记，再设置新的默认模型）
     */
    void updateDefaultModel(@Param("modelCode") String modelCode);
    
    /**
     * 清除所有默认标记
     */
    void clearDefaultFlags();
}
