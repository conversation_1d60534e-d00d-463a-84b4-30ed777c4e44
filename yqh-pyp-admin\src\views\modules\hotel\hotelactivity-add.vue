<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    @closed="closeDialog"
    :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="酒店名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="name" header-align="center" align="center" label="酒店名称">
      </el-table-column>
      <el-table-column prop="star" header-align="center" align="center" label="星级" width="150px">
          <div slot-scope="scope">
            <el-rate disabled v-model="scope.row.star" :colors="['#99A9BF', '#F7BA2A', '#FF9900']">
            </el-rate>
          </div>
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="provinceName" header-align="center" align="center" label="省份">
      </el-table-column>
      <el-table-column prop="cityName" header-align="center" align="center" label="城市">
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" label="详细地址">
      </el-table-column>
      <el-table-column prop="imageUrl" header-align="center" align="center" label="图片">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.imageUrl)" :src="scope.row.imageUrl" />
          <a :href="scope.row.imageUrl" target="_blank" v-else>{{scope.row.imageUrl}}</a>
        </div>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作"  v-if="isAuth('hotel:hotelactivity:save')">
        <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.isSelect" size="small" disabled >已选择</el-button>
            <el-button type="text"  v-else size="small" @click="select(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭页面</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          name: '',
          appid: '',
          activityId: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
      }
    },
    methods: {
      init (activityId) {
        this.dataForm.activityId = activityId
        this.visible = true
        this.getDataList();
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/hotel/hotelactivity/selectList'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'activityId': this.dataForm.activityId,
            'appid': this.$cookie.get("appid"),
            'name': this.dataForm.name
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 选择酒店
      select (v) {
        this.$confirm(`确定选择酒店?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/hotel/hotelactivity/save`),
            method: 'post',
            data: this.$http.adornData({
              'activityId': this.dataForm.activityId,
              'hotelId': v.id,
              'address': v.address,
              'mobile': v.mobile,
              'status': 0,
              'orderBy': 0,
            })
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
              })
              this.getDataList();
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      closeDialog() {
        this.$emit('refreshDataList')
      },
      isImageUrl(url) {
        return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
      }
    }
  }
</script>
