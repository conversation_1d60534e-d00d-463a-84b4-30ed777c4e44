<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="" prop="ip">
      <el-input v-model="dataForm.ip" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="activation">
      <el-input v-model="dataForm.activation" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="text">
      <el-input v-model="dataForm.text" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="状态0-不可用，1-可用" prop="status">
      <el-input v-model="dataForm.status" placeholder="状态0-不可用，1-可用"></el-input>
    </el-form-item>
    <el-form-item label="" prop="createdTime">
      <el-input v-model="dataForm.createdTime" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="updatedTime">
      <el-input v-model="dataForm.updatedTime" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="expireTime">
      <el-input v-model="dataForm.expireTime" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="type">
      <el-input v-model="dataForm.type" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="count">
      <el-input v-model="dataForm.count" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder=""></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          ip: '',
          activation: '',
          mobile: '',
          text: '',
          status: '',
          createdTime: '',
          updatedTime: '',
          expireTime: '',
          type: '',
          count: '',
          activityId: ''
        },
        dataRule: {
          ip: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          activation: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          mobile: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          text: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态0-不可用，1-可用不能为空', trigger: 'blur' }
          ],
          createdTime: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          updatedTime: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          expireTime: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          count: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sms/sms/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.ip = data.sms.ip
                this.dataForm.activation = data.sms.activation
                this.dataForm.mobile = data.sms.mobile
                this.dataForm.text = data.sms.text
                this.dataForm.status = data.sms.status
                this.dataForm.createdTime = data.sms.createdTime
                this.dataForm.updatedTime = data.sms.updatedTime
                this.dataForm.expireTime = data.sms.expireTime
                this.dataForm.type = data.sms.type
                this.dataForm.count = data.sms.count
                this.dataForm.activityId = data.sms.activityId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/sms/sms/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'ip': this.dataForm.ip,
                'activation': this.dataForm.activation,
                'mobile': this.dataForm.mobile,
                'text': this.dataForm.text,
                'status': this.dataForm.status,
                'createdTime': this.dataForm.createdTime,
                'updatedTime': this.dataForm.updatedTime,
                'expireTime': this.dataForm.expireTime,
                'type': this.dataForm.type,
                'count': this.dataForm.count,
                'activityId': this.dataForm.activityId
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
