<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="联系我们" name="contact">
      <tinymce-editor
        ref="editor"
        v-model="dataForm.paramValue"
      ></tinymce-editor>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </el-tab-pane>
    <el-tab-pane label="公司介绍" name="company">
      <tinymce-editor
        ref="editor"
        v-model="dataForm.paramValue"
      ></tinymce-editor>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </el-tab-pane>
    <el-tab-pane label="帮助中心" name="help">
      <tinymce-editor
        ref="editor"
        v-model="dataForm.paramValue"
      ></tinymce-editor>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </el-tab-pane>
    <el-tab-pane label="云会易" name="yunhuiyi" v-if="appid == 'wx6a7f38e0347e6669'">
      <tinymce-editor
        ref="editor"
        v-model="dataForm.paramValue"
      ></tinymce-editor>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </el-tab-pane>
  </el-tabs>
</template>
  <script>
export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
  data() {
    return {
          appid: '',
      activeName: "contact",
      dataForm: {
        id: "",
        paramKey: "",
        paramValue: "",
      },
    };
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.getReuslt();
  },
  methods: {
    handleClick(tab, event) {
      //   console.log(tab.name);
      this.activeName = tab.name;
      this.getReuslt();
    },
    getReuslt() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: this.activeName,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm = data.result;
        }
      });
    },
    dataFormSubmit() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/saveConfig`),
        method: "post",
        data: this.$http.adornData(this.dataForm),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: "操作成功",
            type: "success",
            duration: 1500,
            onClose: () => {
              this.getReuslt();
            },
          });
        } else {
          this.$message.error(data.msg);
        }
      });
    },
  },
};
</script>