<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议酒店房型id" prop="hotelActivityRoomId">
      <el-input v-model="dataForm.hotelActivityRoomId" placeholder="会议酒店房型id"></el-input>
    </el-form-item>
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="酒店id" prop="hotelId">
      <el-input v-model="dataForm.hotelId" placeholder="酒店id"></el-input>
    </el-form-item>
    <el-form-item label="会议酒店id" prop="hotelActivityId">
      <el-input v-model="dataForm.hotelActivityId" placeholder="会议酒店id"></el-input>
    </el-form-item>
    <el-form-item label="房号" prop="number">
      <el-input v-model="dataForm.number" placeholder="房号"></el-input>
    </el-form-item>
    <el-form-item label="是否分配" prop="isAssign">
      <el-input v-model="dataForm.isAssign" placeholder="是否分配"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          hotelActivityRoomId: '',
          activityId: '',
          hotelId: '',
          hotelActivityId: '',
          number: '',
          isAssign: ''
        },
        dataRule: {
          hotelActivityRoomId: [
            { required: true, message: '会议酒店房型id不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          hotelId: [
            { required: true, message: '酒店id不能为空', trigger: 'blur' }
          ],
          hotelActivityId: [
            { required: true, message: '会议酒店id不能为空', trigger: 'blur' }
          ],
          number: [
            { required: true, message: '房号不能为空', trigger: 'blur' }
          ],
          isAssign: [
            { required: true, message: '是否分配不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.createOn = data.hotelActivityRoomNumber.createOn
                this.dataForm.createBy = data.hotelActivityRoomNumber.createBy
                this.dataForm.updateOn = data.hotelActivityRoomNumber.updateOn
                this.dataForm.updateBy = data.hotelActivityRoomNumber.updateBy
                this.dataForm.hotelActivityRoomId = data.hotelActivityRoomNumber.hotelActivityRoomId
                this.dataForm.activityId = data.hotelActivityRoomNumber.activityId
                this.dataForm.hotelId = data.hotelActivityRoomNumber.hotelId
                this.dataForm.hotelActivityId = data.hotelActivityRoomNumber.hotelActivityId
                this.dataForm.number = data.hotelActivityRoomNumber.number
                this.dataForm.isAssign = data.hotelActivityRoomNumber.isAssign
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'hotelActivityId': this.dataForm.hotelActivityId,
                'number': this.dataForm.number,
                'isAssign': this.dataForm.isAssign
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
