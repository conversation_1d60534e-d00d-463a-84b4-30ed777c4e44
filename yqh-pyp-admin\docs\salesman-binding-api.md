# 业务员绑定系统前端API接口文档

## 管理端接口

### 1. 微信用户业务员绑定管理

#### 1.1 获取绑定列表
```
GET /salesman/wxuserbinding/list
参数：
- page: 页码
- limit: 每页数量
- wxUserName: 微信用户昵称（可选）
- salesmanName: 业务员姓名（可选）
- status: 绑定状态（可选）
- bindingType: 绑定方式（可选）

返回：
{
  "code": 0,
  "page": {
    "list": [...],
    "totalCount": 100
  }
}
```

#### 1.2 获取绑定统计
```
GET /salesman/wxuserbinding/stats
返回：
{
  "code": 0,
  "stats": {
    "totalBindings": 100,
    "activeBindings": 80,
    "todayBindings": 5,
    "expiringBindings": 3
  }
}
```

#### 1.3 新增/修改绑定
```
POST /salesman/wxuserbinding/save
POST /salesman/wxuserbinding/update
参数：
{
  "id": 1, // 修改时必填
  "wxUserId": 123,
  "salesmanId": 456,
  "bindingType": 1,
  "bindingSource": "二维码扫描",
  "effectiveTime": "2025-01-01 00:00:00",
  "expiryTime": "2025-12-31 23:59:59",
  "priority": 1,
  "status": 1,
  "remarks": "备注"
}
```

#### 1.4 删除绑定
```
POST /salesman/wxuserbinding/delete
参数：[1, 2, 3] // 绑定ID数组
```

#### 1.5 解除绑定
```
POST /salesman/wxuserbinding/unbind
参数：
- wxUserId: 微信用户ID
- salesmanId: 业务员ID
- reason: 解绑原因
```

#### 1.6 获取绑定历史
```
GET /salesman/wxuserbinding/history
参数：
- page: 页码
- limit: 每页数量
- wxUserId: 微信用户ID
- operationType: 操作类型（可选）
```

### 2. 订单业务员关联管理

#### 2.1 获取订单列表
```
GET /salesman/orderassociation/list
参数：
- page: 页码
- limit: 每页数量
- orderSn: 订单号（可选）
- wxUserName: 微信用户（可选）
- salesmanName: 业务员（可选）
- hasAssociation: 关联状态（可选）
- startDate: 开始日期（可选）
- endDate: 结束日期（可选）
```

#### 2.2 获取关联统计
```
GET /salesman/orderassociation/stats
返回：
{
  "code": 0,
  "stats": {
    "totalOrders": 1000,
    "associatedOrders": 800,
    "unassociatedOrders": 200,
    "associationRate": "80%"
  }
}
```

#### 2.3 关联业务员
```
POST /salesman/orderassociation/associate
参数：
- rechargeRecordId: 订单ID
- salesmanId: 业务员ID
- reason: 关联原因
```

#### 2.4 更换业务员
```
POST /salesman/orderassociation/change
参数：
- rechargeRecordId: 订单ID
- salesmanId: 新业务员ID
- reason: 更换原因
```

#### 2.5 取消关联
```
POST /salesman/orderassociation/disassociate
参数：
- rechargeRecordId: 订单ID
```

#### 2.6 批量关联历史订单
```
POST /salesman/orderassociation/batchAssociateHistorical
```

#### 2.7 验证订单关联
```
GET /salesman/orderassociation/validate
参数：
- rechargeRecordId: 订单ID
```

#### 2.8 根据用户查找业务员
```
GET /salesman/orderassociation/findSalesmanByUser
参数：
- userId: 用户ID
```

### 3. 佣金安全管理

#### 3.1 检查佣金存在
```
GET /salesman/commissionsafety/checkExists
参数：
- businessType: 业务类型
- businessId: 业务ID
```

#### 3.2 重试失败佣金
```
POST /salesman/commissionsafety/retryFailed
```

#### 3.3 验证数据一致性
```
GET /salesman/commissionsafety/validateConsistency
参数：
- commissionRecordId: 佣金记录ID
```

#### 3.4 批量验证一致性
```
POST /salesman/commissionsafety/batchValidate
```

#### 3.5 修复不一致数据
```
POST /salesman/commissionsafety/repairInconsistent
参数：
- commissionRecordId: 佣金记录ID
```

#### 3.6 清理过期数据
```
POST /salesman/commissionsafety/cleanup
```

#### 3.7 查看锁状态
```
GET /salesman/commissionsafety/lockStatus
参数：
- businessType: 业务类型
- businessId: 业务ID
- salesmanId: 业务员ID
```

#### 3.8 释放锁
```
POST /salesman/commissionsafety/releaseLock
参数：
- businessType: 业务类型
- businessId: 业务ID
- salesmanId: 业务员ID
```

#### 3.9 生成唯一标识
```
GET /salesman/commissionsafety/generateUniqueKey
参数：
- businessType: 业务类型
- businessId: 业务ID
- salesmanId: 业务员ID
```

### 4. 辅助接口

#### 4.1 搜索微信用户
```
GET /wx/user/search
参数：
- keyword: 搜索关键词
- limit: 返回数量限制
```

#### 4.2 搜索业务员
```
GET /salesman/salesman/search
参数：
- keyword: 搜索关键词
- limit: 返回数量限制
```

#### 4.3 获取微信用户信息
```
GET /wx/user/info/{id}
```

#### 4.4 获取业务员信息
```
GET /salesman/salesman/info/{id}
```

## 移动端接口

### 1. 用户绑定管理

#### 1.1 获取我的绑定信息
```
GET /web/salesman/binding/myBinding
返回：
{
  "code": 0,
  "binding": {
    "salesmanName": "张三",
    "salesmanCode": "S001",
    "salesmanMobile": "***********",
    "bindingTime": "2025-01-01 10:00:00"
  }
}
```

#### 1.2 通过二维码绑定
```
POST /web/salesman/binding/bindByQrCode
参数：
{
  "qrCodeContent": "业务员二维码内容"
}
```

#### 1.3 通过邀请码绑定
```
POST /web/salesman/binding/bindByInviteCode
参数：
{
  "inviteCode": "ABC123"
}
```

#### 1.4 解除绑定
```
POST /web/salesman/binding/unbind
参数：
{
  "reason": "解绑原因"
}
```

#### 1.5 获取业务员联系信息
```
GET /web/salesman/binding/getSalesmanContact
返回：
{
  "code": 0,
  "salesmanName": "张三",
  "salesmanCode": "S001",
  "salesmanMobile": "***********",
  "bindingTime": "2025-01-01 10:00:00"
}
```

#### 1.6 获取绑定历史
```
GET /web/salesman/binding/getBindingHistory
参数：
- page: 页码
- limit: 每页数量

返回：
{
  "code": 0,
  "page": {
    "list": [
      {
        "id": 1,
        "operationType": 1,
        "oldSalesmanName": null,
        "newSalesmanName": "张三",
        "operationReason": "扫码绑定",
        "bindingSource": "二维码",
        "operatorType": 1,
        "createOn": "2025-01-01 10:00:00"
      }
    ],
    "totalCount": 10
  }
}
```

### 2. 微信相关接口

#### 2.1 获取微信配置
```
GET /web/wechat/config
参数：
- url: 当前页面URL

返回：
{
  "code": 0,
  "config": {
    "appId": "wx123456789",
    "timestamp": 1640995200,
    "nonceStr": "abc123",
    "signature": "signature_string"
  }
}
```

## 数据字典

### 绑定方式 (bindingType)
- 1: 二维码扫描
- 2: 邀请链接
- 3: 手动绑定
- 4: 系统分配

### 绑定状态 (status)
- 0: 已失效
- 1: 有效
- 2: 已解绑

### 操作类型 (operationType)
- 1: 新增绑定
- 2: 更换业务员
- 3: 解除绑定
- 4: 系统自动解绑

### 操作人类型 (operatorType)
- 1: 客户自己
- 2: 业务员
- 3: 管理员
- 4: 系统

### 订单状态
- 0: 待支付
- 1: 已支付
- 2: 已取消
- 3: 已退款

### 失败状态
- 0: 待重试
- 1: 重试成功
- 2: 重试失败
- 3: 已忽略
