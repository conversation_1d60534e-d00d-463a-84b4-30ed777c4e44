<template>
  <div>
    <van-list v-model="loading" :finished="finished" @load="onLoad">
      <div style="display: flex">
        <van-sidebar @change="placeOnChange" v-model="placeActive" style="width: 20%" v-if="placeList.length > 1">
          <van-sidebar-item title="全部场地" />
          <van-sidebar-item v-for="item in placeList" :key="item.id" :title="item.name" />
        </van-sidebar>
        <div class="data" :style="placeList.length > 1 ? 'width: 80%' : 'width: 100%'">
          <div v-for="item in dataList" :key="item.id">
            <van-card slot="title" style="background: white" :thumb="item.imageUrl
                ? item.imageUrl
                : (activityInfo && activityInfo.mobileBanner)
                  ? activityInfo.mobileBanner.split(',')[0]
                  : 'van-icon'
              ">
              <div slot="title" style="font-size: 18px">{{ item.name }}</div>

              <div slot="desc" style="padding-top: 10px; font-size: 12px; color: grey">
                <div v-if="item.startTime">
                  {{ item.startTime.substring(5, 16) }}
                  <span v-if="item.endTime">~ {{ item.endTime.substring(11, 16) }}</span>
                </div>
                <div v-if="item.activitySpeakers && item.activitySpeakers.length > 0">
                  {{ item.aliasSpeakerName || '主席' }}：
                  <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                    v-for="item1 in item.activitySpeakers" :key="item1.id" size="medium" round type="primary" plain>{{
                    item1.name }}</van-tag>
                </div>
                <div v-if="item.activityGuests && item.activityGuests.length > 0">
                  {{ item.aliasGuestName || '主持' }}：
                  <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                    v-for="item1 in item.activityGuests" :key="item1.id" size="medium" round type="primary" plain>{{
                    item1.name }}</van-tag>
                </div>
                <div v-if="item.activityDiscuss && item.activityDiscuss.length > 0">
                  {{ item.aliasDiscussName || '讨论' }}：
                  <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                    v-for="item1 in item.activityDiscuss" :key="item1.id" size="medium" round type="primary" plain>{{
                    item1.name }}</van-tag>
                </div>
              </div>
            </van-card>
            <div>
              <van-steps active="-1" direction="vertical">
                <van-step v-for="item2 in item.placeActivityTopicScheduleEntities" :key="item2.id"
                  style="padding-left:25%">
                  <div>
                    <div class="schedule-date">{{ item2.startTime.substring(11, 16) }}</div>
                    <div style="color: black">{{ item2.name }}</div>
                    <div v-if="item2.activitySpeakers && item2.activitySpeakers.length > 0">
                      {{ item2.aliasSpeakerName || '主持' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activitySpeakers" :key="item3.id" size="medium" type="warning">{{
                        item3.name }}</van-tag>
                    </div>
                    <div v-if="item2.activityGuests && item2.activityGuests.length > 0">
                      {{ item2.aliasGuestName || '讲者' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityGuests" :key="item3.id" size="medium" type="warning">{{ item3.name
                        }}</van-tag>
                    </div>
                    <div v-if="item2.activityDiscuss && item2.activityDiscuss.length > 0">
                      {{ item2.aliasDiscussName || '讨论' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityDiscuss" :key="item3.id" size="medium" round plain
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div>
                      {{ item2.startTime }} ~
                      {{ item2.endTime.substring(11, 19) }}
                    </div>
                  </div>
                </van-step>
              </van-steps>
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script>
import date from "@/js/date.js";
export default {
  data() {
    return {
      activityId: undefined,
      dataForm: {
        name: "",
        placeId: "",
        date: "",
      },
      loading: false,
      finished: false,
      flag: false,
      dataList: [],
      placeList: [],
      activityDateBetween: [],
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      placeActive: 0,
      dateActive: "",
      schedulesActive: [],
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.getActivityInfo();
    this.getActivityDateBetween();
    this.getPlace();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityList();
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            document.title = this.activityInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityDateBetween() {
      this.$fly
        .get(`/pyp/activity/activity/dateBetween/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.activityDateBetween = res.result;
          } else {
            vant.Toast(res.msg);
            this.activityDateBetween = [];
          }
        });
    },
    getPlace() {
      this.$fly
        .get(`/pyp/place/placeactivity/findByActivityId/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.placeList = res.result;
          } else {
            vant.Toast(res.msg);
            this.placeList = [];
          }
        });
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/place/placeactivitytopic/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          placeId: this.dataForm.placeId,
          date: this.dataForm.date,
          name: this.dataForm.name,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
                this.schedulesActive.push(e.id);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    dateOnClick(name, title) {
      this.dataForm.date = name;
      this.onSearch();
    },
    placeOnChange(index) {
      if (index == 0) {
        this.dataForm.placeId = "";
      } else {
        this.placeList.forEach((e, index1) => {
          var realIndex = index1 + 1;
          if (realIndex == index) {
            this.dataForm.placeId = e.id;
          }
        });
      }
      this.onSearch();
    },
    goExpertDetail(v) {
      this.$router.push({
        name: "schedulesSimpleExpertDetail",
        query: { detailId: v, id: this.activityId },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/ .van-cell {
    padding: 0;
    align-items: center;
  }

  /deep/ .van-cell__right-icon {
    margin-right: 20px;
  }

  .schedule-date {
    position: absolute;
    left: 0px;
    font-size: 16px;
    top: 12px;
    color: #1989fa;
    font-weight: bold;
  }

  /deep/ .van-step__circle {
    background-color: #1989fa;
  }

  /deep/ .van-step__line {
    background-color: #1989fa;
  }
}
</style>