-- 活动过期和续费系统数据库变更

-- 1. 为tb_activity表添加过期时间字段
ALTER TABLE `tb_activity` ADD COLUMN `expiration_time` datetime DEFAULT NULL COMMENT '活动过期时间，NULL表示永不过期';

-- 2. 为tb_activity表添加过期状态字段
ALTER TABLE `tb_activity` ADD COLUMN `is_expired` tinyint(1) DEFAULT 0 COMMENT '是否已过期：0-未过期，1-已过期';

-- 3. 为activity_recharge_package表添加续费相关字段
ALTER TABLE `activity_recharge_package` ADD COLUMN `renewal_days` int(11) DEFAULT NULL COMMENT '续费天数，仅续费套餐使用';

-- 4. 为activity_recharge_record表添加续费相关字段
ALTER TABLE `activity_recharge_record` ADD COLUMN `renewal_days` int(11) DEFAULT NULL COMMENT '续费天数';
ALTER TABLE `activity_recharge_record` ADD COLUMN `original_expiration_time` datetime DEFAULT NULL COMMENT '续费前的原过期时间';
ALTER TABLE `activity_recharge_record` ADD COLUMN `new_expiration_time` datetime DEFAULT NULL COMMENT '续费后的新过期时间';

-- 5. 添加索引优化查询性能
ALTER TABLE `tb_activity` ADD INDEX `idx_expiration_time` (`expiration_time`);
ALTER TABLE `tb_activity` ADD INDEX `idx_is_expired` (`is_expired`);
ALTER TABLE `tb_activity` ADD INDEX `idx_expiration_status` (`is_expired`, `expiration_time`);

-- 6. 为现有活动设置默认过期时间（365天后）
UPDATE `tb_activity` SET `expiration_time` = DATE_ADD(NOW(), INTERVAL 365 DAY) WHERE `expiration_time` IS NULL;

-- 7. 插入默认的续费套餐配置
INSERT INTO `activity_recharge_package` (
    `name`, `description`, `package_type`, `count_value`, `price`, `original_price`, 
    `discount_rate`, `status`, `sort_order`, `is_hot`, `is_recommended`, 
    `valid_days`, `renewal_days`, `appid`, `create_on`, `create_by`
) VALUES 
(
    '活动续费30天', '为活动延长30天使用期限', 3, 0, 29.90, 39.90, 
    0.75, 1, 1, 0, 1, 
    NULL, 30, 'default', NOW(), 1
),
(
    '活动续费90天', '为活动延长90天使用期限', 3, 0, 79.90, 99.90, 
    0.80, 1, 2, 1, 1, 
    NULL, 90, 'default', NOW(), 1
),
(
    '活动续费365天', '为活动延长一年使用期限', 3, 0, 299.90, 399.90, 
    0.75, 1, 3, 1, 1, 
    NULL, 365, 'default', NOW(), 1
);
