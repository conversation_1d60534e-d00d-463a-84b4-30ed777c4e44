package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.common.enume.RechargeTypeEnum;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargePackageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.enums.PackageTypeEnum;
import com.cjy.pyp.common.enume.RechargeStatusEnum;
import com.cjy.pyp.modules.activity.service.ActivityExpirationService;
import com.cjy.pyp.modules.activity.service.ActivityExpirationNotifyService;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.vo.ActivityExpirationStatusVo;
import com.cjy.pyp.modules.activity.vo.RenewalOrderVo;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 活动过期管理服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Slf4j
@Service
public class ActivityExpirationServiceImpl implements ActivityExpirationService {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityRechargePackageService activityRechargePackageService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private ActivityExpirationNotifyService activityExpirationNotifyService;

    @Override
    public boolean isActivityExpired(Long activityId) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null || activity.getExpirationTime() == null) {
            return false;
        }
        return new Date().after(activity.getExpirationTime());
    }

    @Override
    public boolean isActivityExpiringSoon(Long activityId) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null || activity.getExpirationTime() == null) {
            return false;
        }
        
        Date now = new Date();
        Date expirationTime = activity.getExpirationTime();
        long diffInMillis = expirationTime.getTime() - now.getTime();
        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);
        
        return diffInDays <= 7 && diffInDays >= 0;
    }

    @Override
    public ActivityExpirationStatusVo getActivityExpirationStatus(Long activityId) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return null;
        }

        ActivityExpirationStatusVo statusVo = new ActivityExpirationStatusVo();
        statusVo.setActivityId(activityId);
        statusVo.setActivityName(activity.getName());
        statusVo.setExpirationTime(activity.getExpirationTime());

        if (activity.getExpirationTime() == null) {
            // 永不过期
            statusVo.setIsExpired(false);
            statusVo.setIsExpiringSoon(false);
            statusVo.setRemainingDays(-1);
            statusVo.setRemainingHours(-1);
            statusVo.setStatusDescription("永不过期");
            statusVo.setCanRenew(false);
            statusVo.setRenewalMessage("");
        } else {
            Date now = new Date();
            Date expirationTime = activity.getExpirationTime();
            
            boolean isExpired = now.after(expirationTime);
            statusVo.setIsExpired(isExpired);

            if (isExpired) {
                statusVo.setIsExpiringSoon(false);
                statusVo.setRemainingDays(0);
                statusVo.setRemainingHours(0);
                statusVo.setStatusDescription("已过期");
                statusVo.setCanRenew(true);
                statusVo.setRenewalMessage("活动已过期，请及时续费以继续使用");
            } else {
                long diffInMillis = expirationTime.getTime() - now.getTime();
                long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);
                
                statusVo.setRemainingDays((int) diffInDays);
                statusVo.setRemainingHours((int) diffInHours);
                
                boolean isExpiringSoon = diffInDays <= 7;
                statusVo.setIsExpiringSoon(isExpiringSoon);
                
                if (isExpiringSoon) {
                    statusVo.setStatusDescription("即将过期（" + diffInDays + "天后）");
                    statusVo.setCanRenew(true);
                    statusVo.setRenewalMessage("活动即将过期，建议提前续费");
                } else {
                    statusVo.setStatusDescription("正常使用中（" + diffInDays + "天后过期）");
                    statusVo.setCanRenew(true);
                    statusVo.setRenewalMessage("");
                }
            }
        }

        return statusVo;
    }

    @Override
    public List<ActivityExpirationStatusVo> getUserActivitiesExpirationStatus(Long userId, String appid) {
        // 获取用户的活动列表
        QueryWrapper<ActivityEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("create_by", userId);
        wrapper.eq("appid", appid);
        wrapper.orderByDesc("create_on");
        
        List<ActivityEntity> activities = activityService.list(wrapper);
        List<ActivityExpirationStatusVo> statusList = new ArrayList<>();
        
        for (ActivityEntity activity : activities) {
            ActivityExpirationStatusVo status = getActivityExpirationStatus(activity.getId());
            if (status != null) {
                statusList.add(status);
            }
        }
        
        return statusList;
    }

    @Override
    public void updateActivityExpirationStatus(Long activityId) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return;
        }

        boolean isExpired = isActivityExpired(activityId);
        Integer currentStatus = activity.getIsExpired();
        
        if (currentStatus == null || (isExpired && currentStatus == 0) || (!isExpired && currentStatus == 1)) {
            activity.setIsExpired(isExpired ? 1 : 0);
            activityService.updateById(activity);
            log.info("Updated expiration status for activity {}: {}", activityId, isExpired ? "expired" : "active");
        }
    }

    @Override
    public void batchUpdateExpirationStatus() {
        log.info("Starting batch update of activity expiration status");
        
        // 获取所有有过期时间的活动
        QueryWrapper<ActivityEntity> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("expiration_time");
        
        List<ActivityEntity> activities = activityService.list(wrapper);
        int updatedCount = 0;
        
        for (ActivityEntity activity : activities) {
            try {
                updateActivityExpirationStatus(activity.getId());
                updatedCount++;
            } catch (Exception e) {
                log.error("Failed to update expiration status for activity {}: {}", activity.getId(), e.getMessage());
            }
        }
        
        log.info("Batch update completed. Updated {} activities", updatedCount);
    }

    @Override
    public R setActivityExpirationTime(Long activityId, Date expirationTime) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        activity.setExpirationTime(expirationTime);
        activity.setIsExpired(expirationTime != null && new Date().after(expirationTime) ? 1 : 0);
        
        boolean success = activityService.updateById(activity);
        if (success) {
            log.info("Set expiration time for activity {}: {}", activityId, expirationTime);
            return R.ok();
        } else {
            return R.error("设置过期时间失败");
        }
    }

    @Override
    public R extendActivityExpiration(Long activityId, Integer days) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        Date currentExpiration = activity.getExpirationTime();
        Date newExpiration;
        
        if (currentExpiration == null) {
            // 如果当前没有过期时间，从现在开始计算
            newExpiration = new Date(System.currentTimeMillis() + days * 24L * 60 * 60 * 1000);
        } else {
            // 如果已过期，从现在开始计算；否则从当前过期时间开始计算
            Date baseTime = new Date().after(currentExpiration) ? new Date() : currentExpiration;
            newExpiration = new Date(baseTime.getTime() + days * 24L * 60 * 60 * 1000);
        }

        return setActivityExpirationTime(activityId, newExpiration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createRenewalOrder(RenewalOrderVo renewalOrderVo, Long userId) {
        // 验证活动是否存在
        ActivityEntity activity = activityService.getById(renewalOrderVo.getActivityId());
        if (activity == null) {
            return R.error("活动不存在");
        }

        // 验证用户是否有权限续费此活动
        if (!activity.getCreateBy().equals(userId)) {
            return R.error("无权限续费此活动");
        }

        // 验证续费套餐是否存在且为续费套餐
        ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(renewalOrderVo.getPackageId());
        if (packageEntity == null) {
            return R.error("续费套餐不存在");
        }
        if (packageEntity.getStatus() != 1) {
            return R.error("续费套餐已禁用");
        }
        if (!PackageTypeEnum.isRenewalActivityPackage(packageEntity.getPackageType())) {
            return R.error("该套餐不是续费套餐");
        }

        // 创建续费订单
        ActivityRechargeRecordEntity record = new ActivityRechargeRecordEntity();
        record.setUserId(userId);
        record.setActivityId(renewalOrderVo.getActivityId());
        record.setPackageId(renewalOrderVo.getPackageId());
        record.setRechargeType(RechargeTypeEnum.RENEWAL_ACTIVITY_PACKAGE.getCode());
        record.setOrderSn(generateOrderSn());
        record.setCountValue(0); // 续费套餐不增加使用次数
        record.setAmount(packageEntity.getPrice());
        record.setPayAmount(packageEntity.getPrice());
        record.setStatus(RechargeStatusEnum.PENDING.getCode());
        record.setSource(1); // 用户充值
        record.setRemarks(renewalOrderVo.getRemarks());
        record.setAppid(renewalOrderVo.getAppid());
        record.setRenewalDays(packageEntity.getRenewalDays());
        record.setOriginalExpirationTime(activity.getExpirationTime());

        // 计算新的过期时间
        Date currentExpiration = activity.getExpirationTime();
        Date newExpiration;
        if (currentExpiration == null || new Date().after(currentExpiration)) {
            // 如果没有过期时间或已过期，从现在开始计算
            newExpiration = new Date(System.currentTimeMillis() + packageEntity.getRenewalDays() * 24L * 60 * 60 * 1000);
        } else {
            // 从当前过期时间开始计算
            newExpiration = new Date(currentExpiration.getTime() + packageEntity.getRenewalDays() * 24L * 60 * 60 * 1000);
        }
        record.setNewExpirationTime(newExpiration);

        activityRechargeRecordService.save(record);

        return R.ok().put("orderSn", record.getOrderSn()).put("amount", record.getAmount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R handleRenewalPaymentSuccess(String orderSn, String payTransaction, String payType) {
        // 查找续费订单
        QueryWrapper<ActivityRechargeRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("order_sn", orderSn);
        wrapper.eq("recharge_type", RechargeTypeEnum.RENEWAL_ACTIVITY_PACKAGE.getCode());
        wrapper.eq("status", RechargeStatusEnum.PENDING.getCode());

        ActivityRechargeRecordEntity record = activityRechargeRecordService.getOne(wrapper);
        if (record == null) {
            return R.error("续费订单不存在或已处理");
        }

        // 更新订单状态
        record.setStatus(RechargeStatusEnum.PAID.getCode());
        record.setPayTransaction(payTransaction);
        record.setPayType(Integer.valueOf(payType));
        record.setPayTime(new Date());

        // 更新活动过期时间
        ActivityEntity activity = activityService.getById(record.getActivityId());
        if (activity != null) {
            activity.setExpirationTime(record.getNewExpirationTime());
            activity.setIsExpired(0); // 续费后重置为未过期状态
            activityService.updateById(activity);
        }

        activityRechargeRecordService.updateById(record);

        log.info("Renewal payment success for activity {}, extended to {}",
                record.getActivityId(), record.getNewExpirationTime());

        // 发送续费成功通知
        try {
            String newExpirationTimeStr = record.getNewExpirationTime() != null ?
                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(record.getNewExpirationTime()) : "未知";
            activityExpirationNotifyService.sendRenewalSuccessNotification(
                activity, record.getRenewalDays(), newExpirationTimeStr);
        } catch (Exception e) {
            log.error("发送续费成功通知失败: {}", e.getMessage());
        }

        return R.ok().put("activityId", record.getActivityId())
                .put("newExpirationTime", record.getNewExpirationTime());
    }

    @Override
    public List<ActivityEntity> getActivitiesExpiringSoon(Integer days) {
        Date futureDate = new Date(System.currentTimeMillis() + days * 24L * 60 * 60 * 1000);

        QueryWrapper<ActivityEntity> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("expiration_time");
        wrapper.le("expiration_time", futureDate);
        wrapper.gt("expiration_time", new Date());
        wrapper.eq("is_expired", 0);

        return activityService.list(wrapper);
    }

    @Override
    public List<ActivityEntity> getExpiredActivities() {
        QueryWrapper<ActivityEntity> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("expiration_time");
        wrapper.le("expiration_time", new Date());
        wrapper.eq("is_expired", 1);

        return activityService.list(wrapper);
    }

    @Override
    public R sendExpirationReminder(Long activityId, Integer days) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        log.info("Sending expiration reminder for activity {}, {} days remaining", activityId, days);
        return activityExpirationNotifyService.sendExpirationReminder(activity, days);
    }

    @Override
    public R sendExpirationNotification(Long activityId) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }

        log.info("Sending expiration notification for activity {}", activityId);
        return activityExpirationNotifyService.sendExpirationNotification(activity);
    }

    /**
     * 生成订单号
     */
    private String generateOrderSn() {
        return "RN" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(4);
    }
}
