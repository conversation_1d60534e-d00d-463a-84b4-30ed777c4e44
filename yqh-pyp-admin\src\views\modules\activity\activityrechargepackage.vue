<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="套餐名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.packageType" placeholder="套餐类型" clearable>
          <el-option label="充值次数套餐" :value="1"></el-option>
          <el-option label="创建活动套餐" :value="2"></el-option>
          <el-option label="活动续费套餐" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="状态" clearable>
          <el-option label="启用" :value="1"></el-option>
          <el-option label="禁用" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:rechargepackage:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:rechargepackage:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="warning" @click="goToCreateActivityPackage()">购买创建活动套餐</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="套餐名称">
      </el-table-column>
      <el-table-column prop="packageType" header-align="center" align="center" label="套餐类型">
        <template slot-scope="scope">
          <el-tag :type="getPackageTypeTagType(scope.row.packageType)">
            {{ getPackageTypeName(scope.row.packageType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" header-align="center" align="center" label="套餐描述" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="countValue" header-align="center" align="center" label="充值次数">
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="现价(元)">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="originalPrice" header-align="center" align="center" label="原价(元)">
        <template slot-scope="scope">
          <span v-if="scope.row.originalPrice" style="text-decoration: line-through; color: #909399;">¥{{ scope.row.originalPrice }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="discountRate" header-align="center" align="center" label="折扣">
        <template slot-scope="scope">
          <span v-if="scope.row.discountRate && scope.row.discountRate < 1">{{ (scope.row.discountRate * 10).toFixed(1) }}折</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="validDays" header-align="center" align="center" label="有效期(天)">
        <template slot-scope="scope">
          <span v-if="scope.row.packageType !== 3">{{ scope.row.validDays || '-' }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="renewalDays" header-align="center" align="center" label="续费天数">
        <template slot-scope="scope">
          <span v-if="scope.row.packageType === 3">{{ scope.row.renewalDays || '-' }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="isHot" header-align="center" align="center" label="热门">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isHot == 1 ? 'danger' : 'info'">{{ scope.row.isHot == 1 ? '热门' : '普通' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isRecommended" header-align="center" align="center" label="推荐">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isRecommended == 1 ? 'success' : 'info'">{{ scope.row.isRecommended == 1 ? '推荐' : '普通' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">{{ scope.row.status == 1 ? '启用' : '禁用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sortOrder" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './activityrechargepackage-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        name: '',
        packageType: '',
        status: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/rechargepackage/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'packageType': this.dataForm.packageType,
          'status': this.dataForm.status
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/rechargepackage/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 跳转到创建活动套餐购买页面
    goToCreateActivityPackage() {
      this.$router.push({ name: 'create-activity-package-order' })
    },
    // 获取套餐类型名称
    getPackageTypeName(packageType) {
      switch (packageType) {
        case 1:
          return '充值次数套餐'
        case 2:
          return '创建活动套餐'
        case 3:
          return '活动续费套餐'
        default:
          return '未知类型'
      }
    },
    // 获取套餐类型标签颜色
    getPackageTypeTagType(packageType) {
      switch (packageType) {
        case 1:
          return 'primary'
        case 2:
          return 'warning'
        case 3:
          return 'success'
        default:
          return 'info'
      }
    }
  }
}
</script>
