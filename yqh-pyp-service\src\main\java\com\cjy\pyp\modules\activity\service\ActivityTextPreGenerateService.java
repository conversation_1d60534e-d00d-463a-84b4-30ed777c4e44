package com.cjy.pyp.modules.activity.service;

import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;

/**
 * 文案预生成服务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01
 */
public interface ActivityTextPreGenerateService {
    
    /**
     * 异步预生成文案
     * 在用户生成文案后，自动在后台预生成2条文案
     * 
     * @param originalText 原始生成的文案实体
     * @param userId 用户ID
     */
    void preGenerateTextAsync(ActivityTextEntity originalText, Long userId);
    
    /**
     * 清理过期的预生成文案
     * 清理超过7天未使用的预生成文案
     */
    void cleanupExpiredPreGeneratedTexts();
}
