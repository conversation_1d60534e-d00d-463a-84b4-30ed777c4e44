<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      
    >
      <el-form-item>
        <el-input
          v-model="dataForm.filename"
          placeholder="文件名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="success" @click="pullVideo()">拉取视频</el-button>

        <!-- <el-button v-if="isAuth('place:placeactivityvideo:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button> -->
        <!-- <el-button
          v-if="isAuth('place:placeactivityvideo:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        > -->
      </el-form-item>
      <el-form-item>
        <el-upload
            :data="{ placeId: placeId,activityId: activityId }"
          :on-success="handleVodUploadSuccess"
          :on-remove="handleVodRemove"
          :before-remove="beforeVodRemove"
          :on-exceed="handleUploadExceed"
          :file-list="fileList"
          :action="url"
          :limit="1"
          class="upload-demo"
        >
          <el-button type="primary">上传视频</el-button>
          <el-tooltip placement="right-end">
            <div slot="content">
              最大支持10G，<br />
              支持3GP、ASF、AVI、DAT、DV、F4V、<br />
              GIF、M2T、M4V、MJ2、MJPEG、MKV、MOV、MP4、<br />
              MPE、MPG、MPEG、MTS、OGG、QT、RM、RMVB、<br />
              SWF、TS、VOB、WMV、WEBM 等视频格式上传
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </el-upload>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="id"
      >
      </el-table-column>
      <el-table-column
        prop="fileId"
        header-align="center"
        align="center"
        width="280px"
        show-overflow-tooltip
        label="视频fileID"
      >
      </el-table-column>
      <el-table-column
        prop="filename"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="视频名称"
      >
      </el-table-column>
      <el-table-column
        prop="fileSize"
        header-align="center"
        align="center"
        label="文件大小"
      >
      </el-table-column>
      <el-table-column
        prop="duration"
        header-align="center"
        align="center"
        label="视频时长"
      >
      </el-table-column>
      <el-table-column
        prop="mediaUrl"
        header-align="center"
        show-overflow-tooltip
        align="center"
        label="视频url"
      >
      </el-table-column>
      <el-table-column
        prop="frameRate"
        header-align="center"
        align="center"
        label="帧率"
      >
      </el-table-column>
      <el-table-column
        prop="paixu"
        header-align="center"
        align="center"
        label="排序"
      >
      </el-table-column>
      <el-table-column
        prop="createOn"
        show-overflow-tooltip
        header-align="center"
        align="center"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        prop="updateOn"
        show-overflow-tooltip
        header-align="center"
        align="center"
        label="更新时间"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="download(scope.row.mediaUrl)"
            >下载视频</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >修改</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from "./placeactivityvideo-add-or-update";
export default {
  data() {
    return {
      pushKey: '',
      dataForm: {
        filename: "",
      },
      placeId: "",
      activityId: "",
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    };
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.placeId = this.$route.query.placeId;
    this.activityId = this.$route.query.activityId;
    this.pushKey = this.$route.query.pushKey;
    this.url = this.$http.adornUrl(
      `/place/placeactivityvideo/uploadAlyVideo?token=${this.$cookie.get("token")}`
    );
    this.getDataList();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/place/placeactivityvideo/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          placeId: this.placeId,
          filename: this.dataForm.filename,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    download(v) {
      window.open(v);
    },
    pullVideo() {

      this.$http({
        url: this.$http.adornUrl("/place/placeactivityvideo/pullVideo"),
        method: "get",
        params: this.$http.adornParams({
          pushKey: this.pushKey,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.getDataList();
        } else {
          this.$message.error(data.msg);
        }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id;
          });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/place/placeactivityvideo/delete"),
          method: "post",
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
                this.getDataList();
          }
        });
      });
    },
    //成功回调
    handleVodUploadSuccess(response, file, fileList) {
      if (response && response.code === 200) {
        this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                location.reload();
              },
            });
      } else {
        this.$message.error(response.msg);
      }
    },
    //视图上传多于一个视频
    handleUploadExceed(files, fileList) {
      this.$message.warning("想要重新上传视频，请先删除已上传的视频");
    },
    //点击确定 调用这个方法
    handleVodRemove() {
      //调用删除视频的接口
      video.deleteAlyVideo(this.video.videoSourceId).then((res) => {
        //提示信息
        this.$message({
          type: "success",
          message: "删除视频成功！！!",
        });
        //文件列表清空
        this.fileList = [];
        this.video.videoSourceId = "";
        this.video.videoOriginalName = "";
      });
    },
    //点击x调用这个方法
    beforeVodRemove(file, FileList) {
      return this.$confirm(`确定移除${file.name}吗？？？`);
    },
  },
};
</script>
