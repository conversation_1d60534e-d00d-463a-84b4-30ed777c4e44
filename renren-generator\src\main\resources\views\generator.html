<!DOCTYPE html>
<html>
<head>
<title>代码生成器</title>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/font-awesome.min.css">
<link rel="stylesheet" href="plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="css/main.css">
<script src="libs/jquery.min.js"></script>
<script src="plugins/layer/layer.js"></script>
<script src="libs/bootstrap.min.js"></script>
<script src="libs/vue.min.js"></script>
<script src="plugins/jqgrid/grid.locale-cn.js"></script>
<script src="plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="js/common.js"></script>
</head>
<body>
<div id="rrapp">
	<div class="grid-btn">
		<div class="form-group col-sm-2">
			<input type="text" class="form-control" v-model="q.tableName" @keyup.enter="query" placeholder="表名">
		</div>
		<a class="btn btn-default" @click="query">查询</a>
		<a class="btn btn-primary" @click="generator"><i class="fa fa-file-code-o"></i>&nbsp;生成代码</a>
	</div>
    <table id="jqGrid"></table>
    <div id="jqGridPager"></div>
</div>

<script src="js/generator.js"></script>
</body>
</html>