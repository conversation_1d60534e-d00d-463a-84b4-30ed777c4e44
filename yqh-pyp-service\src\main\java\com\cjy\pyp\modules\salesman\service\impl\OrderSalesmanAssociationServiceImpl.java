package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.dao.ActivityRechargeRecordDao;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;

import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.service.OrderSalesmanAssociationService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.github.pagehelper.PageHelper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单业务员关联服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Service("orderSalesmanAssociationService")
public class OrderSalesmanAssociationServiceImpl implements OrderSalesmanAssociationService {

    private static final Logger logger = LoggerFactory.getLogger(OrderSalesmanAssociationServiceImpl.class);

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private ActivityRechargeRecordDao activityRechargeRecordDao;

    @Autowired
    private SalesmanCommissionRecordService salesmanCommissionRecordService;

    @Autowired
    private SalesmanCommissionService salesmanCommissionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long associateSalesmanForRechargeOrder(Long rechargeRecordId, Long userId, String appid) {
        try {
            // 查找用户绑定的业务员
            Long salesmanId = findSalesmanByUser(userId, appid);
            if (salesmanId == null) {
                logger.info("用户{}没有绑定业务员，跳过订单关联", userId);
                return null;
            }

            // 更新充值记录的业务员字段
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: {}", rechargeRecordId);
                return null;
            }

            // 如果已经关联了业务员，不重复关联
            if (rechargeRecord.getSalesmanId() != null) {
                logger.info("充值记录{}已关联业务员{}，跳过重复关联", rechargeRecordId, rechargeRecord.getSalesmanId());
                return rechargeRecord.getSalesmanId();
            }

            rechargeRecord.setSalesmanId(salesmanId);
            activityRechargeRecordService.updateById(rechargeRecord);

            logger.info("为充值订单关联业务员成功: rechargeRecordId={}, userId={}, salesmanId={}", 
                       rechargeRecordId, userId, salesmanId);

            return salesmanId;
        } catch (Exception e) {
            logger.error("为充值订单关联业务员失败: rechargeRecordId={}, userId={}", rechargeRecordId, userId, e);
            return null;
        }
    }

    @Override
    public Long findSalesmanByUser(Long userId, String appid) {
        return wxUserSalesmanBindingService.getSalesmanIdByWxUser(userId, appid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualAssociateSalesman(Long rechargeRecordId, Long salesmanId, String appid) {
        try {
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: {}", rechargeRecordId);
                return false;
            }

            // 获取原业务员ID
            Long oldSalesmanId = rechargeRecord.getSalesmanId();

            rechargeRecord.setSalesmanId(salesmanId);
            boolean success = activityRechargeRecordService.updateById(rechargeRecord);

            if (success) {
                logger.info("手动关联订单业务员成功: rechargeRecordId={}, salesmanId={}", rechargeRecordId, salesmanId);

                // 处理佣金调整
                handleCommissionAdjustment(rechargeRecordId, oldSalesmanId, salesmanId, "手动关联业务员", appid);
            } else {
                logger.warn("手动关联订单业务员失败: rechargeRecordId={}, salesmanId={}", rechargeRecordId, salesmanId);
            }

            return success;
        } catch (Exception e) {
            logger.error("手动关联订单业务员异常: rechargeRecordId={}, salesmanId={}", rechargeRecordId, salesmanId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disassociateSalesman(Long rechargeRecordId, String appid) {
        try {
            // 获取原订单信息
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: {}", rechargeRecordId);
                return false;
            }

            Long oldSalesmanId = rechargeRecord.getSalesmanId();

            UpdateWrapper<ActivityRechargeRecordEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", rechargeRecordId)
                        .eq("appid", appid)
                        .set("salesman_id", null);

            boolean success = activityRechargeRecordService.update(updateWrapper);

            if (success) {
                logger.info("取消订单业务员关联成功: rechargeRecordId={}", rechargeRecordId);

                // 处理佣金调整（取消关联）
                if (oldSalesmanId != null) {
                    handleCommissionAdjustment(rechargeRecordId, oldSalesmanId, null, "取消业务员关联", appid);
                }
            } else {
                logger.warn("取消订单业务员关联失败: rechargeRecordId={}", rechargeRecordId);
            }

            return success;
        } catch (Exception e) {
            logger.error("取消订单业务员关联异常: rechargeRecordId={}", rechargeRecordId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchAssociateHistoricalOrders(String appid) {
        try {
            int associatedCount = 0;

            // 查询没有关联业务员的充值记录
            QueryWrapper<ActivityRechargeRecordEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.isNull("salesman_id")
                       .eq("appid", appid)
                       .eq("status", 1); // 只处理已支付的订单

            List<ActivityRechargeRecordEntity> unassociatedOrders = activityRechargeRecordService.list(queryWrapper);

            for (ActivityRechargeRecordEntity order : unassociatedOrders) {
                Long salesmanId = findSalesmanByUser(order.getUserId(), appid);
                if (salesmanId != null) {
                    order.setSalesmanId(salesmanId);
                    if (activityRechargeRecordService.updateById(order)) {
                        associatedCount++;
                    }
                }
            }

            logger.info("批量关联历史订单完成: appid={}, 关联数量={}", appid, associatedCount);
            return associatedCount;
        } catch (Exception e) {
            logger.error("批量关联历史订单失败: appid={}", appid, e);
            return 0;
        }
    }

    @Override
    public String validateSalesmanAssociation(Long rechargeRecordId, String appid) {
        try {
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                return "充值记录不存在";
            }

            if (rechargeRecord.getSalesmanId() == null) {
                return "订单未关联业务员";
            }

            // 检查用户当前绑定的业务员
            Long currentSalesmanId = findSalesmanByUser(rechargeRecord.getUserId(), appid);
            if (currentSalesmanId == null) {
                return "用户当前未绑定业务员";
            }

            if (!rechargeRecord.getSalesmanId().equals(currentSalesmanId)) {
                return String.format("订单关联的业务员(%d)与用户当前绑定的业务员(%d)不一致", 
                                   rechargeRecord.getSalesmanId(), currentSalesmanId);
            }

            return null; // 验证通过
        } catch (Exception e) {
            logger.error("验证业务员关联失败: rechargeRecordId={}", rechargeRecordId, e);
            return "验证过程中发生异常";
        }
    }

    @Override
    public List<ActivityRechargeRecordEntity> queryOrderList(Map<String, Object> params) {
        
        int pageNum = Integer.parseInt(params.getOrDefault("page", "1").toString());
        int pageSize = Integer.parseInt(params.getOrDefault("limit", "10").toString());
        PageHelper.startPage(pageNum, pageSize);
        // 重新查询带关联信息的数据
        List<ActivityRechargeRecordEntity> records = activityRechargeRecordDao.selectOrderListWithAssociation(params);

        return records;
    }

    @Override
    public Map<String, Object> getAssociationStats(Map<String, Object> params) {
        String appid = (String) params.get("appid");
        String wxUserId = (String) params.get("wxUserId");
        String salesmanId = (String) params.get("salesmanId");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");

        Map<String, Object> stats = new HashMap<>();

        // 构建基础查询条件
        QueryWrapper<ActivityRechargeRecordEntity> baseWrapper = new QueryWrapper<ActivityRechargeRecordEntity>()
                .eq("appid", appid);

        // 添加筛选条件
        if (StringUtils.hasText(wxUserId)) {
            baseWrapper.eq("user_id", wxUserId);
        }
        if (StringUtils.hasText(salesmanId)) {
            baseWrapper.eq("salesman_id", salesmanId);
        }
        if (StringUtils.hasText(startDate)) {
            baseWrapper.ge("create_on", startDate);
        }
        if (StringUtils.hasText(endDate)) {
            baseWrapper.le("create_on", endDate);
        }

        // 总订单数（根据筛选条件）
        int totalOrders = activityRechargeRecordService.count(baseWrapper);
        stats.put("totalOrders", totalOrders);

        // 已关联订单数（根据筛选条件）
        QueryWrapper<ActivityRechargeRecordEntity> associatedWrapper = baseWrapper.clone();
        associatedWrapper.isNotNull("salesman_id");
        int associatedOrders = activityRechargeRecordService.count(associatedWrapper);
        stats.put("associatedOrders", associatedOrders);

        // 未关联订单数
        int unassociatedOrders = totalOrders - associatedOrders;
        stats.put("unassociatedOrders", unassociatedOrders);

        // 关联率
        String associationRate = totalOrders > 0 ?
            String.format("%.1f%%", (double) associatedOrders / totalOrders * 100) : "0%";
        stats.put("associationRate", associationRate);

        return stats;
    }

    @Override
    public boolean associateSalesman(Long rechargeRecordId, Long salesmanId, String reason, String appid) {
        return manualAssociateSalesman(rechargeRecordId, salesmanId, appid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeSalesman(Long rechargeRecordId, Long salesmanId, String reason, String appid) {
        try {
            // 获取原订单信息
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: {}", rechargeRecordId);
                return false;
            }

            Long oldSalesmanId = rechargeRecord.getSalesmanId();

            // 更新业务员关联
            rechargeRecord.setSalesmanId(salesmanId);
            boolean success = activityRechargeRecordService.updateById(rechargeRecord);

            if (success) {
                logger.info("更换订单业务员成功: rechargeRecordId={}, oldSalesmanId={}, newSalesmanId={}",
                           rechargeRecordId, oldSalesmanId, salesmanId);

                // 处理佣金调整（更换业务员）
                handleCommissionAdjustment(rechargeRecordId, oldSalesmanId, salesmanId,
                                         "更换业务员: " + (reason != null ? reason : ""), appid);
            } else {
                logger.warn("更换订单业务员失败: rechargeRecordId={}, salesmanId={}", rechargeRecordId, salesmanId);
            }

            return success;
        } catch (Exception e) {
            logger.error("更换订单业务员异常: rechargeRecordId={}, salesmanId={}", rechargeRecordId, salesmanId, e);
            return false;
        }
    }

    @Override
    public String validateOrderAssociation(Long rechargeRecordId, String appid) {
        return validateSalesmanAssociation(rechargeRecordId, appid);
    }

    @Override
    public Integer batchValidateConsistency(String appid) {
        try {
            List<ActivityRechargeRecordEntity> orders = activityRechargeRecordService.list(
                new QueryWrapper<ActivityRechargeRecordEntity>()
                    .eq("appid", appid)
                    .isNotNull("salesman_id")
            );

            int inconsistentCount = 0;
            for (ActivityRechargeRecordEntity order : orders) {
                String validationResult = validateSalesmanAssociation(order.getId(), appid);
                if (validationResult != null) {
                    inconsistentCount++;
                }
            }

            return inconsistentCount;
        } catch (Exception e) {
            logger.error("批量验证一致性失败: appid={}", appid, e);
            return 0;
        }
    }

    @Override
    public Integer batchRepairInconsistency(String appid) {
        try {
            List<ActivityRechargeRecordEntity> orders = activityRechargeRecordService.list(
                new QueryWrapper<ActivityRechargeRecordEntity>()
                    .eq("appid", appid)
                    .isNotNull("salesman_id")
            );

            int repairedCount = 0;
            for (ActivityRechargeRecordEntity order : orders) {
                String validationResult = validateSalesmanAssociation(order.getId(), appid);
                if (validationResult != null) {
                    // 重新关联正确的业务员
                    Long correctSalesmanId = findSalesmanByUser(order.getUserId(), appid);
                    if (correctSalesmanId != null) {
                        if (manualAssociateSalesman(order.getId(), correctSalesmanId, appid)) {
                            repairedCount++;
                        }
                    }
                }
            }

            return repairedCount;
        } catch (Exception e) {
            logger.error("批量修复不一致失败: appid={}", appid, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleCommissionAdjustment(Long rechargeRecordId, Long oldSalesmanId, Long newSalesmanId, String reason, String appid) {
        try {
            logger.info("开始处理佣金调整: rechargeRecordId={}, oldSalesmanId={}, newSalesmanId={}, reason={}",
                       rechargeRecordId, oldSalesmanId, newSalesmanId, reason);

            // 获取充值记录
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: rechargeRecordId={}", rechargeRecordId);
                return false;
            }

            // 只处理已支付的订单
            if (!Integer.valueOf(1).equals(rechargeRecord.getStatus())) {
                logger.info("订单未支付，跳过佣金调整: rechargeRecordId={}, status={}", rechargeRecordId, rechargeRecord.getStatus());
                return true;
            }

            // 1. 处理原业务员的佣金记录
            if (oldSalesmanId != null) {
                handleOldSalesmanCommission(rechargeRecordId, oldSalesmanId, reason, appid);
            }

            // 2. 为新业务员生成佣金记录
            if (newSalesmanId != null) {
                handleNewSalesmanCommission(rechargeRecord, newSalesmanId, reason, appid);
            }

            logger.info("佣金调整处理完成: rechargeRecordId={}", rechargeRecordId);
            return true;

        } catch (Exception e) {
            logger.error("处理佣金调整失败: rechargeRecordId={}, oldSalesmanId={}, newSalesmanId={}",
                        rechargeRecordId, oldSalesmanId, newSalesmanId, e);
            throw e;
        }
    }

    /**
     * 处理原业务员的佣金记录
     */
    private void handleOldSalesmanCommission(Long rechargeRecordId, Long oldSalesmanId, String reason, String appid) {
        try {
            // 查询该订单的佣金记录
            List<SalesmanCommissionRecordEntity> commissionRecords = salesmanCommissionRecordService.list(
                new QueryWrapper<SalesmanCommissionRecordEntity>()
                    .eq("business_id", rechargeRecordId)
                    .eq("salesman_id", oldSalesmanId)
                    .eq("appid", appid)
                    .in("business_type", "CREATE_ACTIVITY", "RECHARGE_COUNT")
            );

            for (SalesmanCommissionRecordEntity record : commissionRecords) {
                // 检查结算状态
                if (Integer.valueOf(1).equals(record.getSettlementStatus())) {
                    // 已结算的佣金，创建负数调整记录
                    createAdjustmentRecord(record, reason, "取消关联-已结算佣金调整");
                    logger.info("已结算佣金创建调整记录: recordId={}, amount={}", record.getId(), record.getCommissionAmount());
                } else {
                    // 未结算的佣金，直接标记为已取消
                    record.setSettlementStatus(2); // 2-已取消
                    record.setSettlementRemarks("取消关联: " + reason);
                    record.setSettlementTime(new Date());
                    salesmanCommissionRecordService.updateById(record);
                    logger.info("未结算佣金标记为取消: recordId={}, amount={}", record.getId(), record.getCommissionAmount());
                }
            }

        } catch (Exception e) {
            logger.error("处理原业务员佣金记录失败: rechargeRecordId={}, oldSalesmanId={}", rechargeRecordId, oldSalesmanId, e);
            throw e;
        }
    }

    /**
     * 为新业务员生成佣金记录
     */
    private void handleNewSalesmanCommission(ActivityRechargeRecordEntity rechargeRecord, Long newSalesmanId, String reason, String appid) {
        try {
            // 检查是否已存在该业务员的佣金记录
            boolean exists = salesmanCommissionRecordService.count(
                new QueryWrapper<SalesmanCommissionRecordEntity>()
                    .eq("business_id", rechargeRecord.getId())
                    .eq("salesman_id", newSalesmanId)
                    .eq("appid", appid)
                    .in("business_type", "CREATE_ACTIVITY", "RECHARGE_COUNT")
                    .ne("settlement_status", 2) // 排除已取消的记录
            ) > 0;

            if (exists) {
                logger.warn("新业务员已存在佣金记录，跳过生成: rechargeRecordId={}, newSalesmanId={}",
                           rechargeRecord.getId(), newSalesmanId);
                return;
            }

            // 重新计算并生成佣金记录
            salesmanCommissionService.processOrderCommission(rechargeRecord);
            logger.info("为新业务员生成佣金记录: rechargeRecordId={}, newSalesmanId={}",
                       rechargeRecord.getId(), newSalesmanId);

        } catch (Exception e) {
            logger.error("为新业务员生成佣金记录失败: rechargeRecordId={}, newSalesmanId={}",
                        rechargeRecord.getId(), newSalesmanId, e);
            throw e;
        }
    }

    /**
     * 创建佣金调整记录
     */
    private void createAdjustmentRecord(SalesmanCommissionRecordEntity originalRecord, String reason, String description) {
        try {
            SalesmanCommissionRecordEntity adjustmentRecord = new SalesmanCommissionRecordEntity();
            adjustmentRecord.setSalesmanId(originalRecord.getSalesmanId());
            adjustmentRecord.setUserId(originalRecord.getUserId());
            adjustmentRecord.setActivityId(originalRecord.getActivityId());
            adjustmentRecord.setCommissionType(originalRecord.getCommissionType());
            adjustmentRecord.setBusinessType(originalRecord.getBusinessType() + "_ADJUSTMENT");
            adjustmentRecord.setBusinessId(originalRecord.getBusinessId());
            adjustmentRecord.setOrderAmount(originalRecord.getOrderAmount());
            adjustmentRecord.setCommissionRate(originalRecord.getCommissionRate());
            // 负数金额表示调整
            adjustmentRecord.setCommissionAmount(originalRecord.getCommissionAmount().negate());
            adjustmentRecord.setCalculationType(originalRecord.getCalculationType());
            adjustmentRecord.setSettlementStatus(0); // 0-未结算
            adjustmentRecord.setBusinessTime(new Date());
            adjustmentRecord.setDescription(description + ": " + reason);
            adjustmentRecord.setAppid(originalRecord.getAppid());

            salesmanCommissionRecordService.save(adjustmentRecord);
            logger.info("创建佣金调整记录成功: originalRecordId={}, adjustmentAmount={}",
                       originalRecord.getId(), adjustmentRecord.getCommissionAmount());

        } catch (Exception e) {
            logger.error("创建佣金调整记录失败: originalRecordId={}", originalRecord.getId(), e);
            throw e;
        }
    }
}
