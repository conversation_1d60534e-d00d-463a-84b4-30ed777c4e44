<template>
  <div class="salesman-scan">
    <!-- 顶部装饰背景 -->
    <div class="header-decoration"></div>

    <!-- 业务员信息卡片 -->
    <div class="salesman-info">
      <div class="card-glow"></div>
      <div class="avatar-container">
        <div class="avatar">
          <img :src="salesman.avatar || 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'" alt="业务员头像" @error="handleImageError">
        </div>
        <div class="online-status"></div>
      </div>
      <div class="info">
        <h3>{{ salesman.name }}</h3>
        <!-- <div class="position-tag">
          <i class="icon-badge"></i>
          <span>{{ salesman.department }} - {{ salesman.position }}</span>
        </div> -->
        <div class="contact-info">
          <i class="icon-phone"></i>
          <span>{{ salesman.mobile }}</span>
        </div>
      </div>
      <div class="trust-badge">
        <i class="icon-verified"></i>
        <span>认证业务员</span>
      </div>
    </div>

    <!-- 充值套餐选择 -->
    <div class="package-section" v-if="rechargePackages.length > 0">
      <div class="section-header">
        <div class="section-icon recharge-icon"></div>
        <h4>充值套餐</h4>
        <div class="section-subtitle">选择适合的充值方案</div>
      </div>
      <div class="package-list">
        <div
          class="package-item"
          v-for="pkg in rechargePackages"
          :key="pkg.id"
          :class="{ active: selectedPackageId == pkg.id && selectedPackageType == 1 }"
          @click="selectPackage(pkg.id, 1)">
          <div class="package-glow"></div>
          <div class="package-header">
            <div class="package-title">
              <span class="package-name">{{ pkg.name }}</span>
              <div class="package-tags">
                <span v-if="pkg.isHot" class="tag hot">
                  <i class="icon-fire"></i>热门
                </span>
                <span v-if="pkg.isRecommended" class="tag recommend">
                  <i class="icon-star"></i>推荐
                </span>
              </div>
            </div>
            <div class="package-price-container">
              <span class="currency">¥</span>
              <span class="package-price">{{ pkg.price }}</span>
            </div>
          </div>
          <div class="package-desc">{{ pkg.description }}</div>
          <div class="package-benefits">
            <div class="benefit-item">
              <i class="icon-count"></i>
              <span class="package-count">{{ pkg.countValue }}次使用</span>
            </div>
          </div>
          <!-- <div class="selection-indicator">
            <div class="check-circle">
              <i class="icon-check"></i>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 创建活动套餐选择 -->
    <div class="package-section" v-if="activityPackages.length > 0">
      <div class="section-header">
        <div class="section-icon activity-icon"></div>
        <h4>活动套餐</h4>
        <div class="section-subtitle">创建营销活动方案</div>
      </div>
      <div class="package-list">
        <div
          class="package-item"
          v-for="pkg in activityPackages"
          :key="pkg.id"
          :class="{ active: selectedPackageId == pkg.id && selectedPackageType == 2 }"
          @click="selectPackage(pkg.id, 2)">
          <div class="package-glow"></div>
          <div class="package-header">
            <div class="package-title">
              <span class="package-name">{{ pkg.name }}</span>
              <div class="package-tags">
                <span v-if="pkg.isHot" class="tag hot">
                  <i class="icon-fire"></i>热门
                </span>
                <span v-if="pkg.isRecommended" class="tag recommend">
                  <i class="icon-star"></i>推荐
                </span>
              </div>
            </div>
            <div class="package-price-container">
              <span class="currency">¥</span>
              <span class="package-price">{{ pkg.price }}</span>
            </div>
          </div>
          <div class="package-desc">{{ pkg.description }}</div>
          <div class="package-benefits">
            <div class="benefit-item">
              <i class="icon-activity"></i>
              <span class="package-count">可转发分享{{ pkg.countValue }}次</span>
            </div>
          </div>
          <!-- <div class="selection-indicator">
            <div class="check-circle">
              <i class="icon-check"></i>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 活动名称输入 -->
    <div class="activity-name-section" v-if="selectedPackageType == 2">
      <div class="section-header">
        <div class="section-icon name-icon"></div>
        <h4>活动信息</h4>
        <div class="section-subtitle">为您的活动起个好名字</div>
      </div>
      <div class="input-container">
        <van-field
          v-model="activityName"
          placeholder="请输入活动名称"
          maxlength="50"
          show-word-limit
          required
          class="custom-field"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        type="primary"
        size="large"
        :disabled="!canSubmit"
        @click="createOrder"
        :loading="submitting"
        class="purchase-button">
        <i class="icon-cart"></i>
        立即购买
      </van-button>
    </div>

    <!-- 业务员介绍 -->
    <div class="salesman-intro" v-if="salesman.remarks">
      <div class="section-header">
        <div class="section-icon intro-icon"></div>
        <h4>业务员介绍</h4>
        <div class="section-subtitle">了解您的专属服务顾问</div>
      </div>
      <div class="intro-content">
        <p>{{ salesman.remarks }}</p>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'SalesmanScan',
  data() {
    return {
      salesman: {},
      rechargePackages: [],
      activityPackages: [],
      selectedPackageId: null,
      selectedPackageType: null, // 1-充值套餐, 2-活动套餐
      activityName: '',
      submitting: false
    }
  },
  computed: {
    canSubmit() {
      if (this.selectedPackageType == 1) {
        // 充值套餐
        return this.selectedPackageId !== null
      } else if (this.selectedPackageType == 2) {
        // 活动套餐
        return this.selectedPackageId !== null && this.activityName.trim() !== ''
      }
      return false
    }
  },
  mounted() {
    document.title = "购买活动套餐"
    this.loadSalesmanInfo()
  },
  methods: {
    
    // 加载业务员信息和套餐
    loadSalesmanInfo() {
      const salesmanId = this.$route.query.salesmanId

      if (!salesmanId) {
        this.$toast('参数错误')
        return
      }
      const urlPackageId = this.$route.query.packageId || '';
      const urlPackageType = this.$route.query.packageType || '';

      this.$fly.get('/pyp/web/salesman/scan', {
        salesmanId
      }).then(res => {
        if (res.code == 200) {
          this.salesman = res.salesman
          this.rechargePackages = res.rechargePackages || []
          this.activityPackages = res.activityPackages || []

          // 检查URL参数，如果有指定套餐则只显示该套餐
          if (urlPackageId && urlPackageType) {
            this.filterAndSelectPackage(urlPackageId, urlPackageType)
          }
        } else {
          this.$toast(res.msg || '加载失败')
        }
      }).catch(err => {
        this.$toast('加载失败')
      })
    },

    // 过滤并选择指定套餐
    filterAndSelectPackage(packageId, packageType) {
      const packageIdNum = parseInt(packageId)
      const packageTypeNum = parseInt(packageType)


      if (packageTypeNum == 1) {
        // 充值套餐
        const originalRechargePackages = [...this.rechargePackages]
        this.rechargePackages = this.rechargePackages.filter(pkg => pkg.id == packageIdNum)
        this.activityPackages = [] // 隐藏活动套餐


        if (this.rechargePackages.length > 0) {
          this.selectPackage(packageIdNum, 1)
        } else {
          this.$toast(`未找到指定的充值套餐`)
        }
      } else if (packageTypeNum == 2) {
        // 活动套餐
        const originalActivityPackages = [...this.activityPackages]
        this.activityPackages = this.activityPackages.filter(pkg => pkg.id == packageIdNum)
        this.rechargePackages = [] // 隐藏充值套餐


        if (this.activityPackages.length > 0) {
          this.selectPackage(packageIdNum, 2)
        } else {
          this.$toast(`未找到指定的活动套餐`)
        }
      }
    },

    // 选择套餐
    selectPackage(packageId, packageType) {
      this.selectedPackageId = packageId
      this.selectedPackageType = packageType

      // 如果切换套餐类型，清空活动名称
      if (packageType !== 2) {
        this.activityName = ''
      }
    },

    // 处理头像加载错误
    handleImageError(event) {
      // 隐藏错误的图片，显示默认头像
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },

    // 创建订单
    createOrder() {
      if (!this.canSubmit) {
        return
      }

      this.submitting = true
      
      const params = {
        salesmanId: this.$route.query.salesmanId,
        packageId: this.selectedPackageId
      }

      // 如果是活动套餐，需要传入活动名称
      if (this.selectedPackageType == 2) {
        params.activityName = this.activityName.trim()
      }

      this.$fly.get('/pyp/web/salesman/createOrder', params)
        .then(res => {
          if (res.code == 200) {
            // 跳转到支付页面
            if (res.orderType == 2) {
              // 充值订单，跳转到充值支付页面
              this.$router.push({
                name: 'rechargePayment',
                query: {
                  orderId: res.orderId,
                  from: 'salesman'
                }
              })
            } else if (res.orderType == 3) {
              // 创建活动订单，跳转到活动支付页面
              this.$router.push({
                name: 'rechargePayment', // 暂时使用充值支付页面
                query: {
                  orderId: res.orderId,
                  from: 'salesman',
                  type: 'activity'
                }
              })
            }
          } else {
            this.$toast(res.msg || '创建订单失败')
          }
        })
        .catch(err => {
          console.error('创建订单失败:', err)
          this.$toast('创建订单失败')
        })
        .finally(() => {
          this.submitting = false
        })
    }
  }
}
</script>

<style scoped>
.salesman-scan {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 顶部装饰背景 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;
}

/* 业务员信息卡片 */
.salesman-info {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  padding: 24px;
  margin: 80px 16px 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  z-index: 1;
  overflow: hidden;
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.1); }
}

.avatar-container {
  position: relative;
  margin-right: 20px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 3px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  background: white;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32px;
}

.icon-user::before {
  content: '👤';
}

.avatar-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  background-clip: border-box;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.online-status {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  background: #10b981;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.info {
  flex: 1;
  z-index: 2;
}

.info h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.position-tag {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 20px;
  width: fit-content;
}

.position-tag .icon-badge::before {
  content: '🏢';
  margin-right: 6px;
}

.position-tag span {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.contact-info {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 14px;
}

.contact-info .icon-phone::before {
  content: '📞';
  margin-right: 8px;
}

.trust-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 16px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
  z-index: 2;
}

.trust-badge .icon-verified::before {
  content: '✓';
  font-size: 16px;
  margin-bottom: 4px;
  display: block;
}

/* 通用卡片样式 */
.package-section,
.activity-name-section,
.action-buttons,
.salesman-intro {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  margin: 0 16px 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

/* 区域标题样式 */
.section-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
}

.section-header h4 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.section-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.recharge-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.recharge-icon::before {
  content: '💰';
  font-size: 14px;
}

.activity-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.activity-icon::before {
  content: '🎯';
  font-size: 14px;
}

.name-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.name-icon::before {
  content: '✏️';
  font-size: 14px;
}

.intro-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.intro-icon::before {
  content: '👤';
  font-size: 14px;
}

.section-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 套餐列表样式 */
.package-list {
  padding: 16px 24px 24px;
}

.package-item {
  position: relative;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  overflow: hidden;
}

.package-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: #cbd5e1;
}

.package-item:hover::before {
  opacity: 1;
}

.package-item.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 16px 32px rgba(102, 126, 234, 0.2);
}

.package-item.active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.package-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-item.active .package-glow {
  opacity: 1;
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.1; }
  100% { transform: scale(1.1); opacity: 0.2; }
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  position: relative;
  z-index: 2;
}

.package-title {
  flex: 1;
}

.package-name {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  display: block;
}

.package-tags {
  display: flex;
  gap: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.tag i {
  margin-right: 4px;
  font-style: normal;
}

.tag.hot {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.tag.hot .icon-fire::before {
  content: '🔥';
}

.tag.recommend {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.tag.recommend .icon-star::before {
  content: '⭐';
}

.package-price-container {
  text-align: right;
  position: relative;
  z-index: 2;
}

.currency {
  font-size: 16px;
  font-weight: 600;
  color: #ef4444;
  vertical-align: top;
}

.package-price {
  font-size: 28px;
  font-weight: 800;
  color: #ef4444;
  text-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.package-desc {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.package-benefits {
  margin-bottom: 16px;
  position: relative;
  z-index: 2;
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 10px;
  border-left: 4px solid #0ea5e9;
}

.benefit-item i {
  margin-right: 8px;
  font-style: normal;
}

.icon-count::before {
  content: '🎫';
}

.icon-activity::before {
  content: '🎪';
}

.package-count {
  font-size: 14px;
  color: #0369a1;
  font-weight: 600;
}

.selection-indicator {
  position: absolute;
  bottom: 16px;
  left: 16px;
  z-index: 3;
}

.check-circle {
  width: 24px;
  height: 24px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
}

.package-item.active .check-circle {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scale(1.1);
}

.check-circle .icon-check {
  font-size: 12px;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.package-item.active .check-circle .icon-check {
  opacity: 1;
}

.icon-check::before {
  content: '✓';
  font-weight: bold;
}

/* 活动名称输入区域 */
.input-container {
  padding: 0 24px 24px;
}

.custom-field {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 12px;
  overflow: hidden;
}

.custom-field :deep(.van-field__control) {
  background: transparent;
  font-size: 16px;
  color: #1f2937;
  padding: 16px;
}

.custom-field :deep(.van-field__control::placeholder) {
  color: #9ca3af;
}

.custom-field :deep(.van-field__word-limit) {
  color: #6b7280;
  font-size: 12px;
  padding: 0 16px 12px;
}

/* 操作按钮区域 */
.action-buttons {
  padding: 24px;
  margin-bottom: 24px;
}

.purchase-button {
  width: 100%;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 18px;
  font-weight: 700;
  color: white;
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.purchase-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.purchase-button:hover::before {
  left: 100%;
}

.purchase-button:active {
  transform: translateY(2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.purchase-button:disabled {
  background: linear-gradient(135deg, #d1d5db, #9ca3af);
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.purchase-button .icon-cart {
  margin-right: 8px;
  font-style: normal;
}

.purchase-button .icon-cart::before {
  content: '🛒';
}

/* 业务员介绍区域 */
.intro-content {
  padding: 0 24px 24px;
}

.intro-content p {
  margin: 0;
  font-size: 15px;
  color: #4b5563;
  line-height: 1.7;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #667eea;
  position: relative;
}

.intro-content p::before {
  content: '"';
  position: absolute;
  top: 8px;
  left: 12px;
  font-size: 24px;
  color: #667eea;
  font-weight: bold;
  opacity: 0.5;
}

.intro-content p::after {
  content: '"';
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 24px;
  color: #667eea;
  font-weight: bold;
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .salesman-info {
    margin: 70px 12px 20px;
    padding: 20px;
  }

  .package-section,
  .activity-name-section,
  .action-buttons,
  .salesman-intro {
    margin: 0 12px 16px;
  }

  .avatar {
    width: 70px;
    height: 70px;
  }

  .info h3 {
    font-size: 20px;
  }

  .package-price {
    font-size: 24px;
  }

  .purchase-button {
    height: 50px;
    font-size: 16px;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.salesman-info,
.package-section,
.activity-name-section,
.action-buttons,
.salesman-intro {
  animation: fadeInUp 0.6s ease-out;
}

.package-section {
  animation-delay: 0.1s;
}

.activity-name-section {
  animation-delay: 0.2s;
}

.action-buttons {
  animation-delay: 0.3s;
}

.salesman-intro {
  animation-delay: 0.4s;
}
</style>
