<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
    <el-form-item label="到款金额" prop="price">
              <el-input v-model="dataForm.price" placeholder="到款金额"></el-input>
          </el-form-item>
    <el-form-item label="备注" prop="remarks">
              <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
          </el-form-item>
    <el-form-item label="结算表ID" prop="activitySettleId">
              <el-input v-model="dataForm.activitySettleId" placeholder="结算表ID"></el-input>
          </el-form-item>
    <el-form-item label="银行账户ID" prop="priceBankId">
              <el-input v-model="dataForm.priceBankId" placeholder="银行账户ID"></el-input>
          </el-form-item>
    <el-form-item label="支付时间" prop="payTime">
              <el-date-picker v-model="dataForm.payTime" type="datetime" placeholder="请选择支付时间"
                        value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
          </el-form-item>
    <el-form-item label="开票类型" prop="invoiceBillType">
              <el-input v-model="dataForm.invoiceBillType" placeholder="开票类型"></el-input>
          </el-form-item>
    <el-form-item label="发票类型" prop="invoiceType">
              <el-input v-model="dataForm.invoiceType" placeholder="发票类型"></el-input>
          </el-form-item>
    <el-form-item label="红冲数据" prop="redActivitySettleInvoiceLogId">
              <el-input v-model="dataForm.redActivitySettleInvoiceLogId" placeholder="红冲数据"></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
    
          price: '',
    
          remarks: '',
        
          activitySettleId: '',
                    
          priceBankId: '',
    
          payTime: '',
    
          invoiceBillType: '',
    
          invoiceType: '',
    
          redActivitySettleInvoiceLogId: ''
        },
        dataRule: {
          price: [
            { required: true, message: '到款金额不能为空', trigger: 'blur' }
          ],
          remarks: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          activitySettleId: [
            { required: true, message: '结算表ID不能为空', trigger: 'blur' }
          ],
          priceBankId: [
            { required: true, message: '银行账户ID不能为空', trigger: 'blur' }
          ],
          payTime: [
            { required: true, message: '支付时间不能为空', trigger: 'blur' }
          ],
          invoiceBillType: [
            { required: true, message: '开票类型不能为空', trigger: 'blur' }
          ],
          invoiceType: [
            { required: true, message: '发票类型不能为空', trigger: 'blur' }
          ],
          redActivitySettleInvoiceLogId: [
            { required: true, message: '红冲数据不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activitysettleinvoicelog/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.price = data.activitySettleInvoiceLog.price
                this.dataForm.remarks = data.activitySettleInvoiceLog.remarks
                this.dataForm.activitySettleId = data.activitySettleInvoiceLog.activitySettleId
                this.dataForm.priceBankId = data.activitySettleInvoiceLog.priceBankId
                this.dataForm.payTime = data.activitySettleInvoiceLog.payTime
                this.dataForm.invoiceBillType = data.activitySettleInvoiceLog.invoiceBillType
                this.dataForm.invoiceType = data.activitySettleInvoiceLog.invoiceType
                this.dataForm.redActivitySettleInvoiceLogId = data.activitySettleInvoiceLog.redActivitySettleInvoiceLogId
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activitysettleinvoicelog/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                            'price': this.dataForm.price,
                            'remarks': this.dataForm.remarks,
                                    'appid': this.$cookie.get('appid'),

                            'activitySettleId': this.dataForm.activitySettleId,
                                                                                                                            'priceBankId': this.dataForm.priceBankId,
                            'payTime': this.dataForm.payTime,
                            'invoiceBillType': this.dataForm.invoiceBillType,
                            'invoiceType': this.dataForm.invoiceType,
                            'redActivitySettleInvoiceLogId': this.dataForm.redActivitySettleInvoiceLogId,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
