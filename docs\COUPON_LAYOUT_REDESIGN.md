# 团购券布局重新设计文档

## 设计目标

将原本臃肿的横向滑动卡片布局改为更紧凑的左图右文列表布局，提升用户体验和空间利用率。

## 布局变化

### 原布局（横向滑动卡片）
```
┌─────────────────────────────────────────────────┐
│ [滑动容器]                                      │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐            │
│ │ ┌─────┐ │ │ ┌─────┐ │ │ ┌─────┐ │            │
│ │ │图片 │ │ │ │图片 │ │ │ │图片 │ │ ...        │
│ │ └─────┘ │ │ └─────┘ │ │ └─────┘ │            │
│ │ 标题    │ │ 标题    │ │ 标题    │            │
│ │ 价格    │ │ 价格    │ │ 价格    │            │
│ │ 平台    │ │ 平台    │ │ 平台    │            │
│ └─────────┘ └─────────┘ └─────────┘            │
└─────────────────────────────────────────────────┘
```

### 新布局（左图右文列表）
```
┌─────────────────────────────────────────────────┐
│ ┌─────┐ 团购券标题                          ➤   │
│ │图片 │ ¥99 ¥199 [抖音团购]                    │
│ └─────┘                                         │
├─────────────────────────────────────────────────┤
│ ┌─────┐ 另一个团购券                        ➤   │
│ │图片 │ ¥88 ¥168 [美团团购]                    │
│ └─────┘                                         │
├─────────────────────────────────────────────────┤
│ ┌─────┐ 第三个团购券                        ➤   │
│ │图片 │ ¥66 ¥128 [大众点评]                    │
│ └─────┘                                         │
└─────────────────────────────────────────────────┘
```

## 技术实现

### 1. HTML 结构变化

**原结构（横向滑动）：**
```vue
<van-swipe :show-indicators="false" :width="280" :loop="false">
  <van-swipe-item v-for="coupon in groupBuyingCoupons" :key="coupon.id">
    <div class="coupon-card">
      <div class="coupon-image">
        <img :src="coupon.coverImage">
      </div>
      <div class="coupon-content">
        <!-- 内容 -->
      </div>
    </div>
  </van-swipe-item>
</van-swipe>
```

**新结构（列表布局）：**
```vue
<div class="coupon-list">
  <div v-for="coupon in groupBuyingCoupons.slice(0, 3)" :key="coupon.id" 
       class="coupon-item" @click="jumpToGroupBuying(coupon)">
    <div class="coupon-image">
      <img :src="coupon.coverImage" :alt="coupon.couponName">
    </div>
    <div class="coupon-content">
      <div class="coupon-name">{{ coupon.couponName }}</div>
      <div class="coupon-price">
        <span class="current-price">¥{{ coupon.groupPrice }}</span>
        <span class="original-price">¥{{ coupon.originalPrice }}</span>
      </div>
      <div class="coupon-platform">{{ getPlatformName(coupon.platformType) }}</div>
    </div>
    <div class="coupon-arrow">
      <van-icon name="arrow" />
    </div>
  </div>
</div>
```

### 2. CSS 样式重构

#### 容器样式
```css
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
```

#### 单项样式
```css
.coupon-item {
  background: white;
  border-radius: 8px;
  border: 1px solid #eee;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
```

#### 图片样式
```css
.coupon-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}
```

#### 内容样式
```css
.coupon-content {
  flex: 1;
  min-width: 0;
}

.coupon-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
```

#### 箭头样式
```css
.coupon-arrow {
  color: #ccc;
  flex-shrink: 0;
}
```

## 设计优势

### 1. 空间利用率提升
- **垂直布局**: 充分利用屏幕宽度，避免横向滑动的空间浪费
- **紧凑设计**: 每个团购券占用更少的垂直空间
- **信息密度**: 在相同区域内展示更多信息

### 2. 用户体验改善
- **一目了然**: 用户无需滑动即可看到多个团购券
- **操作简单**: 直接点击即可，无需先滑动再点击
- **视觉清晰**: 左图右文的布局符合用户阅读习惯

### 3. 移动端适配
- **响应式设计**: 在小屏幕设备上表现更好
- **触摸友好**: 更大的点击区域，更好的触摸体验
- **加载性能**: 减少了滑动组件的复杂度

## 功能特性

### 1. 显示逻辑
- **数量限制**: 最多显示3个团购券 `groupBuyingCoupons.slice(0, 3)`
- **查看全部**: 超过3个时显示"查看全部"按钮
- **动态加载**: 根据实际数据动态渲染

### 2. 交互设计
- **点击反馈**: 点击时有缩放和阴影效果
- **箭头指示**: 右侧箭头提示可点击
- **状态变化**: 激活状态的视觉反馈

### 3. 内容展示
- **图片优化**: 60x60px 的紧凑尺寸
- **文字层次**: 标题、价格、平台的清晰层次
- **价格对比**: 当前价格突出显示，原价删除线

## 响应式适配

### 小屏幕优化（≤375px）
```css
@media (max-width: 375px) {
  .coupon-item {
    padding: 10px;
    gap: 10px;
  }
  
  .coupon-image {
    width: 50px;
    height: 50px;
  }
  
  .coupon-name {
    font-size: 13px;
  }
  
  .current-price {
    font-size: 15px;
  }
}
```

### 适配特点
- **图片缩小**: 50x50px 适应小屏幕
- **间距调整**: 减少内边距和间隙
- **字体缩放**: 适当缩小字体大小
- **保持可读性**: 确保在小屏幕上仍然清晰可读

## 性能优化

### 1. 渲染性能
- **移除滑动组件**: 减少了 van-swipe 组件的开销
- **简化DOM结构**: 更少的嵌套层级
- **CSS优化**: 使用 flexbox 布局，性能更好

### 2. 交互性能
- **减少重绘**: 简化的动画效果
- **触摸优化**: 更大的点击区域，减少误触
- **加载速度**: 更快的首屏渲染

## 兼容性考虑

### 1. 浏览器兼容
- **Flexbox支持**: 现代移动浏览器全面支持
- **CSS3特性**: 圆角、阴影等特性兼容性良好
- **响应式布局**: 媒体查询支持广泛

### 2. 设备兼容
- **iOS Safari**: 完美支持
- **Android Chrome**: 完美支持
- **微信内置浏览器**: 完美支持
- **其他移动浏览器**: 良好支持

## 数据展示策略

### 1. 内容优先级
1. **团购券名称**: 最重要，加粗显示
2. **价格信息**: 次重要，突出当前价格
3. **平台标识**: 辅助信息，小字显示

### 2. 文字处理
- **标题截断**: 单行显示，超出部分省略
- **价格格式**: 统一的货币符号和字体
- **平台标签**: 背景色区分，易于识别

### 3. 图片处理
- **尺寸统一**: 60x60px 的固定尺寸
- **圆角设计**: 6px 圆角，更加美观
- **占位处理**: 无图片时的默认处理

## 总结

新的团购券布局设计实现了以下目标：

1. **解决臃肿问题**: 从横向滑动改为垂直列表，更加紧凑
2. **提升用户体验**: 一屏显示多个选项，操作更便捷
3. **优化视觉效果**: 左图右文布局，符合用户习惯
4. **增强响应式**: 更好的移动端适配和性能表现

这种设计既保持了功能的完整性，又大大改善了界面的美观性和易用性。
