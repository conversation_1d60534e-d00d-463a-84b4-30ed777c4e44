# AI标签图片匹配功能修正版

## 修正说明

根据用户反馈，对AI标签图片匹配功能进行了重要修正：

1. **图片AI标签来源修正**: 图片的AI标签应该从`tb_activity`表的`ai_tag`字段中选择，而不是自由输入
2. **新增批量设置功能**: 实现了图片批量设置AI标签的功能

## 功能实现

### 1. 图片AI标签选择逻辑 ✅

#### 修正前的问题
- 使用`tags-editorlist`组件自由输入标签
- 图片标签与活动标签不一致

#### 修正后的实现
- 从活动的AI标签中选择（下拉多选）
- 确保图片标签与活动标签的一致性
- 支持不选择任何标签（表示通用图片）

#### 技术实现
```vue
<!-- 图片编辑界面 -->
<el-select
  v-model="selectedAiTags"
  multiple
  placeholder="从活动AI标签中选择"
  style="width: 100%">
  <el-option
    v-for="tag in activityAiTags"
    :key="tag"
    :label="tag"
    :value="tag">
  </el-option>
</el-select>
```

#### API接口统一
```javascript
// 统一使用专门的AI标签获取接口
loadActivityAiTags(activityId) {
  this.$http({
    url: this.$http.adornUrl('/web/activity/activitytext/getAiTags'),
    method: 'get',
    params: { activityId: activityId }
  }).then(({ data }) => {
    if (data && data.code === 200) {
      this.activityAiTags = data.tags || []
    }
  })
}
```

### 2. 批量设置AI标签功能 ✅

#### 功能特性
- **批量选择**: 支持选择多张图片进行批量操作
- **标签选择**: 从活动AI标签中选择要设置的标签
- **通用图片**: 不选择任何标签表示设置为通用图片
- **权限控制**: 需要图片更新权限

#### 界面实现
```vue
<!-- 批量操作按钮 -->
<el-button 
  type="warning" 
  @click="batchSetAiTagHandle()"
  :disabled="dataListSelections.length <= 0">
  批量设置AI标签
</el-button>

<!-- 批量设置弹窗 -->
<el-dialog title="批量设置AI标签" :visible.sync="batchAiTagVisible">
  <el-select v-model="batchAiTagForm.selectedTags" multiple>
    <el-option v-for="tag in activityAiTags" :key="tag" :label="tag" :value="tag">
    </el-option>
  </el-select>
</el-dialog>
```

#### 后端API
```java
@RequestMapping("/batchSetAiTag")
@RequiresPermissions("activity:activityimage:update")
@Transactional(rollbackFor = Exception.class)
public R batchSetAiTag(@RequestBody Map<String, Object> params) {
    List<Long> imageIds = (List<Long>) params.get("imageIds");
    String aiTag = (String) params.get("aiTag");
    
    // 批量更新图片的AI标签
    for (Long imageId : imageIds) {
        ActivityImageEntity image = new ActivityImageEntity();
        image.setId(imageId);
        image.setAiTag(aiTag);
        image.setUpdateBy(getUserId());
        activityImageService.updateById(image);
    }
    
    return R.ok("批量设置AI标签成功");
}
```

### 3. 数据流程图

```
活动管理
├── 设置活动AI标签: "男,女,儿童"
└── 保存到 tb_activity.ai_tag

图片管理
├── 加载活动AI标签
├── 单个图片编辑
│   ├── 从活动标签中选择: ["男", "女"]
│   └── 保存到 activity_image.ai_tag: "男,女"
└── 批量设置
    ├── 选择多张图片
    ├── 从活动标签中选择: ["儿童"]
    └── 批量更新 activity_image.ai_tag: "儿童"

文案生成
├── 用户选择AI标签: "男"
├── 筛选图片逻辑:
│   ├── 优先: ai_tag LIKE "%男%"
│   ├── 回退: ai_tag IS NULL OR ai_tag = ""
│   └── 兜底: 所有图片
└── 返回匹配的图片和文案
```

## 使用流程

### 管理员配置流程

1. **配置活动AI标签**
   ```
   活动管理 → 编辑活动 → AI标签字段 → 输入"男,女,儿童"
   ```

2. **配置图片AI标签（单个）**
   ```
   图片管理 → 编辑图片 → AI标签下拉框 → 选择["男", "女"]
   ```

3. **配置图片AI标签（批量）**
   ```
   图片管理 → 选择多张图片 → 批量设置AI标签 → 选择["儿童"]
   ```

### 用户使用流程

1. **查看文案**
   ```
   用户访问活动页面 → 点击平台按钮 → 查看文案和图片
   ```

2. **智能换一篇**
   ```
   点击"换一篇" → 选择目标受众"男" → 生成男性相关的文案和图片
   ```

## 配置示例

### 理发店完整配置

#### 步骤1: 活动AI标签配置
```
活动名称: SO HAIR理发店
AI标签: 男,女,儿童,老人
```

#### 步骤2: 图片AI标签配置
```
男士发型1.jpg → 选择: [男]
男士发型2.jpg → 选择: [男]
女士发型1.jpg → 选择: [女]
女士发型2.jpg → 选择: [女]
儿童发型1.jpg → 选择: [儿童]
老人发型1.jpg → 选择: [老人]
店面环境.jpg → 不选择（通用图片）
理发工具.jpg → 不选择（通用图片）
```

#### 步骤3: 批量设置示例
```
选择: [男士发型1.jpg, 男士发型2.jpg, 男士发型3.jpg]
批量设置AI标签: [男]
结果: 3张图片都设置为"男"标签
```

### 效果验证

#### 用户选择"男"标签
- **优先使用**: 男士发型1.jpg, 男士发型2.jpg
- **回退使用**: 店面环境.jpg, 理发工具.jpg（如果男士图片不够）

#### 用户选择"女"标签
- **优先使用**: 女士发型1.jpg, 女士发型2.jpg
- **回退使用**: 店面环境.jpg, 理发工具.jpg（如果女士图片不够）

#### 用户选择"通用文案"
- **优先使用**: 店面环境.jpg, 理发工具.jpg
- **回退使用**: 所有图片（如果通用图片不够）

## 技术优势

### 1. 数据一致性
- 图片标签来源于活动标签，确保数据一致性
- 避免了标签不匹配的问题

### 2. 用户体验
- 下拉选择比自由输入更直观
- 批量操作提高配置效率
- 智能回退确保总能找到合适图片

### 3. 维护性
- 统一的标签管理，便于维护
- 清晰的数据流向，便于调试

## 测试建议

### 功能测试
1. **活动标签配置**: 测试活动AI标签的设置和保存
2. **图片标签选择**: 测试从活动标签中选择图片标签
3. **批量设置**: 测试批量设置多张图片的AI标签
4. **图片筛选**: 测试不同AI标签的图片筛选效果

### 边界测试
1. **无活动标签**: 测试活动未配置AI标签时的处理
2. **无图片标签**: 测试图片未设置AI标签时的回退逻辑
3. **标签不匹配**: 测试图片标签在活动标签中不存在的情况

### 性能测试
1. **大量图片**: 测试大量图片时的批量设置性能
2. **复杂查询**: 测试复杂AI标签筛选的查询性能

## 注意事项

1. **权限控制**: 确保只有有权限的用户才能批量设置AI标签
2. **数据校验**: 确保选择的标签都来自活动的AI标签列表
3. **事务处理**: 批量操作使用事务确保数据一致性
4. **错误处理**: 提供友好的错误提示和回滚机制

## API接口汇总

### 1. 获取活动AI标签
```
GET /web/activity/activitytext/getAiTags?activityId={activityId}

响应:
{
  "code": 200,
  "tags": ["男", "女", "儿童", "老人"]
}
```

### 2. 批量设置图片AI标签
```
POST /activity/activityimage/batchSetAiTag

请求体:
{
  "imageIds": [1, 2, 3],
  "aiTag": "男,女"
}

响应:
{
  "code": 200,
  "msg": "批量设置AI标签成功"
}
```

### 3. 重新生成文案（支持AI标签）
```
GET /web/activity/review/regenerate/{platform}?activityId={activityId}&aiTag={aiTag}

支持的平台:
- douyin: 抖音点评
- dianping: 大众点评
- meituan: 美团点评
- ctrip: 携程点评/笔记
```

### 4. 图片筛选服务方法
```java
// 根据AI标签筛选图片
List<ActivityImageEntity> findByActivityIdAndAiTag(Long activityId, String aiTag, Integer limit)

// 根据AI标签和平台筛选未使用图片
List<ActivityImageEntity> findByActivityIdAndAiTagNoUseLimitByPlatform(
    Long activityId, String aiTag, String platform, Integer limit)
```

## 扩展计划

1. **智能推荐**: 根据图片内容自动推荐合适的AI标签
2. **标签统计**: 统计各AI标签的图片数量和使用频率
3. **模板配置**: 为不同行业预设AI标签和图片配置模板
4. **导入导出**: 支持AI标签配置的批量导入导出
