<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      
      
      <el-form-item label="客户付" prop="clientPrice" >
            <el-input v-model="dataForm.clientPrice" placeholder="客户付"></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        repeatToken: "",
        id: 0,
        clientPrice:  0,
      },
      dataRule: {
        name: [
          {
            required: true,
            message: "名称不能为空",
            trigger: "blur",
          },
        ],
        priceConfigId: [
          {
            required: true,
            message: "科目不能为空",
            trigger: "blur",
          },
        ],
        price: [
          {
            required: true,
            message: "到款金额不能为空",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "收款or付款不能为空",
            trigger: "blur",
          },
        ],
        payTime: [
          {
            required: true,
            message: "预计支付时间不能为空",
            trigger: "blur",
          },
        ],
        clientPrice: [
          {
            required: true,
            message: "客户付不能为空",
            trigger: "blur",
          },
        ],
        activityId: [
          {
            required: true,
            message: "活动ID不能为空",
            trigger: "blur",
          },
        ],
        priceStatus: [
          {
            required: true,
            message: "收/付款状态不能为空",
            trigger: "blur",
          },
        ],
        arrivePrice: [
          {
            required: true,
            message: "到款金额不能为空",
            trigger: "blur",
          },
        ],
        invoiceStatus: [
          {
            required: true,
            message: "开票状态不能为空",
            trigger: "blur",
          },
        ],
        invoice: [
          {
            required: true,
            message: "开票金额不能为空",
            trigger: "blur",
          },
        ],
        arriveInvoice: [
          {
            required: true,
            message: "已开票金额不能为空",
            trigger: "blur",
          },
        ],
        isInvoice: [
          {
            required: true,
            message: "是否发票不能为空",
            trigger: "blur",
          },
        ],
        invoiceType: [
          {
            required: true,
            message: "发票类型不能为空",
            trigger: "blur",
          },
        ],
        refundPrice: [
          {
            required: true,
            message: "退款金额不能为空",
            trigger: "blur",
          },
        ],
        redInvoice: [
          {
            required: true,
            message: "已红冲金额不能为空",
            trigger: "blur",
          },
        ],
        invoiceBillType: [
          {
            required: true,
            message: "开票类型不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    init(id,activityId) {
      this.getToken();
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.dataForm.activityId = activityId;
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activitysettle/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.clientPrice =
                data.activitySettle.clientPrice;
            }
          });
      
      });
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activitysettle/onlyUpdate`
            ),
            method: "post",
            data: this.$http.adornData({
              repeatToken: this.dataForm.repeatToken,
              id: this.dataForm.id || undefined,
              clientPrice: this.dataForm.clientPrice,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
              if (data.msg != "不能重复提交") {
                this.getToken();
              }
            }
          });
        }
      });
    },
  },
};
</script>
