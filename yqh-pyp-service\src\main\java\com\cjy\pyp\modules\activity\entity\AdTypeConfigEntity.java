package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 广告类型配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
@Data
@TableName("ad_type_config")
@Accessors(chain = true)
public class AdTypeConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long id;
    
    /**
     * 类型编码（如：douyin, xiaohongshu等）
     */
    private String typeCode;
    
    /**
     * 类型名称（如：抖音, 小红书等）
     */
    private String typeName;
    
    /**
     * 平台名称
     */
    private String platform;
    
    /**
     * 内容类型（如：短视频, 图文等）
     */
    private String contentType;
    
    /**
     * 标题长度要求
     */
    private String titleLength;
    
    /**
     * 内容长度要求
     */
    private String contentLength;
    
    /**
     * 话题数量
     */
    private Integer topicsCount;
    
    /**
     * 话题格式要求
     */
    private String topicsFormat;
    
    /**
     * 内容要求（多行文本）
     */
    private String requirements;
    
    /**
     * 风格特点
     */
    private String style;
    
    /**
     * Prompt模板
     */
    private String promptTemplate;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;
}
