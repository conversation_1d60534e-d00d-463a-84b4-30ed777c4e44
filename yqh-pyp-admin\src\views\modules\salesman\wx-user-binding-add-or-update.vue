<template>
  <el-dialog :title="!dataForm.id ? '新增绑定' : '修改绑定'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="微信用户" prop="wxUserId">
        <el-select v-model="dataForm.wxUserId" placeholder="请选择微信用户" filterable remote :remote-method="searchWxUsers"
          :loading="wxUserLoading" style="width: 100%;">
          <el-option v-for="user in wxUserOptions" :key="user.id"
            :label="`${user.nickname} (${user.mobile || '未绑定手机'})`" :value="user.id">
            <span style="float: left">{{ user.nickname }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ user.mobile || '未绑定手机' }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="业务员" prop="salesmanId">
        <el-select v-model="dataForm.salesmanId" placeholder="请选择业务员" filterable remote :remote-method="searchSalesmen"
          :loading="salesmanLoading" style="width: 100%;">
          <el-option v-for="salesman in salesmanOptions" :key="salesman.id"
            :label="`${salesman.name} (${salesman.code})`" :value="salesman.id">
            <span style="float: left">{{ salesman.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ salesman.code }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="绑定方式" prop="bindingType">
        <el-select v-model="dataForm.bindingType" placeholder="请选择绑定方式">
          <el-option label="二维码扫描" :value="1"></el-option>
          <el-option label="邀请链接" :value="2"></el-option>
          <el-option label="手动绑定" :value="3"></el-option>
          <el-option label="系统分配" :value="4"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="绑定来源" prop="bindingSource">
        <el-input v-model="dataForm.bindingSource" placeholder="绑定来源（可选）"></el-input>
      </el-form-item>

      <el-form-item label="生效时间" prop="effectiveTime">
        <el-date-picker v-model="dataForm.effectiveTime" type="datetime" placeholder="选择生效时间"
          format="yyyy/MM/dd HH:mm:ss" value-format="yyyy/MM/dd HH:mm:ss" style="width: 100%;">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="失效时间" prop="expiryTime">
        <el-date-picker v-model="dataForm.expiryTime" type="datetime" placeholder="选择失效时间（不选择表示永久有效）"
          format="yyyy/MM/dd HH:mm:ss" value-format="yyyy/MM/dd HH:mm:ss" style="width: 100%;">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="优先级" prop="priority">
        <el-input-number v-model="dataForm.priority" :min="1" :max="100" placeholder="优先级（数字越大优先级越高）"></el-input-number>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">有效</el-radio>
          <el-radio :label="0">已失效</el-radio>
          <el-radio :label="2">已解绑</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" :rows="3" placeholder="备注信息（可选）">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        wxUserId: '',
        salesmanId: '',
        bindingType: 3, // 默认手动绑定
        bindingSource: '',
        effectiveTime: '',
        expiryTime: '',
        priority: 1,
        status: 1,
        remarks: ''
      },
      dataRule: {
        wxUserId: [
          { required: true, message: '微信用户不能为空', trigger: 'change' }
        ],
        salesmanId: [
          { required: true, message: '业务员不能为空', trigger: 'change' }
        ],
        bindingType: [
          { required: true, message: '绑定方式不能为空', trigger: 'change' }
        ]
      },
      wxUserOptions: [],
      salesmanOptions: [],
      wxUserLoading: false,
      salesmanLoading: false
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()

        // 初始化时间
        if (!this.dataForm.id) {
          this.dataForm.effectiveTime = this.$moment().format('YYYY/MM/DD HH:mm:ss')
        }

        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/salesman/wxuserbinding/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.wxUserId = data.binding.wxUserId
              this.dataForm.salesmanId = data.binding.salesmanId
              this.dataForm.bindingType = data.binding.bindingType
              this.dataForm.bindingSource = data.binding.bindingSource
              this.dataForm.effectiveTime = data.binding.effectiveTime
              this.dataForm.expiryTime = data.binding.expiryTime
              this.dataForm.priority = data.binding.priority
              this.dataForm.status = data.binding.status
              this.dataForm.remarks = data.binding.remarks

              // 加载已选择的用户和业务员信息
              if (data.binding.wxUserId) {
                this.loadWxUserInfo(data.binding.wxUserId)
              }
              if (data.binding.salesmanId) {
                this.loadSalesmanInfo(data.binding.salesmanId)
              }
            }
          })
        }
      })
    },

    // 搜索微信用户
    searchWxUsers(query) {
      if (query !== '') {
        this.wxUserLoading = true
        this.$http({
          url: this.$http.adornUrl('/wxUser/search'),
          method: 'get',
          params: this.$http.adornParams({
            keyword: query,
            limit: 20
          })
        }).then(({ data }) => {
          this.wxUserLoading = false
          if (data && data.code === 200) {
            this.wxUserOptions = data.list || []
          }
        }).catch(() => {
          this.wxUserLoading = false
        })
      } else {
        this.wxUserOptions = []
      }
    },

    // 搜索业务员
    searchSalesmen(query) {
      if (query !== '') {
        this.salesmanLoading = true
        this.$http({
          url: this.$http.adornUrl('/salesman/salesman/search'),
          method: 'get',
          params: this.$http.adornParams({
            keyword: query,
            limit: 20
          })
        }).then(({ data }) => {
          this.salesmanLoading = false
          if (data && data.code === 200) {
            this.salesmanOptions = data.list || []
          }
        }).catch(() => {
          this.salesmanLoading = false
        })
      } else {
        this.salesmanOptions = []
      }
    },

    // 加载微信用户信息
    loadWxUserInfo(wxUserId) {
      this.$http({
        url: this.$http.adornUrl(`/wxUser/info/${wxUserId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxUserOptions = [data.user]
        }
      })
    },

    // 加载业务员信息
    loadSalesmanInfo(salesmanId) {
      this.$http({
        url: this.$http.adornUrl(`/salesman/salesman/info/${salesmanId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.salesmanOptions = [data.salesman]
        }
      })
    },

    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/salesman/wxuserbinding/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'wxUserId': this.dataForm.wxUserId,
              'salesmanId': this.dataForm.salesmanId,
              'bindingType': this.dataForm.bindingType,
              'bindingSource': this.dataForm.bindingSource,
              'effectiveTime': this.dataForm.effectiveTime,
              'expiryTime': this.dataForm.expiryTime,
              'priority': this.dataForm.priority,
              'status': this.dataForm.status,
              'remarks': this.dataForm.remarks
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
