package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金配置服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public interface SalesmanCommissionConfigService extends IService<SalesmanCommissionConfigEntity> {

    /**
     * 分页查询佣金配置列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<SalesmanCommissionConfigEntity> queryPage(Map<String, Object> params);

    /**
     * 根据业务员ID和佣金类型查询配置
     * @param salesmanId 业务员ID
     * @param commissionType 佣金类型
     * @param appid 应用ID
     * @return 佣金配置
     */
    SalesmanCommissionConfigEntity getByTypeAndSalesman(Long salesmanId, Integer commissionType, String appid);

    /**
     * 根据业务员ID查询所有有效的佣金配置
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 佣金配置列表
     */
    List<SalesmanCommissionConfigEntity> getEffectiveConfigsBySalesman(Long salesmanId, String appid);

    /**
     * 批量查询业务员的佣金配置
     * @param salesmanIds 业务员ID列表
     * @param appid 应用ID
     * @return 佣金配置列表
     */
    List<SalesmanCommissionConfigEntity> getBatchConfigsBySalesmen(List<Long> salesmanIds, String appid);

    /**
     * 保存或更新佣金配置
     * @param config 佣金配置
     * @return 是否成功
     */
    boolean saveOrUpdateConfig(SalesmanCommissionConfigEntity config);

    /**
     * 检查佣金配置是否已存在
     * @param salesmanId 业务员ID
     * @param commissionType 佣金类型
     * @param appid 应用ID
     * @param excludeId 排除的配置ID（用于编辑时排除自己）
     * @return 是否存在
     */
    boolean existsConfig(Long salesmanId, Integer commissionType, String appid, Long excludeId);

    /**
     * 批量保存佣金配置
     * @param configs 佣金配置列表
     * @return 是否成功
     */
    boolean batchSaveConfigs(List<SalesmanCommissionConfigEntity> configs);

    /**
     * 删除业务员的佣金配置
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean deleteConfigsBySalesman(Long salesmanId, String appid);

    /**
     * 复制佣金配置
     * @param fromSalesmanId 源业务员ID
     * @param toSalesmanId 目标业务员ID
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean copyConfigs(Long fromSalesmanId, Long toSalesmanId, String appid);
}
