<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="关键词" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.read" placeholder="是否已读" clearable>
          <el-option v-for="item in isRead" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activitynotify:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activitynotify:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="success" @click="readHandle()"
          :disabled="dataListSelections.length <= 0">批量已读</el-button>
      </el-form-item>
      <el-button style="float: right" type="success" @click="$router.go(-1)">返回</el-button>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <!-- <el-table-column prop="activityId" header-align="center" align="center" label="会议id">
      </el-table-column> -->
      <el-table-column prop="read" header-align="center" align="center" label="是否已读">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.read == 1" type="success">已读</el-tag>
          <el-tag v-else type="danger">未读</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="名称">
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="类型">
        <template slot-scope="scope">
          
          <el-tag
            :type="getNotifyTypeTagType(scope.row.type)"
            :class="'tag-color tag-color-' + scope.row.type"
            >{{ activityNotifyType[scope.row.type] ? activityNotifyType[scope.row.type].value : '未知类型' }}</el-tag
          >
        </template>
      </el-table-column>
      <!-- <el-table-column prop="url" header-align="center" align="center" label="链接">
      </el-table-column> -->
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="scope.row.read == 0" @click="readHandle(scope.row.id)">已读</el-button>
          <!-- <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button> -->
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import { activityNotifyType, isRead } from "@/data/activity";
import AddOrUpdate from './activitynotify-add-or-update'
export default {
  data() {
    return {
      activityNotifyType,
      isRead,
      dataForm: {
        name: '',
        appid: '',
        activityId: '',
        read: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId
    this.getDataList()
  },
  methods: {
    readHandle(id) {
      console.log(id)
      let ids = id ? (id + "") : this.dataListSelections.map(item => {
        return item.id
      }).join(",")
      console.log(ids)
      this.$http({
        url: this.$http.adornUrl('/activity/activitynotify/read'),
        method: 'get',
        params: this.$http.adornParams({
          'id': ids
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message.success('操作成功')
          this.getDataList()
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activitynotify/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.dataForm.activityId,
          'appid': this.$cookie.get('appid'),
          'read': this.dataForm.read,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activitynotify/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 获取通知类型标签颜色
    getNotifyTypeTagType(type) {
      const typeColorMap = {
        7: 'warning',  // 即将过期 - 橙色
        8: 'danger',   // 已过期 - 红色
        9: 'success',  // 续费成功 - 绿色
        default: 'primary' // 默认 - 蓝色
      };
      return typeColorMap[type] || typeColorMap.default;
    }
  }
}
</script>
