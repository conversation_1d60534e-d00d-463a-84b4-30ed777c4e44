# 团购券封面图片上传功能

## 功能概述

为团购券添加/编辑页面实现了封面图片上传功能，参考了 activity-add-or-update 页面的图片实现方式，提供了完整的图片选择、预览、更换和删除功能。

## 实现特性

### 1. 图片上传界面
- **占位符显示**: 未上传图片时显示上传占位符
- **图片预览**: 已上传图片时显示缩略图预览
- **操作按钮**: 提供更换和删除按钮
- **尺寸建议**: 显示推荐的图片尺寸和大小

### 2. 图片管理功能
- **素材库选择**: 从现有素材库中选择图片
- **本地上传**: 支持本地图片上传
- **图片预览**: 点击图片可以大图预览
- **图片删除**: 一键删除已选择的图片

### 3. 用户体验优化
- **响应式设计**: 适配不同屏幕尺寸
- **悬停效果**: 鼠标悬停时的视觉反馈
- **加载状态**: 图片加载时的状态提示
- **错误处理**: 图片加载失败的处理

## 技术实现

### 1. 组件结构

```vue
<template>
  <!-- 封面图片上传区域 -->
  <el-form-item label="封面图片" prop="coverImage">
    <div class="image-upload-container">
      <!-- 已上传图片的预览 -->
      <div v-if="coverImages.length > 0" class="image-preview">
        <img :src="coverImages[0].url" alt="封面图片" class="preview-image" @click="previewImage(coverImages[0].url)">
        <div class="image-actions">
          <el-button type="text" size="small" @click="openImageModal('coverImage')">更换</el-button>
          <el-button type="text" size="small" @click="removeCoverImage()">删除</el-button>
        </div>
      </div>
      <!-- 未上传图片的占位符 -->
      <div v-else class="upload-placeholder" @click="openImageModal('coverImage')">
        <i class="el-icon-plus"></i>
        <div>点击上传封面图片</div>
      </div>
    </div>
    <div style="color: #909399; font-size: 12px; margin-top: 5px;">建议尺寸：750*400，大小2MB以下</div>
  </el-form-item>

  <!-- 图片上传弹窗 -->
  <ImageUploadModal
    :visible.sync="imageModalVisible"
    :multiple="false"
    :max-count="1"
    :default-images="getCurrentImages()"
    @confirm="handleImageConfirm"
  />

  <!-- 图片预览弹窗 -->
  <el-dialog :visible.sync="imgDialogVisible" width="60%">
    <img width="100%" :src="dialogImageUrl" alt="">
  </el-dialog>
</template>
```

### 2. 数据定义

```javascript
data() {
  return {
    // 图片上传相关
    imageModalVisible: false,
    currentImageField: '',
    coverImages: [],
    imgDialogVisible: false,
    dialogImageUrl: '',
    
    dataForm: {
      coverImage: '', // 封面图片URL
      // ... 其他字段
    }
  }
}
```

### 3. 核心方法

```javascript
methods: {
  // 初始化图片数组
  initImageArrays() {
    if (this.dataForm.coverImage) {
      this.coverImages = [{
        url: this.dataForm.coverImage,
        name: 'cover.jpg'
      }]
    } else {
      this.coverImages = []
    }
  },

  // 打开图片选择弹窗
  openImageModal(field) {
    this.currentImageField = field
    this.imageModalVisible = true
  },

  // 获取当前字段的图片
  getCurrentImages() {
    switch (this.currentImageField) {
      case 'coverImage':
        return this.coverImages
      default:
        return []
    }
  },

  // 处理图片确认选择
  handleImageConfirm(images) {
    switch (this.currentImageField) {
      case 'coverImage':
        this.coverImages = images
        this.dataForm.coverImage = images.length > 0 ? images[0].url : ''
        break
    }
    this.imageModalVisible = false
  },

  // 预览图片
  previewImage(url) {
    this.dialogImageUrl = url
    this.imgDialogVisible = true
  },

  // 删除封面图片
  removeCoverImage() {
    this.coverImages = []
    this.dataForm.coverImage = ''
  }
}
```

### 4. 样式设计

```css
.image-upload-container {
  width: 100%;
}

.image-preview {
  position: relative;
  display: inline-block;
  width: 150px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 5px;
  text-align: center;
}

.upload-placeholder {
  width: 150px;
  height: 80px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #909399;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}
```

## 使用说明

### 管理员操作

#### 1. 上传封面图片
1. 在团购券添加/编辑页面找到"封面图片"字段
2. 点击上传占位符或"更换"按钮
3. 在弹出的图片选择弹窗中：
   - 选择"素材库"标签页从现有图片中选择
   - 选择"本地上传"标签页上传新图片
4. 选择图片后点击"确定"按钮

#### 2. 预览图片
1. 点击已上传的封面图片缩略图
2. 在弹出的预览弹窗中查看大图

#### 3. 更换图片
1. 点击图片下方的"更换"按钮
2. 重新选择新的图片

#### 4. 删除图片
1. 点击图片下方的"删除"按钮
2. 图片将被移除，显示上传占位符

### 图片规格建议

- **推荐尺寸**: 750px × 400px
- **文件大小**: 2MB以下
- **文件格式**: JPG、PNG、GIF
- **宽高比例**: 约 1.875:1 (接近黄金比例)

## 集成说明

### 1. 组件依赖
- `ImageUploadModal`: 图片上传选择组件
- `Element UI`: 基础UI组件库

### 2. 数据流程
1. **初始化**: `initImageArrays()` 根据 `dataForm.coverImage` 初始化图片数组
2. **选择图片**: 通过 `ImageUploadModal` 组件选择图片
3. **确认选择**: `handleImageConfirm()` 更新图片数组和表单数据
4. **提交保存**: 表单提交时 `dataForm.coverImage` 包含图片URL

### 3. 后端集成
- 封面图片URL存储在 `group_buying_coupon.cover_image` 字段
- 支持完整的CRUD操作
- 图片文件通过现有的OSS上传系统管理

## 扩展功能

### 1. 多图片支持
如需支持多张封面图片，可以修改：
```javascript
// 修改为支持多图片
:multiple="true"
:max-count="3"
```

### 2. 图片裁剪
可以集成图片裁剪功能：
```javascript
// 添加裁剪配置
:crop-options="{
  aspectRatio: 1.875,
  viewMode: 1
}"
```

### 3. 图片压缩
可以添加客户端图片压缩：
```javascript
// 上传前压缩
beforeUpload(file) {
  return this.compressImage(file)
}
```

## 注意事项

1. **图片尺寸**: 建议按照推荐尺寸上传，确保显示效果
2. **文件大小**: 控制文件大小，避免影响加载速度
3. **浏览器兼容**: 确保在主流浏览器中正常显示
4. **网络环境**: 考虑网络较慢时的加载体验
5. **存储空间**: 合理管理图片存储，定期清理无用图片
