package com.cjy.pyp.modules.channel.utils;

import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.sys.service.SysUserService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.wx.service.WxUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 渠道数据工具类
 * 用于自动设置实体的渠道归属
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-23
 */
@Component
public class ChannelDataUtils {

    private static final Logger logger = LoggerFactory.getLogger(ChannelDataUtils.class);

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;
    
    @Autowired
    private SalesmanService salesmanService;
    
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private WxUserService wxUserService;

    /**
     * 为活动自动设置渠道ID
     * 优先级：
     * 1. 如果创建者是微信用户且绑定了业务员，使用业务员的渠道ID
     * 2. 如果创建者是系统用户且有渠道归属，使用系统用户的渠道ID
     * 3. 否则不设置渠道ID
     * 
     * @param activity 活动实体
     * @param userId 创建者用户ID
     * @param appid 应用ID
     */
    public void setActivityChannelId(ActivityEntity activity, Long userId, String appid) {
        if (activity == null || userId == null || appid == null) {
            return;
        }

        try {
            // 1. 尝试通过微信用户业务员绑定关系获取渠道ID
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(userId, appid);
            if (binding != null && binding.getSalesmanId() != null) {
                SalesmanEntity salesman = salesmanService.getById(binding.getSalesmanId());
                if (salesman != null && salesman.getChannelId() != null) {
                    activity.setChannelId(salesman.getChannelId());
                    logger.debug("通过业务员绑定设置活动渠道ID: activityId={}, channelId={}, salesmanId={}", 
                        activity.getId(), salesman.getChannelId(), salesman.getId());
                    return;
                }
            }

            // 2. 尝试通过系统用户的渠道归属获取渠道ID
            SysUserEntity sysUser = sysUserService.getById(userId);
            if (sysUser != null && sysUser.getChannelId() != null) {
                activity.setChannelId(sysUser.getChannelId());
                logger.debug("通过系统用户设置活动渠道ID: activityId={}, channelId={}, userId={}", 
                    activity.getId(), sysUser.getChannelId(), userId);
                return;
            }

            logger.debug("未找到用户的渠道归属，活动不设置渠道ID: userId={}, appid={}", userId, appid);
            
        } catch (Exception e) {
            logger.warn("自动设置活动渠道ID失败: userId={}, appid={}, error={}", userId, appid, e.getMessage());
        }
    }

    /**
     * 检查用户是否有权限访问指定渠道的数据
     * 
     * @param userId 用户ID
     * @param targetChannelId 目标渠道ID
     * @return 是否有权限
     */
    public boolean hasChannelAccess(Long userId, Long targetChannelId) {
        if (userId == null || targetChannelId == null) {
            return false;
        }

        try {
            // 获取用户信息
            SysUserEntity user = sysUserService.getById(userId);
            if (user == null) {
                return false;
            }

            // 如果用户没有渠道归属，表示是系统管理员，有全部权限
            if (user.getChannelId() == null) {
                return true;
            }

            // 检查是否为同一渠道或子渠道
            // 这里需要实现渠道层级检查逻辑
            return user.getChannelId().equals(targetChannelId);
            
        } catch (Exception e) {
            logger.warn("检查渠道权限失败: userId={}, targetChannelId={}, error={}", userId, targetChannelId, e.getMessage());
            return false;
        }
    }

    /**
     * 根据用户ID获取其可访问的渠道ID列表
     * 
     * @param userId 用户ID
     * @return 可访问的渠道ID列表，null表示无限制
     */
    public java.util.List<Long> getAccessibleChannelIds(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            SysUserEntity user = sysUserService.getById(userId);
            if (user == null || user.getChannelId() == null) {
                return null; // 系统管理员，无限制
            }

            // 返回用户所属渠道及其子渠道ID列表
            java.util.List<Long> channelIds = new java.util.ArrayList<>();
            channelIds.add(user.getChannelId());
            
            // TODO: 这里需要添加获取子渠道的逻辑
            // channelIds.addAll(channelService.getAllChildChannelIds(user.getChannelId()));
            
            return channelIds;
            
        } catch (Exception e) {
            logger.warn("获取用户可访问渠道列表失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 为微信用户自动设置渠道ID
     * 通过业务员绑定关系确定渠道归属
     *
     * @param wxUser 微信用户实体
     * @param appid 应用ID
     */
    public void setWxUserChannelId(WxUser wxUser, String appid) {
        if (wxUser == null || wxUser.getId() == null || appid == null) {
            return;
        }

        try {
            // 通过微信用户业务员绑定关系获取渠道ID
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUser.getId(), appid);
            if (binding != null && binding.getSalesmanId() != null) {
                SalesmanEntity salesman = salesmanService.getById(binding.getSalesmanId());
                if (salesman != null && salesman.getChannelId() != null) {
                    wxUser.setChannelId(salesman.getChannelId());
                    // 更新数据库
                    wxUserService.updateById(wxUser);
                    logger.debug("通过业务员绑定设置微信用户渠道ID: wxUserId={}, channelId={}, salesmanId={}",
                        wxUser.getId(), salesman.getChannelId(), salesman.getId());
                    return;
                }
            }

            logger.debug("未找到微信用户的业务员绑定关系，不设置渠道ID: wxUserId={}, appid={}", wxUser.getId(), appid);

        } catch (Exception e) {
            logger.warn("自动设置微信用户渠道ID失败: wxUserId={}, appid={}, error={}", wxUser.getId(), appid, e.getMessage());
        }
    }

    /**
     * 批量更新微信用户的渠道归属
     * 用于数据迁移或批量处理
     *
     * @param appid 应用ID
     */
    public void batchUpdateWxUserChannelId(String appid) {
        try {
            // 查询所有没有渠道ID的微信用户
            java.util.List<WxUser> wxUsers = wxUserService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<WxUser>()
                    .eq("appid", appid)
                    .isNull("channel_id")
            );

            for (WxUser wxUser : wxUsers) {
                setWxUserChannelId(wxUser, appid);
            }

            logger.info("批量更新微信用户渠道归属完成: appid={}, 处理用户数={}", appid, wxUsers.size());

        } catch (Exception e) {
            logger.error("批量更新微信用户渠道归属失败: appid={}, error={}", appid, e.getMessage());
        }
    }
}
