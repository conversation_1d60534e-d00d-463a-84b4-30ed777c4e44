<template>
  <div class="mod-config">
    <div style="
        text-align: center;
        padding: 10px;
        font-weight: bold;
        font-size: 24px;
      ">
      议程配置
    </div>
    <el-row :gutter="12" style="margin-bottom: 30px">
      <el-col :span="6">
        <el-card shadow="always" class="card"> 会场管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="placeactivitytopic">
        <el-card shadow="always" class="card"> 主题管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="placeactivitytopicschedule">
        <el-card shadow="always" class="card"> 日程管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityguest">
        <el-card shadow="always" class="card"> 嘉宾管理 </el-card>
      </el-col>
    </el-row>
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="会场名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('place:placeactivity:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('place:placeactivity:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="会场名称">
      </el-table-column>
      <el-table-column prop="paixu" header-align="center" align="center" label="排序(越小越前)">
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="280" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="placeactivitytopic(scope.row)">主题管理</el-button>
          <el-button type="text" size="small" v-if="isAuth('place:placeactivity:live') && scope.row.isLive == 1"
            @click="openPush(scope.row.id)">推流</el-button>
          <el-button type="text" size="small" v-if="isAuth('place:placeactivity:live') && scope.row.isLive == 1"
            @click="placeactivitylive(scope.row.id)">流量</el-button>
          <el-button type="text" size="small" v-if="isAuth('place:chat:list') && scope.row.isLive == 1"
            @click="openChat(scope.row)">聊天室</el-button>
          <el-button type="text" size="small" v-if="isAuth('place:placeactivityvideo:list') && scope.row.isLive == 1"
            @click="openVideo(scope.row)">录播</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 开启推流 -->
    <openpush v-if="openpushVisible" ref="openpush" @refreshDataList="getDataList"></openpush>
    <placeactivitylive v-if="placeactivityliveVisible" ref="placeactivitylive" @refreshDataList="getDataList"></placeactivitylive>
  </div>
</template>

<script>
import AddOrUpdate from "./placeactivity-add-or-update";
import openpush from "./placeactivity-openpush";
import placeactivitylive from "./placeactivity-live";
export default {
  data() {
    return {
      dataForm: {
        name: "",
        activityId: undefined,
      },
      url: '',
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      openpushVisible: false,
      placeactivityliveVisible: false,
    };
  },
  components: {
    AddOrUpdate,
    openpush,
    placeactivitylive,
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/place/placeactivity/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.dataForm.activityId,
          name: this.dataForm.name,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id);
      });
    },
    // 开启推流
    openPush(id) {
      this.openpushVisible = true;
      this.$nextTick(() => {
        this.$refs.openpush.init(id);
      });
    },
    placeactivitylive(id) {
      this.placeactivityliveVisible = true;
      this.$nextTick(() => {
        this.$refs.placeactivitylive.init(id);
      });
    },
    openChat(v) {
      this.$router.push({
        name: 'chat',
        query: {
          activityId: this.dataForm.activityId,
          placeId: v.id,
          pushKey: v.pushKey,
        }
      })
    },
    // 跳转录播视频
    openVideo(v) {
      this.$router.push({
        name: 'placeactivityvideo',
        query: {
          activityId: this.dataForm.activityId,
          placeId: v.id,
          pushKey: v.pushKey,
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.id;
        });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/place/placeactivity/delete"),
          method: "post",
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    placeactivitytopic(v) {
      if (v.id != null) {
        this.$router.push({
          name: "placeactivitytopic",
          query: {
            activityId: this.dataForm.activityId,
            placeId: v.id,
          },
        });
      } else {
        this.$router.push({
          name: "placeactivitytopic",
          query: {
            activityId: this.dataForm.activityId,
          },
        });
      }
    },
    placeactivitytopicschedule() {
      this.$router.push({
        name: "placeactivitytopicschedule",
        query: {
          activityId: this.dataForm.activityId,
        },
      });
    },
    activityguest() {
      this.$router.push({
        name: "activityguest",
        query: {
          activityId: this.dataForm.activityId,
        },
      });
    },
  },
};
</script>

<style scoped>
.card {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-top: 20px;
}
</style>