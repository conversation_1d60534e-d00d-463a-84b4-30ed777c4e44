<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="80%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型编码" prop="typeCode">
            <el-input v-model="dataForm.typeCode" placeholder="类型编码（如：douyin）" :disabled="!!dataForm.id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型名称" prop="typeName">
            <el-input v-model="dataForm.typeName" placeholder="类型名称（如：抖音）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="平台名称" prop="platform">
            <el-input v-model="dataForm.platform" placeholder="平台名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内容类型" prop="contentType">
            <el-input v-model="dataForm.contentType" placeholder="内容类型（如：短视频）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标题长度要求" prop="titleLength">
            <el-input v-model="dataForm.titleLength" placeholder="标题长度要求"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内容长度要求" prop="contentLength">
            <el-input v-model="dataForm.contentLength" placeholder="内容长度要求"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="话题数量" prop="topicsCount">
            <el-input-number v-model="dataForm.topicsCount" :min="1" :max="20" placeholder="话题数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number v-model="dataForm.sortOrder" :min="0" placeholder="排序"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="话题格式要求" prop="topicsFormat">
        <el-input v-model="dataForm.topicsFormat" placeholder="话题格式要求（如：不带#号，用逗号分隔）"></el-input>
      </el-form-item>
      <el-form-item label="风格特点" prop="style">
        <el-input v-model="dataForm.style" placeholder="风格特点"></el-input>
      </el-form-item>
      <el-form-item label="内容要求" prop="requirements">
        <el-input
          type="textarea"
          :rows="4"
          v-model="dataForm.requirements"
          placeholder="内容要求（每行一个要求）">
        </el-input>
      </el-form-item>
      <el-form-item label="Prompt模板" prop="promptTemplate">
        <el-input
          type="textarea"
          :rows="8"
          v-model="dataForm.promptTemplate"
          placeholder="Prompt模板，支持占位符：{platform}, {content_type}, {keyword}, {title_section}, {title_length}, {content_length}, {topics_count}, {topics_format}, {requirements}, {style}">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: 0,
        typeCode: '',
        typeName: '',
        platform: '',
        contentType: '',
        titleLength: '',
        contentLength: '',
        topicsCount: 5,
        topicsFormat: '',
        requirements: '',
        style: '',
        promptTemplate: '',
        sortOrder: 0,
        status: 1
      },
      dataRule: {
        typeCode: [
          { required: true, message: '类型编码不能为空', trigger: 'blur' }
        ],
        typeName: [
          { required: true, message: '类型名称不能为空', trigger: 'blur' }
        ],
        platform: [
          { required: true, message: '平台名称不能为空', trigger: 'blur' }
        ],
        contentType: [
          { required: true, message: '内容类型不能为空', trigger: 'blur' }
        ],
        titleLength: [
          { required: true, message: '标题长度要求不能为空', trigger: 'blur' }
        ],
        contentLength: [
          { required: true, message: '内容长度要求不能为空', trigger: 'blur' }
        ],
        topicsCount: [
          { required: true, message: '话题数量不能为空', trigger: 'blur' }
        ],
        topicsFormat: [
          { required: true, message: '话题格式要求不能为空', trigger: 'blur' }
        ],
        requirements: [
          { required: true, message: '内容要求不能为空', trigger: 'blur' }
        ],
        style: [
          { required: true, message: '风格特点不能为空', trigger: 'blur' }
        ],
        promptTemplate: [
          { required: true, message: 'Prompt模板不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/adtypeconfig/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.dataForm.typeCode = data.adTypeConfig.typeCode
              this.dataForm.typeName = data.adTypeConfig.typeName
              this.dataForm.platform = data.adTypeConfig.platform
              this.dataForm.contentType = data.adTypeConfig.contentType
              this.dataForm.titleLength = data.adTypeConfig.titleLength
              this.dataForm.contentLength = data.adTypeConfig.contentLength
              this.dataForm.topicsCount = data.adTypeConfig.topicsCount
              this.dataForm.topicsFormat = data.adTypeConfig.topicsFormat
              this.dataForm.requirements = data.adTypeConfig.requirements
              this.dataForm.style = data.adTypeConfig.style
              this.dataForm.promptTemplate = data.adTypeConfig.promptTemplate
              this.dataForm.sortOrder = data.adTypeConfig.sortOrder
              this.dataForm.status = data.adTypeConfig.status
            }
          })
        } else {
          // 新增时设置默认的Prompt模板
          this.dataForm.promptTemplate = `请为{platform}平台生成{content_type}文案。

关键词：{keyword}
{title_section}
要求：
{requirements}

请以JSON格式返回以下内容（键名使用英文）：
- 标题（title，{title_length}）
- 内容（content，{content_length}）
- 话题（topics，{topics_count}个，用逗号分隔，{topics_format}）

风格特点：{style}`
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/adtypeconfig/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'typeCode': this.dataForm.typeCode,
              'typeName': this.dataForm.typeName,
              'platform': this.dataForm.platform,
              'contentType': this.dataForm.contentType,
              'titleLength': this.dataForm.titleLength,
              'contentLength': this.dataForm.contentLength,
              'topicsCount': this.dataForm.topicsCount,
              'topicsFormat': this.dataForm.topicsFormat,
              'requirements': this.dataForm.requirements,
              'style': this.dataForm.style,
              'promptTemplate': this.dataForm.promptTemplate,
              'sortOrder': this.dataForm.sortOrder,
              'status': this.dataForm.status
            })
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
