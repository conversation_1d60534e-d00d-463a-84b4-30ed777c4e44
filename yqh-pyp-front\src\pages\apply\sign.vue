<template>
  <div class="page">
    <img style="width: 100%;" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230324/9dc5404a275c42ac9a07f42ccd9cc245.png"
      alt="">
    <div class="activity-info">
      <div style="
            font-size: 18px;
            font-weight: bold;
            padding: 3px 0px;
          ">
        {{ activityInfo.name }}
      </div>
      <div style="
            font-size: 16px;
            font-weight: bold;
            padding: 3px 0px;
          ">
        {{ activityInfo.startTime.substring(0, 11) }} -
        {{ activityInfo.endTime.substring(5, 11) }}
      </div>
    </div>
    <div v-if="userInfo.status == 1 && userInfo.signType == 1" class="sign-info">
      <img style="width: 180px;"
        src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png" alt="">
      <van-button round type="info" class="bottom-button" block>签到成功</van-button>
    </div>
    <div v-else-if="userInfo.status == 1 && userInfo.signType != 1" class="sign-info">
      <img style="width: 180px;"
        src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20221020/168906ff7dc74239835061f0a8cfe5fd.png" alt="">
      <van-button round type="warning" class="bottom-button" block @click="sign">点击签到</van-button>
    </div>
    <div v-else class="sign-info">
      <img style="width: 180px;"
        src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png" alt="">
      <van-button round type="warning" class="bottom-button" block
        @click="$router.push({ name: 'applyIndex', query: { id: activityId } })">您还未报名，点击报名</van-button>
    </div>
    <div v-if="userInfo.status == 1" class="apply-info">
      <div style="padding-left: 30px;">姓名：{{ userInfo.contact }}</div>
      <div style="padding-left: 30px;">联系方式：{{ userInfo.mobile }}</div>
      <div style="padding-left: 30px;">报名通道：{{ channelInfo.name }}</div>
    </div>


  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  components: {
    VueQrcode
  },
  data() {
    return {
      activityId: undefined,
      userInfo: {},
      activityInfo: {},
      channelInfo: {},
    };
  },
  mounted() {
    document.title = "电子签到";
    this.activityId = this.$route.query.id;
    this.getActivityInfo();
    this.getUserActivityInfo();
  },
  methods: {
    sign() {
      this.$fly
        .get(
          `/pyp/web/activityusersign/signByQrCode`,
          {
            activityUserId: this.userInfo.id,
            type: 1,
          }
        )
        .then((res1) => {
          if (res1.code == 200) {
            vant.Dialog.confirm({
              title: "提示",
              message: "“" + this.userInfo.contact + "”签到成功",
              confirmButtonText: "确定",
              showCancelButton: false,
            })
              .then(() => {
                // on close
                this.getUserActivityInfo();
              })
              .catch(() => {
                // on close
              });
          } else {
            vant.Dialog.confirm({
              title: "失败",
              message: res1.msg,
              confirmButtonText: "确定",
              showCancelButton: false,
            })
              .then(() => {
                // on close
              })
              .catch(() => {
                // on close
              });
          }
        });
    },
    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/web/activity/activityuserapplyorder/applyUserInfo/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.activityUserEntity;

            if (this.userInfo.applyActivityChannelConfigId) {
              this.getApplyActivityChannelConfig(this.userInfo.applyActivityChannelConfigId);
            }
          } else {
            vant.Toast('您还未报名，点击报名');
          }
        });
    },
    getApplyActivityChannelConfig(v) {
      this.$fly
        .get(`/pyp/web/apply/applyactivitychannelconfig/info/${v}`)
        .then((res) => {
          if (res.code == 200) {
            this.channelInfo = res.applyActivityChannelConfig;
          }
        });
    },
    getActivityInfo() {
      this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then(res => {
        if (res.code == 200) {
          this.activityInfo = res.activity
        } else {
          this.activityInfo = {}
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #f6f6f6;
}

.activity-info {
  text-align: center;
  padding: 10px 0;
  width: 90%;
  margin-left: 5%;
  background: white;
  border-radius: 20px;
  position: fixed;
  top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
  height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.sign-info {
  text-align: center;
  padding: 10px 0;
  width: 90%;
  margin-left: 5%;
  background: white;
  border-radius: 20px;
  position: fixed;
  top: 120px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.apply-info {
  padding: 10px 0;
  width: 90%;
  margin-left: 5%;
  background: white;
  border-radius: 20px;
  position: fixed;
  top: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.bottom-button {
  width: 90%;
}</style>