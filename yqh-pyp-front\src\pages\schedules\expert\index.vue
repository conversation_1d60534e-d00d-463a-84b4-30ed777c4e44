<template>
    <div>
        <!-- <van-swipe :autoplay="3000" :class="activityId == 1648304873736306690 ? 'animate-flipInX' : ''"> -->
        <van-swipe :autoplay="3000">
            <van-swipe-item v-for="(image, index) in activityInfo.appFileList" :key="index">
                <van-image width="100%" :src="image.url"> </van-image>
            </van-swipe-item>
        </van-swipe>
        <van-card style="background: white"
            :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
            <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
            <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary"
                    plain>{{
                        guestInfo.unit }}</van-tag>
                <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning"
                    plain>{{
                        guestInfo.duties }}</van-tag>
            </div>
        </van-card>
        <!-- 九宫格===模板1=== -->
        <div style="position: relative">
            <!-- 九宫格===模板7=== -->
            <div class="bgStyle" :style="activityInfo.background
                ? 'position: absolute;z-index: 99;top: 0;width: 100%;'
                : 'background:white;position: absolute;z-index: 99;top: 0;width: 100%;'
                ">
                <div v-if="activityConfig.contact || activityConfig.qrcode" class="nav-list"
                    style="width: 90%;margin-left: 5%;padding: 10px 0;justify-content: space-between;">
                    <div @click="callPhone(activityConfig.mobile)" v-if="activityConfig.contact" style="font-size: 14px;">会务联系人:{{ activityConfig.contact }} {{
                        activityConfig.mobile ? ('-' +
                        activityConfig.mobile) : '' }}</div>
                    <van-button v-if="activityConfig.qrcode" type="primary" size="small" round
                        @click.native="showFollowMOdal = true;">点击添加微信联系</van-button>
                </div>
                <div v-if="activityConfig.guestInfo" class="nav-list" @click="turnToGuestInfo">
                    <div class="template7 gradient-deep-1">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241024/11145333ceee4385a7c3120af9afb1c8.png" />
                        <!-- </div> -->
                        <div class="bottom">基本信息</div>
                        <div class="bottom" v-if="guestInfo.isInfo">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <div v-if="activityConfig.guestServiceInfo" class="nav-list" @click="turnToGuestServiceInfo">
                    <div class="template7 gradient-deep-2">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241024/b81eb5a257854441b91ff5d41a396cf0.png" />
                        <!-- </div> -->
                        <div class="bottom">劳务费信息</div>
                        <div class="bottom" v-if="guestInfo.isService">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <div v-if="activityConfig.guestSchedule" class="nav-list" @click="turnToGuestSchedule">
                    <div class="template7 gradient-deep-3">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241024/f7ed8d4104e74710a839c6c380619bc3.png" />
                        <!-- </div> -->
                        <div class="bottom">任务信息</div>
                        <div class="bottom" v-if="guestInfo.isSchedule">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <div v-if="activityConfig.guestTrip" class="nav-list" @click="turnToGuestTrip">
                    <div class="template7 gradient-deep-4">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241024/9cab22bd65dd4b9fad1f54fc49c17ba9.png" />
                        <!-- </div> -->
                        <div class="bottom">行程信息</div>
                        <div class="bottom" v-if="guestInfo.isTrip">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <div v-if="activityConfig.guestLink" class="nav-list" @click="turnToGuestLink">
                    <div class="template7 gradient-deep-4">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250312/f9737e91484d4ad0a5e20f479c46bf8a.png" />
                        <!-- </div> -->
                        <div class="bottom">接送确认</div>
                        <div class="bottom" v-if="guestInfo.isLink">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <div v-if="activityConfig.guestService" class="nav-list" @click="turnToGuestService">
                    <div class="template7 gradient-deep-2">
                        <img class="image"
                            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241024/b81eb5a257854441b91ff5d41a396cf0.png" />
                        <!-- </div> -->
                        <div class="bottom">劳务费签字确认</div>
                        <div class="bottom" v-if="guestInfo.isServiceSign">(完成)</div>
                        <div class="bottom" v-else>(未完成)</div>
                    </div>
                </div>
                <!-- 九宫格底部-文件下载 -->
                <!-- <div class="download" v-if="activityInfo.type &&
            activityInfo.type.includes('2') &&
            activityBottom[2]
        " style="margin-top: 10px">
            <van-collapse v-model="downActive">
                <van-collapse-item title="文件下载" name="0">
                    <div v-for="item in activityBottom[2]" :key="item.id">
                        <van-cell :title="item.name" v-if="item.type == 2" @click="download(item.url)" value="点击下载" />
                    </div>
                </van-collapse-item>
            </van-collapse>
        </div> -->
                <!-- 九宫格底部-banner广告 -->
                <div v-if="activityInfo.type &&
                    activityInfo.type.includes('1') &&
                    activityBottom[1]
                ">
                    <van-swipe :autoplay="3000">
                        <div v-for="(image, index) in activityBottom[1]" :key="index">
                            <van-swipe-item v-if="image.type == 1">
                                <van-image width="100%" height="100px" :src="image.name" @click="openUrl(image.url)">
                                </van-image>
                            </van-swipe-item>
                        </div>
                    </van-swipe>
                </div>
                <!-- 九宫格底部-自定义 -->
                <div class="bottomdiy" :style="{ backgroundColor: activityInfo.bottomColor }" v-if="activityInfo.type &&
                    activityInfo.type.includes('0') &&
                    activityBottom[0]
                ">
                    <div v-for="(item, index) in activityBottom[0]" :key="index">
                        <a style="display: flex; align-items: center" class="item" v-if="item.name.includes('时代会务')"
                            :href="item.url">
                            <div>会务支持&nbsp;</div>
                            <img style="width: 25px; height: 25px"
                                src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20230627/4ed61de97deb43b48fc3b9bd72f4f3af.png"
                                alt="" />
                            <div>&nbsp;时代会务</div>
                        </a>
                        <a class="item" v-else-if="item.type == 0" :href="item.url">{{
                            item.name
                        }}</a>
                    </div>
                </div>
            </div>
        </div>
        <qrcode :show="showFollowMOdal" :qrcodeImgUrl="activityConfig.qrcode" @close="showFollowMOdal = false"></qrcode>

        <img class="back" @click="cmsTurnBack"
            src="https://mpjoy.oss-cn-beijing.aliyuncs.com/20250117/c9d2700eaf9e481794bf54b3192daa04.png" alt="" />
    </div>
</template>

<script>
import date from "@/js/date.js";
export default {
    data() {
        return {
            showFollowMOdal: false,
            appid: '',
            planeShow: 0,
            openid: undefined,
            activityId: undefined,
            downActive: ["0"],
            cmsList: [],
            activityBottom: {},
            bannerIndex: 0,
            activityInfo: {},
            activityConfig: {},
            dateCompare: 0,
            onOrOff: "on",
            subUrl: "",
            showSub: 0,
            userInfo: {},
            adTimer: null,
            id: '',
            guestInfo: '',
        };
    },
    components: {
        qrcode: () => import("@/components/qrcode"),
    },
    mounted() {
        this.id = this.$route.query.detailId;
        this.openid = this.$cookie.get("openid");
        this.appid = this.$cookie.get("appid");
        this.getActivityList();
    },
    methods: {
        turnToGuestLink() {
            this.$router.push({
                path: "/schedules/expertLink",
                query: {
                    detailId: this.id,
                },
            });
        },
        turnToGuestInfo() {
            this.$router.push({
                path: "/schedules/expertInfo",
                query: {
                    detailId: this.id,
                },
            });
        },
        turnToGuestSchedule() {
            this.$router.push({
                path: "/schedules/expertDetail",
                query: {
                    detailId: this.id,
                    c: 1,
                },
            });
        },
        turnToGuestService() {
            this.$router.push({
                path: "/schedules/servicefee",
                query: {
                    detailId: this.id,
                },
            });
        },
        turnToGuestServiceInfo() {
            this.$router.push({
                path: "/schedules/servicefeeinfo",
                query: {
                    detailId: this.id,
                },
            });
        },
        turnToGuestTrip() {
            this.$router.push({
                path: "/schedules/expertTrip",
                query: {
                    detailId: this.id,
                },
            });
        },
        closePlane() {
            this.planeShow = 0;
            sessionStorage.setItem("planeShow", "false");
        },
        getActivityBottom() {
            this.$fly
                .get(
                    `/pyp/web/activity/activitybottom/findByActivity/${this.activityId}`,
                    {
                        type: this.activityInfo.type,
                    }
                )
                .then((res) => {
                    if (res.code == 200) {
                        this.activityBottom = res.result;
                    }
                });
        },
        getActivityInfo() {
            this.$fly
                .get(`/pyp/activity/activity/info/${this.activityId}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.activityInfo = res.activity;
                        this.activityInfo.backImg =
                            this.activityInfo.backImg ||
                            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
                        document.title = "专家首页-" + this.guestInfo.name;
                        let startTime = date.formatDate.format(
                            new Date(this.activityInfo.startTime),
                            "yyyy年MM月dd日"
                        );
                        let endTime = date.formatDate.format(
                            new Date(this.activityInfo.endTime),
                            "MM月dd日"
                        );
                        if (startTime.includes(endTime)) {
                            let desc =
                                "时间:" +
                                startTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                this.guestInfo.name + "-专家首页-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        } else {
                            let desc =
                                "时间:" +
                                startTime +
                                "-" +
                                endTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                this.guestInfo.name + "-专家首页-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        }
                        this.getActivityBottom();
                    } else {
                        vant.Toast(res.msg);
                        this.activityInfo = {};
                    }
                });
        },
        getActivityList() {
            this.$fly
                .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
                .then((res) => {
                    if (res.code == 200) {
                        this.guestInfo = res.result;
                        this.activityId = res.result.activityId;
                        this.getActivityInfo();
                        this.getActivityConfig();
                    } else {
                        vant.Toast(res.msg);
                        this.guestInfo = {};
                    }
                });
        },
        getActivityConfig() {
            this.$fly
                .get(`/pyp/web/activity/activityConfig/check`, {
                    activityId: this.activityId,
                    guestId: this.guestInfo.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.activityConfig = res.result;
                    } else {
                        vant.Toast(res.msg);
                        this.activityConfig = {};
                    }
                });
        },
        openUrl(v) {
            if (v) {
                window.open(v);
            }
        },

        cmsTurnBack() {
            if (this.activityInfo.backUrl) {
                window.open(this.activityInfo.backUrl);
            } else {
                this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
            }
        },
        callPhone(mobile) {
            if (mobile) {
                window.location.href = 'tel:' + mobile;
            } else {
                vant.Toast("无可用电话");
            }
        },
    },
};
</script>

<style lang="less" scoped>
.bgStyle {
    padding-top: 20px;
    padding-bottom: 40px;
    background-size: cover;
    margin-top: -5px;
    position: relative;
}

.coutdown {
    .van-count-down {
        line-height: 24px;
    }

    .van-cell__title,
    .van-cell__value {
        text-align: center;
    }
}

.nav-list {
    // height: 400px;
    flex-wrap: wrap;
    margin-left: 0.3%; // justify-content: space-around;
}

.nav-list,
.nav-list .nav-item {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
}

.nav-list .nav-item {
    width: 33%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-pack: justify;
    justify-content: space-between;
}

.nav-list .nav-item .top {
    width: 94px;
    height: 94px; // background-color: #4B00FF;
    background: -webkit-gradient(linear,
            left top,
            left bottom,
            from(#4b8ce6),
            to(#5f34df));
    background: -webkit-linear-gradient(top, #4b8ce6, #5f34df);
    background: linear-gradient(180deg, #4b8ce6, #5f34df);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
    border-radius: 50%;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
}

.nav-list .nav-item .top .image {
    width: 64px;
    height: 64px;
}

.nav-list .nav-item .bottom {
    font-weight: 500;
    line-height: 34px;
    color: #646566;
    font-size: 12px;
}

.download {
    /deep/ .van-collapse-item__content {
        padding: 0px;
    }

    /deep/ .van-cell__value {
        color: red;
    }
}

.bottomdiy {
    margin-top: -5px;
    height: 60px;
    width: 100%;
    background-color: #254288; // background: -webkit-linear-gradient(top, #4b8ce6, #5f34df);
    // background: linear-gradient(180deg, #4b8ce6, #5f34df);
    display: flex;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    flex-direction: column;
    position: relative;

    .item {
        color: white;
    }
}

.copyright {
    height: 46px;
    font-size: 14px;
    font-weight: 400;
    line-height: 46px;
    text-align: center;
    color: black;
}

.grid-text {
    margin-top: 5px;
    color: #646566;
    font-size: 12px;
    line-height: 1.5;
    word-break: break-all;
    text-align: center;
}

.audioBase {
    width: 30px;
    height: 30px;
    position: fixed;
    top: 10px;
    right: 10px;
    background: #eee;
    z-index: 9999;
    border: 1px #eee solid;
    border-radius: 50%;
}

.audioBase #audio-btn {
    width: 30px;
    height: 30px;
    background-size: 100% 100%;
}

.audioBase .on {
    background: url("http://mpjoy.oss-cn-beijing.aliyuncs.com/20230412/a20a62a3ca5d47a08e6b1845d6b0a183.png") no-repeat 0 0;
    -webkit-animation: rotating 1.2s linear infinite;
    animation: rotating 1.2s linear infinite;
}

.audioBase .off {
    background: url("http://mpjoy.oss-cn-beijing.aliyuncs.com/20230412/2b476c2edcea42fc8c96cbf80018fc62.png") no-repeat 0 0;
}

.template7 {
    margin-left: 3%;
    margin-bottom: 20px;
    position: relative;
    height: 80px;
    width: 94%;
    border-radius: 10px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 2px;

    .image {
        width: 30px;
        height: 30px;
        margin-right: 5px;
    }
}
/deep/ .van-card__thumb  img {
    object-fit: contain !important;
}
</style>