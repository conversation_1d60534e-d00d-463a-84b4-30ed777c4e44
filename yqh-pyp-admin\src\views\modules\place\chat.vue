<template>
  <div class="chat-admin">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
        <el-button  v-if="isAuth('place:chat:clean')" style="margin-left: 20px;" type="primary" @click="clean">清空</el-button>
    <div class="content">
      <div class="chat-content">
        <div class="chat-message" ref="message-list">
          <div style="text-align: center">
            <el-button type="primary" :disabled="finished" @click="getChatMsg"
              >加载更多</el-button
            >
          </div>
          <div v-for="message in chatMsg" :key="message.uuid">
            <div v-if="!message.isBack" class="message-box">
              <img
                class="message-img"
                :src="message.avatar"
                @error="imgError(message)"
              />
              <div class="message-item">
                <div
                  style="display: flex; align-items: center; margin-bottom: 4px"
                >
                  <!-- <div class="message-nick admin" v-if="message.nameCard == 'Admin'">{{$t('app.admin')}}</div>
                <div class="message-nick admin" v-else-if="message.nameCard == 'Owner'">{{$t('app.owner')}}</div> -->
                  <div class="message-nick">{{ message.username }}</div>
                  <el-tag
                    style="margin: 0 5px"
                    type="success"
                    v-if="message.roleId && message.roleId == 1"
                    >管理员</el-tag
                  >
                  <div class="message-date">{{ message.createOn }}</div>
                </div>
                <div class="message-container" @click="back(message.index)">
                  <div class="triangle"></div>
                  <template>
                    <img
                      v-if="message.type == 'img'"
                      :src="message.msg"
                      width="100px"
                      height="100px"
                    />
                    <span v-else class="message-text">{{ message.msg }}</span>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-input">
          <img
            class="addEmoji"
            @click="addEmoji"
            style="width: 25px; height: 25px"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20220825/85100d9867dd47528e82740cb40b9f53.png"
          />

          <el-upload
          :before-upload="checkFileSize" 
            :show-file-list="false"
            :data="{ pushKey: pushKey,activityId: activityId }"
            accept=".jpg, .jpeg, .png, .gif"
            :on-success="backgroundSuccessHandle"
            :action="url"
          >
            <img
              class="pic"
              style="width: 25px; height: 25px"
              src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20220825/6556e98d409041aeae041149aae0e91e.png"
            />
          </el-upload>
          <textarea v-model="text" placeholder="输入聊天内容"></textarea>
          <div class="send-btn">
            <el-button round size="mini" @click="sendTextMessage"
              ><span>发送</span></el-button
            >
            <!--<el-button round size="mini" @click="clearHandle"><span>发送</span></el-button>-->
          </div>
          <div class="emoji" v-show="isShow">
            <div class="emoji-content">
              <div
                class="content-div"
                v-for="(item, index) in emoji"
                :key="index"
                :index="index"
                @click="biaoqing(index)"
              >
                {{ item }}
              </div>
            </div>
            <i @click="closeEmoji" class="el-icon-circle-close"></i>
          </div>
        </div>
      </div>
      <div class="user-content">
        <div style="display: flex; margin: 10px">
          <el-input v-model="contact" placeholder="姓名" clearable></el-input>
          <el-input
            style="margin-left: 10px"
            v-model="mobile"
            placeholder="联系方式"
            clearable
          ></el-input>
          <el-button
            style="margin-left: 10px"
            type="primary"
            @click="
              userpageIndex = 1;
              users = [];
              getUserList();
            "
            >查询</el-button
          >
        </div>
        <div class="user-li" v-for="user in users" :key="user.id">
          <span class="el-avatar el-avatar--circle user-avatar">
            <img
              :src="
                user.avatar
                  ? user.avatar
                  : 'http://conferencehuizhan.oss-cn-beijing.aliyuncs.com/20220301/95110361e65a4c84920a5ef5e13f3509.png'
              "
            />
          </span>
          <span class="user-name">{{ user.contact + "-" + user.mobile }}</span>
          <el-tag type="primary" v-if="user.roleId == 1">管理员</el-tag>
          <el-tag type="info" v-else>用户</el-tag>
          <div class="options">
            <el-button
              size="mini"
              type="text"
              :loading="forbitBtn"
              @click="isForbit(user.contact, user.id,user.userId, 0)"
              v-if="user.isTaboo == 1"
              >解除禁言</el-button
            >
            <el-button
              type="text"
              size="mini"
              :loading="forbitBtn"
              @click="isForbit(user.contact, user.id,user.userId, 1)"
              v-else
              >禁言</el-button
            >
            <el-button
              type="text"
              size="mini"
              :loading="isAdminBtn"
              @click="changeAdmin(user.contact, user.id, 1)"
              v-if="user.roleId == 0"
              >设置为管理员</el-button
            >
            <el-button
              type="text"
              size="mini"
              :loading="isAdminBtn"
              @click="changeAdmin(user.contact, user.id, 0)"
              v-else
              >解除管理员</el-button
            >
          </div>
        </div>
        <div style="text-align: center">
          <el-button
            type="primary"
            :disabled="userfinished"
            @click="getUserList"
            >加载更多</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Compressor from 'compressorjs';
import { data } from "@/utils/emoji";

export default {
  data() {
    return {
      url: "",
      contact: "",
      mobile: "",
      isShow: false,
      forbitBtn: false,
      isAdminBtn: false,
      finished: false,
      userId: "",
      placeId: "",
      activityId: "",
      pushKey: "",
      chatUser: "",
      text: "",
      messages: [],
      content: "",
      emoji: [...data],
      chatMsg: [],
      uuid: [],
      pageIndex: 2,
      pageSize: 5,
      totalPage: 0,
      timer: "",
      users: [],
      userfinished: false,
      userpageIndex: 1,
      userpageSize: 5,
      usertotalPage: 0,
    };
  },
  updated() {
    this.keepMessageListOnButtom();
  },
  activated() {
    this.url = this.$http.adornUrl(
      `/chat/sendImage?token=${this.$cookie.get("token")}`
    );
    //刷新
    window.addEventListener("beforeunload", this.clear);
    this.placeId = this.$route.query.placeId;
    this.activityId = this.$route.query.activityId;
    this.pushKey = this.$route.query.pushKey;
    this.init();
    this.timer = setInterval(() => {
      console.log("---------------------定时器执行---------------------");
      this.refreshMsg();
    }, 5 * 1000);
  },
  destroyed() {
    console.log("destroyed");
    this.clear();
  },
  methods: {
    init() {
      this.firstGetChatMsg();
      this.getUserList();
    },

    getUserList() {
      this.$http({
        url: this.$http.adornUrl("/chat/userList"),
        method: "get",
        params: this.$http.adornParams({
          page: this.userpageIndex,
          limit: this.userpageSize,
          activityId: this.activityId,
          contact: this.contact,
          mobile: this.mobile,
        }),
      }).then(({ data }) => {
        if (data.code == 200) {
          if (data.page.list && data.page.list.length > 0) {
            data.page.list.forEach((e) => {
              this.users.unshift(e);
            });
            this.usertotalPage = data.page.totalPage;
            this.userpageIndex++;
            this.loading = false;
            if (this.usertotalPage < this.userpageIndex) {
              this.userfinished = true;
            } else {
              this.userfinished = false;
            }
          } else {
            this.userfinished = true;
          }
        } else {
          this.users = [];
          this.usertotalPage = 0;
          this.userfinished = true;
        }
      });
    },
    getChatMsg() {
      this.$http({
        url: this.$http.adornUrl("/chat/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          pushKey: this.pushKey,
        }),
      }).then(({ data }) => {
        if (data.code == 200) {
          if (data.list && data.list.length > 0) {
            data.list.forEach((e) => {
              if (!this.uuid.includes(e.uuid)) {
                this.chatMsg.unshift(e);
                this.uuid.push(e.uuid);
              }
            });
            this.totalPage = data.totalPage;
            this.pageIndex++;
            this.loading = false;
            if (this.totalPage < this.pageIndex) {
              this.finished = true;
            } else {
              this.finished = false;
            }
          } else {
            this.finished = true;
          }
        } else {
          this.chatMsg = [];
          this.totalPage = 0;
          this.finished = true;
        }
      });
    },
    firstGetChatMsg() {
      this.$http({
        url: this.$http.adornUrl("/chat/list"),
        method: "get",
        params: this.$http.adornParams({
          page: 1,
          limit: 5,
          pushKey: this.pushKey,
        }),
      }).then(({ data }) => {
        if (data.code == 200) {
          if (data.list && data.list.length > 0) {
            data.list.forEach((e) => {
              if (!this.uuid.includes(e.uuid)) {
                this.chatMsg.unshift(e);
                this.uuid.push(e.uuid);
              }
            });
          } else {
            this.finished = true;
          }
        } else {
          this.chatMsg = [];
          this.finished = true;
        }
      });
    },
    refreshMsg() {
      this.$http({
        url: this.$http.adornUrl("/chat/list"),
        method: "get",
        params: this.$http.adornParams({
          page: 1,
          limit: 5,
          pushKey: this.pushKey,
        }),
      }).then(({ data }) => {
        if (data.code == 200) {
          if (data.list && data.list.length > 0) {
            data.list.forEach((e) => {
              if (!this.uuid.includes(e.uuid)) {
                this.chatMsg.push(e);
                this.uuid.push(e.uuid);
              }
            });
          }
        } else {
          this.chatMsg = [];
          this.finished = true;
        }
      });
    },
    sendTextMessage() {
      window.scroll(0, 0); //ios键盘回落
      if (this.text === "" || this.text.trim().length === 0) {
        this.text = "";
        vant.Toast("不能发送空消息");
        return false;
      }
      this.$http({
        url: this.$http.adornUrl("/chat/send"),
        method: "get",
        params: this.$http.adornParams({
          pushKey: this.pushKey,
          msg: this.text,
          activityId: this.activityId,
        }),
      }).then(({ data }) => {
        if (data.code == 200) {
          this.emojiShow = false;
          this.refreshMsg();
          this.$nextTick(() => {
            let ele = this.$refs["message-list"];
            console.log(ele);
            ele.scrollTop = ele.scrollHeight;
          });
        } else {
        }
      });
      this.text = "";
    },
    // 如果滚到底部就保持在底部，否则提示是否要滚到底部
    keepMessageListOnButtom() {
      let messageListNode = this.$refs["message-list"];
      if (!messageListNode) {
        return;
      }
      // 距离底部20px内强制滚到底部,否则提示有新消息
      if (
        this.preScrollHeight -
          messageListNode.clientHeight -
          messageListNode.scrollTop <
        20
      ) {
        this.$nextTick(() => {
          messageListNode.scrollTop = messageListNode.scrollHeight + 60;
        });
        this.isShowScrollButtomTips = false;
      } else {
        this.isShowScrollButtomTips = true;
      }
      this.preScrollHeight = messageListNode.scrollHeight;
    },
    addEmoji() {
      if (this.isShow) {
        this.isShow = false;
      } else {
        this.isShow = true;
      }
    },
    closeEmoji() {
      this.isShow = false;
    },
    // 点击表情
    biaoqing(index) {
      this.text += this.emoji[index];
      this.isShow = false;
    },
    /* val 0-解除禁言 1-禁言 */
    isForbit(contact, activityUserId,userId, val) {
      this.$confirm(
        `确定对[${contact}]进行${val == 0 ? "解除禁言" : "禁言"}操作`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.forbitBtn = true;
        this.$http({
          url: this.$http.adornUrl(`/chat/changeIsTaboo`),
          method: "post",
          data: this.$http.adornData({
            id: activityUserId,
            userId: userId,
            isTaboo: val,
            pushKey: this.pushKey
          }),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.userpageIndex = 1;
                this.users = [];
                this.getUserList();
                this.forbitBtn = false;
              },
            });
          } else {
            this.forbitBtn = false;
            this.$message.error(data.msg);
          }
        });
      });
    },
    /* val 0-解除管理员 1-设置为管理员 */
    changeAdmin(contact, activityUserId, val) {
      this.$confirm(
        `确定对[${contact}]进行${val == 0 ? "解除管理员" : "设置为管理员"}操作`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.isAdminBtn = true;
        this.$http({
          url: this.$http.adornUrl(`/chat/changeRole`),
          method: "post",
          data: this.$http.adornData({
            id: activityUserId,
            roleId: val,
          }),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.userpageIndex = 1;
                this.users = [];
                this.getUserList();
                this.isAdminBtn = false;
              },
            });
          } else {
            this.isAdminBtn = false;
            this.$message.error(data.msg);
          }
        });
      });
    },
    back(v) {
      this.$confirm(`确定撤回该消息`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(`/chat/back`),
            method: "get",
            params: this.$http.adornParams({
              index: v,
              pushKey: this.pushKey,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.chatMsg = [];
                  this.uuid = [];
                  this.pageIndex = 1;
                  this.finished = false;
                  this.refreshMsg();
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        })
        .catch(() => {});
    },
    clean() {
      this.$confirm(`确认清空消息`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl(`/chat/clean`),
            method: "get",
            params: this.$http.adornParams({
              pushKey: this.pushKey,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.chatMsg = [];
                  this.uuid = [];
                  this.pageIndex = 1;
                  this.finished = false;
                  this.refreshMsg();
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        })
        .catch(() => {});
    },
    async clear() {
      this.timer = clearInterval(this.timer);
    },
    // 上传之前
    checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
    // 上传成功（背景）
    backgroundSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.refreshMsg();
        this.$nextTick(() => {
          let ele = this.$refs["message-list"];
          console.log(ele);
          ele.scrollTop = ele.scrollHeight;
        });
      } else {
        this.$message.error(response.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.chat-admin {
  // margin: -20px;
  // background: #f7f7f7;
}

.title,
.user-list,
.user-content,
.chat-content {
  background: #ffffff;
  float: left;
}

.top {
  width: 100%;
  /*height: 88px;*/
  height: 88px;
  margin-bottom: 14px;
}

.title {
  width: 66%;
  height: 88px;
  margin-right: 1%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.title-left {
  display: Flex;
  align-items: center;
  margin-left: 30px;
}

.title-right {
  float: right;
}

.title-left span,
.user-list span {
  width: 100px;
  height: 88px;
  font-size: 21px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #60ca7d;
  line-height: 88px;
  margin-left: 3px;
}

.search-input {
  width: 500px;
  height: 32px;
  background: #f7f7f7;
  border-radius: 6px;
}

.user-list {
  width: 33%;
  height: 88px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.list-content {
  margin-left: 30px;
  display: Flex;
  align-items: center;
}

.content {
  width: 100%;
  height: 753px;
}

/* 聊天室 */
.chat-content {
  width: 66%;
  height: 100%;
  margin-right: 1%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px 6px 0px 0px;
  display: flex;
  flex-direction: column;
}

.chat-message {
  height: 100%;
  padding: 20px;
  border-bottom: #f6f6f6 2px solid;
  overflow: auto;
}

.chat-input {
  width: 100%;
  height: 150px;
  padding: 5px;
  position: relative;
}

.chat-input .addEmoji {
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 99;
}
.chat-input .pic {
  position: absolute;
  left: 10px;
  top: 50px;
  z-index: 99;
}

.send-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 99;
}

.chat-input textarea {
  position: absolute;
  left: 0px;
  top: 0px;
  padding: 13px 72px 5px 42px;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
}

.chat-input span {
  width: 28px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #919398;
  line-height: 20px;
}

/* 表情 */

.emoji {
  position: absolute;
  top: -160px;
  left: 35px;
  width: 480px;
  height: 200px;
  background-color: white;
  border-radius: 5px;
}

.emoji-content {
  margin: 10px;
  display: flex;
  flex-wrap: wrap;
  height: 180px;
  justify-content: flex-start;
  overflow-y: scroll;
  overflow-x: hidden;
}

.content-div {
  cursor: pointer;
  padding: 5px;
  height: 30px;
  width: 30px;
  border: 1px solid rgba(170, 166, 166, 0.473);
  border-top: 1px solid transparent;
  border-left: 1px solid transparent;
}

.el-icon-circle-close {
  position: absolute;
  top: 2px;
  right: 30px;
}

/* 用户列表 */
.user-content {
  width: 33%;
  height: 100%;
  overflow: scroll;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  // box-shadow: 0px 2px 4px 0px rgba(48, 195, 208, 0.2);
  border-radius: 6px;
  // overflow: auto;
}

.user-li {
  border-top: 1px solid #f6f6f6;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-avatar {
  line-height: 80px;
  margin-left: 20px;
}

.user-li img {
  object-fit: cover;
}

.user-name {
  max-width: 180px;
  margin-left: 10px;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #6d7277;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.user-role {
  max-width: 100px;
  height: 24px;
  line-height: 24px;
  background: #60ca7d;
  border-radius: 12px;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  margin-left: 10px;
}

.role-gray {
  background-color: #d8d8d8;
}

.role-hot {
  background-color: #ff6c00;
}

.role-super {
  background-color: #5b8ff9;
}

.options {
}

.turn-right {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  border-radius: 6px;
}

.light-green {
  background-color: #5ad8a6;
}

/* 聊天消息 */
.chat-apply-name {
  width: 69px;
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #919398;
  line-height: 22px;
  margin-right: 10px;
}

.user-label {
  max-width: 100px;
  height: 18px;
  border-radius: 9px;
  font-size: 11px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ffffff;
  line-height: 16px;
  padding-left: 8px;
  padding-right: 8px;
  margin-right: 6px;
}

.chat-time {
  width: 62px;
  height: 16px;
  font-size: 11px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #919398;
  line-height: 16px;
}

.tip {
  max-width: 400px;
  background: #f7f7f7;
  border-radius: 6px;
  margin-top: 6px;
  padding: 14px 17px 16px 17px;
}

.tip-message {
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #303133;
  line-height: 22px;
}

.left {
  float: left;
}
.right {
  float: right;
  text-align: left;
}

.message-box {
  font-family: Microsoft YaHei, Arial, Helvetica, sans-serif, SimSun;
  display: flex;
  .message-item {
    font-size: 14px;
    padding: 4px 8px;
    position: relative;
    line-height: 18px;
    word-wrap: break-word;
    white-space: normal;
    width: 90%;
    margin-left: 2px;
    .message-nick,
    .message-date {
      font-size: 12px;
      line-height: 23px;
      color: black;
    }
    .admin {
      border: 1px solid #dff4e5;
      background-color: green;
      border-color: green;
      color: white;
      border-radius: 4px;
      padding: 0px 5px;
      margin-right: 5px;
    }
    .owner {
      border: 1px solid #dff4e5;
      background-color: blue;
      border-color: blue;
      color: white;
      border-radius: 4px;
      padding: 0px 5px;
      margin-right: 5px;
    }
    .message-date {
      margin-left: 5px;
    }
    .message-container {
      display: inline-block;
      position: relative;
      background-color: #f6f6f6;
      // border-radius: 12px;
      /*border-top-left-radius:0*/
      padding: 8px 11px;
      .triangle {
        // width: 0;
        // height: 0;
        // border-top:5px solid transparent;
        // border-bottom: 10px solid transparent;
        // border-right: 10px solid black;
        // border-bottom-right-radius: 2px;
        // position: absolute;
        // left: -8px;
        // top: 6px;
      }
    }
  }
  .tip-text,
  .tip-leave {
    font-size: 14px;
    position: relative;
    line-height: 18px;
    word-wrap: break-word;
    white-space: normal;
    /*margin 0 auto*/
    color: rgb(245, 166, 35); //#258ff3//#fea602
    .tips-img {
      display: inline-block;
      width: 20px;
      vertical-align: center;
    }
  }
  .tip-text {
    padding: 4px 35px;
  }
  .tip-leave {
    padding: 4px 40px;
  }
  .message-text {
    font-size: 14px;
    white-space: normal;
    word-break: break-all;
    word-wrap: break-word;
    color: black;
  }
}
.message-img {
  display: inline-block;
  min-width: 40px;
  max-width: 40px;
  height: 40px;
  border-radius: 50%;
}
</style>