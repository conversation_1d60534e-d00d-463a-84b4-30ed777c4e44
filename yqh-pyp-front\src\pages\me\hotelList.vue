<template>
  <div>
  <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">酒店订单</div>
        </div>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div class="data">
        <van-collapse v-model="schedulesActive">
          <van-collapse-item
            v-for="item in dataList"
            :key="item.id"
            :name="item.id"
            style="margin-bottom: 10px"
          >
            <van-card slot="title" style="background: white">
              <div slot="title" style="font-size: 18px">{{ item.name }}</div>
              <div
                slot="desc"
                style="padding-top: 10px; font-size: 14px; color: grey"
              >
                <div>
                  订单号(后四位)：{{
                    item.orderSn.substring(
                      item.orderSn.length - 4,
                      item.orderSn.length
                    )
                  }}
                </div>
                <div>订单金额：￥{{ item.totalAmount }}元</div>
                <div>创建时间：{{ item.createOn }}</div>
              </div>
              <template #tags>
                <div style="display: flex; justify-content: space-between;width: 110%;">
                  <van-tag
                    style="margin: 5px 10px 5px 0px"
                    size="medium"
                    plain
                    :type="item.status | statusTypeFilter"
                    >{{ item.status | statusFilter }}</van-tag
                  >
                  <div style="display: flex">
                    <van-button
                      size="small"
                      style="width: 60px"
                      v-if="item.status == 0"
                      @click="$router.push({name: 'hotelSuccess',query: {orderId: item.id,id: activityId} })"
                      round
                      block
                      type="primary"
                      >去支付</van-button
                    >
                    <van-button
                      size="small"
                      @click="cancelOrder(item.id)"
                      style="width: 60px; margin-left: 10px"
                      v-if="item.status == 0 || item.status == 1"
                      round
                      block
                      type="danger"
                      >取消</van-button
                    >
                  </div>
                </div>
              </template>
            </van-card>
            <van-steps active="-1" direction="vertical">
              <van-step
                v-for="item2 in item.hotelOrderDetailEntities"
                :key="item2.id"
              >
                <div>
                  <div>
                    酒店名称：{{ item2.hotelName ? item2.hotelName : "未知" }}
                  </div>
                  <div>房型名称：{{ item2.roomName }}</div>
                  <div>数量：{{ item2.number }}</div>
                  <div>
                    入住时间： {{ item2.inDate.substring(0, 10) }} ~
                    {{ item2.outDate.substring(5, 10) }}
                  </div>
                  <div>价格：￥{{ item2.price }}元</div>
                </div>
              </van-step>
            </van-steps>
          </van-collapse-item>
        </van-collapse>
      </div>
    </van-list>
  </div>
</template>

<script>
import orderStatus from "@/data/orderStatus.json";
export default {
  data() {
    return {
      activityId: undefined,
      loading: false,
      finished: false,
      flag: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      placeActive: 0,
      orderStatus: orderStatus,
      schedulesActive: [],
    };
  },
  filters: {
    statusFilter(v) {
      let data = orderStatus.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    statusTypeFilter(v) {
      let data = orderStatus.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].type;
      }
    },
  },
  mounted() {
          document.title ="酒店订单列表";
    this.activityId = this.$route.query.id;
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityList();
      }
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/hotel/hotelorder/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
                this.schedulesActive.push(e.id);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    cancelOrder(v) {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认取消订单?",
      })
        .then(() => {
          this.$fly
            .get("/pyp/web/hotel/hotelorder/cancel", {
              orderId: v,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("取消成功");
                this.onSearch();
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/ .van-cell {
    padding: 0;
    align-items: center;
  }
  /deep/ .van-cell__right-icon {
    margin-right: 20px;
  }
}
/deep/ .van-card__num {
  display: flex;
}
</style>