# 订单状态直接修改功能使用指南

## 功能概述

为了方便管理员直接管理订单状态，我们在使用记录和充值记录页面添加了直接修改订单状态的功能。

## 功能特性

### 1. 充值记录状态修改
- **位置**: 活动管理 → 使用记录对话框 → 充值记录标签页
- **功能**: 可以直接修改充值订单的状态
- **支持状态**:
  - 0: 待支付
  - 1: 已支付
  - 2: 已取消
  - 3: 已退款

### 2. 使用记录管理
- **位置**: 活动管理 → 使用记录对话框 → 使用记录标签页
- **功能**: 可以删除使用记录
- **操作**: 点击删除按钮，确认后删除记录

## 使用流程

### 修改充值记录状态

1. **进入页面**
   - 在活动管理页面，点击某个活动的"使用记录"按钮
   - 在弹出的对话框中，切换到"充值记录"标签页

2. **修改状态**
   - 在充值记录列表中，找到需要修改的订单
   - 点击操作列中的"修改状态"按钮
   - 在弹出的对话框中：
     - 查看当前订单号和状态
     - 选择新的状态
     - 填写修改原因或备注（可选）
     - 点击"确定"按钮

3. **状态变更逻辑**
   - **待支付 → 已支付**: 自动设置支付时间、支付方式(手动标记)、支付流水号，并触发佣金计算
   - **其他状态变更**: 直接更新状态，添加备注信息
   - **验证规则**: 已支付的订单不能直接取消，需要先退款

### 删除使用记录

1. **进入页面**
   - 在活动管理页面，点击某个活动的"使用记录"按钮
   - 在弹出的对话框中，切换到"使用记录"标签页

2. **删除记录**
   - 在使用记录列表中，找到需要删除的记录
   - 点击操作列中的"删除"按钮
   - 在确认对话框中点击"确定"

## 技术实现

### 后端接口

#### 修改充值记录状态
```
POST /activity/rechargerecord/updateStatus
参数:
- id: 订单ID
- status: 新状态 (0-待支付, 1-已支付, 2-已取消, 3-已退款)
- remarks: 备注信息
```

#### 删除使用记录
```
POST /activity/activityrechargeusage/delete
参数: [记录ID数组]
```

### 前端实现

#### 状态修改对话框
- 使用Element UI的Dialog组件
- 表单验证确保必填字段
- 状态标签显示当前状态
- 下拉选择新状态
- 文本域输入备注

#### 删除确认
- 使用MessageBox确认对话框
- 防止误操作删除

## 权限要求

### 充值记录状态修改
- 需要权限: `activity:rechargerecord:update`

### 使用记录删除
- 需要权限: `activity:rechargeusage:delete`

## 注意事项

1. **状态变更限制**
   - 已支付的订单不能直接取消
   - 状态变更会记录操作日志
   - 从待支付改为已支付会触发佣金计算

2. **数据安全**
   - 删除操作不可恢复，请谨慎操作
   - 建议在删除前确认记录的重要性

3. **业务影响**
   - 修改订单状态可能影响用户的活动使用权限
   - 删除使用记录会影响统计数据

## 错误处理

- 网络错误时会显示相应提示
- 权限不足时会显示权限错误
- 状态变更失败时会显示具体错误信息
- 操作成功后会自动刷新列表数据

## 日志记录

所有的状态修改和删除操作都会记录在系统日志中，包括：
- 操作时间
- 操作用户
- 操作类型
- 操作对象
- 操作结果
