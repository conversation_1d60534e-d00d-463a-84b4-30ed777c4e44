<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="业务员姓名"></el-input>
      </el-form-item>
      <el-form-item label="编号" prop="code">
        <el-input v-model="dataForm.code" placeholder="业务员编号">
          <el-button slot="append" @click="generateCode" :loading="generating">
            {{ generating ? '生成中' : '自动生成' }}
          </el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
      </el-form-item>
      <el-form-item label="所属渠道" prop="channelId">
        <el-select v-model="dataForm.channelId" placeholder="请选择所属渠道" clearable>
          <el-option
            v-for="channel in channelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input v-model="dataForm.department" placeholder="部门"></el-input>
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="dataForm.position" placeholder="职位"></el-input>
      </el-form-item>
      <el-form-item label="上级业务员" prop="parentId">
        <el-select v-model="dataForm.parentId" placeholder="请选择上级业务员" clearable>
          <el-option
            v-for="salesman in parentSalesmanList"
            :key="salesman.id"
            :label="salesman.name"
            :value="salesman.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input v-model="dataForm.tags" placeholder="标签，多个用逗号分隔"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" placeholder="备注信息"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        code: '',
        mobile: '',
        email: '',
        channelId: '',
        department: '',
        position: '',
        parentId: '',
        status: 1,
        tags: '',
        remarks: ''
      },
      channelList: [],
      parentSalesmanList: [],
      generating: false,
      dataRule: {
        name: [
          { required: true, message: '业务员姓名不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '业务员编号不能为空', trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ],
        channelId: [
          { required: true, message: '请选择所属渠道', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.getChannelList()
      this.getParentSalesmanList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/channel/salesman/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.salesman.name
              this.dataForm.code = data.salesman.code
              this.dataForm.mobile = data.salesman.mobile
              this.dataForm.email = data.salesman.email
              this.dataForm.channelId = data.salesman.channelId
              this.dataForm.department = data.salesman.department
              this.dataForm.position = data.salesman.position
              this.dataForm.parentId = data.salesman.parentId
              this.dataForm.status = data.salesman.status
              this.dataForm.tags = data.salesman.tags
              this.dataForm.remarks = data.salesman.remarks
            }
          })
        }
      })
    },
    // 获取渠道列表
    getChannelList() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.channelList || []
        }
      })
    },
    // 获取上级业务员列表
    getParentSalesmanList() {
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/findByAppid'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.parentSalesmanList = data.result || []
          // 过滤掉当前业务员，避免选择自己作为上级
          if (this.dataForm.id) {
            this.parentSalesmanList = this.parentSalesmanList.filter(item => item.id !== this.dataForm.id)
          }
        }
      })
    },
    // 自动生成编号
    generateCode() {
      this.generating = true

      // 生成规则：YW + 当前日期(YYYYMMDD) + 4位随机数
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0')

      const generatedCode = `YW${year}${month}${day}${random}`

      // 检查编号是否重复
      this.$http({
        url: this.$http.adornUrl('/channel/salesman/checkCode'),
        method: 'get',
        params: this.$http.adornParams({
          code: generatedCode,
          excludeId: this.dataForm.id || null
        })
      }).then(({ data }) => {
        this.generating = false
        if (data && data.code === 200) {
          if (data.exists) {
            // 如果重复，递归重新生成
            this.generateCode()
          } else {
            // 如果不重复，使用该编号
            this.dataForm.code = generatedCode
            this.$message.success('编号生成成功')
          }
        } else {
          // 如果检查失败，仍然使用生成的编号
          this.dataForm.code = generatedCode
          this.$message.success('编号生成成功')
        }
      }).catch(() => {
        this.generating = false
        // 如果请求失败，仍然使用生成的编号
        this.dataForm.code = generatedCode
        this.$message.success('编号生成成功')
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/channel/salesman/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'code': this.dataForm.code,
              'mobile': this.dataForm.mobile,
              'email': this.dataForm.email,
              'channelId': this.dataForm.channelId,
              'department': this.dataForm.department,
              'position': this.dataForm.position,
              'parentId': this.dataForm.parentId || null,
              'status': this.dataForm.status,
              'tags': this.dataForm.tags,
              'remarks': this.dataForm.remarks
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
