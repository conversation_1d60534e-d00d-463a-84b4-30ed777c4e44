package com.cjy.pyp.modules.activity.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 续费订单VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Data
public class RenewalOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 续费套餐ID
     */
    @NotNull(message = "续费套餐ID不能为空")
    private Long packageId;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 防重令牌
     */
    @NotNull(message = "防重令牌不能为空")
    private String repeatToken;
}
