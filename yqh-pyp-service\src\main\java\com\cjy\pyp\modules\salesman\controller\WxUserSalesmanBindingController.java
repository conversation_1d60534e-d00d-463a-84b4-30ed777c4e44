package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 微信用户业务员绑定管理控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("salesman/wxuserbinding")
@Api(tags = "微信用户业务员绑定管理")
public class WxUserSalesmanBindingController extends AbstractController {

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:wxuserbinding:list")
    @ApiOperation(value = "绑定关系列表", notes = "分页查询微信用户业务员绑定关系")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<WxUserSalesmanBindingEntity> page = wxUserSalesmanBindingService.queryPage(params);
        return R.okList(page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:wxuserbinding:info")
    @ApiOperation(value = "绑定关系详情", notes = "根据ID查询绑定关系详情")
    public R info(@PathVariable("id") Long id) {
        WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getById(id);
        return R.ok().put("binding", binding);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("salesman:wxuserbinding:save")
    @ApiOperation(value = "创建绑定关系", notes = "手动创建微信用户与业务员的绑定关系")
    public R save(@Valid @RequestBody WxUserSalesmanBindingEntity binding, @CookieValue String appid) {
        ValidatorUtils.validateEntity(binding);
        
        binding.setAppid(appid);
        binding.setCreateBy(getUserId());
        
        try {
            WxUserSalesmanBindingEntity result = wxUserSalesmanBindingService.createBinding(
                    binding.getWxUserId(), 
                    binding.getSalesmanId(), 
                    3, // 手动绑定
                    "管理员手动绑定", 
                    appid
            );
            return R.ok().put("binding", result);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("salesman:wxuserbinding:update")
    @ApiOperation(value = "更新绑定关系", notes = "更新绑定关系信息")
    public R update(@Valid @RequestBody WxUserSalesmanBindingEntity binding, @CookieValue String appid) {
        ValidatorUtils.validateEntity(binding);
        
        binding.setAppid(appid);
        binding.setUpdateBy(getUserId());
        
        wxUserSalesmanBindingService.updateById(binding);
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("salesman:wxuserbinding:delete")
    @ApiOperation(value = "删除绑定关系", notes = "批量删除绑定关系")
    public R delete(@RequestBody Long[] ids, @CookieValue String appid) {
        wxUserSalesmanBindingService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 解除绑定
     */
    @RequestMapping("/unbind")
    @RequiresPermissions("salesman:wxuserbinding:unbind")
    @ApiOperation(value = "解除绑定", notes = "解除微信用户与业务员的绑定关系")
    public R unbind(@RequestParam Long wxUserId, 
                   @RequestParam Long salesmanId,
                   @RequestParam(required = false) String reason,
                   @CookieValue String appid) {
        try {
            boolean success = wxUserSalesmanBindingService.unbindSalesman(
                    wxUserId, salesmanId, reason != null ? reason : "管理员解除绑定", appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("解除绑定失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 更换业务员
     */
    @RequestMapping("/changeSalesman")
    @RequiresPermissions("salesman:wxuserbinding:change")
    @ApiOperation(value = "更换业务员", notes = "为微信用户更换业务员")
    public R changeSalesman(@RequestParam Long wxUserId,
                           @RequestParam Long oldSalesmanId,
                           @RequestParam Long newSalesmanId,
                           @RequestParam(required = false) String reason,
                           @CookieValue String appid) {
        try {
            boolean success = wxUserSalesmanBindingService.changeSalesman(
                    wxUserId, oldSalesmanId, newSalesmanId, 
                    reason != null ? reason : "管理员更换业务员", appid);
            if (success) {
                return R.ok();
            } else {
                return R.error("更换业务员失败");
            }
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 查询微信用户的业务员绑定
     */
    @RequestMapping("/getByWxUser")
    @RequiresPermissions("salesman:wxuserbinding:info")
    @ApiOperation(value = "查询用户绑定", notes = "查询微信用户的业务员绑定关系")
    public R getByWxUser(@RequestParam Long wxUserId, @CookieValue String appid) {
        WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
        return R.ok().put("binding", binding);
    }

    /**
     * 查询业务员的客户绑定列表
     */
    @RequestMapping("/getBySalesman")
    @RequiresPermissions("salesman:wxuserbinding:info")
    @ApiOperation(value = "查询业务员客户", notes = "查询业务员的客户绑定列表")
    public R getBySalesman(@RequestParam Long salesmanId, @CookieValue String appid) {
        List<WxUserSalesmanBindingEntity> bindings = wxUserSalesmanBindingService.getBindingsBySlesman(salesmanId, appid);
        return R.ok().put("bindings", bindings);
    }

    /**
     * 统计业务员的客户数量
     */
    @RequestMapping("/countBySalesman")
    @RequiresPermissions("salesman:wxuserbinding:info")
    @ApiOperation(value = "统计客户数量", notes = "统计业务员的客户数量")
    public R countBySalesman(@RequestParam Long salesmanId, @CookieValue String appid) {
        Integer count = wxUserSalesmanBindingService.countBindingsBySlesman(salesmanId, appid);
        return R.ok().put("count", count);
    }

    /**
     * 查询即将过期的绑定关系
     */
    @RequestMapping("/getExpiring")
    @RequiresPermissions("salesman:wxuserbinding:list")
    @ApiOperation(value = "即将过期绑定", notes = "查询即将过期的绑定关系")
    public R getExpiring(@RequestParam(defaultValue = "7") Integer days, @CookieValue String appid) {
        List<WxUserSalesmanBindingEntity> bindings = wxUserSalesmanBindingService.getExpiringBindings(days, appid);
        return R.ok().put("bindings", bindings);
    }

    /**
     * 自动失效过期的绑定关系
     */
    @RequestMapping("/expireOverdue")
    @RequiresPermissions("salesman:wxuserbinding:update")
    @ApiOperation(value = "失效过期绑定", notes = "自动失效过期的绑定关系")
    public R expireOverdue(@CookieValue String appid) {
        Integer count = wxUserSalesmanBindingService.expireOverdueBindings(appid);
        return R.ok().put("expiredCount", count);
    }

    /**
     * 批量关联历史订单
     */
    @RequestMapping("/batchAssociateOrders")
    @RequiresPermissions("salesman:wxuserbinding:update")
    @ApiOperation(value = "关联历史订单", notes = "批量为历史订单关联业务员")
    public R batchAssociateOrders(@CookieValue String appid) {
        // 这个功能需要在OrderSalesmanAssociationService中实现
        return R.ok().put("message", "批量关联功能待实现");
    }

    /**
     * 获取绑定统计数据
     */
    @RequestMapping("/stats")
    @RequiresPermissions("salesman:wxuserbinding:list")
    @ApiOperation(value = "绑定统计", notes = "获取绑定关系的统计数据")
    public R getStats(@CookieValue String appid) {
        Map<String, Object> stats = wxUserSalesmanBindingService.getBindingStats(appid);
        return R.ok().put("stats", stats);
    }

    /**
     * 获取绑定历史记录
     */
    @RequestMapping("/history")
    @RequiresPermissions("salesman:wxuserbinding:list")
    @ApiOperation(value = "绑定历史", notes = "获取绑定变更历史记录")
    public R getHistory(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        List<WxUserSalesmanBindingLogEntity> page = wxUserSalesmanBindingService.getBindingHistory(params);
        return R.okList( page);
    }

    /**
     * 获取业务员客户统计
     */
    @RequestMapping("/customerStats")
    @RequiresPermissions("salesman:wxuserbinding:list")
    @ApiOperation(value = "客户统计", notes = "获取业务员的客户统计数据")
    public R getCustomerStats(@RequestParam Long salesmanId, @CookieValue String appid) {
        Map<String, Object> stats = wxUserSalesmanBindingService.getCustomerStats(salesmanId, appid);
        return R.ok().put("stats", stats);
    }

    /**
     * 验证邀请码（前端接口）
     */
    @PostMapping("/web/verifyInviteCode")
    @ApiOperation(value = "验证邀请码", notes = "验证邀请码并返回业务员信息")
    public R verifyInviteCode(@RequestBody Map<String, Object> params) {
        String inviteCode = (String) params.get("inviteCode");
        String appid = (String) params.get("appid");

        try {
            Map<String, Object> result = wxUserSalesmanBindingService.verifyInviteCode(inviteCode, appid);
            return R.ok().put("salesmanInfo", result);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 验证业务员ID（前端接口）
     */
    @PostMapping("/web/verifySalesmanId")
    @ApiOperation(value = "验证业务员ID", notes = "验证业务员ID并返回业务员信息")
    public R verifySalesmanId(@RequestBody Map<String, Object> params) {
        Long salesmanId = Long.valueOf(params.get("salesmanId").toString());
        String appid = (String) params.get("appid");

        try {
            Map<String, Object> result = wxUserSalesmanBindingService.verifySalesmanId(salesmanId, appid);
            return R.ok().put("salesmanInfo", result);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 客户绑定业务员（前端接口）
     */
    @PostMapping("/web/bindCustomer")
    @ApiOperation(value = "绑定客户", notes = "客户绑定业务员")
    public R bindCustomer(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        String inviteCode = (String) params.get("inviteCode");
        String bindingMethod = (String) params.get("bindingMethod");
        String appid = (String) params.get("appid");

        // 获取当前用户ID（从session或token中获取）
        Long wxUserId = getCurrentWxUserId(request);
        if (wxUserId == null) {
            return R.error("用户未登录");
        }

        try {
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.bindCustomerBySalesman(
                wxUserId, inviteCode, bindingMethod, appid);
            return R.ok().put("binding", binding);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 获取当前微信用户ID
     */
    private Long getCurrentWxUserId(HttpServletRequest request) {
        // 这里需要根据实际的用户认证方式来获取用户ID
        // 可能从session、token或其他方式获取
        return 1L; // 临时返回，实际需要实现
    }
}
