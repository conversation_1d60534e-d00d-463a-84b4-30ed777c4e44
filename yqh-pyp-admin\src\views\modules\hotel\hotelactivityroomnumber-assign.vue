<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    @closed="closeDialog"
    :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.orderSn" placeholder="订单编号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table size="mini" :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column show-overflow-tooltip prop="orderSn" header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="orderStatus" header-align="center" align="center" width="75"  label="订单状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-'+(scope.row.orderStatus)">{{orderStatus[scope.row.orderStatus].value}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="roomType" header-align="center" align="center" width="75"  label="房间类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-'+(scope.row.roomType)">{{ 
              roomType[scope.row.roomType].value}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" width="60"  label="已分/总">
        <div slot-scope="scope">
          {{ scope.row.assignNumber }}/{{ scope.row.number }}
        </div>
      </el-table-column>
      <el-table-column prop="contact" header-align="center" align="center" width="130"  label="联系人">
        <div slot-scope="scope">
          <el-input size="mini" width="100%" :disabled="scope.row.assignNumber == scope.row.number" v-model="scope.row.contact" placeholder="输入联系人" />
        </div>
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" width="130"  label="联系方式">
        <div slot-scope="scope">
          <el-input size="mini" width="100%" :disabled="scope.row.assignNumber == scope.row.number" v-model="scope.row.mobile" placeholder="输入联系方式" />
        </div>
      </el-table-column>
      <el-table-column prop="checkSex" header-align="center" align="center" width="70"  label="校验男女">
        <div slot-scope="scope">
        <el-switch v-if="scope.row.roomType != 0" :disabled="scope.row.assignNumber == scope.row.number" v-model="scope.row.checkSex">
        </el-switch>
        </div>
      </el-table-column>
      <el-table-column prop="tag" header-align="center" align="center" width="130"  label="备注(选填)">
        <div slot-scope="scope">
          <el-select filterable size="mini" v-model="scope.row.tag">
            <el-option v-for="item in assignTag" :key="item.id"  :label="item.name" :value="item.name"></el-option>
          </el-select>
        </div>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="80" label="操作" >
        <template slot-scope="scope">
            <el-button type="text"  v-if="scope.row.assignNumber != scope.row.number" size="small" @click="select(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭页面</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {orderStatus} from '@/data/common'
import {roomAssignStatus,roomType,roomTypeFjsd} from "@/data/room.js";
  export default {
    data () {
      return {
        appid: '',
        roomType,
        roomTypeFjsd,
        roomAssignStatus,
        orderStatus,
        visible: false,
        dataForm: {
          contact: '',
          mobile: '',
          roomId: '',
          numberId: '',
          activityId: '',
        },
        dataList: [],
        assignTag: [],
        dataListLoading: false,
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
      }
    },
    methods: {
      init (numberId,activityId,roomId) {
        this.appid = this.$cookie.get("appid");
        this.dataForm.numberId = numberId
        this.dataForm.roomId = roomId
        this.dataForm.activityId = activityId
        this.visible = true
        this.getDataList();
        this.getTag();
      },
      onSearch() {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList() {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelorderdetail/findByParams`),
          method: 'get',
          params: this.$http.adornParams({
            'roomId': this.dataForm.roomId,
            'activityId': this.dataForm.activityId,
            'orderSn': this.dataForm.orderSn,
            'contact': this.dataForm.contact,
            'mobile': this.dataForm.mobile,
            'page': this.pageIndex,
            'limit': this.pageSize,
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
        })
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      getTag() {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelassigntag/findByActivityId/${this.dataForm.activityId}`),
          method: 'get',
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.assignTag = data.result
          } 
        })
      },
      // 选择酒店
      select (v) {
        this.$confirm(`确认把该房号分配给该订单?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/hotel/hotelactivityroomnumber/assign`),
            method: 'post',
            data: this.$http.adornData({
              'numberId': this.dataForm.numberId,
              'orderDetailId': v.id,
              'mobile': v.mobile,
              'contact': v.contact,
              'checkSex': v.checkSex,
              'tag': v.tag,
            })
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      closeDialog() {
        this.$emit('refreshDataList')
      },
      isImageUrl(url) {
        return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
      }
    }
  }
</script>
