<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <el-row class="row">
        <el-col :span="8">
          <el-form-item label="选择酒店" prop="hotelActivityId">
            <el-select v-model="dataForm.hotelActivityId" @change="hotelChange">
              <el-option v-for="item in hotels" :key="item.id" :label="item.hotelName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择房型" prop="hotelActivityRoomId">
            <el-select v-model="dataForm.hotelActivityRoomId" @change="roomChange">
              <el-option v-for="item in rooms" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="indexRoom.bedStatus == 1 && indexRoom.bedNumber > 1">
          <el-form-item label="入住类型" prop="roomType">
            <el-select v-model="dataForm.roomType" @change="countPrice">
              <el-option v-for="item in roomType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="16">
          <el-form-item label="酒店时间" prop="inDate">
            <el-date-picker v-model="times" :picker-options="pickerOptions" @change="dateChange" value-format="yyyy/MM/dd"
              type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数量" prop="number">
            <el-input v-model="dataForm.number" @input="countPrice" placeholder="数量" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contact">
            <el-input v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="mobile">
            <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="12">
          <el-form-item label="订单状态" prop="status">
            <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
              <el-option v-for="item in orderStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付来源" prop="source">
            <el-select v-model="dataForm.source" placeholder="支付来源" filterable>
              <el-option v-for="item in sources" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="订单金额" prop="price">
        <el-input disabled v-model="dataForm.price" placeholder="订单总金额"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
      </el-form-item>
      <!-- <el-form-item label="应付总金额" prop="payAmount">
        <el-input v-model="dataForm.payAmount" placeholder="应付总金额"></el-input>
      </el-form-item>
      <el-form-item label="参会表id" prop="activityUserId">
        <el-input v-model="dataForm.activityUserId" placeholder="参会表id"></el-input>
      </el-form-item>
      <el-form-item label="付款人账号" prop="payAccount">
        <el-input v-model="dataForm.payAccount" placeholder="付款人账号"></el-input>
      </el-form-item>
      <el-form-item label="付款人支付流水号" prop="payTransaction">
        <el-input v-model="dataForm.payTransaction" placeholder="付款人支付流水号"></el-input>
      </el-form-item>
      <el-form-item label="支付时间" prop="payTime">
        <el-input v-model="dataForm.payTime" placeholder="支付时间"></el-input>
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  sources,
  orderStatus
} from '@/data/common'
import {
  roomType,
  roomTypeFjsd,
} from '@/data/room'
export default {
  data() {
    return {
      times: [],
      indexRoom: {},
      roomType,
      roomTypeFjsd,
      sources,
      orderStatus,
      hotels: [],
      rooms: [],
      visible: false,
      dataForm: {
        id: 0,
        activityId: '',
        status: 0,
        source: 0,
        price: 0,
        remarks: '',
        hotelId: '',
        hotelActivityId: '',
        hotelActivityRoomId: '',
        contact: '',
        mobile: '',
        inDate: '',
        outDate: '',
        dayNumber: '',
        roomType: 0,
        number: 1,
      },
      timeOptionRange: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            this.timeOptionRange = minDate
          }
          if (maxDate) {
            this.timeOptionRange = null
          }
        },
        disabledDate: (time) => {
          // 获取选中时间
          let timeOptionRange = this.timeOptionRange
          if (timeOptionRange) {
            return time.getTime() === timeOptionRange.getTime()
          }
        },
        disabledDate: (time) => {
          // time 是一个new Date数据
          if (this.indexRoom.inDate != null && this.indexRoom.outDate != null) {
            return (time.getTime()) < (new Date(this.indexRoom.inDate).getTime()) || time.getTime() >= (new Date(this.indexRoom.outDate).getTime() + 24 * 60 * 60 * 1000);//时间范围必须是时间戳
          }
        },
      },
      dataRule: {
        hotelActivityId: [{
          required: true,
          message: '会议酒店不能为空',
          trigger: 'blur'
        }],
        hotelActivityRoomId: [{
          required: true,
          message: '会议酒店房型不能为空',
          trigger: 'blur'
        }],
        status: [{
          required: true,
          message: '订单状态不能为空',
          trigger: 'blur'
        }],
        contact: [{
          required: true,
          message: '联系人不能为空',
          trigger: 'blur'
        }],
        mobile: [{
          required: true,
          message: '联系方式不能为空',
          trigger: 'blur'
        }],
        source: [{
          required: true,
          message: '支付来源不能为空',
          trigger: 'blur'
        }],
        price: [{
          required: true,
          message: '订单总金额不能为空',
          trigger: 'blur'
        }],
        activityUserId: [{
          required: true,
          message: '参会表id不能为空',
          trigger: 'blur'
        }],
        payAccount: [{
          required: true,
          message: '付款人账号不能为空',
          trigger: 'blur'
        }],
        payTransaction: [{
          required: true,
          message: '付款人支付流水号不能为空',
          trigger: 'blur'
        }],
        outDate: [{
          required: true,
          message: '支付时间不能为空',
          trigger: 'blur'
        }],
        number: [{
          required: true,
          message: '数量不能为空',
          trigger: 'blur'
        }],
        inDate: [{
          required: true,
          message: '支付时间不能为空',
          trigger: 'blur'
        }],
      }
    }
  },
  methods: {
    dateChange(v) {
      this.dataForm.inDate = v[0];
      this.dataForm.outDate = v[1];
      var times = new Date(v[1]).getTime() / 1000 - new Date(v[0]).getTime() / 1000;
      this.dataForm.dayNumber = parseInt(times / 60 / 60 / 24); //相差天数
      this.countPrice();
    },
    countPrice() {
      if (this.dataForm.hotelActivityRoomId && this.dataForm.outDate && this.dataForm.inDate && this.dataForm.roomType != null && this.dataForm.roomType !== '') {
        if (this.dataForm.roomType == 0) {
          // 购买整间按整间价格算
          this.dataForm.price =
            this.dataForm.dayNumber * this.indexRoom.price * this.dataForm.number;
        } else {
          // 购买床位按床位价格算
          this.dataForm.price =
            this.dataForm.dayNumber *
            this.indexRoom.bedPrice *
            this.dataForm.number;
        }
      }
    },
    roomChange(v) {
      this.indexRoom = this.rooms.filter(e => e.id == v)[0];
    },
    init(activityId) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.times = [];
        this.indexRoom = {};
        this.dataForm = {
          id: 0,
          activityId: '',
          status: 0,
          source: 0,
          price: 0,
          remarks: '',
          hotelId: '',
          hotelActivityId: '',
          hotelActivityRoomId: '',
          contact: '',
          mobile: '',
          inDate: '',
          outDate: '',
          dayNumber: '',
          roomType: 0,
          number: 1,
        },
          this.dataForm.activityId = activityId || 0
        this.findHotel()
      })
    },
    findHotel() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivity/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.hotels = data.result
          if (this.hotels.length == 1) {
            this.dataForm.hotelActivityId = this.hotels[0].id;
            this.findRoom(this.dataForm.hotelActivityId);
          }
        }
      })
    },
    hotelChange(v) {
      this.dataForm.hotelActivityRoomId = '';
      this.dataForm.roomType = 0;
      this.findRoom(v);
    },
    findRoom(v) {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroom/findByHotelActivityId/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.rooms = data.result
          if (this.rooms.length == 1) {
            this.indexRoom = this.rooms[0];
            this.dataForm.hotelActivityRoomId = this.rooms[0].id;
            this.countPrice();
          }
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/hotel/hotelorder/createOrder`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
