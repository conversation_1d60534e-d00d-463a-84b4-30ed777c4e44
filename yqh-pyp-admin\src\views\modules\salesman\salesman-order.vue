<template>
  <div class="mod-config">
    <!-- 页面标题 -->
    <div v-if="dataForm.salesmanName" class="page-title" style="margin-bottom: 20px;">
      <h3>{{ dataForm.salesmanName }} - 业务订单</h3>
    </div>

    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.userName" placeholder="用户姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.userMobile" placeholder="用户手机号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.orderType" placeholder="订单类型" clearable>
          <el-option label="充值套餐" :value="1"></el-option>
          <el-option label="活动套餐" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.orderStatus" placeholder="订单状态" clearable>
          <el-option label="待支付" :value="0"></el-option>
          <el-option label="已支付" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery()">查询</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计信息卡片 -->
    <div class="stats-cards" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ stats.totalOrders || 0 }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ stats.totalAmount || 0 }}</div>
              <div class="stats-label">总金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ stats.totalPayAmount || 0 }}</div>
              <div class="stats-label">已付款金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ stats.totalCommission || 0 }}</div>
              <div class="stats-label">总佣金</div>
            </div>
          </el-card>
        </el-col>
        <!-- <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ stats.activityOrders || 0 }} / {{ stats.rechargeOrders || 0 }}</div>
              <div class="stats-label">活动套餐 / 充值套餐</div>
            </div>
          </el-card>
        </el-col> -->
      </el-row>
    </div>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="业务员姓名">
      </el-table-column>
      <el-table-column prop="orderSn" header-align="center" align="center" width="180" label="订单编号">
        <template slot-scope="scope">
          {{ scope.row.orderSn || scope.row.id }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" header-align="center" align="center" width="120" label="用户姓名">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" width="130" label="用户手机号">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" width="100" label="订单状态">
        <template slot-scope="scope">
          <el-tag :type="getOrderStatusType(scope.row.status)">
            {{ getOrderStatusDesc(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rechargeType" header-align="center" align="center" width="100" label="订单类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.rechargeType === 4 ? 'success' : 'primary'">
            {{ scope.row.rechargeType === 4 ? '创建活动套餐' : '充值套餐' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" header-align="center" align="center" label="订单金额">
        <template slot-scope="scope">
          ¥{{ scope.row.payAmount || scope.row.amount }}
        </template>
      </el-table-column>
      <el-table-column prop="commissionRate" header-align="center" align="center" label="佣金比例">
        <template slot-scope="scope">
          {{ scope.row.commissionRate ? (scope.row.commissionRate * 100).toFixed(2) + '%' : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="commissionAmount" header-align="center" align="center" label="佣金金额">
        <template slot-scope="scope">
          {{ scope.row.commissionAmount ? '¥' + scope.row.commissionAmount : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="payTime" header-align="center" align="center" width="180" label="支付时间">
        <template slot-scope="scope">
          {{ scope.row.payTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="180" label="创建时间">
      </el-table-column>
      <!-- <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="updateCommissionHandle(scope.row.id)">设置佣金</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 佣金设置弹窗 -->
    <el-dialog title="设置佣金" :visible.sync="commissionDialogVisible" width="600px">
      <div
        style="margin-bottom: 15px; padding: 10px; background: #f5f7fa; border-radius: 4px; color: #606266; font-size: 13px;">
        <i class="el-icon-info" style="color: #409EFF;"></i>
        <strong>自动计算说明：</strong>修改佣金比例时会自动计算佣金金额，修改佣金金额时会自动计算佣金比例。
      </div>
      <el-form :model="commissionForm" :rules="commissionRules" ref="commissionForm" label-width="100px">
        <el-form-item label="订单金额" prop="orderAmount">
          <el-input-number v-model="commissionForm.orderAmount" :precision="2" :min="0" disabled placeholder="订单金额">
          </el-input-number>
          <span style="margin-left: 10px; color: #999;">订单总金额</span>
        </el-form-item>
        <el-form-item label="佣金比例" prop="commissionRate">
          <el-input-number v-model="commissionForm.commissionRate" :precision="4" :step="0.01" :max="1" :min="0"
            @change="onCommissionRateChange" placeholder="请输入佣金比例（0-1之间）">
          </el-input-number>
          <span style="margin-left: 10px; color: #999;">例：0.05 表示 5%，修改后自动计算金额</span>
        </el-form-item>
        <el-form-item label="佣金金额" prop="commissionAmount">
          <el-input-number v-model="commissionForm.commissionAmount" :precision="2" :step="0.01" :min="0"
            @change="onCommissionAmountChange" placeholder="请输入佣金金额">
          </el-input-number>
          <span style="margin-left: 10px; color: #999;">修改后自动计算比例</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="commissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateCommission()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        salesmanName: '',
        userName: '',
        userMobile: '',
        orderType: '',
        orderStatus: '',
        userId:'',
        salesmanId: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      stats: {},
      overallStats: {},
      commissionDialogVisible: false,
      commissionForm: {
        id: '',
        orderAmount: 0,
        commissionRate: 0,
        commissionAmount: 0
      },
      commissionRules: {
        commissionRate: [
          { required: true, message: '请输入佣金比例', trigger: 'blur' }
        ],
        commissionAmount: [
          { required: true, message: '请输入佣金金额', trigger: 'blur' }
        ]
      }
    }
  },
  activated() {
    this.initFromQuery()
    this.getDataList()
    this.getStats()
    this.getOverallStats()
  },
  mounted() {
    this.initFromQuery()
  },
  methods: {
    // 从查询参数初始化数据
    initFromQuery() {
      const query = this.$route.query
      if (query.salesmanId) {
        this.dataForm.salesmanId = query.salesmanId
      }
      if (query.userId) {
        this.dataForm.userId = query.userId
      }
      if (query.salesmanName) {
        this.dataForm.salesmanName = query.salesmanName
      }
    },
    // 处理查询操作
    handleQuery() {
      this.getDataList()
      this.getStats()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/rechargerecord/salesmanOrderList'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'salesmanName': this.dataForm.salesmanName,
          'userName': this.dataForm.userName,
          'userMobile': this.dataForm.userMobile,
          'salesmanId': this.dataForm.salesmanId,
          'orderType': this.dataForm.orderType,
            'userId': this.dataForm.userId,
          'orderStatus': this.dataForm.orderStatus
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取统计信息
    getStats() {
      this.$http({
        url: this.$http.adornUrl('/activity/rechargerecord/salesmanOrderStats'),
        method: 'get',
        params: this.$http.adornParams({
          'salesmanName': this.dataForm.salesmanName,
          'userName': this.dataForm.userName,
          'userMobile': this.dataForm.userMobile,
          'salesmanId': this.dataForm.salesmanId,
          'orderType': this.dataForm.orderType,
          'userId': this.dataForm.userId,
          'orderStatus': this.dataForm.orderStatus
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.stats = data.stats
          this.statsVisible = true
        }
      })
    },
    // 获取总体统计
    getOverallStats() {
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/orderStats'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.overallStats = data.stats
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 设置佣金
    updateCommissionHandle(id) {
      // 重置表单
      this.$refs['commissionForm'] && this.$refs['commissionForm'].resetFields()

      // 找到对应的订单数据
      const orderData = this.dataList.find(item => item.id === id)
      if (orderData) {
        this.commissionForm.id = id
        this.commissionForm.orderAmount = parseFloat(orderData.orderAmount) || 0
        this.commissionForm.commissionRate = parseFloat(orderData.commissionRate) || 0
        this.commissionForm.commissionAmount = parseFloat(orderData.commissionAmount) || 0
      }
      this.commissionDialogVisible = true
    },
    // 佣金比例变化时自动计算佣金金额
    onCommissionRateChange(value) {
      if (value !== null && value !== undefined && this.commissionForm.orderAmount > 0) {
        // 避免循环触发
        const calculatedAmount = parseFloat((this.commissionForm.orderAmount * value).toFixed(2))
        this.commissionForm.commissionAmount = calculatedAmount
      }
    },
    // 佣金金额变化时自动计算佣金比例
    onCommissionAmountChange(value) {
      if (value !== null && value !== undefined && this.commissionForm.orderAmount > 0) {
        // 避免循环触发
        const calculatedRate = parseFloat((value / this.commissionForm.orderAmount).toFixed(4))
        this.commissionForm.commissionRate = calculatedRate
      }
    },
    // 更新佣金
    updateCommission() {
      this.$refs['commissionForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/activity/rechargerecord/updateSalesmanOrderCommission'),
            method: 'post',
            data: this.$http.adornData(this.commissionForm)
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.commissionDialogVisible = false
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/rechargerecord/deleteSalesmanOrder'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 获取订单状态类型
    getOrderStatusType(status) {
      switch (status) {
        case 0:
          return 'warning' // 待支付
        case 1:
          return 'success' // 已支付
        case 2:
          return 'danger'  // 已取消
        case 3:
          return 'info'    // 已退款
        default:
          return 'info'    // 未知
      }
    },
    // 获取订单状态描述
    getOrderStatusDesc(status) {
      switch (status) {
        case 0:
          return '待支付'
        case 1:
          return '已支付'
        case 2:
          return '已取消'
        case 3:
          return '已退款'
        default:
          return '未知'
      }
    }
  }
}
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}
</style>
