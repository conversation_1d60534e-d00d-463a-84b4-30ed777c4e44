<template>
  <div>
    <div v-show="activityInfo.createBy != '1648304084598980609'" style="display: flex">
      <van-tabs :ellipsis="false" style="width: 80%" v-model="cmsId" @click="onClick">
        <van-tab v-for="item in cmsList" :key="item.id" :title="item.title" :name="item.id">
        </van-tab>
      </van-tabs>
      <van-dropdown-menu style="width: 20%">
        <van-dropdown-item title="更多" v-model="cmsId" :options="drList" @change="droChange" />
      </van-dropdown-menu>
    </div>
    <div class="content" v-html="cmsInfo.mobileContent || cmsInfo.content" @click="showImg($event)"></div>
    <div v-show="cmsInfo.longitude  && cmsInfo.latitude ">
      <div class="map" id="allmap"></div>
      <van-button @click="map" class="map-button" round type="info">导航去这里</van-button>
      <!-- <div class="text"><button @click="map"><img src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20231106/d60434b6c746480ebc9f299852f6d233.png"><span>导航去这里</span></button></div> -->
    </div>
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
  </div>
</template>

<script>
import { isURL } from "@/js/validate";

export default {
  components: {},
  data() {
    return {
    isExpire: false,
      openid: undefined,
      cmsId: undefined,
      cmsList: [],
      drList: [],
      cmsInfo: {},
      activityInfo: {},
    };
  },
  mounted() {
    this.openid = this.$cookie.get("openid");
    this.cmsId = this.$route.query.id;
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/cms/cms/info/${this.cmsId}`).then((res) => {
        if (res.code == 200) {
          this.cmsInfo = res.cms;
          document.title = this.cmsInfo.title;
          this.getActivityInfo(this.cmsInfo.activityId);
          this.getCmsList(this.cmsInfo.activityId);
          if (this.cmsInfo.longitude && this.cmsInfo.latitude) {
            this.theLocation(parseFloat(this.cmsInfo.longitude), parseFloat(this.cmsInfo.latitude));
          }
        } else {
          vant.Toast(res.msg);
          this.cmsInfo = {};
        }
      });
    },
    getIsExpire() {
      this.$fly.get(`/pyp/common/isExpire`, {
        endDate: this.activityInfo.endTime,
      }).then((res) => {
        if (res.code == 200) {
          this.isExpire = res.result;
          if (this.isExpire) {
            this.cmsInfo.content = '';
          }
        }
      });
    },
    onClick(item) {
      this.cmsInfo = this.cmsList.filter((q) => q.id == item)[0];
      if (this.cmsInfo.url && isURL(this.cmsInfo.url)) {
        location.href = this.cmsInfo.url;
      } else if (
        this.cmsInfo.model &&
        this.isJSON(this.cmsInfo.model)
      ) {
        var result = this.cmsInfo.model.replace(
          "${activityId}",
          this.cmsInfo.activityId
        );
        this.$router.push(JSON.parse(result));
      }
          if (this.isExpire) {
            this.cmsInfo.content = '';
          }
    },
    droChange(item) {
      if (item == 0) {
        this.cmsTurnBack();
        return false;
      }
      this.cmsInfo = this.cmsList.filter((q) => q.id == item)[0];
      if (this.cmsInfo.url && isURL(this.cmsInfo.url)) {
        location.href = this.cmsInfo.url;
      } else if (
        this.cmsInfo.model &&
        this.isJSON(this.cmsInfo.model)
      ) {
        var result = this.cmsInfo.model.replace(
          "${activityId}",
          this.cmsInfo.activityId
        );
        this.$router.push(JSON.parse(result));
      }
    },
    cmsTurnBack() {
      this.$router.go(-1);
    },
    getActivityInfo(id) {
      this.$fly.get(`/pyp/activity/activity/info/${id}`).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.activityInfo = res.activity;
          this.activityInfo.backImg =
            this.activityInfo.backImg ||
            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
          this.$wxShare(
            ((this.activityInfo.name || '') + "-" + (this.cmsInfo.title || '')),
            (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
            null
          ); //加载微信分享
          this.getIsExpire();
        } else {
          vant.Toast(res.msg);
          this.activityInfo = {};
        }
      });
    },
    getCmsList(id) {
      this.$fly.get(`/pyp/cms/cms/findByActivityId/${id}`).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.cmsList = res.result;
          this.drList.push({
            text: "返回首页",
            value: 0,
          });
          this.cmsList.forEach((e) => {
            this.drList.push({
              text: e.title,
              value: e.id,
            });
          });
        } else {
          vant.Toast(res.msg);
          this.cmsList = [];
        }
      });
    },
    isJSON(v) {
      try {
        JSON.parse(v);
        return true;
      } catch (error) {
        return false;
      }
    }, // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
    map() {
      wx.openLocation({
        latitude: parseFloat(this.cmsInfo.longitude), // 纬度，浮点数，范围为90 ~ -90
        longitude: parseFloat(this.cmsInfo.latitude), // 经度，浮点数，范围为180 ~ -180。
        name: '导航目的地', // 位置名
        address: '导航目的地', // 地址详情说明
        scale: 20, // 地图缩放级别,整形值,范围从1~28。默认为最大
        infoUrl: 'https://weixin.qq.com' // 在查看位置界面底部显示的超链接,可点击跳转
      });
      // Toast("功能正在开发中..")
    },
    // 用经纬度设置地图中心点
    theLocation(longitude, latitude) {
      if (longitude != "" && latitude != "") {
        var center = new qq.maps.LatLng(longitude, latitude);
        var map = new qq.maps.Map(document.getElementById('allmap'), {
          center: center,
          zoom: 18
        });
        //创建marker
        var marker = new qq.maps.Marker({
          position: center,
          map: map
        });
      }
    }
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;

  /deep/ p {
    width: 100%;
  }

  /deep/ img {
    width: 100%;
    height: auto;
  }
}

.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}

.map {
  font-size: 12px;
  height: 200px;
  width: 100%;
  padding: 8px 0 20px
}

.map-button {
  width: 90%;
  margin-left: 5%;
  margin-top: 10px;
}
</style>