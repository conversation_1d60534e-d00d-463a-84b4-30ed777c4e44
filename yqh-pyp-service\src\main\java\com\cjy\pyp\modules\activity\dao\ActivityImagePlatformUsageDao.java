package com.cjy.pyp.modules.activity.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.activity.entity.ActivityImagePlatformUsageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 图片平台使用记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-29
 */
@Mapper
public interface ActivityImagePlatformUsageDao extends BaseMapper<ActivityImagePlatformUsageEntity> {
    
    /**
     * 获取图片在指定平台的使用次数
     * 
     * @param imageId 图片ID
     * @param platform 平台
     * @return 使用次数，如果没有记录返回0
     */
    Integer getUsageCount(@Param("imageId") Long imageId, @Param("platform") String platform);
    
    /**
     * 增加图片在指定平台的使用次数 - 已改为Java代码实现
     *
     * @param imageId 图片ID
     * @param platform 平台
     * @param activityId 活动ID
     * @return 影响的行数
     */
    // int incrementUsageCount(@Param("imageId") Long imageId, @Param("platform") String platform, @Param("activityId") Long activityId);
}
