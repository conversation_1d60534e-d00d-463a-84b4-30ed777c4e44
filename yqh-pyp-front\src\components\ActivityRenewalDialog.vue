<template>
  <van-popup 
    v-model="visible" 
    position="bottom" 
    :style="{ height: '70%' }"
    round
    closeable
    @close="handleClose"
  >
    <div class="renewal-dialog">
      <div class="dialog-header">
        <h3>活动续费</h3>
        <p class="activity-name">{{ activityName }}</p>
      </div>
      
      <div class="dialog-content">
        <!-- 当前状态 -->
        <div class="current-status">
          <h4>当前状态</h4>
          <ActivityExpirationStatus 
            :status="expirationStatus" 
            :show-renewal-button="false"
          />
        </div>
        
        <!-- 续费套餐选择 -->
        <div class="package-selection">
          <h4>选择续费套餐</h4>
          <div class="package-list">
            <div 
              v-for="pkg in renewalPackages" 
              :key="pkg.id"
              class="package-item"
              :class="{ active: selectedPackageId === pkg.id }"
              @click="selectPackage(pkg)"
            >
              <div class="package-info">
                <div class="package-name">{{ pkg.name }}</div>
                <div class="package-desc">{{ pkg.description }}</div>
                <div class="package-days">延长 {{ pkg.renewalDays }} 天</div>
              </div>
              <div class="package-price">
                <span class="current-price">¥{{ pkg.price }}</span>
                <span v-if="pkg.originalPrice > pkg.price" class="original-price">
                  ¥{{ pkg.originalPrice }}
                </span>
              </div>
              <van-icon 
                v-if="selectedPackageId === pkg.id" 
                name="success" 
                color="#07c160"
              />
            </div>
          </div>
        </div>
        
        <!-- 续费后预览 -->
        <div v-if="selectedPackage" class="renewal-preview">
          <h4>续费后</h4>
          <div class="preview-info">
            <div class="preview-item">
              <span>延长天数：</span>
              <span class="highlight">{{ selectedPackage.renewalDays }} 天</span>
            </div>
            <div class="preview-item">
              <span>新过期时间：</span>
              <span class="highlight">{{ getNewExpirationTime() }}</span>
            </div>
            <div class="preview-item">
              <span>支付金额：</span>
              <span class="price-highlight">¥{{ selectedPackage.price }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <van-button 
          block 
          type="primary" 
          :disabled="!selectedPackageId"
          :loading="submitting"
          @click="handleRenewal"
        >
          {{ submitting ? '处理中...' : `立即支付 ¥${selectedPackage ? selectedPackage.price : 0}` }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import ActivityExpirationStatus from './ActivityExpirationStatus.vue';

export default {
  name: 'ActivityRenewalDialog',
  components: {
    ActivityExpirationStatus
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: [String, Number],
      default: null
    },
    activityName: {
      type: String,
      default: ''
    },
    expirationStatus: {
      type: Object,
      default: null
    },
    renewalPackages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: this.value,
      selectedPackageId: null,
      submitting: false
    };
  },
  computed: {
    selectedPackage() {
      return this.renewalPackages.find(pkg => pkg.id === this.selectedPackageId);
    }
  },
  watch: {
    value(newVal) {
      this.visible = newVal;
      if (newVal) {
        this.resetSelection();
      }
    },
    visible(newVal) {
      this.$emit('input', newVal);
    }
  },
  methods: {
    selectPackage(pkg) {
      this.selectedPackageId = pkg.id;
    },
    
    resetSelection() {
      this.selectedPackageId = null;
      this.submitting = false;
    },
    
    getNewExpirationTime() {
      if (!this.selectedPackage || !this.expirationStatus) return '';
      
      const currentExpiration = this.expirationStatus.expirationTime;
      const now = new Date();
      let baseTime;
      
      if (!currentExpiration || new Date(currentExpiration) < now) {
        // 如果没有过期时间或已过期，从现在开始计算
        baseTime = now;
      } else {
        // 从当前过期时间开始计算
        baseTime = new Date(currentExpiration);
      }
      
      const newExpiration = new Date(baseTime.getTime() + this.selectedPackage.renewalDays * 24 * 60 * 60 * 1000);
      return newExpiration.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    async handleRenewal() {
      if (!this.selectedPackageId || !this.activityId) return;
      
      this.submitting = true;
      try {
        const orderData = {
          activityId: this.activityId,
          packageId: this.selectedPackageId,
          repeatToken: this.generateRepeatToken()
        };
        
        this.$emit('renewal', orderData);
      } catch (error) {
        console.error('续费失败:', error);
        this.$toast.fail(error.message || '续费失败');
      } finally {
        this.submitting = false;
      }
    },
    
    handleClose() {
      this.visible = false;
    },
    
    generateRepeatToken() {
      return Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
  }
};
</script>

<style scoped>
.renewal-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: 20px 16px 16px;
  border-bottom: 1px solid #ebedf0;
}

.dialog-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.activity-name {
  margin: 0;
  font-size: 14px;
  color: #969799;
}

.dialog-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.current-status,
.package-selection,
.renewal-preview {
  margin-bottom: 24px;
}

.current-status h4,
.package-selection h4,
.renewal-preview h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.package-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.package-item.active {
  border-color: #07c160;
  background-color: #f7fcff;
}

.package-info {
  flex: 1;
}

.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.package-desc {
  font-size: 13px;
  color: #969799;
  margin-bottom: 4px;
}

.package-days {
  font-size: 14px;
  color: #07c160;
  font-weight: 500;
}

.package-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #ee0a24;
}

.original-price {
  font-size: 12px;
  color: #969799;
  text-decoration: line-through;
}

.preview-info {
  background-color: #f7f8fa;
  padding: 16px;
  border-radius: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.highlight {
  color: #07c160;
  font-weight: 500;
}

.price-highlight {
  color: #ee0a24;
  font-weight: 600;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #ebedf0;
}
</style>
