<template>
  <div class="children-page">
    <pcheader v-if="!isMobilePhone" />

    <!-- 自定义导航栏 -->
    <div class="custom-navbar">
      <van-icon name="arrow-left" @click="$router.go(-1)" class="nav-back" />
      <span class="nav-title">子业务员管理</span>
      <div class="nav-actions">
        <van-icon name="orders-o" @click="goToOrderList" class="nav-action" />
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#ffffff" size="24px" class="loading-center">
      正在加载...
    </van-loading>

    <!-- 内容区域 -->
    <div v-else class="content-section">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <div class="stats-card">
          <div class="card-header">
            <div class="header-icon">
              <van-icon name="chart-trending-o" size="20" color="#ffffff" />
            </div>
            <div class="header-text">
              <h3>团队概况</h3>
              <p>下级业务员统计</p>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ children.length }}</div>
              <div class="stat-label">直属下级</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ totalGrandChildren }}</div>
              <div class="stat-label">间接下级</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ totalTeamOrders }}</div>
              <div class="stat-label">团队订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">¥{{ totalTeamAmount.toFixed(0) }}</div>
              <div class="stat-label">团队业绩</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 子业务员列表 -->
      <div v-if="children.length > 0" class="children-list">
        <div class="section-header">
          <h3>下级业务员列表</h3>
          <span class="count-badge">{{ children.length }}人</span>
        </div>

        <div
          v-for="child in children"
          :key="child.id"
          class="child-card"
          @click="viewChildDetails(child)"
        >
          <div class="child-header">
            <div class="child-avatar">
              <van-icon name="manager-o" size="24" />
            </div>
            <div class="child-info">
              <div class="child-name">
                {{ child.name }}
                <van-tag
                  v-if="child.level"
                  :type="getLevelType(child.level)"
                  size="mini"
                  class="level-tag"
                >
                  L{{ child.level }}
                </van-tag>
              </div>
              <div class="child-details">
                <span class="detail-item">{{ child.code }}</span>
                <span class="detail-item">{{ child.mobile }}</span>
              </div>
            </div>
            <div class="child-actions">
              <van-icon name="arrow" size="16" />
            </div>
          </div>

          <!-- 业绩统计 -->
          <div class="performance-grid">
            <div class="perf-item">
              <div class="perf-number">{{ child.totalOrders || 0 }}</div>
              <div class="perf-label">订单</div>
            </div>
            <div class="perf-item">
              <div class="perf-number">¥{{ (child.totalAmount || 0).toFixed(0) }}</div>
              <div class="perf-label">销售额</div>
            </div>
            <div class="perf-item">
              <div class="perf-number">{{ child.childrenCount || 0 }}</div>
              <div class="perf-label">下级</div>
            </div>
          </div>

          <div class="child-footer">
            <span class="join-time">加入时间：{{ formatDate(child.createOn) }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <van-empty description="暂无下级业务员">
          <van-button type="primary" size="small" @click="goToInvite" round>
            邀请业务员
          </van-button>
        </van-empty>
      </div>
    </div>

    <!-- 子业务员详情弹窗 -->
    <van-popup v-model="showChildDetails" position="bottom" round>
      <div v-if="selectedChild" class="details-popup">
        <!-- 弹窗头部 -->
        <div class="details-header">
          <div class="header-left">
            <div class="avatar-container">
              <div class="avatar">
                <van-icon name="manager-o" size="24" />
              </div>
              <div class="level-badge" :class="'level-' + (selectedChild.level || 1)">
                L{{ selectedChild.level || 1 }}
              </div>
            </div>
            <div class="header-info">
              <h3>{{ selectedChild.name }}</h3>
              <p>{{ selectedChild.code }}</p>
            </div>
          </div>
          <div class="header-right">
            <van-icon name="cross" @click="showChildDetails = false" class="close-btn" />
          </div>
        </div>

        <!-- 弹窗内容 -->
        <div class="details-content">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <van-icon name="contact" size="18" />
              <h4>基本信息</h4>
            </div>
            <div class="card-content">
              <div class="info-row">
                <div class="info-item">
                  <van-icon name="user-o" size="16" />
                  <span class="info-label">姓名</span>
                  <span class="info-value">{{ selectedChild.name }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <van-icon name="idcard" size="16" />
                  <span class="info-label">编号</span>
                  <span class="info-value">{{ selectedChild.code }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <van-icon name="phone-o" size="16" />
                  <span class="info-label">手机</span>
                  <span class="info-value">{{ selectedChild.mobile }}</span>
                </div>
              </div>
              <div class="info-row" v-if="selectedChild.email">
                <div class="info-item">
                  <van-icon name="envelop-o" size="16" />
                  <span class="info-label">邮箱</span>
                  <span class="info-value">{{ selectedChild.email }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 业绩统计卡片 -->
          <div class="info-card" v-if="selectedChildStats">
            <div class="card-header">
              <van-icon name="chart-trending-o" size="18" />
              <h4>业绩统计</h4>
            </div>
            <div class="card-content">
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-icon orders">
                    <van-icon name="orders-o" size="20" />
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">{{ selectedChildStats.totalOrders || 0 }}</span>
                    <span class="stat-label">总订单</span>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon amount">
                    <van-icon name="gold-coin-o" size="20" />
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">¥{{ (selectedChildStats.totalAmount || 0).toFixed(2) }}</span>
                    <span class="stat-label">销售总额</span>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon commission">
                    <van-icon name="cash-back-record" size="20" />
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">¥{{ (selectedChildStats.totalCommission || 0).toFixed(2) }}</span>
                    <span class="stat-label">佣金收入</span>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon team">
                    <van-icon name="friends-o" size="20" />
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">{{ selectedChildStats.grandChildrenCount || 0 }}</span>
                    <span class="stat-label">下级业务员</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 弹窗底部 -->
        <div class="details-footer">
          <van-button
            @click="showChildDetails = false"
            size="large"
            type="primary"
            round
            block
          >
            <van-icon name="success" size="16" />
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  isMobilePhone
} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";

export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      loading: false,
      children: [],
      totalGrandChildren: 0,
      showChildDetails: false,
      selectedChild: null,
      selectedChildStats: null
    }
  },
  computed: {
    // 计算团队总订单数
    totalTeamOrders() {
      return this.children.reduce((total, child) => {
        return total + (child.totalOrders || 0);
      }, 0);
    },
    // 计算团队总业绩
    totalTeamAmount() {
      return this.children.reduce((total, child) => {
        return total + (child.totalAmount || 0);
      }, 0);
    }
  },
  mounted() {
    document.title = "子业务员管理";
    this.loadChildren();
  },
  methods: {
    // 加载子业务员列表
    async loadChildren() {
      try {
        this.loading = true;
        const res = await this.$fly.get('/pyp/web/salesman/getChildren');
        
        if (res.code === 200) {
          this.children = res.children || [];
          // 计算总的间接下级数量
          this.totalGrandChildren = this.children.reduce((total, child) => {
            return total + (child.childrenCount || 0);
          }, 0);
        } else {
          vant.Toast(res.msg || '加载失败');
        }
      } catch (error) {
        console.error('加载子业务员列表失败:', error);
        vant.Toast('加载失败');
      } finally {
        this.loading = false;
      }
    },

    // 查看子业务员详情
    async viewChildDetails(child) {
      try {
        this.selectedChild = child;
        
        // 获取详细统计数据
        const res = await this.$fly.get('/pyp/web/salesman/getChildStats', {
          childId: child.id
        });
        
        if (res.code === 200) {
          this.selectedChildStats = res.stats;
          this.showChildDetails = true;
        } else {
          vant.Toast(res.msg || '获取详情失败');
        }
      } catch (error) {
        console.error('获取子业务员详情失败:', error);
        vant.Toast('获取详情失败');
      }
    },

    // 获取层级类型
    getLevelType(level) {
      switch (level) {
        case 1:
          return 'success';  // 一级：绿色
        case 2:
          return 'primary';  // 二级：蓝色
        case 3:
          return 'warning';  // 三级：橙色
        default:
          return 'default';  // 其他：灰色
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString('zh-CN');
    },

    // 跳转到邀请页面
    goToInvite() {
      this.$router.push({
        name: 'salesmanInvite',
        query: this.$route.query
      });
    },

    // 跳转到订单列表页面
    goToOrderList() {
      this.$router.push({ name: 'salesmanOrders' });
    }
  }
}
</script>

<style lang="less" scoped>
.children-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: transparent;
  position: relative;
  z-index: 10;
}

.nav-back {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  background: transparent;
}

.nav-actions {
  display: flex;
  gap: 12px;
}

.nav-action {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.nav-action:hover {
  background: rgba(255, 255, 255, 0.3);
}

.loading-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
}

/* 内容区域 */
.content-section {
  background: #f8f9fa;
  border-radius: 12px ;
  margin-top: 20px;
  position: relative;
  z-index: 2;
  min-height: calc(100vh - 100px);
  padding: 20px 16px ;
  margin: 10px;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 20px;
}

.stats-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #1989fa 0%, #0066cc 100%);
  border-radius: 10px;
  margin-right: 12px;
  color: white;
}

.header-text h3 {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.header-text p {
  margin: 0;
  font-size: 12px;
  color: #999;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 16px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

/* 子业务员列表 */
.children-list {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.count-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.child-card {
  background: white;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.child-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.child-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.child-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin-right: 12px;
  color: white;
}

.child-info {
  flex: 1;
}

.child-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-tag {
  font-size: 10px !important;
}

.child-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.detail-item {
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
}

.child-actions {
  color: #c8c9cc;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.perf-item {
  text-align: center;
  padding: 10px 6px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.perf-number {
  font-size: 14px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 2px;
}

.perf-label {
  font-size: 10px;
  color: #666;
}

.child-footer {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.join-time {
  font-size: 11px;
  color: #999;
}

/* 空状态 */
.empty-state {
  margin-top: 100px;
  text-align: center;
}

.performance-stats {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.mini-stats {
  text-align: center;
  padding: 8px 5px;
}

.mini-number {
  font-size: 14px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 3px;
}

.mini-label {
  font-size: 11px;
  color: #999;
}

.join-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 详情弹窗样式 */
/* 详情弹窗样式 */
.details-popup {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  max-height: 75vh;
  display: flex;
  flex-direction: column;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px 20px;
  background: transparent;
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  position: relative;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.level-badge {
  position: absolute;
  top: -3px;
  right: -3px;
  padding: 1px 5px;
  border-radius: 6px;
  font-size: 9px;
  font-weight: 700;
  color: white;
  min-width: 18px;
  text-align: center;
}

.level-1 { background: #4CAF50; }
.level-2 { background: #2196F3; }
.level-3 { background: #FF9800; }
.level-4 { background: #E91E63; }
.level-5 { background: #9C27B0; }

.header-info h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
}

.header-info p {
  margin: 2px 0 0 0;
  font-size: 13px;
  opacity: 0.9;
}

.close-btn {
  padding: 6px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.details-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
  background: #f8f9fa;
}

.info-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.card-header .van-icon {
  color: #667eea;
}

.card-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 16px;
}

.info-row {
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
}

.info-item .van-icon {
  color: #667eea;
  flex-shrink: 0;
}

.info-label {
  color: #666;
  font-size: 13px;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-size: 13px;
  font-weight: 600;
  flex: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  color: white;
}

.stat-icon.orders {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #FF9800, #f57c00);
}

.stat-icon.commission {
  background: linear-gradient(135deg, #E91E63, #c2185b);
}

.stat-icon.team {
  background: linear-gradient(135deg, #2196F3, #1976d2);
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.stat-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.details-footer {
  padding: 16px 20px 20px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.details-footer .van-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.details-footer .van-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}
</style>
