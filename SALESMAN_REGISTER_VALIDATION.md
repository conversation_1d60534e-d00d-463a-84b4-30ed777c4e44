# 业务员注册页面身份校验功能

## 功能概述

在业务员注册页面添加了身份校验功能，确保只有非业务员用户才能进行注册，并自动填充用户的手机号和姓名信息。

## 主要功能

### 1. 身份校验
- **校验接口**：使用 `/pyp/web/salesman/scanByAuth` 接口检查用户是否已经是业务员
- **校验时机**：页面加载时自动进行身份校验
- **校验结果**：
  - 如果已是业务员：显示提示并跳转到业务员页面
  - 如果不是业务员：允许继续注册流程

### 2. 用户信息自动填充
- **获取接口**：使用 `/pyp/wxUser/getUserInfo` 接口获取用户信息
- **自动填充字段**：
  - 姓名：自动填充到表单的姓名字段
  - 手机号：自动填充到表单的手机号字段
- **填充时机**：获取用户信息成功后自动填充

### 3. 页面状态管理
- **加载状态**：显示"正在验证身份..."的加载提示
- **条件显示**：只有通过身份校验的非业务员用户才能看到注册表单
- **错误处理**：处理各种异常情况，确保用户体验

## 技术实现

### 1. 数据属性扩展
```javascript
data() {
  return {
    // ... 其他属性
    userInfo: null,           // 用户信息
    isSalesman: false,        // 是否为业务员
    checkingStatus: true,     // 是否正在检查状态
  }
}
```

### 2. 核心方法

#### getUserInfo() - 获取用户信息
```javascript
getUserInfo() {
  this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
    if (res.code == 200) {
      this.userInfo = res.data;
      this.fillUserInfo();           // 自动填充用户信息
      this.checkSalesmanStatus();    // 检查业务员身份
      this.loadInviterInfo();        // 加载邀请信息
    } else {
      if (!handleMobileError(res, window.location.href)) {
        vant.Toast(res.msg);
      }
    }
  })
}
```

#### fillUserInfo() - 自动填充用户信息
```javascript
fillUserInfo() {
  if (this.userInfo) {
    this.form.name = this.userInfo.name || '';
    this.form.mobile = this.userInfo.mobile || '';
  }
}
```

#### checkSalesmanStatus() - 检查业务员身份
```javascript
async checkSalesmanStatus() {
  try {
    const res = await this.$fly.get('/pyp/web/salesman/scanByAuth');
    
    if (res.code === 200) {
      // 已是业务员，显示提示并跳转
      this.isSalesman = true;
      vant.Dialog.alert({
        title: '提示',
        message: '您已经是业务员，无需重复注册',
        confirmButtonText: '确定'
      }).then(() => {
        this.$router.push('/salesman/qrcode');
      });
    } else {
      // 不是业务员，可以继续注册
      this.isSalesman = false;
    }
  } catch (error) {
    // 接口调用失败，假设不是业务员
    this.isSalesman = false;
  } finally {
    this.checkingStatus = false;
  }
}
```

### 3. 页面模板结构
```html
<template>
  <div>
    <!-- 页面标题 -->
    <van-nav-bar title="业务员注册" />

    <!-- 加载状态 -->
    <div v-if="checkingStatus">
      <van-loading>正在验证身份...</van-loading>
    </div>

    <!-- 主要内容 - 只有非业务员才显示 -->
    <div v-else-if="!isSalesman">
      <!-- 邀请信息 -->
      <van-card>...</van-card>
      
      <!-- 注册表单 -->
      <van-form>...</van-form>
      
      <!-- 业务员权益说明 -->
      <van-card>...</van-card>
    </div>

    <!-- 成功注册弹窗 -->
    <van-dialog>...</van-dialog>
  </div>
</template>
```

## 用户流程

### 1. 正常注册流程
1. 用户点击邀请链接进入注册页面
2. 页面显示"正在验证身份..."加载状态
3. 系统获取用户信息并检查业务员身份
4. 如果不是业务员：
   - 自动填充姓名和手机号
   - 显示邀请信息和注册表单
   - 用户完善其他信息并提交注册

### 2. 已是业务员的流程
1. 用户点击邀请链接进入注册页面
2. 页面显示"正在验证身份..."加载状态
3. 系统检测到用户已是业务员
4. 显示提示弹窗："您已经是业务员，无需重复注册"
5. 用户点击确定后跳转到业务员页面

### 3. 异常处理流程
- **网络错误**：显示错误提示，允许用户重试
- **手机号未绑定**：使用handleMobileError处理手机号相关错误
- **接口调用失败**：假设用户不是业务员，允许继续注册

## 依赖和导入

### 1. 新增导入
```javascript
import { handleMobileError } from "@/js/common";
```

### 2. 使用的接口
- `/pyp/wxUser/getUserInfo` - 获取用户信息
- `/pyp/web/salesman/scanByAuth` - 检查业务员身份
- `/pyp/web/salesman/info/{id}` - 获取邀请业务员信息
- `/pyp/web/channel/info/{id}` - 获取邀请渠道信息

## 优势特点

1. **防重复注册**：有效防止已是业务员的用户重复注册
2. **用户体验优化**：自动填充用户信息，减少输入工作
3. **状态反馈**：清晰的加载状态和错误提示
4. **流程引导**：已是业务员的用户自动跳转到相应页面
5. **兼容性好**：保持与原有功能的完全兼容

## 测试建议

1. **非业务员用户测试**：
   - 使用未注册为业务员的账号访问邀请链接
   - 验证信息自动填充功能
   - 验证注册流程正常

2. **已是业务员用户测试**：
   - 使用已注册为业务员的账号访问邀请链接
   - 验证提示弹窗显示
   - 验证跳转到业务员页面

3. **异常情况测试**：
   - 网络异常时的处理
   - 接口返回错误时的处理
   - 手机号未绑定时的处理
