package com.cjy.pyp.modules.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.activity.dao.ActivityRefundRecordDao;
import com.cjy.pyp.modules.activity.entity.ActivityRefundRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRefundRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 退款记录表
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service("activityRefundRecordService")
public class ActivityRefundRecordServiceImpl extends ServiceImpl<ActivityRefundRecordDao, ActivityRefundRecordEntity> implements ActivityRefundRecordService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<ActivityRefundRecordEntity> page = this.page(
                new Query<ActivityRefundRecordEntity>().getPage(params),
                new QueryWrapper<ActivityRefundRecordEntity>()
        );

        return new PageUtils(page);
    }

    @Override
    public void createRefundRecord(ActivityRefundRecordEntity refundRecord) {
        refundRecord.setCreateTime(new Date());
        refundRecord.setUpdateTime(new Date());
        this.save(refundRecord);
    }

    @Override
    public void updateRefundStatus(Long refundRecordId, Integer status, String remarks) {
        ActivityRefundRecordEntity refundRecord = this.getById(refundRecordId);
        if (refundRecord != null) {
            refundRecord.setStatus(status);
            refundRecord.setUpdateTime(new Date());
            if (remarks != null) {
                refundRecord.setRemarks(remarks);
            }
            this.updateById(refundRecord);
        }
    }

    @Override
    public ActivityRefundRecordEntity getByRechargeRecordId(Long rechargeRecordId) {
        QueryWrapper<ActivityRefundRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("recharge_record_id", rechargeRecordId);
        wrapper.orderByDesc("create_time");
        wrapper.last("LIMIT 1");
        return this.getOne(wrapper);
    }
}
