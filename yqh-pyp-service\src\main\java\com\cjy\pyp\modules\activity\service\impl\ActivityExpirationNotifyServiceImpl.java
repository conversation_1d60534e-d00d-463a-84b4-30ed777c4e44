package com.cjy.pyp.modules.activity.service.impl;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityNotifyEntity;
import com.cjy.pyp.modules.activity.service.ActivityExpirationNotifyService;
import com.cjy.pyp.modules.activity.service.ActivityExpirationService;
import com.cjy.pyp.modules.activity.service.ActivityNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 活动过期通知服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Slf4j
@Service
public class ActivityExpirationNotifyServiceImpl implements ActivityExpirationNotifyService {

    @Autowired
    private ActivityNotifyService activityNotifyService;

    @Autowired
    private ActivityExpirationService activityExpirationService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    // 通知类型常量
    private static final int NOTIFY_TYPE_EXPIRING_SOON = 7;  // 即将过期
    private static final int NOTIFY_TYPE_EXPIRED = 8;        // 已过期
    private static final int NOTIFY_TYPE_RENEWAL_SUCCESS = 9; // 续费成功

    @Override
    public R sendExpirationReminder(ActivityEntity activity, Integer remainingDays) {
        try {
            // 构建提醒消息
            String message = buildExpirationReminderMessage(activity, remainingDays);
            String url = buildActivityUrl(activity.getId());

            // 创建通知记录
            ActivityNotifyEntity notify = new ActivityNotifyEntity();
            notify.setActivityId(activity.getId());
            notify.setName(message);
            notify.setType(NOTIFY_TYPE_EXPIRING_SOON);
            notify.setUrl(url);
            notify.setAppid(activity.getAppid());
            notify.setRead(0); // 未读状态

            // 保存通知
            activityNotifyService.save(notify);

            log.info("发送活动即将过期提醒成功: activityId={}, remainingDays={}", activity.getId(), remainingDays);
            return R.ok().put("notifyId", notify.getId());

        } catch (Exception e) {
            log.error("发送活动即将过期提醒失败: activityId={}, error={}", activity.getId(), e.getMessage(), e);
            return R.error("发送提醒失败: " + e.getMessage());
        }
    }

    @Override
    public R sendExpirationNotification(ActivityEntity activity) {
        try {
            // 构建过期通知消息
            String message = buildExpirationNotificationMessage(activity);
            String url = buildRenewalUrl(activity.getId());

            // 创建通知记录
            ActivityNotifyEntity notify = new ActivityNotifyEntity();
            notify.setActivityId(activity.getId());
            notify.setName(message);
            notify.setType(NOTIFY_TYPE_EXPIRED);
            notify.setUrl(url);
            notify.setAppid(activity.getAppid());
            notify.setRead(0); // 未读状态

            // 保存通知
            activityNotifyService.save(notify);

            log.info("发送活动已过期通知成功: activityId={}", activity.getId());
            return R.ok().put("notifyId", notify.getId());

        } catch (Exception e) {
            log.error("发送活动已过期通知失败: activityId={}, error={}", activity.getId(), e.getMessage(), e);
            return R.error("发送通知失败: " + e.getMessage());
        }
    }

    @Override
    public R sendRenewalSuccessNotification(ActivityEntity activity, Integer renewalDays, String newExpirationTime) {
        try {
            // 构建续费成功消息
            String message = buildRenewalSuccessMessage(activity, renewalDays, newExpirationTime);
            String url = buildActivityUrl(activity.getId());

            // 创建通知记录
            ActivityNotifyEntity notify = new ActivityNotifyEntity();
            notify.setActivityId(activity.getId());
            notify.setName(message);
            notify.setType(NOTIFY_TYPE_RENEWAL_SUCCESS);
            notify.setUrl(url);
            notify.setAppid(activity.getAppid());
            notify.setRead(0); // 未读状态

            // 保存通知
            activityNotifyService.save(notify);

            log.info("发送活动续费成功通知成功: activityId={}, renewalDays={}", activity.getId(), renewalDays);
            return R.ok().put("notifyId", notify.getId());

        } catch (Exception e) {
            log.error("发送活动续费成功通知失败: activityId={}, error={}", activity.getId(), e.getMessage(), e);
            return R.error("发送通知失败: " + e.getMessage());
        }
    }

    @Override
    public R batchSendExpirationReminders(Integer days) {
        try {
            // 获取即将过期的活动列表
            List<ActivityEntity> expiringSoonActivities = activityExpirationService.getActivitiesExpiringSoon(days);
            
            List<ActivityNotifyEntity> notifications = new ArrayList<>();
            int successCount = 0;

            for (ActivityEntity activity : expiringSoonActivities) {
                try {
                    // 计算剩余天数
                    long remainingDays = calculateRemainingDays(activity.getExpirationTime());
                    
                    // 检查是否已经发送过相同的提醒（避免重复发送）
                    if (!hasRecentReminder(activity.getId(), NOTIFY_TYPE_EXPIRING_SOON, (int) remainingDays)) {
                        String message = buildExpirationReminderMessage(activity, (int) remainingDays);
                        String url = buildActivityUrl(activity.getId());

                        ActivityNotifyEntity notify = new ActivityNotifyEntity();
                        notify.setActivityId(activity.getId());
                        notify.setName(message);
                        notify.setType(NOTIFY_TYPE_EXPIRING_SOON);
                        notify.setUrl(url);
                        notify.setAppid(activity.getAppid());
                        notify.setRead(0);

                        notifications.add(notify);
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理活动过期提醒失败: activityId={}, error={}", activity.getId(), e.getMessage());
                }
            }

            // 批量保存通知
            if (!notifications.isEmpty()) {
                activityNotifyService.saveBatch(notifications);
            }

            log.info("批量发送过期提醒完成: 总数={}, 成功={}", expiringSoonActivities.size(), successCount);
            return R.ok().put("totalCount", expiringSoonActivities.size()).put("successCount", successCount);

        } catch (Exception e) {
            log.error("批量发送过期提醒失败: error={}", e.getMessage(), e);
            return R.error("批量发送提醒失败: " + e.getMessage());
        }
    }

    @Override
    public R batchSendExpirationNotifications() {
        try {
            // 获取已过期的活动列表
            List<ActivityEntity> expiredActivities = activityExpirationService.getExpiredActivities();
            
            List<ActivityNotifyEntity> notifications = new ArrayList<>();
            int successCount = 0;

            for (ActivityEntity activity : expiredActivities) {
                try {
                    // 检查是否已经发送过过期通知（避免重复发送）
                    if (!hasRecentNotification(activity.getId(), NOTIFY_TYPE_EXPIRED)) {
                        String message = buildExpirationNotificationMessage(activity);
                        String url = buildRenewalUrl(activity.getId());

                        ActivityNotifyEntity notify = new ActivityNotifyEntity();
                        notify.setActivityId(activity.getId());
                        notify.setName(message);
                        notify.setType(NOTIFY_TYPE_EXPIRED);
                        notify.setUrl(url);
                        notify.setAppid(activity.getAppid());
                        notify.setRead(0);

                        notifications.add(notify);
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理活动过期通知失败: activityId={}, error={}", activity.getId(), e.getMessage());
                }
            }

            // 批量保存通知
            if (!notifications.isEmpty()) {
                activityNotifyService.saveBatch(notifications);
            }

            log.info("批量发送过期通知完成: 总数={}, 成功={}", expiredActivities.size(), successCount);
            return R.ok().put("totalCount", expiredActivities.size()).put("successCount", successCount);

        } catch (Exception e) {
            log.error("批量发送过期通知失败: error={}", e.getMessage(), e);
            return R.error("批量发送通知失败: " + e.getMessage());
        }
    }

    /**
     * 构建即将过期提醒消息
     */
    private String buildExpirationReminderMessage(ActivityEntity activity, Integer remainingDays) {
        String expirationTimeStr = activity.getExpirationTime() != null ? 
            DATE_FORMAT.format(activity.getExpirationTime()) : "未知";
        
        return String.format("【过期提醒】活动「%s」将在%d天后过期（%s），请及时续费以免影响使用", 
            activity.getName(), remainingDays, expirationTimeStr);
    }

    /**
     * 构建已过期通知消息
     */
    private String buildExpirationNotificationMessage(ActivityEntity activity) {
        String expirationTimeStr = activity.getExpirationTime() != null ? 
            DATE_FORMAT.format(activity.getExpirationTime()) : "未知";
        
        return String.format("【过期通知】活动「%s」已于%s过期，请立即续费以恢复使用", 
            activity.getName(), expirationTimeStr);
    }

    /**
     * 构建续费成功消息
     */
    private String buildRenewalSuccessMessage(ActivityEntity activity, Integer renewalDays, String newExpirationTime) {
        return String.format("【续费成功】活动「%s」续费%d天成功，新的过期时间为%s", 
            activity.getName(), renewalDays, newExpirationTime);
    }

    /**
     * 构建活动链接
     */
    private String buildActivityUrl(Long activityId) {
        return "/activity/detail?id=" + activityId;
    }

    /**
     * 构建续费链接
     */
    private String buildRenewalUrl(Long activityId) {
        return "/activity/renewal?id=" + activityId;
    }

    /**
     * 计算剩余天数
     */
    private long calculateRemainingDays(Date expirationTime) {
        if (expirationTime == null) return -1;
        long diffInMillis = expirationTime.getTime() - System.currentTimeMillis();
        return Math.max(0, diffInMillis / (24 * 60 * 60 * 1000));
    }

    /**
     * 检查是否已有最近的提醒（24小时内）
     */
    private boolean hasRecentReminder(Long activityId, Integer type, Integer remainingDays) {
        // 简化实现，实际可以查询数据库检查是否有24小时内的相同提醒
        // 这里暂时返回false，表示总是发送提醒
        return false;
    }

    /**
     * 检查是否已有最近的通知（24小时内）
     */
    private boolean hasRecentNotification(Long activityId, Integer type) {
        // 简化实现，实际可以查询数据库检查是否有24小时内的相同通知
        // 这里暂时返回false，表示总是发送通知
        return false;
    }
}
