package com.cjy.pyp.modules.activity.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;

import com.cjy.pyp.modules.activity.dao.ActivityImageDao;
import com.cjy.pyp.modules.activity.dao.ActivityImagePlatformUsageDao;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityImagePlatformUsageEntity;

import com.cjy.pyp.modules.activity.service.ActivityImageService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Date;


@Service("activityImageService")
public class ActivityImageServiceImpl extends ServiceImpl<ActivityImageDao, ActivityImageEntity> implements ActivityImageService {

    @Autowired
    private ActivityImagePlatformUsageDao activityImagePlatformUsageDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String activityId = (String) params.get("activityId");
        IPage<ActivityImageEntity> page = this.page(
                new Query<ActivityImageEntity>().getPage(params),
                new QueryWrapper<ActivityImageEntity>()
                        .eq("activity_id",activityId)
                .orderByDesc("id")
        );

        return new PageUtils(page);
    }

    @Override
    public List<ActivityImageEntity> findByIds(List<Long> ids) {
        return ids.size() > 0 ? this.listByIds(ids) : new ArrayList<>();
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdNoUseLimit(Long activityId,Integer limit) {
        return this.list(new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .eq("use_count", 0)
                .orderByAsc("create_on")
                .last("LIMIT " + (limit == null ? 3 : limit)));
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdLimit(Long activityId,Integer limit) {
        return this.list(new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .orderByAsc("create_on")
                .last("LIMIT " + (limit == null ? 3 : limit)));
    }

    @Override
    public void incrementUseCount(Long textId) {
        ActivityImageEntity entity = this.getById(textId);
        if (entity != null) {
            entity.setUseCount(entity.getUseCount() == null ? 1 : entity.getUseCount() + 1);
            this.updateById(entity);
        }
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdNoUseLimitByPlatform(Long activityId, String platform, Integer limit) {
        // 查询活动下的所有图片
        List<ActivityImageEntity> allImages = this.list(new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .orderByAsc("create_on"));

        // 过滤出在指定平台未使用的图片
        List<ActivityImageEntity> availableImages = new ArrayList<>();
        for (ActivityImageEntity image : allImages) {
            Integer usageCount = activityImagePlatformUsageDao.getUsageCount(image.getId(), platform);
            if (usageCount == null || usageCount == 0) {
                availableImages.add(image);
                // 如果达到限制数量，停止添加
                if (limit != null && availableImages.size() >= limit) {
                    break;
                }
            }
        }

        return availableImages;
    }

    @Override
    public void incrementUseCountByPlatform(Long imageId, String platform, Long activityId) {
        // 使用Java代码实现，而不是数据库的INSERT ON DUPLICATE KEY UPDATE

        // 1. 先查询是否已存在记录
        QueryWrapper<ActivityImagePlatformUsageEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("image_id", imageId)
                   .eq("platform", platform);

        ActivityImagePlatformUsageEntity existingRecord = activityImagePlatformUsageDao.selectOne(queryWrapper);

        if (existingRecord != null) {
            // 2. 如果存在，更新使用次数
            existingRecord.setUseCount(existingRecord.getUseCount() + 1);
            existingRecord.setLastUsedTime(new Date());
            existingRecord.setUpdateTime(new Date());
            activityImagePlatformUsageDao.updateById(existingRecord);
        } else {
            // 3. 如果不存在，插入新记录
            ActivityImagePlatformUsageEntity newRecord = new ActivityImagePlatformUsageEntity();
            newRecord.setActivityId(activityId);
            newRecord.setImageId(imageId);
            newRecord.setPlatform(platform);
            newRecord.setUseCount(1);
            newRecord.setFirstUsedTime(new Date());
            newRecord.setLastUsedTime(new Date());
            newRecord.setCreateTime(new Date());
            newRecord.setUpdateTime(new Date());
            activityImagePlatformUsageDao.insert(newRecord);
        }
    }

    @Override
    public Integer getUsageCountByPlatform(Long imageId, String platform) {
        Integer count = activityImagePlatformUsageDao.getUsageCount(imageId, platform);
        return count != null ? count : 0;
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdAndAiTag(Long activityId, String aiTag, Integer limit) {
        QueryWrapper<ActivityImageEntity> wrapper = new QueryWrapper<ActivityImageEntity>()
                .eq("activity_id", activityId)
                .orderByAsc("create_on");

        if (aiTag != null && !aiTag.trim().isEmpty()) {
            // 查找包含指定AI标签的图片（支持多标签匹配）
            wrapper.and(w -> w.like("ai_tag", aiTag).or().isNull("ai_tag").or().eq("ai_tag", ""));
        } else {
            // 查找通用图片（ai_tag为空或null）
            wrapper.and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""));
        }

        if (limit != null) {
            wrapper.last("LIMIT " + limit);
        }

        return this.list(wrapper);
    }

    @Override
    public List<ActivityImageEntity> findByActivityIdAndAiTagNoUseLimitByPlatform(Long activityId, String aiTag, String platform, Integer limit) {
        // 先根据AI标签筛选图片
        List<ActivityImageEntity> candidateImages = new ArrayList<>();

        if (aiTag != null && !aiTag.trim().isEmpty()) {
            // 优先查找带有对应AI标签的图片
            List<ActivityImageEntity> taggedImages = this.list(new QueryWrapper<ActivityImageEntity>()
                    .eq("activity_id", activityId)
                    .like("ai_tag", aiTag)
                    .orderByAsc("create_on"));
            candidateImages.addAll(taggedImages);

            // 如果没有找到对应标签的图片，则查找通用图片
            if (candidateImages.isEmpty()) {
                List<ActivityImageEntity> generalImages = this.list(new QueryWrapper<ActivityImageEntity>()
                        .eq("activity_id", activityId)
                        .and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""))
                        .orderByAsc("create_on"));
                candidateImages.addAll(generalImages);
            }
        } else {
            // 查找通用图片
            List<ActivityImageEntity> generalImages = this.list(new QueryWrapper<ActivityImageEntity>()
                    .eq("activity_id", activityId)
                    .and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""))
                    .orderByAsc("create_on"));
            candidateImages.addAll(generalImages);
        }

        // 如果还是没有图片，则使用所有图片
        if (candidateImages.isEmpty()) {
            candidateImages = this.list(new QueryWrapper<ActivityImageEntity>()
                    .eq("activity_id", activityId)
                    .orderByAsc("create_on"));
        }

        // 过滤出在指定平台未使用的图片
        List<ActivityImageEntity> availableImages = new ArrayList<>();
        for (ActivityImageEntity image : candidateImages) {
            Integer usageCount = activityImagePlatformUsageDao.getUsageCount(image.getId(), platform);
            if (usageCount == null || usageCount == 0) {
                availableImages.add(image);
                // 如果达到限制数量，停止添加
                if (limit != null && availableImages.size() >= limit) {
                    break;
                }
            }
        }

        return availableImages;
    }

}