package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 微信用户业务员绑定DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Mapper
public interface WxUserSalesmanBindingDao extends BaseMapper<WxUserSalesmanBindingEntity> {

    /**
     * 分页查询绑定关系列表
     * @param params 查询参数
     * @return 绑定关系列表
     */
    List<WxUserSalesmanBindingEntity> queryPage(Map<String, Object> params);

    /**
     * 根据微信用户ID查询有效的业务员绑定
     * @param wxUserId 微信用户ID
     * @param appid 应用ID
     * @return 有效的绑定关系
     */
    WxUserSalesmanBindingEntity getActiveBindingByWxUser(@Param("wxUserId") Long wxUserId, 
                                                         @Param("appid") String appid);

    /**
     * 根据业务员ID查询绑定的微信用户列表
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 绑定的微信用户列表
     */
    List<WxUserSalesmanBindingEntity> getBindingsBySlesman(@Param("salesmanId") Long salesmanId, 
                                                           @Param("appid") String appid);

    /**
     * 根据微信用户ID查询所有绑定历史
     * @param wxUserId 微信用户ID
     * @param appid 应用ID
     * @return 绑定历史列表
     */
    List<WxUserSalesmanBindingEntity> getBindingHistoryByWxUser(@Param("wxUserId") Long wxUserId, 
                                                                @Param("appid") String appid);

    /**
     * 检查绑定关系是否存在
     * @param wxUserId 微信用户ID
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 是否存在
     */
    boolean existsBinding(@Param("wxUserId") Long wxUserId, 
                         @Param("salesmanId") Long salesmanId, 
                         @Param("appid") String appid);

    /**
     * 批量查询微信用户的业务员绑定
     * @param wxUserIds 微信用户ID列表
     * @param appid 应用ID
     * @return 绑定关系列表
     */
    List<WxUserSalesmanBindingEntity> getBatchActiveBindingsByWxUsers(@Param("wxUserIds") List<Long> wxUserIds, 
                                                                      @Param("appid") String appid);

    /**
     * 统计业务员的绑定客户数量
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 绑定客户数量
     */
    Integer countBindingsBySlesman(@Param("salesmanId") Long salesmanId, 
                                  @Param("appid") String appid);

    /**
     * 查询即将过期的绑定关系
     * @param days 提前天数
     * @param appid 应用ID
     * @return 即将过期的绑定关系列表
     */
    List<WxUserSalesmanBindingEntity> getExpiringBindings(@Param("days") Integer days, 
                                                          @Param("appid") String appid);

    /**
     * 自动失效过期的绑定关系
     * @param appid 应用ID
     * @return 影响的记录数
     */
    Integer expireOverdueBindings(@Param("appid") String appid);
}
