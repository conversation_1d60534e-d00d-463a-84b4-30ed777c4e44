<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.clientId" filterable>
          <el-option :value="''" label="全部(客户)"></el-option>
          <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.provinceId" placeholder="省份" filterable @change="provinceChange">
          <el-option :value="''" label="全部(省份)"></el-option>
          <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.cityId" placeholder="城市" filterable>
          <el-option :value="''" label="全部(城市)"></el-option>
          <el-option v-for="item in cities" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.clientType" filterable>
          <el-option :value="''" label="全部(客户类型)"></el-option>
          <el-option v-for="item in clientType" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.oldOrNew" filterable>
          <el-option :value="''" label="全部(新老客户)"></el-option>
          <el-option v-for="item in oldOrNew" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.activityType" filterable>
          <el-option :value="''" label="全部(会议形式)"></el-option>
          <el-option v-for="item in activityType" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.userId" filterable>
          <el-option :value="''" label="全部(员工)"></el-option>
          <el-option v-for="item in sysuser" :key="item.userId" :label="item.username" :value="item.userId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="timeArray" type="daterange" value-format="yyyy/MM/dd" range-separator="至"
          start-placeholder="开始日期(开始时间)" end-placeholder="结束日期(开始时间)" :picker-options="pickerOptions" />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:activity:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activity:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" height="800px" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="clientType" header-align="center" align="center" label="客户类型">
      </el-table-column>
      <el-table-column prop="oldOrNew" header-align="center" align="center" label="新老客户">
      </el-table-column>
      <el-table-column prop="clientName" header-align="center" align="center" label="客户名称">
      </el-table-column>
      <el-table-column prop="duties" header-align="center" align="center" label="科室">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="会议名称">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="会议编码">
      </el-table-column>
      <el-table-column prop="startTime" header-align="center" align="center" label="开始时间">
        <div slot-scope="scope">{{ scope.row.startTime | dateFilter }}</div>
      </el-table-column>
      <el-table-column prop="endTime" header-align="center" align="center" label="结束时间">
        <div slot-scope="scope">{{ scope.row.endTime | dateFilter }}</div>
      </el-table-column>
      <el-table-column prop="activityType" header-align="center" align="center" label="会议形式">
      </el-table-column>
      <el-table-column prop="provinceName" header-align="center" align="center" label="区域">
        <div slot-scope="scope">{{ scope.row.provinceName + '-' + scope.row.cityName }}</div>
      </el-table-column>
      <!-- <el-table-column prop="cityName" header-align="center" align="center" label="城市">
      </el-table-column> -->
      <el-table-column prop="address" header-align="center" align="center" label="会议地址">
      </el-table-column>
      <el-table-column prop="role" header-align="center" align="center" label="工作组">
        <div slot-scope="scope">
          <div v-for="item in scope.row.activityRoleEntities" :key="item.id">
            <el-tag>{{ item.username }}-{{ item.roleId == null ? '空' : (
              activityRole[item.roleId].value) }}</el-tag>
          </div>
        </div>
      </el-table-column>

      <el-table-column fixed="right" header-align="center" align="center" width="250" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="acitivitySettle(scope.row.id)">会议结算表</el-button>
          <el-button type="text" size="small" @click="acitivityRole(scope.row.id)">工作人员配置</el-button>
          <el-button v-if="isAuth('activity:activity:update')" type="text" size="small"
            @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="isAuth('activity:activity:delete')" type="text" size="small"
            @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { yesOrNo } from "@/data/common"
import { activityRole } from "@/data/activity"
export default {
  data() {
    return {
      timeArray: [],
      sysuser: [],
      clientType: [],
      oldOrNew: [],
      activityType: [],
      client: [],
      provinces: [],
      cities: [],
      appid: '',
      wxAccount: {},
      yesOrNo,
      activityRole,
      dataForm: {
        name: '',
        clientId: '',
        clientType: '',
        oldOrNew: '',
        activityType: '',
        userId: '',
        provinceId: '',
        cityId: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
    }
  },
  components: {
    VueQrcode
  },
  filters: {

    dateFilter(v) {
      return v ? v.substring(0, 10) : '';
    },
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.getDataList()
    this.getAccountInfo()
    this.findClient()
    this.getResult()
    this.getSysUser()
    this.getProvinces()
  },
  methods: {
    // 获取省份数据
    getProvinces() {
      this.$http({
        url: this.$http.adornUrl("/sys/region/pid/100000"),
        method: "get",
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.provinces = data.list;
        } else {
          this.provinces = [];
        }
      });
    },
    // 根据省份ID获取城市数据
    provinceChange(val) {
      if (val === undefined || val === '') {
        this.cities = [];
        this.dataForm.cityId = '';
        this.getDataList();
        return;
      }
      this.$http({
        url: this.$http.adornUrl(`/sys/region/pid/${val}`),
        method: "get",
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.cities = data.list;
        } else {
          this.cities = [];
        }
        this.getDataList();
      });
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activity/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'clientId': this.dataForm.clientId,
          'clientType': this.dataForm.clientType,
          'oldOrNew': this.dataForm.oldOrNew,
          'userId': this.dataForm.userId,
          'activityType': this.dataForm.activityType,
          'name': this.dataForm.name,
          'provinceId': this.dataForm.provinceId,
          'cityId': this.dataForm.cityId,
          start: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : '',
          end: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : '',
        })
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    findClient() {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
          }
        })
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
        }
      });
    },
    getSysUser() {
      this.$http({
        url: this.$http.adornUrl("/sys/user/findByAppid"),
        method: "get",
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.sysuser = data.result;
        } else {
          this.sysuser = [];
        }
      });
    },
    getResult() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'clientType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.clientType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'oldOrNew',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.oldOrNew = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'activityType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$router.push({
        name: 'activityextraAddOrUpdate',
        query: {
          id: id
        }
      })
      // this.$nextTick(() => {
      //   this.$refs.addOrUpdate.init(id)
      // })
    },
    acitivitySettle(id) {
      this.$router.push({
        name: 'activitysettle',
        query: {
          activityId: id
        }
      })
    },
    acitivityRole(id) {
      this.$router.push({
        name: 'activityrole',
        query: {
          activityId: id
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activity/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    copy(id) {
      this.$confirm(`确定复制会议操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activity/copy'),
          method: 'get',
          params: this.$http.adornParams({ id: id })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    acitivityConfig(v) {
      this.$router.push({
        name: 'activityConfig',
        query: {
          activityId: v
        }
      })
    },
    openUrl(v) {
      window.open(v)
    },
  }
}
</script>
