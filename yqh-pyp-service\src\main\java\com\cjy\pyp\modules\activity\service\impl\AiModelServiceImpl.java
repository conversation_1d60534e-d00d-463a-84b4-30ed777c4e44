package com.cjy.pyp.modules.activity.service.impl;

import com.cjy.pyp.modules.activity.entity.AiModelConfigEntity;
import com.cjy.pyp.modules.activity.service.AiModelConfigService;
import com.cjy.pyp.modules.activity.service.AiModelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型统一服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class AiModelServiceImpl implements AiModelService {
    
    @Autowired
    private AiModelConfigService aiModelConfigService;
    
    @Autowired
    private DeepSeekModelService deepSeekModelService;
    
    @Autowired
    private KimiModelService kimiModelService;
    
    @Override
    public String generateText(String prompt, String modelCode) throws Exception {
        if (StringUtils.isBlank(prompt)) {
            throw new Exception("提示词不能为空");
        }
        
        // 如果没有指定模型，使用默认模型
        if (StringUtils.isBlank(modelCode)) {
            AiModelConfigEntity defaultModel = getDefaultModel();
            if (defaultModel == null) {
                throw new Exception("未找到可用的默认AI模型");
            }
            modelCode = defaultModel.getModelCode();
        }
        
        // 获取模型配置
        AiModelConfigEntity config = aiModelConfigService.getByModelCode(modelCode);
        if (config == null) {
            throw new Exception("未找到模型配置: " + modelCode);
        }
        
        if (config.getStatus() != 1) {
            throw new Exception("模型已禁用: " + modelCode);
        }
        
        // 根据服务提供商选择对应的服务
        String provider = config.getProvider();
        switch (provider) {
            case "deepseek":
                return deepSeekModelService.generateText(prompt, config);
            case "moonshot":
                return kimiModelService.generateText(prompt, config);
            default:
                throw new Exception("不支持的AI模型提供商: " + provider);
        }
    }
    
    @Override
    public String generateText(String prompt) throws Exception {
        return generateText(prompt, null);
    }
    
    @Override
    public List<AiModelConfigEntity> getAvailableModels() {
        return aiModelConfigService.getEnabledModels();
    }
    
    @Override
    public AiModelConfigEntity getDefaultModel() {
        return aiModelConfigService.getDefaultModel();
    }
    
    @Override
    public boolean isModelAvailable(String modelCode) {
        if (StringUtils.isBlank(modelCode)) {
            return false;
        }
        
        AiModelConfigEntity config = aiModelConfigService.getByModelCode(modelCode);
        return config != null && config.getStatus() == 1;
    }
    
    @Override
    public boolean testModelConnection(String modelCode) {
        try {
            AiModelConfigEntity config = aiModelConfigService.getByModelCode(modelCode);
            if (config == null || config.getStatus() != 1) {
                return false;
            }
            
            // 根据服务提供商选择对应的服务进行测试
            String provider = config.getProvider();
            switch (provider) {
                case "deepseek":
                    return deepSeekModelService.testConnection(config);
                case "moonshot":
                    return kimiModelService.testConnection(config);
                default:
                    log.warn("不支持的AI模型提供商: {}", provider);
                    return false;
            }
        } catch (Exception e) {
            log.error("测试模型连接失败: {}", modelCode, e);
            return false;
        }
    }
    
    @Override
    public Object getModelConfigInfo(String modelCode) {
        AiModelConfigEntity config = aiModelConfigService.getByModelCode(modelCode);
        if (config == null) {
            return null;
        }
        
        Map<String, Object> info = new HashMap<>();
        info.put("modelCode", config.getModelCode());
        info.put("modelName", config.getModelName());
        info.put("provider", config.getProvider());
        info.put("apiUrl", config.getApiUrl());
        info.put("maxTokens", config.getMaxTokens());
        info.put("temperature", config.getTemperature());
        info.put("timeout", config.getTimeout());
        info.put("maxRetries", config.getMaxRetries());
        info.put("retryDelay", config.getRetryDelay());
        info.put("status", config.getStatus());
        info.put("isDefault", config.getIsDefault());
        info.put("hasApiKey", StringUtils.isNotBlank(config.getApiKey()));
        
        return info;
    }
}
