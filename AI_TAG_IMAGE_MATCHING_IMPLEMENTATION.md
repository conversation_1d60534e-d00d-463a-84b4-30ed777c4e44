# AI标签图片匹配功能实现文档

## 功能概述

实现了AI标签图片匹配功能，允许根据用户选择的AI标签智能筛选对应的图片，提升文案和图片的匹配度。

## 实现内容

### 1. 数据库层面 ✅

#### 表结构更新
- **文件**: `V1.0.13__add_ai_tag_to_activity_image.sql`
- **内容**: 为`activity_image`表添加`ai_tag`字段
- **字段类型**: `varchar(100)`，支持多个标签用逗号分隔
- **索引优化**: 添加了复合索引提高查询性能

```sql
ALTER TABLE `activity_image` ADD COLUMN `ai_tag` varchar(100) DEFAULT NULL;
ALTER TABLE `activity_image` ADD INDEX `idx_activity_ai_tag` (`activity_id`, `ai_tag`);
ALTER TABLE `activity_image` ADD INDEX `idx_activity_type_tag` (`activity_id`, `type`, `ai_tag`);
```

### 2. 后端实体类更新 ✅

#### ActivityImageEntity
- **文件**: `ActivityImageEntity.java`
- **新增字段**: `aiTag`
- **映射更新**: 更新了XML映射文件

### 3. 图片查询服务优化 ✅

#### ActivityImageService
- **新增方法**:
  - `findByActivityIdAndAiTag()`: 根据AI标签筛选图片
  - `findByActivityIdAndAiTagNoUseLimitByPlatform()`: 根据AI标签和平台筛选未使用图片

#### 筛选逻辑
1. **有AI标签时**: 优先选择带有对应标签的图片
2. **无对应标签图片**: 使用未设置ai_tag的通用图片
3. **无通用图片**: 使用所有图片
4. **选择"通用文案"**: 优先使用未设置ai_tag的图片

### 4. 文案生成逻辑优化 ✅

#### WebActivityReviewController
- **更新方法**: 所有regenerate方法都支持aiTag参数
- **图片筛选**: 根据选择的AI标签筛选对应图片
- **向下兼容**: 保持原有API的兼容性

#### 支持的平台
- 抖音点评 (`douyin_review`)
- 大众点评 (`dianping`)
- 美团点评 (`meituan`)
- 携程点评/笔记 (`ctrip`, `ctrip_review`, `ctrip_notes`)

### 5. 管理端图片标签配置 ✅

#### 图片编辑界面
- **文件**: `activityimage-add-or-update.vue`
- **新增功能**: AI标签配置组件
- **组件**: 使用`tags-editorlist`组件
- **限制**: 最多5个标签，每个标签最多10个字符

#### 图片列表界面
- **文件**: `activityimage.vue`
- **新增列**: AI标签显示列
- **样式**: 标签以卡片形式显示，通用图片显示"通用图片"

### 6. API接口调整 ✅

#### 现有API增强
- **regenerate接口**: 所有平台的重新生成接口都支持`aiTag`参数
- **图片筛选**: 自动根据AI标签筛选对应图片
- **使用统计**: 正确记录图片使用次数

## 使用流程

### 管理员配置流程

1. **配置活动AI标签**
   - 在活动管理中设置AI标签（如：男,女,儿童）

2. **配置图片AI标签**
   - 进入活动图片管理
   - 编辑图片，设置对应的AI标签
   - 可以设置多个标签或留空（通用图片）

### 用户使用流程

1. **查看文案**
   - 用户访问活动页面
   - 点击平台按钮查看文案

2. **智能换一篇**
   - 点击"换一篇"按钮
   - 如果有AI标签，显示标签选择器
   - 选择目标受众标签
   - 系统生成对应的文案和图片

## 技术特性

### 智能匹配算法

```java
// 图片筛选逻辑
if (aiTag != null && !aiTag.trim().isEmpty()) {
    // 1. 优先查找带有对应AI标签的图片
    List<ActivityImageEntity> taggedImages = findByAiTag(activityId, aiTag);
    
    // 2. 如果没有对应标签图片，使用通用图片
    if (taggedImages.isEmpty()) {
        taggedImages = findGeneralImages(activityId);
    }
    
    // 3. 如果还没有，使用所有图片
    if (taggedImages.isEmpty()) {
        taggedImages = findAllImages(activityId);
    }
}
```

### 性能优化

1. **数据库索引**: 添加复合索引优化查询性能
2. **缓存机制**: 利用现有的图片使用统计缓存
3. **批量处理**: 支持批量设置图片标签

### 向下兼容

1. **现有图片**: 未设置AI标签的图片视为通用图片
2. **API兼容**: 所有现有API保持兼容
3. **数据兼容**: 新字段为可空，不影响现有数据

## 配置示例

### 理发店示例

**活动AI标签**: `男,女,儿童`

**图片配置**:
- `男士发型1.jpg` → AI标签: `男`
- `女士发型1.jpg` → AI标签: `女`
- `儿童发型1.jpg` → AI标签: `儿童`
- `店面环境.jpg` → AI标签: 空（通用图片）

**效果**:
- 选择"男" → 使用男士发型图片
- 选择"女" → 使用女士发型图片
- 选择"儿童" → 使用儿童发型图片
- 选择"通用文案" → 使用店面环境图片

### 餐厅示例

**活动AI标签**: `情侣,家庭,商务`

**图片配置**:
- `浪漫包间.jpg` → AI标签: `情侣`
- `亲子餐厅.jpg` → AI标签: `家庭`
- `商务包间.jpg` → AI标签: `商务`
- `招牌菜品.jpg` → AI标签: 空（通用图片）

## 测试建议

### 功能测试

1. **标签配置测试**
   - 测试图片AI标签的添加、修改、删除
   - 测试多标签设置和显示

2. **图片筛选测试**
   - 测试不同AI标签的图片筛选效果
   - 测试通用图片的回退逻辑

3. **文案生成测试**
   - 测试各平台的AI标签文案生成
   - 测试图片和文案的匹配度

### 性能测试

1. **查询性能**: 测试大量图片时的查询速度
2. **并发测试**: 测试多用户同时使用的性能
3. **缓存效果**: 验证缓存机制的有效性

### 兼容性测试

1. **数据兼容**: 验证现有数据的正常使用
2. **API兼容**: 验证现有API的正常调用
3. **界面兼容**: 验证新界面在不同浏览器的显示

## 注意事项

1. **标签命名**: 使用简洁明了的词汇，避免特殊字符
2. **标签数量**: 建议每个图片不超过5个标签
3. **通用图片**: 确保有足够的通用图片作为回退
4. **性能监控**: 关注图片筛选的查询性能

## 扩展计划

1. **智能推荐**: 根据图片内容自动推荐合适的AI标签
2. **批量操作**: 支持批量为图片设置AI标签
3. **统计分析**: 统计不同AI标签的使用频率和效果
4. **模板预设**: 为不同行业预设常用的AI标签模板
