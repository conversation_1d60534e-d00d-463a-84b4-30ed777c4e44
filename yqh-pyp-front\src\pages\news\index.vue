<template>
  <div>
    <div style="display: flex">
      <van-tabs style="width: 80%" v-model="cmsId" @click="onClick">
        <van-tab
          v-for="item in drList"
          :key="item.value"
          :title="item.text"
          :name="item.value"
        >
        </van-tab>
      </van-tabs>
      <van-dropdown-menu style="width: 20%">
        <van-dropdown-item
          title="更多"
          v-model="cmsId"
          :options="drList"
          @change="droChange"
        />
      </van-dropdown-menu>
    </div>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
      style="display: flex;flex-wrap: wrap;justify-content: space-between;"
    >
      <van-card
        class="big"
        style="background: white"
        v-for="item in dataList"
        :key="item.id"
        @click="turnDetail(item)"
        :thumb="!item.picUrl ? 'van-icon' : item.picUrl"
      >
        <div slot="title" class="title">{{ item.name }}</div>
        <div 
          slot="desc"
          style="padding-top: 10px; font-size: 14px; color: grey"
        >
        <div>{{item.brief}}</div>
        </div>
        <template #num>
          <span>{{ item.createOn }}</span>
          <!-- <van-button  size="small" round type="primary" plain
            >查看详情</van-button
          > -->
        </template>
      </van-card>
    </van-list>
  </div>
</template>

<script>
import { isURL } from "@/js/validate";

export default {
  components: {},
  data() {
    return {
      flag: false,
      dataForm: {
        name: "",
        status: 1,
      },
      loading: false,
      finished: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      cmsId: '',
      drList: [],
    };
  },
  mounted() {
          document.title ="资讯列表";
    this.cmsId = '';
    this.drList = [];
    this.getCmsInfo();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      if (!this.flag) {
      this.getActivityList();
      }
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/news/news/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          newsTypeId: this.cmsId,
          name: this.dataForm.name,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    turnDetail(item) {
        if(item.url) {
            location.href = item.url;
        } else {
        this.$router.push({
            name: 'newsDetail',
            query: { id: item.id },
          })
        }
    },
    getCmsInfo() {
      this.$fly.get(`/pyp/web/news/news/findType`).then((res) => {
        if (res.code == 200) {
          document.title = "资讯列表";
          this.drList.push({
            text: "全部",
            value: '',
          });
          res.result.forEach((e) => {
            this.drList.push({
              text: e.name,
              value: e.id,
            });
          });
        } else {
          vant.Toast(res.msg);
          this.drList = [];
        }
      });
    },
    onClick(item) {
      this.onSearch();
    },
    droChange(item) {
      this.onSearch();
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  /deep/ p {
    width: 100%;
  }
  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
.big {
  width: 100%;
}
.small {
  width: 49%;
}
.van-card {
  margin-top: 8px;
}
.van-card__header {
  align-items: center;
  justify-content: center;
}
.small /deep/ .van-card__content {
  flex: 0;
}
/deep/ .van-list__finished-text {
  width: 100%;
}
.title {
  font-size: 18px;
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
</style>