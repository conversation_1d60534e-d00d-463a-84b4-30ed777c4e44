package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.channel.utils.ChannelPermissionUtils;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;

/**
 * 渠道统计报表控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/channel/statistics")
@Api(tags = "渠道统计报表")
public class ChannelStatisticsController extends AbstractController {
    
    @Autowired
    private ChannelService channelService;
    
    @Autowired
    private SalesmanService salesmanService;
    
    @Autowired
    private SalesmanCommissionRecordService salesmanCommissionRecordService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;
    
    @Autowired
    private ChannelPermissionUtils channelPermissionUtils;

    /**
     * 获取订单统计数据（替代原来的 salesmanOrderService.getStatsByCondition）
     */
    private Map<String, Object> getOrderStatsByCondition(Map<String, Object> params) {
        Map<String, Object> stats = new HashMap<>();

        String appid = (String) params.get("appid");
        List<Long> channelIds = (List<Long>) params.get("channelIds");

        // 构建查询条件
        QueryWrapper<ActivityRechargeRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("appid", appid)
               .eq("status", 1) // 只统计已支付的订单
               .isNotNull("salesman_id"); // 只统计有业务员的订单

        // 如果有渠道限制，需要通过业务员表关联查询
        if (channelIds != null && !channelIds.isEmpty()) {
            List<SalesmanEntity> salesmen = salesmanService.list(
                new QueryWrapper<SalesmanEntity>()
                    .in("channel_id", channelIds)
                    .eq("appid", appid)
            );
            List<Long> salesmanIds = salesmen.stream()
                .map(SalesmanEntity::getId)
                .collect(Collectors.toList());

            if (salesmanIds.isEmpty()) {
                // 没有符合条件的业务员，返回空统计
                stats.put("totalOrders", 0);
                stats.put("activityOrders", 0);
                stats.put("rechargeOrders", 0);
                stats.put("totalAmount", BigDecimal.ZERO);
                stats.put("totalCommission", BigDecimal.ZERO);
                stats.put("totalSalesmen", 0);
                return stats;
            }

            wrapper.in("salesman_id", salesmanIds);
        }

        List<ActivityRechargeRecordEntity> orders = activityRechargeRecordService.list(wrapper);

        int totalOrders = orders.size();
        int activityOrders = (int) orders.stream().filter(o -> o.getRechargeType() == 4).count();
        int rechargeOrders = (int) orders.stream().filter(o -> o.getRechargeType() == 1 || o.getRechargeType() == 2).count();
        BigDecimal totalAmount = orders.stream()
                .map(o -> o.getPayAmount() != null ? o.getPayAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取佣金统计
        Map<String, Object> commissionParams = new HashMap<>();
        commissionParams.put("appid", appid);
        if (channelIds != null && !channelIds.isEmpty()) {
            commissionParams.put("channelIds", channelIds);
        }
        Map<String, Object> commissionStats = salesmanCommissionRecordService.getCommissionStats(commissionParams);
        BigDecimal totalCommission = (BigDecimal) commissionStats.getOrDefault("totalAmount", BigDecimal.ZERO);

        // 统计业务员数量
        QueryWrapper<SalesmanEntity> salesmanWrapper = new QueryWrapper<>();
        salesmanWrapper.eq("appid", appid);
        if (channelIds != null && !channelIds.isEmpty()) {
            salesmanWrapper.in("channel_id", channelIds);
        }
        int totalSalesmen = salesmanService.count(salesmanWrapper);

        stats.put("totalOrders", totalOrders);
        stats.put("activityOrders", activityOrders);
        stats.put("rechargeOrders", rechargeOrders);
        stats.put("totalAmount", totalAmount);
        stats.put("totalCommission", totalCommission);
        stats.put("totalSalesmen", totalSalesmen);

        return stats;
    }

    /**
     * 渠道总体统计
     */
    @RequestMapping("/overview")
    // @RequiresPermissions("channel:statistics:view")
    @ApiOperation(value = "渠道总体统计", notes = "")
    public R overview(@CookieValue String appid) {
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        
        Map<String, Object> overview;
        if (accessibleChannelIds != null && !accessibleChannelIds.isEmpty()) {
            // 渠道管理员查看指定渠道统计
            overview = getChannelOverviewStats(accessibleChannelIds, appid);
        } else {
            // 超级管理员查看全部统计
            overview = channelService.getOverallStatsByAppid(appid);
        }
        
        return R.ok().put("overview", overview);
    }

    /**
     * 渠道业务员统计
     */
    @RequestMapping("/salesman")
    // @RequiresPermissions("channel:statistics:view")
    @ApiOperation(value = "渠道业务员统计", notes = "")
    public R salesmanStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 获取业务员统计数据
        Map<String, Object> stats = getSalesmanStatsByChannel(params);
        
        return R.ok().put("stats", stats);
    }

    /**
     * 渠道订单统计
     */
    @RequestMapping("/orders")
    // @RequiresPermissions("channel:statistics:view")
    @ApiOperation(value = "渠道订单统计", notes = "")
    public R orderStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 获取订单统计数据
        Map<String, Object> stats = getOrderStatsByCondition(params);
        
        return R.ok().put("stats", stats);
    }

    /**
     * 渠道佣金统计
     */
    @RequestMapping("/commission")
    // @RequiresPermissions("channel:statistics:view")
    @ApiOperation(value = "渠道佣金统计", notes = "")
    public R commissionStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 获取佣金统计数据
        Map<String, Object> stats = getCommissionStatsByChannel(params);
        
        return R.ok().put("stats", stats);
    }

    /**
     * 渠道趋势统计
     */
    @RequestMapping("/trend")
    // @RequiresPermissions("channel:statistics:view")
    @ApiOperation(value = "渠道趋势统计", notes = "")
    public R trendStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 获取趋势统计数据
        Map<String, Object> stats = getTrendStatsByChannel(params);
        
        return R.ok().put("stats", stats);
    }

    /**
     * 获取渠道总体统计（指定渠道）
     */
    private Map<String, Object> getChannelOverviewStats(List<Long> channelIds, String appid) {
        Map<String, Object> overview = new java.util.HashMap<>();
        
        try {
            // 渠道数量
            overview.put("channelCount", channelIds.size());
            
            // 业务员统计
            Map<String, Object> salesmanParams = new java.util.HashMap<>();
            salesmanParams.put("appid", appid);
            salesmanParams.put("channelIds", channelIds);
            
            // 订单统计
            Map<String, Object> orderParams = new java.util.HashMap<>();
            orderParams.put("appid", appid);
            orderParams.put("channelIds", channelIds);
            
            Map<String, Object> orderStats = getOrderStatsByCondition(orderParams);
            
            overview.put("totalSalesmanCount", 0);
            overview.put("activeSalesmanCount", 0);
            overview.put("totalOrders", orderStats.getOrDefault("totalOrders", 0));
            overview.put("totalAmount", orderStats.getOrDefault("totalAmount", 0.0));
            overview.put("totalCommission", orderStats.getOrDefault("totalCommission", 0.0));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return overview;
    }

    /**
     * 获取业务员统计数据
     */
    private Map<String, Object> getSalesmanStatsByChannel(Map<String, Object> params) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            // 这里可以调用具体的业务员统计查询
            stats.put("totalSalesman", 0);
            stats.put("activeSalesman", 0);
            stats.put("newSalesman", 0);
            stats.put("topPerformers", new java.util.ArrayList<>());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return stats;
    }

    /**
     * 获取佣金统计数据
     */
    private Map<String, Object> getCommissionStatsByChannel(Map<String, Object> params) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            // 这里可以调用具体的佣金统计查询
            stats.put("totalCommission", 0.0);
            stats.put("paidCommission", 0.0);
            stats.put("pendingCommission", 0.0);
            stats.put("commissionRate", 0.0);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return stats;
    }

    /**
     * 获取趋势统计数据
     */
    private Map<String, Object> getTrendStatsByChannel(Map<String, Object> params) {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            // 这里可以调用具体的趋势统计查询
            stats.put("dailyTrend", new java.util.ArrayList<>());
            stats.put("monthlyTrend", new java.util.ArrayList<>());
            stats.put("growthRate", 0.0);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return stats;
    }
}
