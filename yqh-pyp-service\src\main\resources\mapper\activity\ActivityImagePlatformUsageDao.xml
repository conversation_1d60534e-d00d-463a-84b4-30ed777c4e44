<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.activity.dao.ActivityImagePlatformUsageDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.activity.entity.ActivityImagePlatformUsageEntity" id="activityImagePlatformUsageMap">
        <result property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="imageId" column="image_id"/>
        <result property="platform" column="platform"/>
        <result property="useCount" column="use_count"/>
        <result property="firstUsedTime" column="first_used_time"/>
        <result property="lastUsedTime" column="last_used_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 获取图片在指定平台的使用次数 -->
    <select id="getUsageCount" resultType="java.lang.Integer">
        SELECT COALESCE(use_count, 0) 
        FROM activity_image_platform_usage 
        WHERE image_id = #{imageId} AND platform = #{platform}
    </select>

    <!-- 增加图片在指定平台的使用次数 -->
    <update id="incrementUsageCount">
        INSERT INTO activity_image_platform_usage 
        (activity_id, image_id, platform, use_count, first_used_time, last_used_time, create_time, update_time)
        VALUES (#{activityId}, #{imageId}, #{platform}, 1, NOW(), NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
            use_count = use_count + 1,
            last_used_time = NOW(),
            update_time = NOW()
    </update>

</mapper>
