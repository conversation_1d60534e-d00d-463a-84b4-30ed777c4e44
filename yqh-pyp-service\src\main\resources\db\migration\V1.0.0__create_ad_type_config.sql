-- 广告类型配置表
CREATE TABLE `ad_type_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type_code` varchar(50) NOT NULL COMMENT '类型编码（如：douyin, xiaohongshu等）',
  `type_name` varchar(100) NOT NULL COMMENT '类型名称（如：抖音, 小红书等）',
  `platform` varchar(100) NOT NULL COMMENT '平台名称',
  `content_type` varchar(100) NOT NULL COMMENT '内容类型（如：短视频, 图文等）',
  `title_length` varchar(200) NOT NULL COMMENT '标题长度要求',
  `content_length` varchar(200) NOT NULL COMMENT '内容长度要求',
  `topics_count` int(11) NOT NULL DEFAULT '5' COMMENT '话题数量',
  `topics_format` varchar(200) NOT NULL COMMENT '话题格式要求',
  `requirements` text NOT NULL COMMENT '内容要求（多行文本）',
  `style` varchar(500) NOT NULL COMMENT '风格特点',
  `prompt_template` text NOT NULL COMMENT 'Prompt模板',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `create_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告类型配置表';

-- 插入默认数据
INSERT INTO `ad_type_config` (`type_code`, `type_name`, `platform`, `content_type`, `title_length`, `content_length`, `topics_count`, `topics_format`, `requirements`, `style`, `prompt_template`, `sort_order`, `status`) VALUES
('douyin', '抖音', '抖音', '短视频', '15字以内，吸引眼球', '50-100字，简洁有力', 5, '不带#号，用逗号分隔', '- 语言要生动活泼\n- 要有强烈的视觉冲击力\n- 内容要有趣味性和互动性\n- 适合年轻用户群体\n- 要有明确的行动召唤', '年轻化、潮流、有趣、互动性强', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含任何#号或话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 1, 1),
('xiaohongshu', '小红书', '小红书', '图文', '20字以内，突出亮点', '80-150字，详细描述', 8, '带#号，用空格分隔', '- 要有生活化的场景描述\n- 内容要真实可信\n- 要有实用价值和分享价值\n- 适合女性用户群体\n- 要有美感和质感', '精致、生活化、实用、有美感', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}（每个话题前加#号，用空格分隔）\n\n风格特点：{style}', 2, 1),
('kuaishou', '快手', '快手', '短视频', '18字以内，接地气', '60-120字，通俗易懂', 6, '不带#号，用逗号分隔', '- 语言要接地气，贴近生活\n- 内容要有人情味\n- 要突出实用性和性价比\n- 适合下沉市场用户\n- 要有真实感和亲和力', '接地气、真实、亲民、实用', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含任何#号或话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 3, 1),
('dianping', '大众点评', '大众点评', '商户评价', '25字以内，突出体验', '100-200字，详细评价', 3, '不带#号，用逗号分隔', '- 要有具体的体验描述\n- 内容要客观真实\n- 要突出服务和产品特色\n- 要有评分建议\n- 要对其他用户有参考价值', '客观、详细、实用、有参考价值', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 4, 1),
('meituan', '美团点评', '美团点评', '商户评价', '25字以内，突出性价比', '100-200字，详细评价', 3, '不带#号，用逗号分隔', '- 要突出性价比和优惠信息\n- 内容要有消费建议\n- 要描述具体的产品和服务\n- 要有实用的消费提醒\n- 要对其他消费者有帮助', '实用、性价比、消费导向、有帮助', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 5, 1),
('weixin', '微信朋友圈', '微信朋友圈', '社交分享', '30字以内，引发共鸣', '80-150字，有互动性', 5, '不带#号，用逗号分隔', '- 要有社交属性和分享价值\n- 内容要引发共鸣和讨论\n- 要适合朋友圈的社交场景\n- 要有一定的情感色彩\n- 要鼓励互动和转发', '社交化、有共鸣、互动性强、情感化', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 6, 1),
('douyin_review', '抖音点评', '抖音点评', '商户点评', '18字以内，有话题性', '90字以内，生动有趣', 6, '不带#号，用逗号分隔', '- 要有真实的体验感受\n- 语言要生动有趣，适合短视频\n- 要有视觉冲击力的描述\n- 要能引起用户互动和关注\n- 要突出商户的特色和亮点', '生动、有趣、真实体验、互动性强', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含任何话题标签或#号）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含任何#号或话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 7, 1),
('general', '通用文案', '通用', '营销文案', '25字以内，突出核心', '100-200字，全面描述', 5, '不带#号，用逗号分隔', '- 语言要通俗易懂\n- 内容要有普适性\n- 要突出核心价值\n- 适合多平台使用', '通用、易懂、有价值、适应性强', '请为{platform}平台生成{content_type}文案。\n\n关键词：{keyword}\n{title_section}\n要求：\n{requirements}\n\n请以JSON格式返回以下内容（键名使用英文）：\n- 标题（title，{title_length}）\n- 内容（content，{content_length}，注意：内容中不要包含话题标签）\n- 话题（topics，{topics_count}个，{topics_format}，单独列出，不要放在内容中）\n\n重要提醒：\n- content字段中不要包含话题标签\n- 所有话题标签都应该单独放在topics字段中\n- topics格式：{topics_format}\n\n风格特点：{style}', 8, 1);
