<template>
  <el-dialog title="开启推流" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="推流过期时间">
        <!-- :disabled="dataForm.pushStatus == 1" -->
        <el-date-picker style="width: 100%" v-model="dataForm.pushExpiredTime" type="datetime" placeholder="请选择推流过期时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
        <div style="color: red">设置后，不要轻易修改</div>
      </el-form-item>
      <el-form-item v-if="dataForm.pushStatus == 1" style="width: 100%" label="推流地址">
        <el-input disabled="true" v-model="dataForm.pushUrl" placeholder="推流地址" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.pushStatus == 1" style="width: 100%" label="推流名称">
        <el-input disabled="true" v-model="dataForm.obsPushUrl" placeholder="推流名称" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.pushStatus == 1" style="width: 100%" label="推流秘钥">
        <el-input disabled="true" v-model="dataForm.obsPushSecretKey" placeholder="推流秘钥" clearable></el-input>
      </el-form-item>
      <el-form-item style="width: 100%" label="拉流地址">
        <el-input v-model="dataForm.playUrl" placeholder="拉流地址" clearable></el-input>
      </el-form-item>
      <div class="mod-user">
        <div style="display: flex;align-items: center;justify-content: center;">
          <vue-qrcode :options="{ width: 120 }" :value="iframeSrc"></vue-qrcode>
          <div style="margin-left: 20px;font-size: 24px;font-weight: 600;">扫码通过手机预览</div>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  data() {
    return {
      wxAccount: {},
      appid: '',
        iframeSrc: '',
      visible: false,
      dataForm: {
        id: 0,
        activityId: "",
        pushKey: "",
        pushExpiredTime: "",
        pushUrl: "",
        obsPushUrl: "",
        obsPushSecretKey: "",
        pushStatus: "",
        advice: "",
        playUrl: "",
        playStatus: "",
        playRtmpUrl: "",
        playFlvUrl: "",
        playUdpUrl: "",
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        pushExpiredTime: [
          { required: true, message: "推流过期时间不能为空", trigger: "blur" },
        ],
      },
    };
  },
    components: {
      VueQrcode
    },
  methods: {
    init(id) {
      this.appid = this.$cookie.get("appid");
      this.dataForm.id = id;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.getAccountInfo();
      });
    },
    getPlaceInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/info/${this.dataForm.id}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm = data.placeActivity;
          this.iframeSrc = this.wxAccount.baseUrl + 'lives/detail?detailId='+this.dataForm.id+'&id=' + this.dataForm.activityId;
          console.log(this.iframeSrc)
        }
      });
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
          this.getPlaceInfo();
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/place/placeactivity/openPush`),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              pushKey: this.dataForm.pushKey,
              pushExpiredTime: this.dataForm.pushExpiredTime,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1000,
                onClose: () => {
                  this.getPlaceInfo();
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
  },
};
</script>
