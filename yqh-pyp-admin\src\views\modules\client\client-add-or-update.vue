<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="150px">
      <el-form-item label="客户名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="客户名称"></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系人" prop="username">
            <el-input v-model="dataForm.username" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="mobile">
            <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="dataForm.type" placeholder="类型" filterable>
              <el-option v-for="item in clientType" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'区域'" prop="area">
            <el-cascader style="width: 100%" size="large" :options="options" v-model="dataForm.area"
              @change="handleChange">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input v-model="dataForm.address" placeholder="地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="银行账户信息" prop="isBank">
        <el-switch v-model="dataForm.isBank" active-color="#13ce66" inactive-color="#6f6f6f">
        </el-switch>
      </el-form-item>
      <el-row v-if="dataForm.isBank">
        <el-col :span="12">
          <el-form-item label="银行卡号" prop="bankAccount">
            <el-input v-model="dataForm.bankAccount" placeholder="银行卡号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行" prop="bankAddress">
            <el-input v-model="dataForm.bankAddress" placeholder="开户行"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="发票信息" prop="isInvoice">
        <el-switch v-model="dataForm.isInvoice" active-color="#13ce66" inactive-color="#6f6f6f">
        </el-switch>
      </el-form-item>
      <el-form-item v-if="dataForm.isInvoice" label="统一社会编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="统一社会编码"></el-input>
      </el-form-item>
      <el-row v-if="dataForm.isInvoice">
        <el-col :span="12">
          <el-form-item label="注册地址（专票）" prop="registerAddress">
            <el-input v-model="dataForm.registerAddress" placeholder="注册地址（专票）"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话（专票）" prop="registerTelephone">
            <el-input v-model="dataForm.registerTelephone" placeholder="注册电话（专票）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dataForm.isInvoice">
        <el-col :span="12">
          <el-form-item label="注册银行（专票）" prop="registerBank">
            <el-input v-model="dataForm.registerBank" placeholder="注册银行（专票）"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册账户（专票）" prop="registerAccount">
            <el-input v-model="dataForm.registerAccount" placeholder="注册账户（专票）"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { regionData } from 'element-china-area-data'
export default {
  data() {
    return {
      options: regionData,
      clientType: [],
      loading: false,
      visible: false,
      dataForm: {
        id: 0,
        isBank: true,
        isInvoice: false,
        name: '',
        username: '',
        code: '',
        address: '',
        email: '',
        mobile: '',
        registerAddress: '',
        registerTelephone: '',
        registerBank: '',
        repeatToken: '',
        bankAccount: '',
        bankAddress: '',
        registerAccount: '',
        type: 0,
        area: '',
      },
      dataRule: {
        name: [
          { required: true, message: '客户名称不能为空', trigger: 'blur' }
        ],
        // code: [
        //   { required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }
        // ],
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/client/client/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.client.name
              this.dataForm.username = data.client.username
              this.dataForm.code = data.client.code
              this.dataForm.address = data.client.address
              this.dataForm.email = data.client.email
              this.dataForm.mobile = data.client.mobile
              this.dataForm.registerAddress = data.client.registerAddress
              this.dataForm.registerTelephone = data.client.registerTelephone
              this.dataForm.registerBank = data.client.registerBank
              this.dataForm.registerAccount = data.client.registerAccount
              this.dataForm.bankAccount = data.client.bankAccount
              this.dataForm.bankAddress = data.client.bankAddress
              this.dataForm.appid = data.client.appid
              this.dataForm.type = data.client.type
              this.dataForm.area = data.client.area ? data.client.area.split(',') : []
              if (this.dataForm.bankAccount) {
                this.dataForm.isBank = true;
              }
              if (this.dataForm.code) {
                this.dataForm.isInvoice = true;
              }
            }
          })
        } else {
          this.dataForm.appid = this.$cookie.get("appid");
          this.dataForm.companyRoleId = this.$cookie.get("companyRoleId");
        }
      })
      this.getToken();
      this.getResult();
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    getResult() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'clientType',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.clientType = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(`/client/client/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'username': this.dataForm.username,
              'code': this.dataForm.code,
              'address': this.dataForm.address,
              'email': this.dataForm.email,
              'mobile': this.dataForm.mobile,
              'registerAddress': this.dataForm.registerAddress,
              'registerTelephone': this.dataForm.registerTelephone,
              'registerBank': this.dataForm.registerBank,
              'bankAddress': this.dataForm.bankAddress,
              'bankAccount': this.dataForm.bankAccount,
              'repeatToken': this.dataForm.repeatToken,
              appid: this.dataForm.appid,
              type: this.dataForm.type,
              area: this.dataForm.area ? this.dataForm.area.join(',') : '',
              'registerAccount': this.dataForm.registerAccount
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.result)
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
            this.loading = false;
          })
        }
      })
    }
  }
}
</script>
