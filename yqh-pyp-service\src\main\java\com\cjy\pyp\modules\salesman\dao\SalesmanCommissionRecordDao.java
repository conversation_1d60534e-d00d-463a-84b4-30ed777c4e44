package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 业务员佣金记录DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Mapper
public interface SalesmanCommissionRecordDao extends BaseMapper<SalesmanCommissionRecordEntity> {

    /**
     * 分页查询佣金记录列表
     * @param params 查询参数
     * @return 佣金记录列表
     */
    List<SalesmanCommissionRecordEntity> queryPage(Map<String, Object> params);

    /**
     * 查询业务员佣金统计
     * @param params 查询参数
     * @return 统计结果
     */
    Map<String, Object> getCommissionStats(Map<String, Object> params);

    /**
     * 查询待结算的佣金记录
     * @param params 查询参数
     * @return 佣金记录列表
     */
    List<SalesmanCommissionRecordEntity> getUnsettledRecords(Map<String, Object> params);

    /**
     * 批量更新结算状态
     * @param recordIds 记录ID列表
     * @param settlementStatus 结算状态
     * @param settlementBatchNo 结算批次号
     * @param settlementTime 结算时间
     * @param settlementRemarks 结算备注
     * @return 更新数量
     */
    int batchUpdateSettlementStatus(@Param("recordIds") List<Long> recordIds,
                                   @Param("settlementStatus") Integer settlementStatus,
                                   @Param("settlementBatchNo") String settlementBatchNo,
                                   @Param("settlementTime") String settlementTime,
                                   @Param("settlementRemarks") String settlementRemarks);

    /**
     * 检查业务记录是否已生成佣金
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param appid 应用ID
     * @return 是否存在
     */
    boolean existsByBusiness(@Param("businessType") String businessType,
                            @Param("businessId") Long businessId,
                            @Param("appid") String appid);

    /**
     * 查询业务员佣金汇总
     * @param params 查询参数
     * @return 汇总结果列表
     */
    List<Map<String, Object>> getCommissionSummary(Map<String, Object> params);

    /**
     * 查询佣金趋势数据
     * @param params 查询参数
     * @return 趋势数据列表
     */
    List<Map<String, Object>> getCommissionTrend(Map<String, Object> params);
}
