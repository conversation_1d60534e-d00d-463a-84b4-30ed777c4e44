package com.cjy.pyp.modules.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cjy.pyp.common.utils.TextUtils;
import com.cjy.pyp.config.TaskExcutor;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextPreGenerateService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import com.cjy.pyp.modules.activity.service.AiModelService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 文案预生成服务实现
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class ActivityTextPreGenerateServiceImpl implements ActivityTextPreGenerateService {

    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private AdTypeConfigService adTypeConfigService;

    @Override
    @Async
    public void preGenerateTextAsync(ActivityTextEntity originalText, Long userId) {
        log.info("开始预生成文案，原始文案ID: {}, 活动ID: {}, 广告类型: {}",
                originalText.getId(), originalText.getActivityId(), originalText.getAdType());

        // 使用线程池异步执行预生成任务
        TaskExcutor.submit(() -> {
            try {
                // 预生成2条文案
                for (int i = 1; i <= 2; i++) {
                    generateSinglePreText(originalText, userId, i);

                    // 每条文案生成间隔1秒，避免API调用过于频繁
                    Thread.sleep(1000);
                }

                log.info("预生成文案完成，活动ID: {}, 广告类型: {}",
                        originalText.getActivityId(), originalText.getAdType());

            } catch (Exception e) {
                log.error("预生成文案失败，活动ID: {}, 广告类型: {}, 错误: {}",
                        originalText.getActivityId(), originalText.getAdType(), e.getMessage(), e);
            }
        });
    }

    /**
     * 生成单条预生成文案
     */
    private void generateSinglePreText(ActivityTextEntity originalText, Long userId, int index) {
        try {
            // 创建预生成文案实体
            ActivityTextEntity preText = new ActivityTextEntity();
            preText.setActivityId(originalText.getActivityId());
            preText.setAdType(originalText.getAdType());
            preText.setModel(originalText.getModel());
            preText.setQuery(originalText.getQuery());
            preText.setNameMode(originalText.getNameMode());
            preText.setName(originalText.getName());
            preText.setUserCustomInput(originalText.getUserCustomInput());
            preText.setAiTag(originalText.getAiTag()); // 复制AI标签
            preText.setStatus("pre_generated"); // 标记为预生成
            preText.setUseCount(0);
            preText.setCreateBy(userId);

            // 构建提示词
            String fullPrompt;
            if (originalText.getPrompt() != null && !originalText.getPrompt().trim().isEmpty()) {
                fullPrompt = originalText.getPrompt();
            } else {
                fullPrompt = adTypeConfigService.buildPrompt(
                        originalText.getAdType(),
                        originalText.getQuery(),
                        originalText.getNameMode(),
                        originalText.getName(),
                        originalText.getUserCustomInput(),
                        originalText.getAiTag());
            }
            preText.setPrompt(fullPrompt);

            // 调用AI模型生成文案
            String generatedText = aiModelService.generateText(fullPrompt, originalText.getModel());

            // 解析生成的文案
            parseAndSaveGeneratedText(preText, generatedText);

            log.info("预生成文案成功，第{}条，活动ID: {}, 广告类型: {}",
                    index, originalText.getActivityId(), originalText.getAdType());

        } catch (Exception e) {
            log.error("生成第{}条预生成文案失败，活动ID: {}, 广告类型: {}, 错误: {}",
                    index, originalText.getActivityId(), originalText.getAdType(), e.getMessage(), e);
        }
    }

    /**
     * 解析并保存生成的文案
     */
    private void parseAndSaveGeneratedText(ActivityTextEntity activityText, String generatedText) {

        // 清理返回的JSON格式
        generatedText = TextUtils.cleanJsonResponse(generatedText);
        JSONObject jsonObject = JSON.parseObject(generatedText);
        if (StringUtils.isNotEmpty(activityText.getNameMode()) && activityText.getNameMode().equals("manual")) {

            if (null != jsonObject) {
                String content = jsonObject.getString("content");
                String topics = jsonObject.getString("topics");
                activityText.setTitle(topics);
                activityText.setResult(content);
            }
        } else {

            if (null != jsonObject) {
                String title = jsonObject.getString("title");
                String content = jsonObject.getString("content");
                String topics = jsonObject.getString("topics");
                activityText.setName(title);
                activityText.setTitle(topics);
                activityText.setResult(content);
            }
        }

        // 保存到数据库
        activityTextService.save(activityText);
    }

    @Override
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredPreGeneratedTexts() {
        log.info("开始清理过期的预生成文案...");

        try {
            // 计算7天前的时间
            Date sevenDaysAgo = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L);

            // 查询7天前的预生成文案
            QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("status", "pre_generated")
                    .eq("use_count", 0) // 未使用的
                    .lt("create_on", sevenDaysAgo);

            List<ActivityTextEntity> expiredTexts = activityTextService.list(wrapper);

            if (!expiredTexts.isEmpty()) {
                // 删除过期的预生成文案
                activityTextService.removeByIds(expiredTexts.stream().map(ActivityTextEntity::getId)
                        .collect(java.util.stream.Collectors.toList()));

                log.info("清理完成，删除了 {} 条过期的预生成文案", expiredTexts.size());
            } else {
                log.info("没有需要清理的过期预生成文案");
            }

        } catch (Exception e) {
            log.error("清理过期预生成文案失败", e);
        }
    }
}
