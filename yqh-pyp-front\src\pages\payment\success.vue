<template>
  <div class="payment-success">
    <!-- 成功图标 -->
    <div class="success-icon">
      <van-icon name="checked" size="60" color="#07c160" />
    </div>

    <!-- 成功信息 -->
    <div class="success-info">
      <h2>支付成功</h2>
      <p class="success-desc">{{ successMessage }}</p>
    </div>

    <!-- 订单信息 -->
    <div class="order-summary" v-if="!loading">
      <div class="summary-item">
        <span class="label">订单类型：</span>
        <span class="value">{{ orderTypeText }}</span>
      </div>
      <div class="summary-item">
        <span class="label">套餐名称：</span>
        <span class="value">{{ orderInfo.packageName }}</span>
      </div>
      <div class="summary-item">
        <span class="label">{{ orderType === 'activity' ? '可创建活动：' : '充值次数：' }}</span>
        <span class="value">{{ orderInfo.countValue }}{{ orderType === 'activity' ? '个' : '次' }}</span>
      </div>
      <div class="summary-item">
        <span class="label">支付金额：</span>
        <span class="value amount">¥{{ payAmount }}</span>
      </div>
      <div class="summary-item">
        <span class="label">订单编号：</span>
        <span class="value">{{ orderSn }}</span>
      </div>
      <div class="summary-item">
        <span class="label">支付时间：</span>
        <span class="value">{{ payTime }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <van-loading size="24px" vertical>加载订单信息中...</van-loading>
    </div>

    <!-- 业务员信息 -->
    <div class="salesman-thanks" v-if="salesmanInfo && !loading">
      <div class="thanks-card">
        <div class="avatar">
          <img :src="salesmanInfo.avatar || '../../assets/mine.png'" alt="业务员头像">
        </div>
        <div class="thanks-content">
          <h4>感谢您的信任</h4>
          <p>推荐业务员：{{ salesmanInfo.name || salesmanInfo.realName }}</p>
          <p>联系方式：{{ salesmanInfo.mobile || salesmanInfo.phone }}</p>
          <p v-if="salesmanInfo.company">所属公司：{{ salesmanInfo.company }}</p>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button 
        type="primary" 
        size="large" 
        @click="goToHome">
        返回首页
      </van-button>
      <van-button 
        size="large" 
        @click="viewOrder"
        style="margin-top: 10px;">
        查看订单
      </van-button>
    </div>

    <!-- 温馨提示 -->
    <div class="tips" v-if="!loading">
      <h4>温馨提示</h4>
      <ul>
        <li v-if="orderType === 'recharge'">充值成功后，次数已自动添加到您的账户</li>
        <li v-if="orderType === 'activity'">活动套餐购买成功，可在首页创建活动</li>
        <li v-if="salesmanInfo">如有疑问，请联系您的专属业务员：{{ salesmanInfo.name || salesmanInfo.realName }}</li>
        <li v-else>如有疑问，请联系客服</li>
        <li>感谢您的支持与信任</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentSuccess',
  data() {
    return {
      orderType: '',
      orderInfo: {
        packageName: '',
        countValue: 0,
        amount: 0,
        orderSn: '',
        payTime: '',
        createTime: ''
      },
      salesmanInfo: null,
      loading: true
    }
  },
  computed: {
    orderTypeText() {
      return this.orderType === 'recharge' ? '充值订单' : '活动套餐'
    },
    successMessage() {
      if (this.orderType === 'recharge') {
        return '充值成功，次数已添加到您的账户'
      } else {
        return '购买成功，活动套餐已激活'
      }
    },
    payAmount() {
      return this.orderInfo.amount || 0
    },
    orderSn() {
      return this.orderInfo.orderSn || ''
    },
    payTime() {
      return this.orderInfo.payTime || this.orderInfo.createTime || ''
    }
  },
  mounted() {
    this.loadOrderInfo()
  },
  methods: {
    // 加载订单信息
    loadOrderInfo() {
      const orderId = this.$route.query.orderId
      const type = this.$route.query.type
      const from = this.$route.query.from
      const salesmanId = this.$route.query.salesmanId

      this.orderType = type || 'recharge'

      if (!orderId) {
        this.$toast('订单ID不能为空')
        this.$router.push({ name: 'home' })
        return
      }

      // 根据来源选择不同的API
      let apiUrl = '/pyp/web/salesman/getRechargeOrderInfo'
      if (from === 'account') {
        apiUrl = '/pyp/web/account/getOrderInfo'
      }

      // 调用API获取订单详情
      this.$fly.get(apiUrl, {
        orderId
      }).then(res => {
        if (res.code === 200) {
          let orderInfo, packageInfo

          if (from === 'account') {
            // 账户页面的响应格式
            orderInfo = res.orderInfo || res.data
            packageInfo = res.packageInfo || res.data
          } else {
            // 业务员页面的响应格式
            orderInfo = res.orderInfo
            packageInfo = res.packageInfo
          }

          this.orderInfo = {
            packageName: packageInfo.name || packageInfo.packageName || '未知套餐',
            countValue: packageInfo.countValue || 0,
            amount: orderInfo.payAmount || orderInfo.amount || orderInfo.totalAmount || 0,
            orderSn: orderInfo.orderSn || '',
            payTime: orderInfo.payTime || orderInfo.updateTime || '',
            createTime: orderInfo.createTime || ''
          }
        } else {
          this.$toast(res.msg || '加载订单信息失败')
        }
        this.loading = false
      }).catch(err => {
        console.error('加载订单信息失败:', err)
        this.$toast('加载订单信息失败')
        this.loading = false
      })

      // 如果有业务员ID，获取业务员信息
      if (salesmanId) {
        this.loadSalesmanInfo(salesmanId)
      }
    },

    // 加载业务员信息
    loadSalesmanInfo(salesmanId) {
      this.$fly.get(`/pyp/web/salesman/info/${salesmanId}`).then(res => {
        if (res.code === 200) {
          this.salesmanInfo = res.result || res.salesman
        } else {
          console.error('加载业务员信息失败:', res.msg)
        }
      }).catch(err => {
        console.error('加载业务员信息失败:', err)
      })
    },

    // 返回首页
    goToHome() {
      this.$router.push({ name: 'home' })
    },

    // 查看订单
    viewOrder() {
      if (this.orderType === 'recharge') {
        this.$router.push({ name: 'rechargeOrders' })
      } else {
        this.$router.push({ name: 'myOrders' })
      }
    }
  }
}
</script>

<style scoped>
.payment-success {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  text-align: center;
}

.success-icon {
  margin: 40px 0 20px 0;
}

.success-info h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
  font-weight: bold;
}

.success-desc {
  margin: 0 0 30px 0;
  font-size: 16px;
  color: #666;
}

.order-summary {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: left;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;
}

.value.amount {
  color: #ff4444;
  font-weight: bold;
  font-size: 16px;
}

.salesman-thanks {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.thanks-card {
  display: flex;
  align-items: center;
  text-align: left;
}

.thanks-card .avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.thanks-card .avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thanks-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.thanks-content p {
  margin: 2px 0;
  font-size: 14px;
  color: #666;
}

.action-buttons {
  margin: 30px 0;
}

.tips {
  background: white;
  border-radius: 10px;
  padding: 20px;
  text-align: left;
}

.tips h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.tips ul {
  margin: 0;
  padding-left: 20px;
}

.tips li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.tips li:last-child {
  margin-bottom: 0;
}

.loading-container {
  background: white;
  border-radius: 10px;
  padding: 40px 20px;
  margin-bottom: 20px;
  text-align: center;
}
</style>
