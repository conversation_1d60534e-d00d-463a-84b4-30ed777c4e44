package com.cjy.pyp.modules.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.channel.dao.ChannelRefundQuotaRecordDao;
import com.cjy.pyp.modules.channel.entity.ChannelRefundQuotaRecordEntity;
import com.cjy.pyp.modules.channel.service.ChannelRefundQuotaRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 渠道退款名额使用记录服务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
@Service("channelRefundQuotaRecordService")
public class ChannelRefundQuotaRecordServiceImpl extends ServiceImpl<ChannelRefundQuotaRecordDao, ChannelRefundQuotaRecordEntity> implements ChannelRefundQuotaRecordService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        // 计算分页参数
        int currPage = 1;
        int limit = 10;
        
        if (params.get("page") != null) {
            currPage = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("limit") != null) {
            limit = Integer.parseInt(params.get("limit").toString());
        }
        
        int offset = (currPage - 1) * limit;
        params.put("offset", offset);
        params.put("limit", limit);
        
        // 查询数据
        List<ChannelRefundQuotaRecordEntity> list = baseMapper.queryPage(params);
        int total = baseMapper.queryPageCount(params);
        
        PageUtils pageUtil = new PageUtils(list, total, limit, currPage);
        return pageUtil;
    }

    @Override
    public Integer getChannelAssignedQuotaCount(Long channelId) {
        return baseMapper.getChannelAssignedQuotaCount(channelId);
    }

    @Override
    public Integer getOrderQuotaSequence(Long channelId, Long orderId) {
        return baseMapper.getOrderQuotaSequence(channelId, orderId);
    }

    @Override
    public List<Map<String, Object>> getChannelPaidOrdersForQuotaAssignment(Long channelId) {
        return baseMapper.getChannelPaidOrdersForQuotaAssignment(channelId);
    }

    @Override
    public boolean batchInsert(List<ChannelRefundQuotaRecordEntity> records) {
        if (records == null || records.isEmpty()) {
            return true;
        }
        
        // 设置创建时间
        Date now = new Date();
        for (ChannelRefundQuotaRecordEntity record : records) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
        }
        
        return baseMapper.batchInsert(records) > 0;
    }

    @Override
    public boolean deleteByChannelId(Long channelId) {
        return baseMapper.deleteByChannelId(channelId) >= 0;
    }

    @Override
    public Map<String, Object> getChannelRefundQuotaStats(Long channelId) {
        return baseMapper.getChannelRefundQuotaStats(channelId);
    }

    @Override
    public List<Map<String, Object>> getChannelEligibleOrders(Long channelId) {
        return baseMapper.getChannelEligibleOrders(channelId);
    }

    @Override
    public void recordQuotaAssignment(Long channelId, Long orderId, String orderSn, Integer quotaSequence, String remarks) {
        ChannelRefundQuotaRecordEntity record = new ChannelRefundQuotaRecordEntity();
        record.setChannelId(channelId);
        record.setOrderId(orderId);
        record.setOrderSn(orderSn);
        record.setActionType(ChannelRefundQuotaRecordEntity.ActionType.ASSIGN.getCode());
        record.setQuotaSequence(quotaSequence);
        record.setCreateTime(new Date());
        record.setRemarks(remarks);
        
        this.save(record);
    }

    @Override
    public void recordQuotaRelease(Long channelId, Long orderId, String orderSn, String remarks) {
        ChannelRefundQuotaRecordEntity record = new ChannelRefundQuotaRecordEntity();
        record.setChannelId(channelId);
        record.setOrderId(orderId);
        record.setOrderSn(orderSn);
        record.setActionType(ChannelRefundQuotaRecordEntity.ActionType.RELEASE.getCode());
        record.setQuotaSequence(null); // 释放时不需要序号
        record.setCreateTime(new Date());
        record.setRemarks(remarks);
        
        this.save(record);
    }
}
