<template>
  <el-dialog :title="'签到二维码'" :close-on-click-modal="false" :visible.sync="visible">
    <!-- <div style="text-align: center;font-size: 30px;font-weight: bold;">{{ dataForm.contact }}的签到二维码</div> -->

    <vue-qrcode :options="{ width: 240 }" :value="dataForm.activityUserId"></vue-qrcode>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { sources } from '@/data/common'
import { orderStatus } from '@/data/common'
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        activityId: '',
        activityUserId: '',
        userId: '',
        status: '',
        name: '',
        orderSn: '',
        source: '',
        remarks: '',
      },
      sources,
      orderStatus: orderStatus,
      dataRule: {
        status: [
          { required: true, message: '订单状态不能为空', trigger: 'blur' }
        ],
        source: [
          { required: true, message: '支付来源不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  components: {
    VueQrcode
  },
  methods: {
    init(id) {
      this.dataForm.id = id
      this.visible = true
        this.$http({
          url: this.$http.adornUrl(`/activity/activityuserapplyorder/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm = data.activityUserApplyOrder
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityuserapplyorder/update`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'activityId': this.dataForm.activityId,
              'userId': this.dataForm.userId,
              'status': this.dataForm.status,
              'name': this.dataForm.name,
              'activityUserId': this.dataForm.activityUserId,
              'orderSn': this.dataForm.orderSn,
              'source': this.dataForm.source,
              'remarks': this.dataForm.remarks,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
