<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
    <el-form-item label="是否显示联系酒店按钮" prop="isMobile" >
        <el-select v-model="dataForm.isMobile" placeholder="是否显示联系酒店按钮" filterable>
        <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="是否显示详情按钮" prop="isDetail">
        <el-select v-model="dataForm.isDetail" placeholder="是否显示详情按钮" filterable>
        <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="联系方式" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="地址" prop="address">
      <el-input v-model="dataForm.address" placeholder="地址"></el-input>
    </el-form-item>
    <el-form-item label="标签(逗号隔开)" prop="brief">
      <el-input v-model="dataForm.brief" placeholder="标签(逗号隔开)"></el-input>
    </el-form-item>
    <el-form-item label="内容" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { yesOrNo } from "@/data/common"
  export default {
    data () {
      return {
        yesOrNo,
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          hotelId: '',
          status: '',
          orderBy: '',
          brief: '',
          content: '',
          address: '',
          mobile: '',
          isMobile:1,
          isDetail:1,
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          hotelId: [
            { required: true, message: '酒店id不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '销售状态：0-未开启，1-已开启不能为空', trigger: 'blur' }
          ],
          orderBy: [
            { required: true, message: '排序，数值越小越靠前不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    components: {
      TinymceEditor: () =>
        import ("@/components/tinymce-editor"),
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivity/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.hotelActivity.activityId
                this.dataForm.hotelId = data.hotelActivity.hotelId
                this.dataForm.status = data.hotelActivity.status
                this.dataForm.orderBy = data.hotelActivity.orderBy
                this.dataForm.brief = data.hotelActivity.brief
                this.dataForm.content = data.hotelActivity.content
                this.dataForm.isMobile = data.hotelActivity.isMobile
                this.dataForm.address = data.hotelActivity.address
                this.dataForm.isDetail = data.hotelActivity.isDetail
                this.dataForm.mobile = data.hotelActivity.mobile
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivity/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'status': this.dataForm.status,
                'orderBy': this.dataForm.orderBy,
                'content': this.dataForm.content,
                'brief': this.dataForm.brief,
                'isMobile': this.dataForm.isMobile,
                'address': this.dataForm.address,
                'isDetail': this.dataForm.isDetail,
                'mobile': this.dataForm.mobile,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
