<template>
    <div class="train-select">
        <!-- 日期导航 -->
        <div class="date-nav">
            <van-button plain size="small" @click="switchDay(-1)" :disabled="isLoading">前一天</van-button>
            <div class="current-date">{{ inDate }}</div>
            <van-button plain size="small" @click="switchDay(1)" :disabled="isLoading">后一天</van-button>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-overlay">
            <van-loading type="spinner" color="#1989fa" />
            <div class="loading-text">火车票信息加载中...</div>
        </div>

        <!-- 火车列表 -->
        <template v-if="!isLoading && !showSeats">
            <div v-if="trains.length > 0" class="train-list">
                <div v-for="train in trains" :key="train.trainCode" class="train-item" @click="showTrainSeats(train)">
                    <div class="time-row">
                        <div class="time-info">
                            <span class="departure-time">{{ train.fromDateTime }}</span>
                            <span class="station-name">{{ train.fromStation }}</span>
                        </div>
                        <div class="train-duration">
                            <div class="duration-line"></div>
                            <span class="duration-text">{{ train.runTime }}时</span>
                        </div>
                        <div class="time-info text-right">
                            <span class="arrival-time">{{ train.toDateTime }}</span>
                            <span class="station-name">{{ train.toStation }}</span>
                        </div>
                    </div>

                    <div class="train-detail">
                        <div class="train-number">
                            <span>{{ train.trainCode }}</span>
                            <span class="train-type">{{ train.trainType || '普通列车' }}</span>
                        </div>
                        <div class="price-preview">
                            <span class="price-from">¥{{ getLowestPrice(train) }}</span>
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <van-empty v-else description="暂无火车票信息" />
        </template>

        <!-- 座位选择 -->
        <template v-if="!isLoading && showSeats">
            <div class="seat-header">
                <van-icon name="arrow-left" @click="backToTrainList" class="back-icon" />
                <div class="selected-train-info">
                    <div class="time-row">
                        <div class="time-info">
                            <span class="departure-time">{{ currentTrain.fromDateTime }}</span>
                            <span class="station-name">{{ currentTrain.fromStation }}</span>
                        </div>
                        <div class="train-duration">
                            <div class="duration-line"></div>
                            <span class="duration-text">{{ currentTrain.runTime }}时</span>
                        </div>
                        <div class="time-info text-right">
                            <span class="arrival-time">{{ currentTrain.toDateTime }}</span>
                            <span class="station-name">{{ currentTrain.toStation }}</span>
                        </div>
                    </div>
                    <div class="train-number">
                        <span>{{ currentTrain.trainCode }}</span>
                        <span class="train-type">{{ currentTrain.trainType || '普通列车' }}</span>
                    </div>
                </div>
            </div>

            <div class="seat-list">
                <div v-for="seat in currentTrain.Seats" :key="seat.seatType" class="seat-item"
                    :class="{ 'selected-item': selectedSeat.seatType === seat.seatType }" @click="selectSeat(seat)">
                    <div class="seat-info">
                        <div class="seat-left">
                            <span class="seat-name">{{ seat.seatTypeName }}</span>
                            <span class="seat-desc">剩余座位：{{ seat.leftTicketNum }}</span>
                        </div>
                        <div class="seat-right">
                            <span class="seat-price">¥{{ seat.ticketPrice }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
            <van-button plain block @click="goBack">取消</van-button>
            <van-button type="primary" block @click="confirmSelection"
                :disabled="!selectedSeat.seatType">确定</van-button>
        </div>
    </div>
</template>

<script>
import date from "@/js/date.js";

export default {
    name: 'TrainSelect',
    data() {
        return {
            isLoading: false,
            inDate: '',
            inStartPlace: '',
            inEndPlace: '',
            trains: [],
            showSeats: false,
            currentTrain: {},
            selectedSeat: {},
            tripInfo: {}
        };
    },
    mounted() {
        // 获取路由参数
        this.tripInfo = JSON.parse(this.$route.query.tripInfo || '{}');
        this.inDate = this.tripInfo.inDate ? date.formatDate.format(new Date(this.tripInfo.inDate), "yyyy/MM/dd") : date.formatDate.format(new Date(), "yyyy/MM/dd");
        this.inStartPlace = this.tripInfo.inStartPlace || '';
        this.inEndPlace = this.tripInfo.inEndPlace || '';

        // 加载火车票信息
        this.loadTrains();
    },
    methods: {
        goBack() {
            if (this.showSeats) {
                this.backToTrainList();
            } else {
                this.$router.back();
            }
        },

        // 切换日期
        switchDay(offset) {
            const currentDate = new Date(this.inDate);
            currentDate.setDate(currentDate.getDate() + offset);
            this.inDate = date.formatDate.format(currentDate, "yyyy/MM/dd");
            this.loadTrains();
            this.showSeats = false;
        },

        // 加载火车票信息
        loadTrains() {
            if (!this.inStartPlace || !this.inEndPlace) {
                vant.Toast('请选择出发地和目的地');
                return;
            }

            this.isLoading = true;
            this.trains = [];

            this.$fly.get('/pyp/panhe/searchTrain', {
                fromCity: this.inStartPlace,
                toCity: this.inEndPlace,
                fromDate: this.inDate
            }).then(res => {
                this.isLoading = false;
                if (res && res.code === 200) {
                    this.trains = res.result || [];
                } else {
                    vant.Toast(res.msg || '获取火车票信息失败');
                }
            }).catch(() => {
                this.isLoading = false;
                vant.Toast('网络错误，请重试');
            });
        },

        // 获取最低价格
        getLowestPrice(train) {
            if (!train.Seats || train.Seats.length === 0) {
                return '暂无';
            }

            let lowestPrice = Number.MAX_VALUE;
            train.Seats.forEach(seat => {
                if (seat.ticketPrice < lowestPrice) {
                    lowestPrice = seat.ticketPrice;
                }
            });

            return lowestPrice === Number.MAX_VALUE ? '暂无' : lowestPrice;
        },

        // 显示火车座位
        showTrainSeats(train) {
            this.currentTrain = train;
            this.showSeats = true;
            this.selectedSeat = {};
        },

        // 返回火车列表
        backToTrainList() {
            this.showSeats = false;
            this.currentTrain = {};
            this.selectedSeat = {};
        },

        // 选择座位
        selectSeat(seat) {
            this.selectedSeat = seat;
        },

        // 确认选择
        confirmSelection() {
            if (!this.selectedSeat.seatType) {
                vant.Toast('请选择座位');
                return;
            }

            // 构建行程信息
            const tripData = {
                ...this.tripInfo,
                inNumber: this.currentTrain.trainCode,
                inStartPlace: this.currentTrain.fromStation,
                inEndPlace: this.currentTrain.toStation,
                inStartDate: (this.currentTrain.fromDateTime + ':00').replaceAll("-", "/"),
                inEndDate: (this.currentTrain.toDateTime + ':00').replaceAll("-", "/"),
                cabinBookPara: this.selectedSeat.seatType,
                cabinCode: this.selectedSeat.seatTypeName,
                price: this.selectedSeat.ticketPrice,
                isBuy: 0,
                type: 0, // 单程
                typeName: '单程'
            };

            // 保存行程信息
            this.saveTrip(tripData);
        },

        // 保存行程信息
        saveTrip(tripData) {
            this.isLoading = true;
            const url = tripData.id ? "/pyp/web/activity/activityguest/updateTrip" : "/pyp/web/activity/activityguest/saveTrip";

            this.$fly.post(url, tripData).then(res => {
                this.isLoading = false;
                if (res && res.code === 200) {
                    vant.Toast('保存成功');
                    // 返回行程列表页面
                    this.$router.replace({
                        path: '/schedules/expertTrip',
                        query: { detailId: tripData.activityGuestId }
                    });
                } else {
                    vant.Toast(res.msg || '保存失败');
                }
            }).catch(() => {
                this.isLoading = false;
                vant.Toast('网络错误，请重试');
            });
        }
    }
};
</script>

<style lang="less" scoped>
.train-select {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.date-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #fff;
    margin-bottom: 10px;
}

.current-date {
    font-size: 16px;
    font-weight: bold;
}

.loading-overlay {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-text {
    margin-top: 10px;
    color: #999;
}

.train-list {
    padding: 0 10px;
    flex: 1;
    overflow-y: auto;
}

.train-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.time-info {
    display: flex;
    flex-direction: column;
    width: 30%;
}

.text-right {
    text-align: right;
}

.departure-time,
.arrival-time {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.station-name {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.train-duration {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 40%;
}

.duration-line {
    width: 100%;
    height: 1px;
    background-color: #ddd;
    position: relative;
}

.duration-line:before,
.duration-line:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #ddd;
    top: -2.5px;
}

.duration-line:before {
    left: 0;
}

.duration-line:after {
    right: 0;
}

.duration-text {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.train-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f5f5f5;
    padding-top: 10px;
}

.train-number {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.train-type {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
}

.price-preview {
    display: flex;
    align-items: center;
}

.price-from {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
    margin-right: 5px;
}

/* 座位选择样式 */
.seat-header {
    background-color: #fff;
    padding: 15px;
    position: relative;
    margin-bottom: 10px;
}

.back-icon {
    position: absolute;
    left: 15px;
    top: 15px;
    font-size: 20px;
    color: #333;
}

.selected-train-info {
    padding-left: 30px;
}

.seat-list {
    padding: 0 10px;
    flex: 1;
    overflow-y: auto;
}

.seat-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.seat-item.selected-item {
    background-color: #e6f7ff;
    border: 1px solid #1989fa;
}

.seat-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.seat-left {
    display: flex;
    flex-direction: column;
}

.seat-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.seat-desc {
    font-size: 12px;
    color: #999;
}

.seat-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.seat-price {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
}

.bottom-buttons {
    display: flex;
    padding: 10px;
    gap: 10px;
    background-color: #fff;
    border-top: 1px solid #eee;
    position: sticky;
    bottom: 0;
    z-index: 10;
}
</style>