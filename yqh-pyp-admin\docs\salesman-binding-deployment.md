# 业务员绑定系统部署说明

## 概述

本文档说明业务员佣金计算和绑定系统的前端部署步骤和注意事项。

## 已实现的功能

### 管理端页面
1. **微信用户业务员绑定管理** (`/salesman-wx-user-binding`)
   - 绑定关系的CRUD操作
   - 绑定统计展示
   - 批量操作功能
   - 绑定历史查看

2. **订单业务员关联管理** (`/salesman-order-association`)
   - 订单与业务员关联管理
   - 批量关联功能
   - 历史订单关联
   - 关联一致性验证

3. **佣金安全管理** (`/salesman-commission-safety`)
   - 防重复检查
   - 失败重试管理
   - 数据一致性验证
   - 分布式锁管理

### 移动端页面
1. **我的专属业务员** (`/salesman/my-salesman`)
   - 查看绑定的业务员信息
   - 业务员联系方式
   - 解除绑定功能
   - 绑定业务员入口

2. **扫码绑定业务员** (`/salesman/scan-qrcode`)
   - 二维码扫描绑定
   - 手动输入邀请码
   - 微信JS-SDK集成
   - 绑定结果展示

3. **联系业务员** (`/salesman/contact-salesman`)
   - 业务员联系方式展示
   - 多种联系方式支持
   - 服务时间说明
   - 常见问题解答

4. **绑定历史** (`/salesman/binding-history`)
   - 绑定变更历史
   - 时间线展示
   - 操作详情查看
   - 分页加载

## 部署步骤

### 1. 后端部署

#### 1.1 数据库表创建
确保以下数据库表已创建：
- `wx_user_salesman_binding` - 微信用户业务员绑定表
- `wx_user_salesman_binding_log` - 绑定变更记录表
- `wx_user_salesman_binding_stats` - 绑定统计表
- `salesman_commission_failure_log` - 佣金计算失败记录表
- `activity_recharge_record` 表添加 `salesman_id` 字段

#### 1.2 后端代码部署
确保以下后端组件已部署：
- Controller层：各种管理接口
- Service层：业务逻辑实现
- Entity层：数据实体类
- 佣金计算触发机制集成

### 2. 管理端部署

#### 2.1 文件清单
确保以下文件已添加到管理端项目：
```
yqh-pyp-admin/src/views/modules/salesman/
├── wx-user-binding.vue                    # 绑定管理主页面
├── wx-user-binding-add-or-update.vue      # 新增/编辑绑定
├── wx-user-binding-history.vue            # 绑定历史查看
├── order-association.vue                  # 订单关联管理
├── order-associate-salesman.vue           # 关联业务员弹窗
└── commission-safety.vue                  # 佣金安全管理
```

#### 2.2 路由配置
在 `yqh-pyp-admin/src/router/index.js` 中已添加以下路由：
```javascript
// 业务员绑定管理相关路由
{ path: '/salesman-wx-user-binding', component: () => import('@/views/modules/salesman/wx-user-binding'), name: 'salesman-wx-user-binding', meta: { title: '微信用户业务员绑定管理' } },
{ path: '/salesman-order-association', component: () => import('@/views/modules/salesman/order-association'), name: 'salesman-order-association', meta: { title: '订单业务员关联管理' } },
{ path: '/salesman-commission-safety', component: () => import('@/views/modules/salesman/commission-safety'), name: 'salesman-commission-safety', meta: { title: '佣金安全管理' } },
```

#### 2.3 菜单配置
需要在系统菜单管理中添加以下菜单项：
- 微信用户业务员绑定管理
- 订单业务员关联管理
- 佣金安全管理

### 3. 移动端部署

#### 3.1 文件清单
确保以下文件已添加到移动端项目：
```
yqh-pyp-front/src/pages/salesman/
├── my-salesman.vue          # 我的专属业务员
├── scan-qrcode.vue          # 扫码绑定业务员
├── contact-salesman.vue     # 联系业务员
└── binding-history.vue      # 绑定历史
```

#### 3.2 路由配置
在 `yqh-pyp-front/src/router.js` 中已添加以下路由：
```javascript
// 新增业务员绑定相关路由
{ path: '/salesman/my-salesman', name: 'mySalesman', component: () => import('@/pages/salesman/my-salesman'), meta: { title: '我的专属业务员' } },
{ path: '/salesman/scan-qrcode', name: 'scanQrcode', component: () => import('@/pages/salesman/scan-qrcode'), meta: { title: '扫码绑定业务员' } },
{ path: '/salesman/contact-salesman', name: 'contactSalesman', component: () => import('@/pages/salesman/contact-salesman'), meta: { title: '联系业务员' } },
{ path: '/salesman/binding-history', name: 'bindingHistory', component: () => import('@/pages/salesman/binding-history'), meta: { title: '绑定历史' } },
```

### 4. 依赖检查

#### 4.1 管理端依赖
确保以下依赖已安装：
- Element UI（表格、表单、弹窗等组件）
- Vue Router（路由管理）
- Axios（HTTP请求）

#### 4.2 移动端依赖
确保以下依赖已安装：
- Vant UI（移动端组件库）
- Vue Router（路由管理）
- Axios（HTTP请求）

### 5. 配置说明

#### 5.1 API接口配置
确保前端项目中的API基础URL配置正确，能够访问到后端接口。

#### 5.2 微信JS-SDK配置
移动端扫码功能需要微信JS-SDK支持：
1. 确保后端提供微信配置接口 `/web/wechat/config`
2. 配置微信公众号的JS接口安全域名
3. 确保微信JS-SDK正确加载

#### 5.3 权限配置
管理端页面需要配置相应的权限：
- `salesman:wxuserbinding:list` - 查看绑定列表
- `salesman:wxuserbinding:save` - 新增绑定
- `salesman:wxuserbinding:update` - 修改绑定
- `salesman:wxuserbinding:delete` - 删除绑定
- `salesman:orderassociation:*` - 订单关联相关权限
- `salesman:commissionsafety:*` - 佣金安全相关权限

## 测试验证

### 1. 管理端测试
1. 访问绑定管理页面，验证列表展示
2. 测试新增、编辑、删除绑定功能
3. 测试订单关联管理功能
4. 测试佣金安全管理功能

### 2. 移动端测试
1. 访问我的专属业务员页面
2. 测试扫码绑定功能（需要在微信环境）
3. 测试手动输入邀请码绑定
4. 测试联系业务员功能
5. 测试绑定历史查看

### 3. 接口测试
使用API文档中的接口进行测试，确保前后端数据交互正常。

## 注意事项

1. **微信环境**：扫码功能需要在微信浏览器中使用
2. **权限控制**：确保用户有相应的操作权限
3. **数据一致性**：注意绑定关系的数据一致性
4. **错误处理**：完善错误提示和异常处理
5. **性能优化**：大数据量时注意分页和性能优化

## 后续扩展

1. **消息通知**：绑定成功/失败的消息推送
2. **数据统计**：更详细的绑定数据统计分析
3. **批量导入**：支持Excel批量导入绑定关系
4. **API优化**：根据实际使用情况优化API性能
5. **移动端优化**：根据用户反馈优化移动端体验
