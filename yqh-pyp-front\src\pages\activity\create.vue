<template>
  <div class="activity-create">
    <van-nav-bar
      title="创建活动"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />
    
    <van-form @submit="onSubmit">
      <van-field
        v-model="form.name"
        name="name"
        label="活动名称"
        placeholder="请输入活动名称"
        :rules="[{ required: true, message: '请输入活动名称' }]"
      />
      
      <van-field
        v-model="form.description"
        name="description"
        label="活动描述"
        type="textarea"
        placeholder="请输入活动描述"
        rows="3"
      />
      
      <van-field
        v-model="form.address"
        name="address"
        label="活动地址"
        placeholder="请输入活动地址"
        :rules="[{ required: true, message: '请输入活动地址' }]"
      />
      
      <van-field
        v-model="form.startTime"
        name="startTime"
        label="开始时间"
        placeholder="请选择开始时间"
        readonly
        @click="showStartPicker = true"
        :rules="[{ required: true, message: '请选择开始时间' }]"
      />
      
      <van-field
        v-model="form.endTime"
        name="endTime"
        label="结束时间"
        placeholder="请选择结束时间"
        readonly
        @click="showEndPicker = true"
        :rules="[{ required: true, message: '请选择结束时间' }]"
      />
      
      <van-field
        v-model="form.maxApply"
        name="maxApply"
        label="报名人数限制"
        type="number"
        placeholder="请输入最大报名人数（0为不限制）"
      />
      
      <div class="form-section">
        <h3>活动海报</h3>
        <van-uploader
          v-model="bannerList"
          :max-count="1"
          :after-read="afterReadBanner"
          accept="image/*"
        >
          <van-button icon="plus" type="primary" size="small">上传海报</van-button>
        </van-uploader>
      </div>
      
      <div class="submit-section">
        <van-button round block type="primary" native-type="submit" :loading="submitting">
          创建活动
        </van-button>
      </div>
    </van-form>

    <!-- 时间选择器 -->
    <van-popup v-model="showStartPicker" position="bottom">
      <van-datetime-picker
        v-model="startDate"
        type="datetime"
        title="选择开始时间"
        @confirm="onStartConfirm"
        @cancel="showStartPicker = false"
      />
    </van-popup>
    
    <van-popup v-model="showEndPicker" position="bottom">
      <van-datetime-picker
        v-model="endDate"
        type="datetime"
        title="选择结束时间"
        @confirm="onEndConfirm"
        @cancel="showEndPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'ActivityCreate',
  data() {
    return {
      form: {
        name: '',
        description: '',
        address: '',
        startTime: '',
        endTime: '',
        maxApply: 0
      },
      bannerList: [],
      submitting: false,
      showStartPicker: false,
      showEndPicker: false,
      startDate: new Date(),
      endDate: new Date()
    }
  },
  methods: {
    onSubmit() {
      this.submitting = true
      
      // 模拟提交
      setTimeout(() => {
        this.$toast.success('活动创建成功')
        this.submitting = false
        this.$router.push('/')
      }, 2000)
    },
    
    afterReadBanner(file) {
      this.$toast.loading('上传中...')
      // 模拟上传
      setTimeout(() => {
        this.$toast.success('上传成功')
      }, 1000)
    },
    
    onStartConfirm() {
      this.form.startTime = this.formatDateTime(this.startDate)
      this.showStartPicker = false
    },
    
    onEndConfirm() {
      this.form.endTime = this.formatDateTime(this.endDate)
      this.showEndPicker = false
    },
    
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style scoped>
.activity-create {
  background: #f5f5f5;
  min-height: 100vh;
}

.form-section {
  background: white;
  padding: 15px;
  margin: 10px 0;
}

.form-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.submit-section {
  padding: 20px;
  background: white;
  margin-top: 20px;
}
</style>
