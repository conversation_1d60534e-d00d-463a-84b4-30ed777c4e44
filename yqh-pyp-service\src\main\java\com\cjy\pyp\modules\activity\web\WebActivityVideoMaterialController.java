package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.dto.VideoEditRequest;
import com.cjy.pyp.modules.activity.dto.VideoEditResponse;
import com.cjy.pyp.modules.activity.entity.ActivityVideoEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityVideoService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Map;

/**
 * 活动视频素材Web接口
 */
@RestController
@RequestMapping("web/activity/activityvideo")
public class WebActivityVideoMaterialController extends AbstractController {
    
    @Autowired
    private ActivityVideoService activityVideoService;
    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;


    @PostMapping("/uploadVideo")
    public R uploadAlyVideo(@RequestParam("file") MultipartFile file,
                            @RequestParam("activityId") Long activityId,
                            @CookieValue String appid) {
        //返回上传视频id
        String videoId = activityVideoService.uploadVideoAly(file,activityId,appid);
        return R.ok().put("videoId", videoId);
    }

    /**
     * 提交视频混剪任务（自动获取活动下的素材视频和文案）
     */
    @RequestMapping("/submitVideoEdit")
    public R submitVideoEdit(@RequestParam("activityId") Long activityId, @CookieValue String appid) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    getUserId(),
                    activityId,
                    2, // 生成视频类型
                    null,
                    "生成视频"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }
            VideoEditRequest request = new VideoEditRequest();
            request.setActivityId(activityId);
            request.setAppid(appid);

            VideoEditResponse response = activityVideoService.submitVideoEdit(request, getUserId());
            return R.ok().put("response", response);
        } catch (Exception e) {
            return R.error("提交视频混剪任务失败: " + e.getMessage());
        }
    }
    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            PageUtils page = activityVideoService.queryPage(params);
            return R.ok().put("page", page);
        } catch (Exception e) {
            return R.error("获取视频列表失败: " + e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        try {
            ActivityVideoEntity activityVideo = activityVideoService.getById(id);
            return R.ok().put("activityVideo", activityVideo);
        } catch (Exception e) {
            return R.error("获取视频信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ActivityVideoEntity activityVideo) {
        try {
            // 设置创建用户
            activityVideo.setCreateBy(getUserId());
            activityVideoService.save(activityVideo);
            return R.ok();
        } catch (Exception e) {
            return R.error("保存视频失败: " + e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody ActivityVideoEntity activityVideo) {
        try {
            // 设置更新用户
            activityVideo.setUpdateBy(getUserId());
            activityVideoService.updateById(activityVideo);
            return R.ok();
        } catch (Exception e) {
            return R.error("更新视频失败: " + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        try {
            activityVideoService.removeByIds(Arrays.asList(ids));
            return R.ok();
        } catch (Exception e) {
            return R.error("删除视频失败: " + e.getMessage());
        }
    }

}
