<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.WxUserSalesmanBindingLogDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity" id="wxUserSalesmanBindingLogMap">
        <result property="id" column="id"/>
        <result property="wxUserId" column="wx_user_id"/>
        <result property="oldSalesmanId" column="old_salesman_id"/>
        <result property="newSalesmanId" column="new_salesman_id"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationReason" column="operation_reason"/>
        <result property="bindingSource" column="binding_source"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operatorId" column="operator_id"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <!-- 关联查询字段 -->
        <result property="wxUserName" column="wx_user_name"/>
        <result property="wxUserMobile" column="wx_user_mobile"/>
        <result property="oldSalesmanName" column="old_salesman_name"/>
        <result property="oldSalesmanCode" column="old_salesman_code"/>
        <result property="newSalesmanName" column="new_salesman_name"/>
        <result property="newSalesmanCode" column="new_salesman_code"/>
        <result property="operationTypeDesc" column="operation_type_desc"/>
        <result property="operatorTypeDesc" column="operator_type_desc"/>
        <result property="operatorName" column="operator_name"/>
    </resultMap>

    <select id="queryPage" resultMap="wxUserSalesmanBindingLogMap">
        SELECT 
            log.id,
            log.wx_user_id,
            log.old_salesman_id,
            log.new_salesman_id,
            log.operation_type,
            log.operation_reason,
            log.binding_source,
            log.operator_type,
            log.operator_id,
            log.appid,
            log.create_on,
            -- 微信用户信息
            wu.nickname AS wx_user_name,
            wu.mobile AS wx_user_mobile,
            -- 原业务员信息
            old_s.name AS old_salesman_name,
            old_s.code AS old_salesman_code,
            -- 新业务员信息
            new_s.name AS new_salesman_name,
            new_s.code AS new_salesman_code,
            -- 操作类型描述
            CASE log.operation_type
                WHEN 1 THEN '新增绑定'
                WHEN 2 THEN '更换业务员'
                WHEN 3 THEN '解除绑定'
                WHEN 4 THEN '系统自动解绑'
                ELSE '未知操作'
            END AS operation_type_desc,
            -- 操作人类型描述
            CASE log.operator_type
                WHEN 1 THEN '客户自己'
                WHEN 2 THEN '业务员'
                WHEN 3 THEN '管理员'
                WHEN 4 THEN '系统'
                ELSE '未知'
            END AS operator_type_desc,
            -- 操作人姓名
            CASE log.operator_type
                WHEN 1 THEN wu.nickname
                WHEN 2 THEN op_s.name
                WHEN 3 THEN su.username
                WHEN 4 THEN '系统'
                ELSE '未知'
            END AS operator_name
        FROM wx_user_salesman_binding_log log
        LEFT JOIN wx_user wu ON log.wx_user_id = wu.id
        LEFT JOIN salesman old_s ON log.old_salesman_id = old_s.id
        LEFT JOIN salesman new_s ON log.new_salesman_id = new_s.id
        LEFT JOIN salesman op_s ON log.operator_type = 2 AND log.operator_id = op_s.id
        LEFT JOIN sys_user su ON log.operator_type = 3 AND log.operator_id = su.user_id
        <where>
            log.appid = #{appid}
            <if test="wxUserId != null and wxUserId != ''">
                AND log.wx_user_id = #{wxUserId}
            </if>
            <if test="operationType != null and operationType != ''">
                AND log.operation_type = #{operationType}
            </if>
            <if test="operatorType != null and operatorType != ''">
                AND log.operator_type = #{operatorType}
            </if>
            <if test="startDate != null and startDate != ''">
                AND log.create_on &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND log.create_on &lt;= #{endDate}
            </if>
            <if test="wxUserName != null and wxUserName != ''">
                AND wu.nickname LIKE CONCAT('%', #{wxUserName}, '%')
            </if>
            <if test="salesmanName != null and salesmanName != ''">
                AND (old_s.name LIKE CONCAT('%', #{salesmanName}, '%') 
                     OR new_s.name LIKE CONCAT('%', #{salesmanName}, '%'))
            </if>
        </where>
        ORDER BY log.create_on DESC
    </select>

    <!-- 根据微信用户ID查询绑定历史 -->
    <select id="getBindingHistoryByWxUserId" resultMap="wxUserSalesmanBindingLogMap">
        SELECT 
            log.id,
            log.wx_user_id,
            log.old_salesman_id,
            log.new_salesman_id,
            log.operation_type,
            log.operation_reason,
            log.binding_source,
            log.operator_type,
            log.operator_id,
            log.appid,
            log.create_on,
            -- 原业务员信息
            old_s.name AS old_salesman_name,
            old_s.code AS old_salesman_code,
            -- 新业务员信息
            new_s.name AS new_salesman_name,
            new_s.code AS new_salesman_code,
            -- 操作类型描述
            CASE log.operation_type
                WHEN 1 THEN '新增绑定'
                WHEN 2 THEN '更换业务员'
                WHEN 3 THEN '解除绑定'
                WHEN 4 THEN '系统自动解绑'
                ELSE '未知操作'
            END AS operation_type_desc,
            -- 操作人类型描述
            CASE log.operator_type
                WHEN 1 THEN '本人操作'
                WHEN 2 THEN '业务员操作'
                WHEN 3 THEN '管理员操作'
                WHEN 4 THEN '系统操作'
                ELSE '未知'
            END AS operator_type_desc
        FROM wx_user_salesman_binding_log log
        LEFT JOIN salesman old_s ON log.old_salesman_id = old_s.id
        LEFT JOIN salesman new_s ON log.new_salesman_id = new_s.id
        WHERE log.wx_user_id = #{wxUserId}
        AND log.appid = #{appid}
        ORDER BY log.create_on DESC
    </select>

    <!-- 统计查询 -->
    <select id="getOperationStats" resultType="java.util.Map">
        SELECT 
            operation_type,
            COUNT(*) as count
        FROM wx_user_salesman_binding_log
        WHERE appid = #{appid}
        <if test="startDate != null and startDate != ''">
            AND create_on &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_on &lt;= #{endDate}
        </if>
        GROUP BY operation_type
        ORDER BY operation_type
    </select>

    <!-- 查询最近的绑定变更记录 -->
    <select id="getRecentBindingChanges" resultMap="wxUserSalesmanBindingLogMap">
        SELECT 
            log.id,
            log.wx_user_id,
            log.old_salesman_id,
            log.new_salesman_id,
            log.operation_type,
            log.operation_reason,
            log.binding_source,
            log.operator_type,
            log.create_on,
            -- 微信用户信息
            wu.nickname AS wx_user_name,
            wu.mobile AS wx_user_mobile,
            -- 新业务员信息
            new_s.name AS new_salesman_name,
            new_s.code AS new_salesman_code,
            -- 操作类型描述
            CASE log.operation_type
                WHEN 1 THEN '新增绑定'
                WHEN 2 THEN '更换业务员'
                WHEN 3 THEN '解除绑定'
                WHEN 4 THEN '系统自动解绑'
                ELSE '未知操作'
            END AS operation_type_desc
        FROM wx_user_salesman_binding_log log
        LEFT JOIN wx_user wu ON log.wx_user_id = wu.id
        LEFT JOIN salesman new_s ON log.new_salesman_id = new_s.id
        WHERE log.appid = #{appid}
        ORDER BY log.create_on DESC
        LIMIT #{limit}
    </select>

    <!-- 根据业务员ID查询相关的绑定变更记录 -->
    <select id="getBindingChangesBySalesmanId" resultMap="wxUserSalesmanBindingLogMap">
        SELECT 
            log.id,
            log.wx_user_id,
            log.old_salesman_id,
            log.new_salesman_id,
            log.operation_type,
            log.operation_reason,
            log.binding_source,
            log.operator_type,
            log.create_on,
            -- 微信用户信息
            wu.nickname AS wx_user_name,
            wu.mobile AS wx_user_mobile,
            -- 操作类型描述
            CASE log.operation_type
                WHEN 1 THEN '新增绑定'
                WHEN 2 THEN '更换业务员'
                WHEN 3 THEN '解除绑定'
                WHEN 4 THEN '系统自动解绑'
                ELSE '未知操作'
            END AS operation_type_desc
        FROM wx_user_salesman_binding_log log
        LEFT JOIN wx_user wu ON log.wx_user_id = wu.id
        WHERE log.appid = #{appid}
        AND (log.old_salesman_id = #{salesmanId} OR log.new_salesman_id = #{salesmanId})
        ORDER BY log.create_on DESC
    </select>

</mapper>
