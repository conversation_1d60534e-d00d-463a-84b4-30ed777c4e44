package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户业务员绑定实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Data
@TableName("wx_user_salesman_binding")
@Accessors(chain = true)
public class WxUserSalesmanBindingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 微信用户ID（对应wx_user.id）
     */
    private Long wxUserId;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 绑定方式：1-二维码扫描，2-邀请链接，3-手动绑定，4-系统分配
     */
    private Integer bindingType;

    /**
     * 绑定来源（二维码ID、邀请码等）
     */
    private String bindingSource;

    /**
     * 绑定时间
     */
    private Date bindingTime;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 失效时间（NULL表示永久有效）
     */
    private Date expiryTime;

    /**
     * 状态：0-已失效，1-有效，2-已解绑
     */
    private Integer status;

    /**
     * 优先级（数字越大优先级越高，用于多业务员绑定时的选择）
     */
    private Integer priority;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    // 关联查询字段
    /**
     * 业务员姓名
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 业务员编号
     */
    @TableField(exist = false)
    private String salesmanCode;

    /**
     * 业务员手机号
     */
    @TableField(exist = false)
    private String salesmanMobile;

    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String wxUserName;

    /**
     * 客户手机号
     */
    @TableField(exist = false)
    private String wxUserMobile;
    @TableField(exist = false)
    private String wxUserOpenid;

    /**
     * 订单数量（关联查询字段）
     */
    @TableField(exist = false)
    private Integer orderCount;

    /**
     * 订单金额（关联查询字段）
     */
    @TableField(exist = false)
    private java.math.BigDecimal orderAmount;

    /**
     * 绑定类型描述
     */
    @TableField(exist = false)
    private String bindingTypeDesc;

    /**
     * 状态描述
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 是否已过期
     */
    @TableField(exist = false)
    private Boolean isExpired;

    /**
     * 剩余有效天数
     */
    @TableField(exist = false)
    private Integer remainingDays;
}
