package com.cjy.pyp.modules.groupbuying.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.groupbuying.dao.GroupBuyingCouponDao;
import com.cjy.pyp.modules.groupbuying.entity.GroupBuyingCouponEntity;
import com.cjy.pyp.modules.groupbuying.service.GroupBuyingCouponService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 团购券服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@Service("groupBuyingCouponService")
public class GroupBuyingCouponServiceImpl extends ServiceImpl<GroupBuyingCouponDao, GroupBuyingCouponEntity> implements GroupBuyingCouponService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String couponName = (String) params.get("couponName");
        String platformType = (String) params.get("platformType");
        String activityId = (String) params.get("activityId");
        String status = (String) params.get("status");

        IPage<GroupBuyingCouponEntity> page = this.page(
                new Query<GroupBuyingCouponEntity>().getPage(params),
                new QueryWrapper<GroupBuyingCouponEntity>()
                        .like(StringUtils.isNotBlank(couponName), "coupon_name", couponName)
                        .eq(StringUtils.isNotBlank(platformType), "platform_type", platformType)
                        .eq(StringUtils.isNotBlank(activityId), "activity_id", activityId)
                        .eq(StringUtils.isNotBlank(status), "status", status)
                        .orderByDesc("sort_order")
                        .orderByDesc("create_on")
        );

        return new PageUtils(page);
    }

    @Override
    public List<GroupBuyingCouponEntity> getByActivityId(Long activityId) {
        return this.list(new QueryWrapper<GroupBuyingCouponEntity>()
                .eq("activity_id", activityId)
                .orderByDesc("sort_order")
                .orderByDesc("create_on"));
    }

    @Override
    public List<GroupBuyingCouponEntity> getByActivityIdAndPlatform(Long activityId, String platformType) {
        return this.list(new QueryWrapper<GroupBuyingCouponEntity>()
                .eq("activity_id", activityId)
                .eq("platform_type", platformType)
                .eq("status", 1)
                .orderByDesc("sort_order")
                .orderByDesc("create_on"));
    }

    @Override
    public List<GroupBuyingCouponEntity> getValidCoupons(Long activityId) {
        Date now = new Date();
        return this.list(new QueryWrapper<GroupBuyingCouponEntity>()
                .eq("activity_id", activityId)
                .eq("status", 1)
                .and(wrapper -> wrapper
                        .isNull("start_time")
                        .or()
                        .le("start_time", now))
                .and(wrapper -> wrapper
                        .isNull("end_time")
                        .or()
                        .ge("end_time", now))
                .orderByDesc("sort_order")
                .orderByDesc("create_on"));
    }

    @Override
    public boolean updateSoldCount(Long couponId, Integer soldCount) {
        GroupBuyingCouponEntity coupon = this.getById(couponId);
        if (coupon != null) {
            coupon.setSoldCount((coupon.getSoldCount() == null ? 0 : coupon.getSoldCount()) + soldCount);
            return this.updateById(coupon);
        }
        return false;
    }
}
