<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.activity.dao.ActivityNotifyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.activity.entity.ActivityNotifyEntity" id="activityNotifyMap">
        <result property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="appid" column="appid"/>
        <result property="read" column="read"/>
    </resultMap>


    <!-- queryPage --> 

    <select id="queryPage" parameterType="java.util.Map" resultType="com.cjy.pyp.modules.activity.entity.ActivityNotifyEntity">
        SELECT
        aa.*,
        bb.`name` AS activity_name
        FROM
        activity_notify aa
        LEFT JOIN tb_activity bb ON bb.id = aa.activity_id
        where 1=1
            and aa.activity_id = #{activityId} 

        <if test="read != null and read != ''">
            and aa.read = #{read}
        </if>
        <if test="type != null and type != ''">
            and aa.type = #{type}
        </if>
        <if test="typeList != null and typeList.size() > 0">
            and aa.type in
            <foreach collection="typeList" item="typeItem" open="(" separator="," close=")">
                #{typeItem}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and aa.name like concat('%',#{name},'%')
        </if>
        order by aa.create_on desc
    </select>
</mapper>