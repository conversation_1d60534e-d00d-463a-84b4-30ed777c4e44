<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.channel.dao.ChannelRefundQuotaRecordDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.channel.entity.ChannelRefundQuotaRecordEntity" id="channelRefundQuotaRecordMap">
        <result property="id" column="id"/>
        <result property="channelId" column="channel_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="actionType" column="action_type"/>
        <result property="quotaSequence" column="quota_sequence"/>
        <result property="createTime" column="create_time"/>
        <result property="remarks" column="remarks"/>
        <!-- 关联查询字段 -->
        <result property="channelName" column="channel_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="userName" column="user_name"/>
        <result property="userMobile" column="user_mobile"/>
        <result property="salesmanName" column="salesman_name"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="orderStatus" column="order_status"/>
    </resultMap>

    <!-- 分页查询退款名额使用记录 -->
    <select id="queryPage" resultMap="channelRefundQuotaRecordMap">
        SELECT 
            r.*,
            c.name as channel_name,
            c.code as channel_code,
            wu.nickname as user_name,
            wu.mobile as user_mobile,
            s.name as salesman_name,
            arr.pay_amount as order_amount,
            arr.status as order_status
        FROM channel_refund_quota_record r
        LEFT JOIN channel c ON r.channel_id = c.id
        LEFT JOIN activity_recharge_record arr ON r.order_id = arr.id
        LEFT JOIN salesman s ON arr.salesman_id = s.id
        LEFT JOIN wx_user wu ON arr.user_id = wu.id
        WHERE 1=1
        <if test="channelId != null and channelId != ''">
            AND r.channel_id = #{channelId}
        </if>
        <if test="orderSn != null and orderSn != ''">
            AND r.order_sn LIKE CONCAT('%', #{orderSn}, '%')
        </if>
        <if test="actionType != null and actionType != ''">
            AND r.action_type = #{actionType}
        </if>
        <if test="startTime != null and startTime != ''">
            AND r.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.create_time &lt;= #{endTime}
        </if>
        ORDER BY r.create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 查询退款名额使用记录总数 -->
    <select id="queryPageCount" resultType="int">
        SELECT COUNT(*)
        FROM channel_refund_quota_record r
        LEFT JOIN channel c ON r.channel_id = c.id
        LEFT JOIN activity_recharge_record arr ON r.order_id = arr.id
        WHERE 1=1
        <if test="channelId != null and channelId != ''">
            AND r.channel_id = #{channelId}
        </if>
        <if test="orderSn != null and orderSn != ''">
            AND r.order_sn LIKE CONCAT('%', #{orderSn}, '%')
        </if>
        <if test="actionType != null and actionType != ''">
            AND r.action_type = #{actionType}
        </if>
        <if test="startTime != null and startTime != ''">
            AND r.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND r.create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 获取渠道当前已分配的退款权限数量 -->
    <select id="getChannelAssignedQuotaCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT r.order_id)
        FROM channel_refund_quota_record r
        INNER JOIN activity_recharge_record arr ON r.order_id = arr.id
        WHERE r.channel_id = #{channelId}
        AND r.action_type = 1  -- 分配权限
        AND arr.status = 1     -- 已支付状态
        AND NOT EXISTS (
            -- 排除已释放权限的订单
            SELECT 1 FROM channel_refund_quota_record r2 
            WHERE r2.order_id = r.order_id 
            AND r2.action_type = 2 
            AND r2.create_time > r.create_time
        )
    </select>

    <!-- 获取渠道内订单的退款权限排序 -->
    <select id="getOrderQuotaSequence" resultType="java.lang.Integer">
        SELECT r.quota_sequence
        FROM channel_refund_quota_record r
        WHERE r.channel_id = #{channelId}
        AND r.order_id = #{orderId}
        AND r.action_type = 1  -- 分配权限
        ORDER BY r.create_time DESC
        LIMIT 1
    </select>

    <!-- 获取渠道内按时间排序的已支付订单列表（用于权限分配） -->
    <select id="getChannelPaidOrdersForQuotaAssignment" resultType="java.util.Map">
        SELECT 
            arr.id as order_id,
            arr.order_sn,
            arr.create_on,
            arr.status,
            arr.refund_eligible,
            wu.nickname as user_name,
            wu.mobile as user_mobile,
            s.name as salesman_name
        FROM activity_recharge_record arr
        INNER JOIN salesman s ON arr.salesman_id = s.id
        LEFT JOIN wx_user wu ON arr.user_id = wu.id
        WHERE s.channel_id = #{channelId}
        AND arr.status = 1  -- 已支付
        AND arr.salesman_id IS NOT NULL
        ORDER BY arr.create_on ASC
    </select>

    <!-- 批量插入退款名额使用记录 -->
    <insert id="batchInsert">
        INSERT INTO channel_refund_quota_record 
        (id, channel_id, order_id, order_sn, action_type, quota_sequence, create_time, remarks)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.id}, #{record.channelId}, #{record.orderId}, #{record.orderSn}, 
             #{record.actionType}, #{record.quotaSequence}, #{record.createTime}, #{record.remarks})
        </foreach>
    </insert>

    <!-- 删除渠道的所有退款名额记录（重新分配时使用） -->
    <delete id="deleteByChannelId">
        DELETE FROM channel_refund_quota_record WHERE channel_id = #{channelId}
    </delete>

    <!-- 获取渠道退款名额统计信息 -->
    <select id="getChannelRefundQuotaStats" resultType="java.util.Map">
        SELECT 
            c.refund_quota as total_quota,
            c.refund_quota_used as used_quota,
            (c.refund_quota - c.refund_quota_used) as available_quota,
            COUNT(DISTINCT CASE WHEN r.action_type = 1 AND arr.status = 1 THEN r.order_id END) as assigned_orders,
            COUNT(DISTINCT CASE WHEN arr.status = 3 THEN r.order_id END) as refunded_orders,
            COUNT(DISTINCT arr.id) as total_paid_orders
        FROM channel c
        LEFT JOIN channel_refund_quota_record r ON c.id = r.channel_id
        LEFT JOIN activity_recharge_record arr ON r.order_id = arr.id
        WHERE c.id = #{channelId}
        GROUP BY c.id, c.refund_quota, c.refund_quota_used
    </select>

    <!-- 查询渠道内具有退款权限的订单列表 -->
    <select id="getChannelEligibleOrders" resultType="java.util.Map">
        SELECT 
            arr.id as order_id,
            arr.order_sn,
            arr.create_on,
            arr.pay_amount,
            arr.status,
            arr.refund_eligible,
            arr.refund_quota_assigned_time,
            wu.nickname as user_name,
            wu.mobile as user_mobile,
            s.name as salesman_name,
            r.quota_sequence
        FROM activity_recharge_record arr
        INNER JOIN salesman s ON arr.salesman_id = s.id
        LEFT JOIN wx_user wu ON arr.user_id = wu.id
        LEFT JOIN channel_refund_quota_record r ON arr.id = r.order_id AND r.action_type = 1
        WHERE s.channel_id = #{channelId}
        AND arr.refund_eligible = 1
        AND arr.status = 1  -- 已支付
        ORDER BY r.quota_sequence ASC, arr.create_on ASC
    </select>

</mapper>
