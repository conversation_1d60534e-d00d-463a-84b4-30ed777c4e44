<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="选择导出时间" prop="exportTime">
        <el-date-picker v-model="dataForm.times" style="width: 100%"
         type="datetimerange" value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        activityId: '',
        times: []
      },
      dataRule: {
        times: [
          { required: true, message: "导出时间不能为空", trigger: "blur" },
        ],
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.activityId = id
      this.visible = true
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {

          var url = this.$http.adornUrl("/place/placeactivityhourlog/export?" + [
            "token=" + this.$cookie.get('token'),
            "startTime=" + this.dataForm.times[0],
            "endTime=" + this.dataForm.times[1],
            "activityId=" + this.dataForm.activityId
          ].join('&'));
          window.open(url);
        }
      })
    }
  }
}
</script>
