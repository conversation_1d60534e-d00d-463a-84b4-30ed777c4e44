<template>
  <div
    :class="isMobilePhone ? '' : 'pc-container'"
    
  >
    <pcheader v-if="!isMobilePhone" />
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">申请试用/商务合作</div>
    </div>
    <van-cell-group inset>
      <van-field
        v-model="guestInfo.username"
        name="姓名"
        label="姓名"
        placeholder="请填写姓名"
        required
        :rules="[{ required: true, message: '请填写姓名' }]"
      >
      </van-field>
      <van-field
        v-model="guestInfo.mobile"
        name="联系方式"
        label="联系方式"
        placeholder="请填写联系方式"
        required
        :rules="[{ required: true, message: '请填写联系方式' }]"
      >
      </van-field>
      <van-field
        v-model="guestInfo.companyName"
        name="公司名称"
        label="公司名称"
        placeholder="请填写公司名称"
        required
        :rules="[{ required: true, message: '请填写公司名称' }]"
      >
      </van-field>
    </van-cell-group>
    <div style="margin: 16px">
      <van-button
      v-if="guestInfo.product == 0"
        round
      color="#DD5C5F"
        block
        type="info"
        @click="submit"
        :loading="loading"
        loading-text="提交中"
        >提交</van-button
      >
      <van-button
      v-else
        round
        block
        type="info"
        @click="submit"
        :loading="loading"
        loading-text="提交中"
        >提交</van-button
      >
    </div>
  </div>
</template>

<script>
import { isMobilePhone,isMobile } from "@/js/validate";
import { proxyProduct,proxyStatus } from "@/data/proxy";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: { pcheader },
  data() {
    return {
      proxyProduct,
      proxyStatus,
      loading : false,
      guestInfo: {
        username: '',
        mobile: '',
        companyName: '',
        product: 0,
        status: 0,
      },
      isMobilePhone: isMobilePhone(),
      value: null,
    };
  },
  mounted() {
    document.title = '商务合作'
    this.guestInfo.product = this.$route.query.type;
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
        } else {
          vant.Toast(res.msg);
        }
      });
    },
    submit() {
      if (!this.guestInfo.username) {
        vant.Toast("请输入姓名");
        return false;
      }
      if (!this.guestInfo.mobile) {
        vant.Toast("请输入联系方式");
        return false;
      }
      if (!isMobile(this.guestInfo.mobile)) {
        vant.Toast("手机号格式错误");
        return;
      }
      if (!this.guestInfo.companyName) {
        vant.Toast("请输入公司名称");
        return false;
      }
      this.loading = true;
      // 保存
      this.$fly
        .post(
          "/pyp/web/proxy/proxyapply/save",
          this.guestInfo
        )
        .then((res) => {
          this.loading = false;
          if (res && res.code === 200) {
            
            vant.Dialog.alert({
                title: "提交成功",
                message: "后续会有工作人员与您联系，请耐心等待，谢谢",
              }).then(() => {
                // on close
                if(this.guestInfo.product== 0) {
                  this.$router.replace({
                    name: "yqh",
                  });

                } else {
                  this.$router.replace({
                    name: "yunhuiyi",
                  });
                }
              });
            // 绑定手机
          } else {
            vant.Toast(res.msg);
          }
        });
    }
  },
};
</script>
<style lang="less" scoped>
.transparent {
  /deep/.van-collapse-item__content {
    background: transparent;
  }
}
.content {
  /deep/ img {
    max-width: 100%;
    height: auto;
  }
}
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
.van-cell__title {
  flex: none;
  box-sizing: border-box;
  width: 6.2em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
  line-height: 33px;
}
.van-cell__value {
  text-align: left;
    display: flex;
    align-items: center;
}
.sign {
  position: fixed;
  top: 0;
  background: white;
  height: 100%;

  width: 100%;
  .button {
    text-align: center;
    width: 30%;
    height: 40px;
    line-height: 40px;
    background: #4485ff;
    border-radius: 20px;
    text-align: center;
    color: white;
  }
}
.sign-btns {
  display: flex;
  justify-content: space-between;
  #clear,
  #clear1,
  #save {
    display: inline-block;
    padding: 5px 10px;
    width: 76px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #eee;
    background: #e1e1e1;
    border-radius: 10px;
    text-align: center;
    margin: 20px auto;
    cursor: pointer;
  }
}
</style>