<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.username" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="timeArray" type="daterange" value-format="yyyy/MM/dd" range-separator="至"
          start-placeholder="开始日期(新增时间)" end-placeholder="结束日期(新增时间)" :picker-options="pickerOptions" />
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('client:client:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('client:client:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
          <el-button type="primary" @click="exportHandle()">导出</el-button>
          <el-button type="success" @click="downloadExampleHandle()">模板下载</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/client/client/importExcel?appid=' + $cookie.get('appid')" :name="'客户数据导入'"></Upload>
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border size="mini" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" width="250px" label="客户名称">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="统一社会信用代码">
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="类型">
        <!-- <div slot-scope="scope">
          <el-tag  type="primary" :class="('tag-color tag-color-' + scope.row.type)">{{ scope.row.type == null ? '空' :
              clientType[scope.row.type].value }}</el-tag>
        </div> -->
      </el-table-column>
      <el-table-column prop="username" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="bankAccount" header-align="center" align="center" label="银行账户">
      </el-table-column>
      <el-table-column prop="bankAddress" header-align="center" align="center" label="开户行">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="email" header-align="center" align="center" label="邮箱">
      </el-table-column>
      <el-table-column prop="areaName" header-align="center" align="center" label="区域">
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" label="地址">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="small" @click="bankHandle(scope.row.id)">银行账户</el-button> -->
          <el-button type="text" size="small" @click="thingHandle(scope.row.id)">客户文件</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './client-add-or-update'
import {clientType} from '@/data/client'
export default {
  data() {
    return {
      clientType,
      dataForm: {
        name: '',
        username: '',
        mobile: '',
      },
      timeArray: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,

      dataListSelections: [],
      addOrUpdateVisible: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
    }
  },
  components: {
    AddOrUpdate,
    Upload: () => import('@/components/upload'),
  },
  activated() {
    this.getDataList()
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {

      this.$http({
        url: this.$http.adornUrl('/client/client/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'username': this.dataForm.username,
          appid: this.$cookie.get("appid"),
          start: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : '',
          end: (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : '',
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }

      })
    },
    exportHandle() {
      var url = this.$http.adornUrl("/client/client/export?" + [
        "page=1",
        "limit=100000",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "username=" + this.dataForm.username,
        "appid=" + this.$cookie.get('appid'),
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    bankHandle(id) {
      this.$router.push({
        name: "clientbank",
        query: {
          id: id,
        },
      });
    },
    thingHandle(id) {
      this.$router.push({
        name: "clientthing",
        query: {
          id: id,
        },
      });
    },
    downloadExampleHandle() {
      var url = this.$http.adornUrl("/client/client/exportExcelExample?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/client/client/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
