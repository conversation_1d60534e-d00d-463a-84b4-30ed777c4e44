package com.cjy.pyp.modules.salesman.enums;

/**
 * 佣金结算状态枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
public enum SettlementStatusEnum {
    
    /**
     * 未结算
     */
    UNSETTLED(0, "未结算"),
    
    /**
     * 已结算
     */
    SETTLED(1, "已结算"),
    
    /**
     * 已取消
     */
    CANCELLED(2, "已取消");
    
    private final Integer code;
    private final String desc;
    
    SettlementStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static SettlementStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SettlementStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
