<template>
  <div :class="isMobilePhone ? 'page' : 'page pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-swipe :autoplay="3000" v-if="isMobilePhone">
      <van-swipe-item v-for="(image, index) in activityInfo.appFileList" :key="index">
        <van-image width="100%" :src="image.url"> </van-image>
      </van-swipe-item>
    </van-swipe>
    <van-empty>
      <!-- 审核状态 -->
      <img v-if="channelInfo && channelInfo.isVerify" slot="image" :src="orderInfo.verifyStatus == 0
        ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/********/168906ff7dc74239835061f0a8cfe5fd.png'
        : orderInfo.verifyStatus == 1
          ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png'
          : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png'" alt="" />
      <!-- 付款状态 -->
      <img v-else slot="image" :src="orderInfo.status == 0
        ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/********/168906ff7dc74239835061f0a8cfe5fd.png'
        : orderInfo.status == 1
          ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20210618/7361a2571f8a41809960a577bb123733.png'
          : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20211228/13da716239e94e0fa7f84712e06ce9d1.png'" alt="" />
      <!-- 审核状态 -->
      <div v-if="channelInfo && channelInfo.isVerify" slot="description">{{ verifyText }}</div>
      <!-- 付款状态 -->
      <div v-else slot="description">{{ (activityId == 78 && statusText == '待付款')
        ? '您有一笔注册费待支付' : (orderInfo.totalAmount == 0 && statusText == '已付款')
          ? '已报名' : statusText }}</div>
      <div class="orderInfo" v-if="activityId != 1833036138154082306">
        <div style="
            color: #969799;
            font-size: 16px;
            padding-bottom: 20px;
            text-align: center;
          ">
          报名费用：￥{{ orderInfo.totalAmount }}元
        </div>
      </div>
      <div class="botton" v-if="activityId != 1674612614545080321">
        <div class="button-item">
          <img class="button-image" @click="back"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/191a0fb893ae4ab8af27f20d0f395b7d.png" alt="" />
        </div>
        <div class="button-item" v-if="orderInfo.status == 1 && activityInfo.applySuccessLive">
          <img class="button-image" @click="turnLive" :src="activityId == 135 ? 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20230312/d94288caf6594b0296b02486deca7e24.png' :
            'http://mpjoy.oss-cn-beijing.aliyuncs.com/********/d47c65f2457f46e39bc70b1db37e0c39.png'" alt="" />
        </div>
        <!-- <div class="button-item" v-if="orderInfo.status == 1 && activityId == 1651597072446533634">
          <img
            class="button-image"
            @click="turnExam"
            :src="
            'http://mpjoy.oss-cn-beijing.aliyuncs.com/********/af5395fca56f404abcf8fba4a8756227.png'"
            alt=""
          />
        </div> -->
        <div class="button-item" v-if="orderInfo.status != 2">
          <img class="button-image" @click="cancelApply"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/7b6f394d16be435b963ae7b2101207a5.png" alt="" />
        </div>
        <div class="button-item" v-if="channelInfo && channelInfo.isBankTransfer == 1 && orderInfo.status == 0">
          <img class="button-image" @click="bankTransfer"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/75b6dacaf5714fa28408a26203d52e92.png" alt="" />
        </div>
        <div class="button-item" v-if="channelInfo && channelInfo.isWechatPay == 1 && orderInfo.status == 0">
          <img class="button-image" @click="weixin"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/1d27eefbace94b3c9b993312f66b5501.png" alt="" />
        </div>
        <div class="button-item" v-if="channelInfo && channelInfo.isAliPay == 1 && orderInfo.status == 0">
          <img class="button-image" @click="ali"
            src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c70a37e66e8d494bad00a643ad642d07.png" alt="" />
        </div>
        <div class="button-item" v-if="activityId != 1651597072446533634">
          <img class="button-image" @click="
            $router.push({
              name: 'cmsIndex',
              query: { id: orderInfo.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/c1d6aa26252a40e294ae986c180abc8b.png" alt="" />
        </div>
        <div class="button-item">
          <img class="button-image" @click="
            $router.push({
              name: 'meMine',
              query: { id: orderInfo.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/b8711e1f644f4c919ad39a3186a77c83.png" alt="" />
        </div>
        <div v-if="activityInfo.applyProxy" class="button-item">
          <img class="button-image" @click="
            $router.push({
              name: 'applyProxyList',
              query: { id: orderInfo.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230705/22399e1748d9412498d282a4204be741.png" alt="" />
        </div>
        <div v-if="(userInfo && userInfo.isHotel && userInfo.isHotel == '是') || activityInfo.applySuccessHotel" class="button-item">
          <img class="button-image" @click="
            $router.push({
              name: 'hotelIndex',
              query: { id: orderInfo.activityId },
            })
            " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/********/f333c238845143e38ad317ef5e9377f3.png" alt="" />
        </div>
      </div>
    </van-empty>
    <follow-modal :show="showFollowMOdal" :activityId="activityId" :appid="appid"
      :qrcodeImgUrl="activityInfo.subscribeImg" @close="showFollowMOdal = false"></follow-modal>
    <!-- 九宫格底部-自定义 -->
    <div class="bottomdiy" v-if="activityInfo.applyYhy">
      <a class="item" href="https://zhaoshengniuren.com/mp_yqh/#/yunhuiyi">技术支持：云会易</a>
    </div>
  </div>
</template>

<script>
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: {
    FollowModal: () => import("@/components/FollowModal"),
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      orderId: undefined,
      activityId: undefined,
      appid: undefined,
      activityInfo: {},
      channelInfo: {},
      orderInfo: {},
      userInfo: {},
      showFollowMOdal: false,
      verifyText: '',
      statusText: '',
    };
  },
  computed: {
    isPay: {
      get() {
        return this.$store.state.apply.isPay;
      },
      set(val) {
        this.$store.commit("apply/update", val);
      },
    },
  },
  mounted() {
    document.title = "报名成功";
    this.appid = this.$cookie.get("appid");
    this.orderId = this.$route.query.orderId;
    this.activityId = this.$route.query.id;
    if (this.$route.query.token) {
      this.$cookie.set("token", this.$route.query.token);
    }
    this.rebuildUrl();
    this.getOrderInfo();
  },
  methods: {
    getActivityInfo(activityId) {
      this.$fly.get(`/pyp/activity/activity/info/${activityId}`).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.activityInfo = res.activity;
          if (this.activityInfo.subscribeImg && this.orderInfo.status == 1) {
            this.showFollowMOdal = true;
          }
        } else {
          vant.Toast(res.msg);
          this.activityInfo = {};
        }
      });
    },
    getUserActivityInfo(activityId) {
      this.$fly
        .get(`/pyp/activity/activityuser/meMine`, {
          activityId: activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.result;
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getOrderInfo() {
      this.$fly
        .get(`/pyp/web/activity/activityuserapplyorder/info/${this.orderId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.orderInfo = res.result;
            this.statusText = res.statusText;
            this.verifyText = res.verifyText;
            let status = 0;
            if (this.orderInfo.status == 0) {
              status = 2;
            }
            if (this.orderInfo.status == 1) {
              status = 1;
            }
            this.isPay = status;
            this.getActivityInfo(this.orderInfo.activityId);
            this.getUserActivityInfo(this.orderInfo.activityId);
            if (this.orderInfo.applyActivityChannelConfigId != null) {
              this.getApplyActivityChannelConfig(
                this.orderInfo.applyActivityChannelConfigId
              );
            }
          } else {
            vant.Toast(res.msg);
            this.orderInfo = {};
            this.statusText = "";
          }
        });
    },
    getApplyActivityChannelConfig(v) {
      this.$fly
        .get(`/pyp/web/apply/applyactivitychannelconfig/info/${v}`)
        .then((res) => {
          if (res.code == 200) {
            this.channelInfo = res.applyActivityChannelConfig;
            if (
              this.orderInfo.status == 0 &&
              this.channelInfo.bankTransferNotify
            ) {
              vant.Dialog.confirm({
                title: "付款须知",
                message: this.channelInfo.bankTransferNotify,
                confirmButtonText: "取消",
                cancelButtonText: "已缴费(忽略)",
              })
                .then(() => {
                  // on close
                })
                .catch(() => {
                  // on close
                });
            }
          }
        });
    },
    // 取消报名
    cancelApply() {
      vant.Dialog.confirm({
        title: "提示",
        message: "确认取消报名?",
      })
        .then(() => {
          this.$fly
            .post("/pyp/web/activity/activityuserapplyorder/cancelOrder", {
              id: this.orderInfo.activityUserId,
            })
            .then((res) => {
              if (res && res.code === 200) {
                vant.Toast("取消成功");
                this.$store.commit("apply/update", 0);
                if (this.isMobilePhone) {
                  this.$router.replace({
                    name: "meMine",
                    query: { id: this.orderInfo.activityId },
                  });
                } else {
                  this.$router.go(-1);
                }
              } else {
                vant.Toast(res.msg);
              }
            });
        })
        .catch(() => { });
    },
    bankTransfer() {
      vant.Dialog.confirm({
        title: "付款须知",
        message: this.channelInfo.bankTransferNotify,
        confirmButtonText: "取消",
        cancelButtonText: "已缴费(忽略)",
      })
        .then(() => {
          // on close
        })
        .catch(() => {
          // on close
        });
    },
    weixin() {
      var that = this;
      // 如果需要支付，调用支付接口，并且设置了微信支付，没有设置银行支付的前提下
      this.$fly
        .get(
          "/pyp/web/activity/activityuserapplyorder/pay",
          { orderId: that.orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            WeixinJSBridge.invoke(
              'getBrandWCPayRequest', {
              "appId": res1.result.appId,
              "timeStamp": res1.result.timeStamp,
              "nonceStr": res1.result.nonceStr,
              "package": res1.result.packageValue,
              "signType": res1.result.signType,
              "paySign": res1.result.paySign
            },
              function (res2) {
                console.log("开始支付")
                location.reload();
                // if (res2.err_msg == "get_brand_wcpay_request:ok") {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/apply/success?id=' + that.activityId + "&orderId=" + that.orderId; //支付成功跳转到详情页
                // } else if (res2.err_msg == "get_brand_wcpay_request:cancel") {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/apply/success?id=' + that.activityId + "&orderId=" + that.orderId;
                // } else {
                //   var baseUrl = window.location.href.split("#")[0];
                //   location.href = baseUrl + '#/apply/success?id=' + that.activityId + "&orderId=" + that.orderId;
                // }
              });
          } else {
            vant.Toast(res1.msg);

          }
        })
    },
    ali() {
      this.$fly
        .get(
          "/pyp/web/activity/activityuserapplyorder/payAli",
          { orderId: this.orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            this.$router.push({
              name: 'commonAlipay',
              query: {
                form: encodeURIComponent(res1.result)
              }
            })
          } else {
            vant.Toast(res1.msg);
          }
        })
    },
    back() {
      let url = sessionStorage.getItem("returnUrl");
      if (url) {
        location.href = decodeURIComponent(url);
      } else {
        this.$router.push({
          name: 'cmsIndex',
          query: {
            id: this.activityId
          },
        });
      }
    },
    turnLive() {
      if (this.activityId == 78 && this.orderInfo.status == 1) {
        location.href =
          "https://wx.vzan.com/live/mk/aggspread/618781199/f9dfa6f6-5cbf-11ed-93c5-043f72d45e40?v=1667621522612";
      } else {
        this.$router.push({
          name: "livesIndex",
          query: { id: this.activityId },
        });
      }
    },
    turnExam() {
      this.$router.push({
        name: "examIndex",
        query: { id: this.activityId },
      });
    },
    rebuildUrl() {
      let {
        href,
        protocol,
        host,
        pathname,
        search,
        hash
      } = window.location
      console.log(window.location)
      search = search || '?'
      let newHref = `${protocol}//${host}${pathname}${search}${hash}`
      console.log(newHref)
      if (newHref !== href) {
        window.location.replace(newHref)
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #f7f8fa;
}

.bottom-button {
  width: 30%;
  height: 40px;
  margin-bottom: 10px;
  margin-right: 5px;
}

/deep/ .van-empty__description {
  color: black;
  font-weight: bold;
  font-size: 20px;
}

/deep/ .van-empty__bottom {
  width: 90%;
}

.botton {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .button-item {
    width: 33%;
    // height: 50px;
    // line-height: 50px;
  }

  .button-image {
    width: 100%;
  }
}

.van-dialog /deep/ img {
  width: 100%;
  height: auto;
}

.bottomdiy {

  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  background-color: black;
  opacity: 0.3;

  .item {
    color: white;
  }
}
</style>