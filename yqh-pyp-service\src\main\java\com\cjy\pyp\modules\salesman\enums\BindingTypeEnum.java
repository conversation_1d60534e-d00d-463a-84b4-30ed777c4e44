package com.cjy.pyp.modules.salesman.enums;

/**
 * 绑定方式枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public enum BindingTypeEnum {
    
    /**
     * 二维码扫描
     */
    QR_CODE_SCAN(1, "二维码扫描"),
    
    /**
     * 邀请链接
     */
    INVITE_LINK(2, "邀请链接"),
    
    /**
     * 手动绑定
     */
    MANUAL_BINDING(3, "手动绑定"),
    
    /**
     * 系统分配
     */
    SYSTEM_ASSIGN(4, "系统分配");
    
    private final Integer code;
    private final String desc;
    
    BindingTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static BindingTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BindingTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        BindingTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : "未知";
    }
}
