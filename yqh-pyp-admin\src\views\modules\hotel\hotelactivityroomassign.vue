<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-select v-model="dataForm.hotelActivityId" @change="hotelChange">
          <el-option label="全部(酒店)" value=""></el-option>
          <el-option v-for="item in hotels" :key="item.id" :label="item.hotelName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.hotelActivityRoomId">
          <el-option label="全部(房型)" value=""></el-option>
          <el-option v-for="item in rooms" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status">
          <el-option label="全部(入住状态)" value=""></el-option>
          <el-option v-for="item in roomAssignStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.number" placeholder="房号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="assignHandle()">快速入住</el-button>
        <el-button type="warning" @click="turnNumber()">房号管理</el-button>
        <el-button type="danger" @click="turnTag()">标签管理</el-button>
        <el-button @click="exportHandle()" type="success">导出</el-button>
        <!-- <el-button v-if="isAuth('hotel:hotelactivityroomassign:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table size="mini" height="800px" :data="dataList" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="hotelName" header-align="center" align="center" label="酒店名称">
      </el-table-column>
      <el-table-column prop="roomName" header-align="center" align="center" label="房型名称">
      </el-table-column>
      <el-table-column prop="roomNumber" header-align="center" align="center" label="房号">
      </el-table-column>
      <el-table-column prop="contact" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="入住状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + (scope.row.status)">{{
            roomAssignStatus[scope.row.status].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="roomType" header-align="center" align="center" width="75" label="房间类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + (scope.row.roomType)">{{
            roomType[scope.row.roomType].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column width="85px" prop="inDate" header-align="center" align="center" label="入住日期">
      </el-table-column>
      <el-table-column width="85px" prop="outDate" header-align="center" align="center" label="退房日期">
      </el-table-column>
      <el-table-column prop="dayNumber" header-align="center" align="center" label="总天数">
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" width="60" label="已分/总">
        <div slot-scope="scope">
          {{ scope.row.assignNumber }}/{{ scope.row.number }}
        </div>
      </el-table-column>
      <el-table-column prop="orderSn" show-overflow-tooltip header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="orderStatus" header-align="center" align="center" width="75" label="订单状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color-mini tag-color-' + (scope.row.orderStatus)">{{
            orderStatus[scope.row.orderStatus].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="tag" header-align="center" align="center" width="75" label="备注">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.status == 1" size="small"
            @click="printHandle(scope.row)">打印凭条</el-button>
          <el-button type="text" v-if="scope.row.status == 1" size="small"
            @click="changeHandle(scope.row)">更换房型</el-button>
          <el-button type="text" style="color:red" v-if="scope.row.status == 1" size="small"
            @click="cancel(scope.row.id)">取消入住</el-button>
          <!-- <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <hotelactivityroomassignassign v-if="hotelactivityroomassignassignVisible" ref="hotelactivityroomassignassign"
      @refreshDataList="getDataList" @print="print"></hotelactivityroomassignassign>
    <hotelactivityroomassignchange v-if="hotelactivityroomassignchangeVisible" ref="hotelactivityroomassignchange"
      @refreshDataList="getDataList"></hotelactivityroomassignchange>
    <!-- 打印 -->
    <div style="position: absolute;bottom: 100px;left: 500px;width: 500px;display: none;">
      <div id="printBill">
        <div style="font-size: 13px; color: #000000">
          <div style="width: 100%;text-align: center;margin-top: 30px;font-size: 20px;">
            {{ activityInfo.name }}
          </div>
          <div style="width: 100%;text-align: center;margin-top: 10px;font-size: 20px;">
            房间入住凭据
          </div>
          <div style="margin-top: 20px;  width: 100%">
            <div>酒店名称：{{ indexRoom.hotelName }}</div>
            <div>房型：{{ indexRoom.roomName }}</div>
            <div>姓名：{{ indexRoom.contact }}</div>
            <div>联系方式：{{ indexRoom.mobile }}</div>
            <div>入住时间：{{ indexRoom.inDate }}</div>
            <div>退房时间：{{ indexRoom.outDate }}</div>
            <div>入住天数：{{ indexRoom.dayNumber }}</div>
            <div>房间号：<span style="font-weight: bold;font-size: 15px;">{{ indexRoom.roomNumber }}</span></div>
            <div>备注：{{ indexRoom.tag }}</div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import print from "print-js";
import {
  sources, orderStatus
} from '@/data/common'
import { roomType, roomAssignStatus, roomTypeFjsd } from "@/data/room.js";
import AddOrUpdate from './hotelactivityroomassign-add-or-update'
import hotelactivityroomassignassign from './hotelactivityroomassign-assign'
import hotelactivityroomassignchange from './hotelactivityroomassign-change'
export default {
  data() {
    return {
      appid: '',
      hotels: [],
      rooms: [],
      sources,
      roomAssignStatus,
      roomType,
      roomTypeFjsd,
      orderStatus,
      dataForm: {
        activityId: '',
        contact: '',
        mobile: '',
        hotelActivityRoomId: "",
        hotelActivityId: "",
        number: '',
        status: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      hotelactivityroomassignassignVisible: false,
      hotelactivityroomassignchangeVisible: false,
      indexRoom: {},
      activityInfo: {},
    }
  },
  components: {
    AddOrUpdate,
    hotelactivityroomassignassign,
    hotelactivityroomassignchange,
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.dataForm.hotelActivityId = this.$route.query.hotelActivityId || '';
    this.dataForm.hotelActivityRoomId = this.$route.query.hotelActivityRoomId || '';
    if (this.dataForm.hotelActivityId) {
      this.findRoom(this.dataForm.hotelActivityId);
    }
    this.getActivity()
    this.findHotel()
    this.getDataList()
  },
  methods: {
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
        }
      });
    },
    findHotel() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivity/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.hotels = data.result
        }
      })
    },
    hotelChange(v) {
      this.dataForm.hotelActivityRoomId = '';
      this.findRoom(v);
    },
    findRoom(v) {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroom/findByHotelActivityId/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.rooms = data.result
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hotel/hotelactivityroomassign/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'number': this.dataForm.number,
          'contact': this.dataForm.contact,
          'mobile': this.dataForm.mobile,
          'activityId': this.dataForm.activityId,
          'hotelActivityId': this.dataForm.hotelActivityId,
          'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
          'status': this.dataForm.status,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/hotel/hotelactivityroomassign/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "activityId=" + this.dataForm.activityId,
        "hotelActivityId=" + this.dataForm.hotelActivityId,
        "hotelActivityRoomId=" + this.dataForm.hotelActivityRoomId,
        "contact=" + this.dataForm.contact,
        "mobile=" + this.dataForm.mobile,
        "number=" + this.dataForm.number,
        "status=" + this.dataForm.status,
      ].join('&'));
      window.open(url);
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    assignHandle() {
      this.hotelactivityroomassignassignVisible = true
      this.$nextTick(() => {
        this.$refs.hotelactivityroomassignassign.init(this.dataForm.activityId, this.dataForm.hotelActivityId, this.dataForm.hotelActivityRoomId)
      })
    },
    changeHandle(v) {
      this.hotelactivityroomassignchangeVisible = true
      this.$nextTick(() => {
        this.$refs.hotelactivityroomassignchange.init(v.id)
      })
    },
    turnNumber() {
      this.$router.push({
        name: 'hotelactivityroomnumber',
        query: {
          activityId: this.dataForm.activityId,
          hotelActivityId: this.dataForm.hotelActivityId,
          hotelActivityRoomId: this.dataForm.hotelActivityRoomId,
        }
      })
    },
    turnTag() {
      this.$router.push({
        name: 'hotelassigntag',
        query: {
          activityId: this.dataForm.activityId,
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hotel/hotelactivityroomassign/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 选择酒店
    cancel(v) {
      this.$confirm(`确认取消入住?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/cancel`),
          method: 'get',
          params: this.$http.adornParams({
            'id': v,
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    printHandle(v) {
      this.indexRoom = v;
      this.$set(this.indexRoom, "sourceName", this.sources[this.indexRoom.source].value)
      setTimeout(function () {
        const style = "@page {margin:0 10mm};"; //打印时去掉眉页眉尾
        printJS({
          printable: "printBill", // 备注元素id
          type: "html",
          header: "",
          targetStyles: ["*"],
          scanStyles: false, //打印必须加上，不然页面上的css样式无效
          style,
        });
      }, 500);
    },
    print(v) {
      console.log(v)

      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/infoDetail/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.indexRoom = data.hotelActivityRoomAssign
          setTimeout(function () {
            const style = "@page {margin:0 10mm};"; //打印时去掉眉页眉尾
            printJS({
              printable: "printBill", // 备注元素id
              type: "html",
              header: "",
              targetStyles: ["*"],
              scanStyles: false, //打印必须加上，不然页面上的css样式无效
              style,
            });
          }, 500);

        }
      })
    }
  }
}
</script>
