<template>
  <div id="app">
    <router-view/>
    <van-tabbar v-model="active" v-show="isShowTabbar && isMobilePhone"  style="z-index:9999999!important;box-shadow: rgba(0, 0, 0, 0.05) 0px -6px 11px 0px;">
      <van-tabbar-item info="" to="/">
        <span>首页</span>
        <img slot="icon" slot-scope="props" :src="props.active ? icon.activity_a : icon.activity">
      </van-tabbar-item>
      <van-tabbar-item info="" to="/me/account">
        <span>记录</span>
        <img slot="icon" slot-scope="props" :src="props.active ? icon.history_a : icon.history">
      </van-tabbar-item>
      <van-tabbar-item info="" to="/me/profile">
        <span>我的</span>
        <img slot="icon" slot-scope="props" :src="props.active ? icon.mine_a : icon.mine">
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>
<script>
const APPID = process.env.VUE_APP_WX_APPID;
import { isMobilePhone } from '@/js/validate'
export default {data() {
      return {
        active: 0,
        appid: '',
        isMobilePhone: isMobilePhone(),
        icon: {
          index: require('@/assets/index.png'),
          index_a: require('@/assets/index-a.png'),
          activity: require('@/assets/activity.png'),
          activity_a: require('@/assets/activity-a.png'),
          mine: require('@/assets/mine.png'),
          mine_a: require('@/assets/mine-a.png'),
          history: require('@/assets/history.png'),
          history_a: require('@/assets/history-a.png'),
        }
      }
    },
    computed: {
      isShowTabbar() {
        // if (this.$route.name == "Home") {
        //   this.active = 0
        // } else if (this.$route.name == "historyHome") {
        //   this.active = 1
        // }
        // console.log(this.$route)
        if (this.$route.meta.isTab) {
          return true
        } else {
          return false
        }
      }
    },
    mounted(){
      this.appid = APPID;
      this.$cookie.set('appid',APPID);
      this.$fly.get(`/pyp/web/wxAccount/info`).then((res) => {
        if (res.code == 200) {
          // document.title = res.result
          this.$cookie.set('accountName',res.result);
          this.$cookie.set('slog',res.slog);
          this.$cookie.set('logo',res.logo);
          this.$cookie.set('subUrl',res.subUrl);
          this.$cookie.set('showSub',res.showSub);
        } else {
        }
      });
    }
}
</script>
<style src="./style/common.css" scope></style>
<style lang="less">
/deep/ .van-tabbar-item--active:first {
  color: #cc5154;
}
.pc-container {
  margin: 0 auto;
  width: 70%;
}
.empty-banner{
    background-color: #66c6f2;
    color: #fff;
    font-size: 20px;
    text-align: center;
    line-height: 100px;
}
.nav-title {
  padding: 5px 20px;
  display: flex;
  align-items: center;
  height: 24px;
  line-height: 24px;
  background: #f6f6f6;
  .color {
    width: 3px;height: 14px;background: #4485ff;border-radius: 3px;margin-right: 5px;
  }
}
.back {
  width: 50px;
  position: fixed;
  text-align: center;
  z-index: 99;
  bottom: 100px;
  right: 0;
  overflow: hidden;
}
</style>