package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.salesman.dao.WxUserSalesmanBindingDao;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.enums.BindingStatusEnum;
import com.cjy.pyp.modules.salesman.enums.BindingTypeEnum;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingLogService;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.wx.service.WxUserService;
import com.github.pagehelper.PageHelper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信用户业务员绑定服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Service("wxUserSalesmanBindingService")
public class WxUserSalesmanBindingServiceImpl extends ServiceImpl<WxUserSalesmanBindingDao, WxUserSalesmanBindingEntity> 
        implements WxUserSalesmanBindingService {

    private static final Logger logger = LoggerFactory.getLogger(WxUserSalesmanBindingServiceImpl.class);

    @Autowired
    private WxUserSalesmanBindingLogService bindingLogService;

    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private WxUserService wxUserService;

    @Override
    public List<WxUserSalesmanBindingEntity> queryPage(Map<String, Object> params) {
        
        int page = Integer.parseInt(params.getOrDefault("page", "1").toString());
        int limit = Integer.parseInt(params.getOrDefault("limit", "10").toString());

        PageHelper.startPage(page, limit);
        List<WxUserSalesmanBindingEntity> list = baseMapper.queryPage(params);
        return list;
    }

    @Override
    public WxUserSalesmanBindingEntity getActiveBindingByWxUser(Long wxUserId, String appid) {
        return baseMapper.getActiveBindingByWxUser(wxUserId, appid);
    }

    @Override
    public List<WxUserSalesmanBindingEntity> getBindingsBySlesman(Long salesmanId, String appid) {

        return baseMapper.getBindingsBySlesman(salesmanId, appid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxUserSalesmanBindingEntity createBinding(Long wxUserId, Long salesmanId, Integer bindingType, 
                                                    String bindingSource, String appid) {
        // 验证参数
        String validationResult = validateBindingParams(wxUserId, salesmanId, appid);
        if (validationResult != null) {
            throw new RuntimeException(validationResult);
        }

        // 检查是否已存在有效绑定
        WxUserSalesmanBindingEntity existingBinding = getActiveBindingByWxUser(wxUserId, appid);
        if (existingBinding != null) {
            if (existingBinding.getSalesmanId().equals(salesmanId)) {
                return existingBinding; // 已绑定同一个业务员，直接返回
            } else {
                // 已绑定其他业务员，需要先解绑
                throw new RuntimeException("用户已绑定其他业务员，请先解绑");
            }
        }

        // 创建新的绑定关系
        WxUserSalesmanBindingEntity binding = new WxUserSalesmanBindingEntity();
        binding.setWxUserId(wxUserId);
        binding.setSalesmanId(salesmanId);
        binding.setBindingType(bindingType);
        binding.setBindingSource(bindingSource);
        binding.setBindingTime(new Date());
        binding.setEffectiveTime(new Date());
        binding.setStatus(BindingStatusEnum.ACTIVE.getCode());
        binding.setPriority(1);
        binding.setAppid(appid);

        this.save(binding);

        // 记录绑定日志
        recordBindingLog(wxUserId, null, salesmanId, 1, "新增绑定", bindingSource, appid);

        logger.info("创建微信用户业务员绑定成功: wxUserId={}, salesmanId={}, bindingType={}", 
                   wxUserId, salesmanId, bindingType);

        return binding;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindSalesman(Long wxUserId, Long salesmanId, String reason, String appid) {
        // 查找有效绑定
        WxUserSalesmanBindingEntity binding = getActiveBindingByWxUser(wxUserId, appid);
        if (binding == null || !binding.getSalesmanId().equals(salesmanId)) {
            return false;
        }

        // 更新绑定状态为已解绑
        binding.setStatus(BindingStatusEnum.UNBOUND.getCode());
        binding.setUpdateOn(new Date());
        this.updateById(binding);

        // 记录解绑日志
        recordBindingLog(wxUserId, salesmanId, null, 3, reason, null, appid);

        logger.info("解除微信用户业务员绑定成功: wxUserId={}, salesmanId={}, reason={}", 
                   wxUserId, salesmanId, reason);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeSalesman(Long wxUserId, Long oldSalesmanId, Long newSalesmanId, String reason, String appid) {
        // 验证新业务员参数
        String validationResult = validateBindingParams(wxUserId, newSalesmanId, appid);
        if (validationResult != null) {
            throw new RuntimeException(validationResult);
        }

        // 解绑原业务员
        if (!unbindSalesman(wxUserId, oldSalesmanId, reason, appid)) {
            throw new RuntimeException("解绑原业务员失败");
        }

        // 绑定新业务员
        WxUserSalesmanBindingEntity newBinding = createBinding(wxUserId, newSalesmanId, 
                BindingTypeEnum.MANUAL_BINDING.getCode(), "更换业务员", appid);

        // 记录更换日志
        recordBindingLog(wxUserId, oldSalesmanId, newSalesmanId, 2, reason, null, appid);

        logger.info("更换微信用户业务员成功: wxUserId={}, oldSalesmanId={}, newSalesmanId={}, reason={}", 
                   wxUserId, oldSalesmanId, newSalesmanId, reason);

        return newBinding != null;
    }

    @Override
    public WxUserSalesmanBindingEntity bindByQrCode(Long wxUserId, String qrCodeContent, String appid) {
        // TODO: 解析二维码内容获取业务员ID
        // 这里需要根据实际的二维码格式来解析
        Long salesmanId = parseSalesmanIdFromQrCode(qrCodeContent);
        if (salesmanId == null) {
            throw new RuntimeException("无效的二维码");
        }

        return createBinding(wxUserId, salesmanId, BindingTypeEnum.QR_CODE_SCAN.getCode(), 
                           qrCodeContent, appid);
    }

    @Override
    public WxUserSalesmanBindingEntity bindByInviteCode(Long wxUserId, String inviteCode, String appid) {
        // TODO: 根据邀请码查找业务员ID
        // 这里需要根据实际的邀请码格式来查找
        Long salesmanId = parseSalesmanIdFromInviteCode(inviteCode);
        if (salesmanId == null) {
            throw new RuntimeException("无效的邀请码");
        }

        return createBinding(wxUserId, salesmanId, BindingTypeEnum.INVITE_LINK.getCode(), 
                           inviteCode, appid);
    }

    @Override
    public Map<Long, WxUserSalesmanBindingEntity> getBatchActiveBindingsByWxUsers(List<Long> wxUserIds, String appid) {
        if (wxUserIds == null || wxUserIds.isEmpty()) {
            return new HashMap<>();
        }

        List<WxUserSalesmanBindingEntity> bindings = baseMapper.getBatchActiveBindingsByWxUsers(wxUserIds, appid);
        return bindings.stream().collect(Collectors.toMap(
                WxUserSalesmanBindingEntity::getWxUserId, 
                binding -> binding,
                (existing, replacement) -> existing // 如果有重复，保留第一个
        ));
    }

    @Override
    public Long getSalesmanIdByWxUser(Long wxUserId, String appid) {
        WxUserSalesmanBindingEntity binding = getActiveBindingByWxUser(wxUserId, appid);
        return binding != null ? binding.getSalesmanId() : null;
    }

    @Override
    public boolean existsBinding(Long wxUserId, Long salesmanId, String appid) {
        return baseMapper.existsBinding(wxUserId, salesmanId, appid);
    }

    @Override
    public Integer countBindingsBySlesman(Long salesmanId, String appid) {
        return baseMapper.countBindingsBySlesman(salesmanId, appid);
    }

    @Override
    public List<WxUserSalesmanBindingEntity> getExpiringBindings(Integer days, String appid) {
        return baseMapper.getExpiringBindings(days, appid);
    }

    @Override
    public Integer expireOverdueBindings(String appid) {
        return baseMapper.expireOverdueBindings(appid);
    }

    @Override
    public String validateBindingParams(Long wxUserId, Long salesmanId, String appid) {
        if (wxUserId == null) {
            return "微信用户ID不能为空";
        }
        if (salesmanId == null) {
            return "业务员ID不能为空";
        }
        if (!StringUtils.hasText(appid)) {
            return "应用ID不能为空";
        }

        // 验证微信用户是否存在
        WxUser wxUser = wxUserService.getById(wxUserId);
        if (wxUser == null) {
            return "微信用户不存在";
        }

        // 验证业务员是否存在且有效
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null) {
            return "业务员不存在";
        }
        if (salesman.getStatus() != 1) {
            return "业务员已禁用";
        }

        return null; // 验证通过
    }

    /**
     * 记录绑定变更日志
     */
    private void recordBindingLog(Long wxUserId, Long oldSalesmanId, Long newSalesmanId, 
                                 Integer operationType, String reason, String bindingSource, String appid) {
        try {
            WxUserSalesmanBindingLogEntity log = new WxUserSalesmanBindingLogEntity();
            log.setWxUserId(wxUserId);
            log.setOldSalesmanId(oldSalesmanId);
            log.setNewSalesmanId(newSalesmanId);
            log.setOperationType(operationType);
            log.setOperationReason(reason);
            log.setBindingSource(bindingSource);
            log.setOperatorType(1); // 客户自己操作
            log.setAppid(appid);
            
            bindingLogService.save(log);
        } catch (Exception e) {
            logger.error("记录绑定变更日志失败", e);
        }
    }

    /**
     * 从二维码内容解析业务员ID
     */
    private Long parseSalesmanIdFromQrCode(String qrCodeContent) {
        // TODO: 根据实际的二维码格式实现解析逻辑
        // 这里只是示例，需要根据实际情况修改
        try {
            if (qrCodeContent.startsWith("salesman:")) {
                return Long.parseLong(qrCodeContent.substring(9));
            }
        } catch (NumberFormatException e) {
            logger.warn("解析二维码业务员ID失败: {}", qrCodeContent, e);
        }
        return null;
    }

    /**
     * 从邀请码解析业务员ID
     */
    private Long parseSalesmanIdFromInviteCode(String inviteCode) {
        // TODO: 根据实际的邀请码格式实现解析逻辑
        // 这里只是示例，需要根据实际情况修改
        try {
            if (inviteCode.startsWith("SM") && inviteCode.length() == 8) {
                return Long.parseLong(inviteCode.substring(2));
            }
        } catch (NumberFormatException e) {
            logger.warn("解析邀请码业务员ID失败: {}", inviteCode, e);
        }
        return null;
    }

    @Override
    public Map<String, Object> getBindingStats(String appid) {
        Map<String, Object> stats = new HashMap<>();

        // 总绑定数
        QueryWrapper<WxUserSalesmanBindingEntity> totalWrapper = new QueryWrapper<>();
        totalWrapper.eq("appid", appid);
        int totalBindings = this.count(totalWrapper);
        stats.put("totalBindings", totalBindings);

        // 有效绑定数
        QueryWrapper<WxUserSalesmanBindingEntity> activeWrapper = new QueryWrapper<>();
        activeWrapper.eq("appid", appid).eq("status", BindingStatusEnum.ACTIVE.getCode());
        int activeBindings = this.count(activeWrapper);
        stats.put("activeBindings", activeBindings);

        // 今日新增绑定数
        QueryWrapper<WxUserSalesmanBindingEntity> todayWrapper = new QueryWrapper<>();
        todayWrapper.eq("appid", appid)
                   .ge("binding_time", new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        int todayBindings = this.count(todayWrapper);
        stats.put("todayBindings", todayBindings);

        // 即将过期绑定数（7天内）
        List<WxUserSalesmanBindingEntity> expiringBindings = getExpiringBindings(7, appid);
        stats.put("expiringBindings", expiringBindings.size());

        return stats;
    }

    @Override
    public List<WxUserSalesmanBindingLogEntity> getBindingHistory(Map<String, Object> params) {
        // 这里需要查询绑定变更日志表
        return bindingLogService.queryPage(params);
    }

    @Override
    public Map<String, Object> getCustomerStats(Long salesmanId, String appid) {
        Map<String, Object> stats = new HashMap<>();

        // 总客户数
        int totalCustomers = this.count(
            new QueryWrapper<WxUserSalesmanBindingEntity>()
                .eq("salesman_id", salesmanId)
                .eq("appid", appid)
        );
        stats.put("totalCustomers", totalCustomers);

        // 有效绑定数
        int activeCustomers = this.count(
            new QueryWrapper<WxUserSalesmanBindingEntity>()
                .eq("salesman_id", salesmanId)
                .eq("appid", appid)
                .eq("status", 1)
        );
        stats.put("activeCustomers", activeCustomers);

        // 今日新增绑定数
        String today = java.time.LocalDate.now().toString();
        int todayBindings = this.count(
            new QueryWrapper<WxUserSalesmanBindingEntity>()
                .eq("salesman_id", salesmanId)
                .eq("appid", appid)
                .ge("binding_time", today + " 00:00:00")
                .le("binding_time", today + " 23:59:59")
        );
        stats.put("todayBindings", todayBindings);

        // 订单总额（需要关联订单表查询）
        // 这里先设置为0，后续可以通过关联查询获取
        stats.put("totalOrderAmount", 0);

        return stats;
    }

    @Override
    public Map<String, Object> verifyInviteCode(String inviteCode, String appid) {
        if (!StringUtils.hasText(inviteCode)) {
            throw new RuntimeException("邀请码不能为空");
        }

        // 解析邀请码获取业务员ID
        Long salesmanId = parseSalesmanIdFromInviteCode(inviteCode);
        if (salesmanId == null) {
            throw new RuntimeException("邀请码格式错误");
        }

        // 获取业务员信息
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null) {
            throw new RuntimeException("业务员不存在");
        }

        if (!appid.equals(salesman.getAppid())) {
            throw new RuntimeException("邀请码无效");
        }

        // 检查业务员状态
        if (salesman.getStatus() != 1) {
            throw new RuntimeException("业务员已停用");
        }

        // 返回业务员信息
        Map<String, Object> result = new HashMap<>();
        result.put("id", salesman.getId());
        result.put("name", salesman.getName());
        result.put("mobile", salesman.getMobile());
        result.put("company", ""); // 如果SalesmanEntity没有company字段，设为空
        result.put("avatar", ""); // 如果SalesmanEntity没有avatar字段，设为空

        return result;
    }

    @Override
    public Map<String, Object> verifySalesmanId(Long salesmanId, String appid) {
        if (salesmanId == null) {
            throw new RuntimeException("业务员ID不能为空");
        }

        // 获取业务员信息
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null) {
            throw new RuntimeException("业务员不存在");
        }

        // 检查业务员状态
        if (salesman.getStatus() != 1) {
            throw new RuntimeException("业务员已停用");
        }

        // 返回业务员信息
        Map<String, Object> result = new HashMap<>();
        result.put("id", salesman.getId());
        result.put("name", salesman.getName());
        result.put("mobile", salesman.getMobile());
        result.put("company", ""); // 如果SalesmanEntity没有company字段，设为空
        result.put("avatar", ""); // 如果SalesmanEntity没有avatar字段，设为空

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxUserSalesmanBindingEntity bindCustomerBySalesman(Long wxUserId, String inviteCode,
                                                             String bindingMethod, String appid) {
        if (wxUserId == null) {
            throw new RuntimeException("用户ID不能为空");
        }

        if (!StringUtils.hasText(inviteCode)) {
            throw new RuntimeException("邀请码不能为空");
        }

        // 验证邀请码或业务员ID
        Long salesmanId;
        if (inviteCode.matches("\\d+")) {
            // 如果是纯数字，当作业务员ID处理
            salesmanId = Long.valueOf(inviteCode);
            verifySalesmanId(salesmanId, appid); // 验证业务员ID有效性
        } else {
            // 否则当作邀请码处理
            Map<String, Object> salesmanInfo = verifyInviteCode(inviteCode, appid);
            salesmanId = (Long) salesmanInfo.get("id");
        }

        // 检查是否已存在有效绑定
        WxUserSalesmanBindingEntity existingBinding = getActiveBindingByWxUser(wxUserId, appid);
        if (existingBinding != null) {
            if (existingBinding.getSalesmanId().equals(salesmanId)) {
                return existingBinding; // 已绑定同一个业务员，直接返回
            } else {
                // 已绑定其他业务员，需要先解绑
                throw new RuntimeException("您已绑定其他业务员，请先解绑");
            }
        }

        // 确定绑定类型
        Integer bindingType = getBindingTypeByMethod(bindingMethod);

        // 创建绑定关系
        return createBinding(wxUserId, salesmanId, bindingType, inviteCode, appid);
    }

    /**
     * 根据绑定方式获取绑定类型
     */
    private Integer getBindingTypeByMethod(String bindingMethod) {
        switch (bindingMethod) {
            case "code":
                return BindingTypeEnum.MANUAL_BINDING.getCode(); // 邀请码绑定归类为手动绑定
            case "qr":
                return BindingTypeEnum.QR_CODE_SCAN.getCode();
            case "link":
                return BindingTypeEnum.INVITE_LINK.getCode();
            default:
                return BindingTypeEnum.MANUAL_BINDING.getCode();
        }
    }
}
