package com.cjy.pyp.modules.salesman.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.channel.utils.ChannelPermissionUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务员管理控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("salesman/salesman")
@Api(tags = {"业务员管理-管理后台"})
public class SalesmanController extends AbstractController {
    
    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private ChannelPermissionUtils channelPermissionUtils;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("salesman:salesman:list")
    @ApiOperation(value = "业务员列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        PageUtils page = salesmanService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 根据应用ID查询业务员列表
     */
    @RequestMapping("/findByAppid")
    @ApiOperation(value = "根据应用ID查询业务员列表", notes = "")
    public R findByAppid(@CookieValue String appid) {
        List<SalesmanEntity> list = salesmanService.findByAppid(appid);
        return R.ok().put("result", list);
    }

    /**
     * 列表（包含统计信息）
     */
    @RequestMapping("/listWithStats")
    @RequiresPermissions("salesman:salesman:list")
    @ApiOperation(value = "业务员列表（包含统计信息）", notes = "")
    public R listWithStats(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);

        // 渠道管理员只能查看自己渠道的业务员
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }

        List<SalesmanEntity> page = salesmanService.queryPageWithStats(params);
        return R.okList(page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("salesman:salesman:info")
    @ApiOperation(value = "业务员信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        // 检查权限
        if (!channelPermissionUtils.canAccessSalesman(getUser(), id)) {
            return R.error("无权限访问该业务员信息");
        }

        SalesmanEntity salesman = salesmanService.getById(id);
        return R.ok().put("salesman", salesman);
    }

    /**
     * 检查业务员编号是否存在
     */
    @RequestMapping("/checkCode")
    @RequiresPermissions("salesman:salesman:list")
    @ApiOperation(value = "检查业务员编号", notes = "")
    public R checkCode(@RequestParam String code,
                       @RequestParam(required = false) Long excludeId,
                       @CookieValue String appid) {
        boolean exists = salesmanService.existsByCode(code, appid, excludeId);
        return R.ok().put("exists", exists);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("salesman:salesman:save")
    @SysLog("保存业务员")
    @ApiOperation(value = "保存业务员", notes = "")
    public R save(@RequestBody SalesmanEntity salesman, @CookieValue String appid) {
        ValidatorUtils.validateEntity(salesman);
        
        // 设置应用ID
        salesman.setAppid(appid);

        // 渠道管理员只能在自己的渠道下创建业务员
        if (channelPermissionUtils.isChannelAdmin(getUserId())) {
            if (getUser().getChannelId() == null) {
                return R.error("渠道管理员必须关联渠道");
            }

            // 如果没有指定渠道，默认为当前管理员的渠道
            if (salesman.getChannelId() == null) {
                salesman.setChannelId(getUser().getChannelId());
            } else {
                // 检查是否有权限在指定渠道创建业务员
                if (!channelPermissionUtils.canManageChannel(getUser(), salesman.getChannelId())) {
                    return R.error("无权限在该渠道创建业务员");
                }
            }
        }

        // 检查业务员编号是否重复
        if (salesmanService.existsByCode(salesman.getCode(), appid, null)) {
            return R.error("业务员编号已存在");
        }
        
        // 检查手机号是否重复
        if (salesmanService.existsByMobile(salesman.getMobile(), appid, null)) {
            return R.error("手机号已存在");
        }

        // 计算层级
        salesman.setLevel(salesmanService.calculateLevel(salesman.getParentId()));

        salesmanService.save(salesman);

        // 如果有上级，更新上级的层级信息
        if (salesman.getParentId() != null) {
            salesmanService.updateLevel(salesman.getParentId());
        }

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("salesman:salesman:update")
    @SysLog("修改业务员")
    @ApiOperation(value = "修改业务员", notes = "")
    public R update(@RequestBody SalesmanEntity salesman, @CookieValue String appid) {
        ValidatorUtils.validateEntity(salesman);

        // 检查权限
        if (!channelPermissionUtils.canAccessSalesman(getUser(), salesman.getId())) {
            return R.error("无权限修改该业务员信息");
        }

        // 设置应用ID
        salesman.setAppid(appid);

        // 渠道管理员不能将业务员转移到其他渠道
        if (channelPermissionUtils.isChannelAdmin(getUserId())) {
            SalesmanEntity existingSalesman = salesmanService.getById(salesman.getId());
            if (existingSalesman != null && !existingSalesman.getChannelId().equals(salesman.getChannelId())) {
                if (!channelPermissionUtils.canManageChannel(getUser(), salesman.getChannelId())) {
                    return R.error("无权限将业务员转移到该渠道");
                }
            }
        }

        // 检查业务员编号是否重复
        if (salesmanService.existsByCode(salesman.getCode(), appid, salesman.getId())) {
            return R.error("业务员编号已存在");
        }
        
        // 检查手机号是否重复
        if (salesmanService.existsByMobile(salesman.getMobile(), appid, salesman.getId())) {
            return R.error("手机号已存在");
        }

        // 获取原来的上级信息
        SalesmanEntity oldSalesman = salesmanService.getById(salesman.getId());
        Long oldParentId = oldSalesman != null ? oldSalesman.getParentId() : null;

        // 计算新的层级
        salesman.setLevel(salesmanService.calculateLevel(salesman.getParentId()));

        salesmanService.updateById(salesman);

        // 更新相关层级信息
        if (salesman.getParentId() != null) {
            salesmanService.updateLevel(salesman.getParentId());
        }
        if (oldParentId != null && !oldParentId.equals(salesman.getParentId())) {
            salesmanService.updateLevel(oldParentId);
        }
        // 更新当前业务员及其下级的层级
        salesmanService.updateLevel(salesman.getId());

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("salesman:salesman:delete")
    @SysLog("删除业务员")
    @ApiOperation(value = "删除业务员", notes = "")
    public R delete(@RequestBody Long[] ids) {
        // 检查权限
        for (Long id : ids) {
            if (!channelPermissionUtils.canAccessSalesman(getUser(), id)) {
                return R.error("无权限删除业务员ID: " + id);
            }
        }

        salesmanService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 搜索业务员
     */
    @RequestMapping("/search")
    @RequiresPermissions("salesman:salesman:list")
    @ApiOperation(value = "搜索业务员", notes = "根据关键词搜索业务员")
    public R search(@RequestParam String keyword,
                   @RequestParam(defaultValue = "20") Integer limit,
                   @CookieValue String appid) {
        List<SalesmanEntity> list = salesmanService.searchSalesmen(keyword, limit, appid);
        return R.ok().put("list", list);
    }

    /**
     * 业务员订单总体统计
     */
    @RequestMapping("/orderStats")
    @RequiresPermissions("salesman:salesman:list")
    @ApiOperation(value = "业务员订单总体统计", notes = "获取所有业务员订单的总体统计数据")
    public R getOrderStats(@CookieValue String appid) {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("appid", appid);

        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }

        // 调用统计服务
        Map<String, Object> stats = salesmanService.getOrderStats(params);
        return R.ok().put("stats", stats);
    }


}
