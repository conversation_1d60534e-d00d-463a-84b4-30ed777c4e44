-- 创建退款记录表
CREATE TABLE `activity_refund_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recharge_record_id` bigint(20) NOT NULL COMMENT '充值记录ID',
  `original_order_sn` varchar(64) NOT NULL COMMENT '原订单号',
  `refund_order_sn` varchar(64) NOT NULL COMMENT '退款单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `original_amount` decimal(10,2) NOT NULL COMMENT '原支付金额',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `pay_type` int(11) DEFAULT NULL COMMENT '支付方式：1-微信支付，2-支付宝',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '退款状态：0-申请中，1-审核通过，2-退款成功，3-退款失败，4-审核拒绝',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `refund_transaction_id` varchar(128) DEFAULT NULL COMMENT '第三方退款交易号',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `refund_time` datetime DEFAULT NULL COMMENT '退款完成时间',
  `audit_by` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_order_sn` (`refund_order_sn`),
  KEY `idx_recharge_record_id` (`recharge_record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
