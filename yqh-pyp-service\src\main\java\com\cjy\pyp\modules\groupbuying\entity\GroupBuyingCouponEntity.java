package com.cjy.pyp.modules.groupbuying.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 团购券实体类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-19
 */
@Data
@TableName("group_buying_coupon")
public class GroupBuyingCouponEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 平台类型：douyin-抖音团购，meituan-美团团购，dianping-大众点评团购
     */
    private String platformType;

    /**
     * 团购券名称
     */
    private String couponName;

    /**
     * 团购券描述
     */
    private String couponDescription;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 团购价
     */
    private BigDecimal groupPrice;

    /**
     * 优惠信息
     */
    private String discountInfo;

    /**
     * 团购券链接
     */
    private String couponUrl;

    /**
     * 团购券ID（平台方提供）
     */
    private String couponId;

    /**
     * 二维码链接
     */
    private String qrCodeUrl;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 总数量（NULL表示不限量）
     */
    private Integer totalCount;

    /**
     * 已售数量
     */
    private Integer soldCount;

    /**
     * 状态：0-下架，1-上架
     */
    private Integer status;

    /**
     * 排序（数字越大越靠前）
     */
    private Integer sortOrder;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;
}
