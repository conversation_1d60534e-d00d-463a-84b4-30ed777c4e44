package com.cjy.pyp.modules.activity.controller;

import java.util.*;

import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.activity.service.ActivityTextPreGenerateService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;

import javax.annotation.Resource;

/**
 * AI文案生成控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
@RestController
@RequestMapping("activity/activitytext")
public class ActivityTextController extends AbstractController {
    @Autowired
    private ActivityTextService activityTextService;
    @Autowired
    private ActivityTextPreGenerateService activityTextPreGenerateService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 生成AI文案
     * @throws Exception
     */
    @RequestMapping("/generate")
    // @RequiresPermissions("activity:activitytext:save")
    @Transactional(rollbackFor = Exception.class)
    public R generateText(@RequestBody ActivityTextEntity activityText) throws Exception {
        return activityTextService.generateText(activityText, getUserId());
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:activitytext:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = activityTextService.queryPage(params);

        return R.ok().put("page", page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:activitytext:info")
    public R info(@PathVariable("id") Long id) {
        ActivityTextEntity activityText = activityTextService.getById(id);

        return R.ok().put("activityText", activityText);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:activitytext:save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody ActivityTextEntity activityText) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()),
                activityText.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }
        activityTextService.save(activityText);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:activitytext:update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody ActivityTextEntity activityText) {
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()),
                activityText.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }
        activityTextService.updateById(activityText);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:activitytext:delete")
    public R delete(@RequestBody Long[] ids) {
        activityTextService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
     * 批量设置AI标签
     */
    @RequestMapping("/batchSetAiTag")
    @RequiresPermissions("activity:activitytext:update")
    @Transactional(rollbackFor = Exception.class)
    public R batchSetAiTag(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> textIds = (List<String>) params.get("textIds");
        String aiTag = (String) params.get("aiTag");

        if (textIds == null || textIds.isEmpty()) {
            return R.error("请选择要设置标签的文案");
        }

        // 批量更新文案的AI标签
        for (String textId : textIds) {
            ActivityTextEntity text = new ActivityTextEntity();
            text.setId(Long.valueOf(textId));
            text.setAiTag(aiTag);
            text.setUpdateBy(getUserId());
            activityTextService.updateById(text);
        }

        return R.ok("批量设置AI标签成功");
    }

}
