-- AI模型配置表
CREATE TABLE `ai_model_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_code` varchar(50) NOT NULL COMMENT '模型编码（如：deepseek-chat, moonshot-v1-8k等）',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称（如：DeepSeek Chat, Kimi等）',
  `provider` varchar(50) NOT NULL COMMENT '服务提供商（如：deepseek, moonshot等）',
  `api_url` varchar(500) NOT NULL COMMENT 'API接口地址',
  `api_key` varchar(500) DEFAULT NULL COMMENT 'API密钥',
  `max_tokens` int(11) DEFAULT 4000 COMMENT '最大token数',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '创造性参数（0.0-2.0）',
  `timeout` int(11) DEFAULT 60000 COMMENT 'API调用超时时间（毫秒）',
  `max_retries` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `retry_delay` int(11) DEFAULT 2000 COMMENT '重试延迟（毫秒）',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0：禁用，1：启用）',
  `is_default` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否默认模型（0：否，1：是）',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `config_json` text COMMENT '扩展配置（JSON格式）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_code` (`model_code`),
  KEY `idx_provider` (`provider`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';

-- 插入默认配置数据
INSERT INTO `ai_model_config` (`model_code`, `model_name`, `provider`, `api_url`, `api_key`, `max_tokens`, `temperature`, `timeout`, `max_retries`, `retry_delay`, `status`, `is_default`, `sort_order`, `remark`) VALUES
('deepseek-chat', 'DeepSeek Chat', 'deepseek', 'https://api.deepseek.com', '***********************************', 4000, 0.70, 60000, 3, 2000, 1, 1, 1, 'DeepSeek对话模型'),
('deepseek-coder', 'DeepSeek Coder', 'deepseek', 'https://api.deepseek.com', '***********************************', 4000, 0.70, 60000, 3, 2000, 1, 0, 2, 'DeepSeek代码模型'),
('moonshot-v1-8k', 'Kimi 8K', 'moonshot', 'https://api.moonshot.cn', NULL, 8000, 0.70, 60000, 3, 2000, 0, 0, 3, 'Kimi 8K上下文模型'),
('moonshot-v1-32k', 'Kimi 32K', 'moonshot', 'https://api.moonshot.cn', NULL, 32000, 0.70, 60000, 3, 2000, 0, 0, 4, 'Kimi 32K上下文模型'),
('moonshot-v1-128k', 'Kimi 128K', 'moonshot', 'https://api.moonshot.cn', NULL, 128000, 0.70, 60000, 3, 2000, 0, 0, 5, 'Kimi 128K上下文模型');

-- 添加AI模型配置菜单
INSERT INTO `sys_menu` (`parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES
(1, 'AI模型配置', 'modules/activity/aimodelconfig', NULL, 1, 'fa fa-cogs', 7);

-- 获取刚插入的菜单ID
SET @parentId = LAST_INSERT_ID();

-- 添加按钮权限
INSERT INTO `sys_menu` (`parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`) VALUES
(@parentId, '查看', NULL, 'activity:aimodelconfig:list,activity:aimodelconfig:info', 2, NULL, 1),
(@parentId, '新增', NULL, 'activity:aimodelconfig:save', 2, NULL, 2),
(@parentId, '修改', NULL, 'activity:aimodelconfig:update', 2, NULL, 3),
(@parentId, '删除', NULL, 'activity:aimodelconfig:delete', 2, NULL, 4);
