/**
 * 邮箱
 * @param {*} s
 */
export function isEmail(s) {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile(s) {
  return /^1[0-9]{10}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone(s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 1位小数校验
 * @param s
 * @returns {*|boolean}
 */
 export function isDecimalByOne (s) {
  return /^([\+ \-]?(([1-9]\d*)|(0)))([.]\d{0,1})?$/.test(s)
}

/**
 * 2位小数校验
 * @param s
 * @returns {*|boolean}
 */
export function isDecimalByTwo (s) {
  return /^([\+ \-]?(([1-9]\d*)|(0)))([.]\d{0,2})?$/.test(s)
}
/**
 * 正整数校验
 * @param {*} s
 */
export function isIntegerBigZero (s) {
  return /^[1-9]\d*$/.test(s)
}
/**
 * 大于等于0整数
 * @param {*} s
 */
export function isInteger (s) {
  return /^[0-9]*$/.test(s)
}