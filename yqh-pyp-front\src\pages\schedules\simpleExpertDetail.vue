<template>
  <div>
    <van-card style="background: white"
      :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
      <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary" plain>{{
          guestInfo.unit }}</van-tag>
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning" plain>{{
          guestInfo.duties }}</van-tag>
      </div>
    </van-card>
    <van-tabs v-model="active">
      <van-tab title="负责议程">
        <div v-if="topic && topic.length > 0" style="height: 50px; line-height: 50px; margin-left: 20px">
          负责主题主持任务
        </div>
        <!-- 主题主持工作 -->
        <van-card v-for="item in topic" :key="item.id" style="background: white; margin-top: 5px" :thumb="item.imageUrl
            ? item.imageUrl
            : activityInfo.mobileBanner
              ? activityInfo.mobileBanner.split(',')[0]
              : 'van-icon'
          ">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
        <!-- 议程讲者工作 -->
        <div v-if="schedule && schedule.length > 0" style="height: 50px; line-height: 50px; margin-left: 20px">
          负责讲课任务
        </div>
        <van-card v-for="item in schedule" :key="item.id" style="background: white; margin-top: 5px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
        <!-- 议程讲者主持工作 -->
        <div v-if="scheduleSpeaker && scheduleSpeaker.length > 0"
          style="height: 50px; line-height: 50px; margin-left: 20px">
          负责主持任务
        </div>
        <van-card v-for="item in scheduleSpeaker" :key="item.id" style="background: white; margin-top: 5px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
        <!-- 议程讲者讨论工作 -->
        <div v-if="scheduleDiscuss && scheduleDiscuss.length > 0"
          style="height: 50px; line-height: 50px; margin-left: 20px">
          负责讲课讨论任务
        </div>
        <van-card v-for="item in scheduleDiscuss" :key="item.id" style="background: white; margin-top: 5px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-tab>
      <van-tab title="专家介绍">
        <div class="content" style="
            white-space: pre-wrap;
            word-wrap: break-word;
            table-layout: fixed;
            word-break: break-all;
          " v-if="guestInfo.content" v-html="guestInfo.content"></div>
        <div v-else>暂无介绍</div>
      </van-tab>
    </van-tabs>
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
  </div>
</template>

<script>
import date from "@/js/date.js";
export default {
  data() {
    return {
      openid: undefined,
      active: 0,
      activeName: ["1", "2", "3"],
      activityId: undefined,
      id: undefined,
      guestInfo: {},
      schedule: [],
      scheduleSpeaker: [],
      scheduleDiscuss: [],
      topic: [],
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.id = this.$route.query.detailId;
    this.openid = this.$cookie.get("openid");
    this.getActivityInfo();
    this.getTopicAndSchedule();
  },
  methods: {
    cmsTurnBack() {
      this.$router.replace({
        name: "schedulesSimpleIndex",
        query: { id: this.activityInfo.id },
      });
      // this.$router.go(-1)
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            this.getActivityList();
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getTopicAndSchedule() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getTopicAndSchedule/${this.id}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.topic = res.result.topic;
            this.schedule = res.result.schedule;
            this.scheduleSpeaker = res.result.scheduleSpeaker;
            this.scheduleDiscuss = res.result.scheduleDiscuss;
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityList() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
        .then((res) => {
          if (res.code == 200) {
            this.guestInfo = res.result;
            document.title = "嘉宾介绍-" + this.guestInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-嘉宾详情-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-嘉宾详情-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.guestInfo = {};
          }
        });
    },
  },
};
</script>
<style lang="less" scoped>
.transparent {
  /deep/.van-collapse-item__content {
    background: transparent;
  }
}

.content {
  /deep/ img {
    max-width: 100%;
    height: auto;
  }
}
</style>