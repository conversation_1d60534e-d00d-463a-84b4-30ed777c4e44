package com.cjy.pyp.modules.activity.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI模型配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04
 */
@Data
@TableName("ai_model_config")
@Accessors(chain = true)
public class AiModelConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 模型编码（如：deepseek-chat, moonshot-v1-8k等）
     */
    private String modelCode;
    
    /**
     * 模型名称（如：DeepSeek Chat, Kimi等）
     */
    private String modelName;
    
    /**
     * 服务提供商（如：deepseek, moonshot等）
     */
    private String provider;
    
    /**
     * API接口地址
     */
    private String apiUrl;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 最大token数
     */
    private Integer maxTokens;
    
    /**
     * 创造性参数（0.0-2.0）
     */
    private BigDecimal temperature;
    
    /**
     * API调用超时时间（毫秒）
     */
    private Integer timeout;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries;
    
    /**
     * 重试延迟（毫秒）
     */
    private Integer retryDelay;
    
    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;
    
    /**
     * 是否默认模型（0：否，1：是）
     */
    private Integer isDefault;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 扩展配置（JSON格式）
     */
    private String configJson;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    @TableField(exist = false)
    private String repeatToken;
}
