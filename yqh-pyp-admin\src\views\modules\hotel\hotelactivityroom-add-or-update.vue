<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="房型名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="房型名称"></el-input>
      </el-form-item>
      <el-form-item label="库存数量" prop="stockNumber" v-if="!dataForm.id">
        <el-input v-model="dataForm.stockNumber" placeholder="库存数量">
              <template slot="append">
                间
              </template></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="销售状态" prop="status">
            <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
              <el-option v-for="item in saleStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="床位销售状态" prop="bedStatus">
            <el-select :disabled="dataForm.bedNumber < 2" v-model="dataForm.bedStatus" placeholder="床位销售状态" filterable>
              <el-option v-for="item in saleStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
            <el-form-item label="单价" prop="price">
              <el-input v-model="dataForm.price" placeholder="单价" @change="handleChange">
                <template slot="append">RMB/间
                </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="床位价格" prop="bedPrice">
            <el-input v-model="dataForm.bedPrice" placeholder="床位价格">
              <template slot="append">
                RMB/床位
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="床位数量" prop="bedNumber">
            <el-input v-model="dataForm.bedNumber" placeholder="床位数量" @change="handleChange">
              <template slot="append">
                床位/间
              </template></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="orderBy">
            <el-input v-model="dataForm.orderBy" placeholder="排序">
              <template slot="append">
                值越小越靠前
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
      <el-form-item label="房间备注" prop="roomRemarks">
        <el-input v-model="dataForm.roomRemarks" placeholder="房间备注"/>
      </el-form-item>
        </el-col>
        <el-col :span="12">
      <el-form-item label="床位备注" prop="bedRemarks">
        <el-input v-model="dataForm.bedRemarks" placeholder="床位备注"/>
      </el-form-item>
    </el-col>
      </el-row>
    <el-form-item v-if="dataForm.price > 0" label="微信支付" prop="isWechatPay">
      <el-select v-model="dataForm.isWechatPay" >
        <el-option v-for="item in yesOrNo" :key="item.key"  :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="dataForm.price > 0" label="支付宝支付" prop="isAliPay">
      <el-select v-model="dataForm.isAliPay" >
        <el-option v-for="item in yesOrNo" :key="item.key"  :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="dataForm.price > 0" label="银行转账" prop="isBankTransfer">
      <el-select v-model="dataForm.isBankTransfer">
        <el-option v-for="item in yesOrNo" :key="item.key"  :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
      <el-form-item v-if="dataForm.isBankTransfer == 1" label="银行转账信息" prop="bankTransferNotify">
        <tinymce-editor ref="editor" v-model="dataForm.bankTransferNotify"></tinymce-editor>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isDecimalByOne, isDecimalByTwo,isInteger } from '@/utils/validate'
import {yesOrNo} from "@/data/common.js"
  import {saleStatus} from '@/data/room.js'
  export default {
    components: {
      TinymceEditor: () =>
        import ("@/components/tinymce-editor")
    },
    data() {
      var VisInteger = (rule, value, callback) => {
        if (!isInteger(value)) {
          callback(new Error("必须为数字"));
        } else {
          callback();
        }
      };
      var VisDecimalByTwo = (rule, value, callback) => {
        if (!isDecimalByTwo(value)) {
          callback(new Error("必须为数字"));
        } else {
          callback();
        }
      };
      var VisDecimalByOne = (rule, value, callback) => {
        if (!isDecimalByOne(value)) {
          callback(new Error("必须为数字"));
        } else {
          callback();
        }
      };
      return {
        yesOrNo,
        visible: false,
        saleStatus,
        activityInfo: {},
        dataForm: {
          id: 0,
          name: '',
          activityId: '',
          hotelId: '',
          hotelActivityId: '',
          status: 0,
          orderBy: 0,
          bedStatus: 0,
          price: 0,
          stockNumber: 0,
          bedPrice: 0,
          bedNumber: 1,
          isWechatPay: 0,
          isAliPay: 0,
          isBankTransfer: 0,
          bankTransferNotify: '',
          roomRemarks: '',
          bedRemarks: '',
        },
        dataRule: {
          name: [{
            required: true,
            message: '房型名称不能为空',
            trigger: 'blur'
          }],
          status: [{
            required: true,
            message: '销售状态不能为空',
            trigger: 'blur'
          }],
          bedStatus: [{
            required: true,
            message: '床位销售状态不能为空',
            trigger: 'blur'
          }],
          price: [{
            required: true,
            message: '单价不能为空',
            trigger: 'blur'
          },
            {
              validator: VisDecimalByTwo,
              trigger: 'blur'
            }],
          bedPrice: [{
            required: true,
            message: '床位价格不能为空',
            trigger: 'blur'
          },
            {
              validator: VisDecimalByTwo,
              trigger: 'blur'
            }],
          bedNumber: [{
            required: true,
            message: '床位数量不能为空',
            trigger: 'blur'
          },
            {
              validator: VisInteger,
              trigger: 'blur'
            }],
          stockNumber: [{
            required: true,
            message: '库存数量不能为空',
            trigger: 'blur'
          },
            {
              validator: VisInteger,
              trigger: 'blur'
            }],
        }
      }
    },
    methods: {
      init(id,activityId,hotelActivityId,hotelId) {
        this.dataForm.id = id || 0
        this.dataForm.activityId = activityId
        this.dataForm.hotelActivityId = hotelActivityId
        this.dataForm.hotelId = hotelId
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroom/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.dataForm.name = data.hotelActivityRoom.name
                this.dataForm.activityId = data.hotelActivityRoom.activityId
                this.dataForm.hotelId = data.hotelActivityRoom.hotelId
                this.dataForm.hotelActivityId = data.hotelActivityRoom.hotelActivityId
                this.dataForm.status = data.hotelActivityRoom.status
                this.dataForm.orderBy = data.hotelActivityRoom.orderBy
                this.dataForm.bedStatus = data.hotelActivityRoom.bedStatus
                this.dataForm.price = data.hotelActivityRoom.price
                this.dataForm.bedPrice = data.hotelActivityRoom.bedPrice
                this.dataForm.bedNumber = data.hotelActivityRoom.bedNumber
                this.dataForm.inDate = data.hotelActivityRoom.inDate
                this.dataForm.outDate = data.hotelActivityRoom.outDate
                this.dataForm.bankTransferNotify = data.hotelActivityRoom.bankTransferNotify
                this.dataForm.isWechatPay = data.hotelActivityRoom.isWechatPay
                this.dataForm.isAliPay = data.hotelActivityRoom.isAliPay
                this.dataForm.isBankTransfer = data.hotelActivityRoom.isBankTransfer
                this.dataForm.roomRemarks = data.hotelActivityRoom.roomRemarks
                this.dataForm.bedRemarks = data.hotelActivityRoom.bedRemarks
              }
            })
          } else {
            // this.getActivity();

          }
        })
      },
    handleChange () {
      if(this.dataForm.bedNumber < 2) {
        this.dataForm.bedStatus = 0;
      }
      this.dataForm.bedPrice = this.dataForm.price / this.dataForm.bedNumber
    },
      // 表单提交
      dataFormSubmit() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelactivityroom/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'hotelActivityId': this.dataForm.hotelActivityId,
                'stockNumber': this.dataForm.stockNumber,
                'status': this.dataForm.status,
                'orderBy': this.dataForm.orderBy,
                'bedStatus': this.dataForm.bedStatus,
                'price': this.dataForm.price,
                'bedPrice': this.dataForm.bedPrice,
                'bedNumber': this.dataForm.bedNumber,
                'bankTransferNotify': this.dataForm.bankTransferNotify,
                'isWechatPay': this.dataForm.isWechatPay,
                'isAliPay': this.dataForm.isAliPay,
                'isBankTransfer': this.dataForm.isBankTransfer,
                'roomRemarks': this.dataForm.roomRemarks,
                'bedRemarks': this.dataForm.bedRemarks,
              })
            }).then(({
              data
            }) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      getActivity() {
          this.$http({
              url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
              method: 'get',
              params: this.$http.adornParams()
          }).then(({
              data
          }) => {
              if (data && data.code === 200) {
                  this.activityInfo = data.activity
              }
          })
      },
    }
  }
</script>
