package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.sys.service.SysUserService;
import com.cjy.pyp.modules.sys.service.SysUserRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 渠道管理员管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/channel/admin")
@Api(tags = "渠道管理员管理")
public class ChannelAdminController extends AbstractController {
    
    @Autowired
    private SysUserService sysUserService;
    
    @Autowired
    private SysUserRoleService sysUserRoleService;

    /**
     * 渠道管理员列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("channel:admin:list")
    @ApiOperation(value = "渠道管理员列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        // 只查询渠道管理员角色的用户
        params.put("roleId", 1000000000000000001L);
        
        List<SysUserEntity> userList = sysUserService.queryPageByRole(params);
        return R.okList(userList);
    }

    /**
     * 渠道管理员信息
     */
    @RequestMapping("/info/{userId}")
    // @RequiresPermissions("channel:admin:info")
    @ApiOperation(value = "渠道管理员信息", notes = "")
    public R info(@PathVariable("userId") Long userId) {
        SysUserEntity user = sysUserService.getById(userId);
        return R.ok().put("user", user);
    }

    /**
     * 保存渠道管理员
     */
    @RequestMapping("/save")
    // @RequiresPermissions("channel:admin:save")
    @SysLog("保存渠道管理员")
    @ApiOperation(value = "保存渠道管理员", notes = "")
    public R save(@RequestBody SysUserEntity user, @CookieValue String appid) {
        ValidatorUtils.validateEntity(user);
        
        // 设置应用ID
        user.setAppid(appid);
        
        // 检查用户名是否重复
        SysUserEntity existingUser = sysUserService.queryByUserName(user.getUsername());
        if (existingUser != null) {
            return R.error("用户名已存在");
        }
        
        // 设置默认密码
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            user.setPassword("123456");
        }
        
        // 密码加密
        String salt = RandomStringUtils.randomAlphanumeric(20);
        user.setPassword(new Sha256Hash(user.getPassword(), salt).toHex());
        user.setSalt(salt);
        
        // 设置状态
        if (user.getStatus() == null) {
            user.setStatus(1);
        }
        
        sysUserService.save(user);
        
        // 分配渠道管理员角色
        Long channelAdminRoleId = 1000000000000000001L;
        sysUserRoleService.saveOrUpdate(user.getUserId(), Arrays.asList(channelAdminRoleId));
        
        return R.ok();
    }

    /**
     * 修改渠道管理员
     */
    @RequestMapping("/update")
    // @RequiresPermissions("channel:admin:update")
    @SysLog("修改渠道管理员")
    @ApiOperation(value = "修改渠道管理员", notes = "")
    public R update(@RequestBody SysUserEntity user) {
        ValidatorUtils.validateEntity(user);
        
        // 检查用户名是否重复
        SysUserEntity existingUser = sysUserService.queryByUserName(user.getUsername());
        if (existingUser != null && !existingUser.getUserId().equals(user.getUserId())) {
            return R.error("用户名已存在");
        }
        
        // 如果修改了密码，重新加密
        if (user.getPassword() != null && !user.getPassword().trim().isEmpty()) {
            String salt = RandomStringUtils.randomAlphanumeric(20);
            user.setPassword(new Sha256Hash(user.getPassword(), salt).toHex());
            user.setSalt(salt);
        } else {
            // 不修改密码时，清空密码字段
            user.setPassword(null);
            user.setSalt(null);
        }
        
        sysUserService.updateById(user);
        
        return R.ok();
    }

    /**
     * 删除渠道管理员
     */
    @RequestMapping("/delete")
    // @RequiresPermissions("channel:admin:delete")
    @SysLog("删除渠道管理员")
    @ApiOperation(value = "删除渠道管理员", notes = "")
    public R delete(@RequestBody Long[] userIds) {
        sysUserService.removeByIds(Arrays.asList(userIds));
        return R.ok();
    }

    /**
     * 重置密码
     */
    @RequestMapping("/resetPassword")
    // @RequiresPermissions("channel:admin:update")
    @SysLog("重置渠道管理员密码")
    @ApiOperation(value = "重置密码", notes = "")
    public R resetPassword(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        String newPassword = params.get("password") != null ? 
            params.get("password").toString() : "123456";
        
        SysUserEntity user = new SysUserEntity();
        user.setUserId(userId);
        
        // 密码加密
        String salt = RandomStringUtils.randomAlphanumeric(20);
        user.setPassword(new Sha256Hash(newPassword, salt).toHex());
        user.setSalt(salt);
        
        sysUserService.updateById(user);
        
        return R.ok().put("newPassword", newPassword);
    }

    /**
     * 启用/禁用渠道管理员
     */
    @RequestMapping("/updateStatus")
    // @RequiresPermissions("channel:admin:update")
    @SysLog("修改渠道管理员状态")
    @ApiOperation(value = "启用/禁用渠道管理员", notes = "")
    public R updateStatus(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        
        SysUserEntity user = new SysUserEntity();
        user.setUserId(userId);
        user.setStatus(status);
        
        sysUserService.updateById(user);
        
        return R.ok();
    }
}
