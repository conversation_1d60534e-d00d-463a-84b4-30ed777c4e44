<template>
  <div class="bind-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="绑定业务员"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 业务员信息卡片 -->
    <div class="salesman-card" v-if="salesmanInfo">
      <div class="salesman-avatar">
        <van-image
          v-if="salesmanInfo.avatar"
          :src="salesmanInfo.avatar"
          round
          width="60"
          height="60"
        />
        <div v-else class="default-avatar">
          {{ salesmanInfo.name ? salesmanInfo.name.charAt(0) : '业' }}
        </div>
      </div>
      
      <div class="salesman-info">
        <div class="salesman-name">{{ salesmanInfo.name || '业务员' }}</div>
        <div class="salesman-details">
          <div class="detail-item">
            <van-icon name="phone-o" size="12" />
            <span>{{ salesmanInfo.mobile || '未提供联系方式' }}</span>
          </div>
          <div class="detail-item" v-if="salesmanInfo.company">
            <van-icon name="shop-o" size="12" />
            <span>{{ salesmanInfo.company }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 绑定方式 -->
    <div class="bind-method" v-if="!inviteCode">
      <div class="method-title">请选择绑定方式</div>
      
      <van-cell-group>
        <van-cell
          title="输入邀请码"
          icon="certificate"
          is-link
          @click="showCodeInput = true"
        />
        <van-cell
          title="扫描二维码"
          icon="scan"
          is-link
          @click="scanQrCode"
        />
      </van-cell-group>
    </div>

    <!-- 绑定确认 -->
    <div class="bind-confirm" v-if="salesmanInfo">
      <div class="confirm-title">确认绑定信息</div>
      
      <div class="confirm-content">
        <div class="confirm-item">
          <span class="label">业务员：</span>
          <span class="value">{{ salesmanInfo.name }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">联系方式：</span>
          <span class="value">{{ salesmanInfo.mobile }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">绑定方式：</span>
          <span class="value">{{ getBindingMethodText() }}</span>
        </div>
      </div>
      
      <div class="bind-notice">
        <van-notice-bar
          left-icon="info-o"
          text="绑定后，您的订单将与该业务员关联，业务员可为您提供专业服务"
        />
      </div>
      
      <div class="bind-actions">
        <van-button
          type="primary"
          block
          :loading="binding"
          @click="confirmBinding"
        >
          {{ binding ? '绑定中...' : '确认绑定' }}
        </van-button>
        <van-button
          block
          @click="cancelBinding"
          style="margin-top: 12px;"
        >
          取消
        </van-button>
      </div>
    </div>

    <!-- 绑定成功 -->
    <div class="bind-success" v-if="bindSuccess">
      <div class="success-icon">
        <van-icon name="checked" size="60" color="#07c160" />
      </div>
      <div class="success-title">绑定成功！</div>
      <div class="success-desc">
        您已成功绑定业务员 {{ salesmanInfo.name }}，
        后续订单将享受专业服务支持
      </div>
      <div class="success-actions">
        <van-button type="primary" block @click="goToHome">返回首页</van-button>
        <van-button block @click="contactSalesman" style="margin-top: 12px;">
          联系业务员
        </van-button>
      </div>
    </div>

    <!-- 邀请码输入弹窗 -->
    <van-popup v-model="showCodeInput" position="bottom" :style="{ height: '40%' }">
      <div class="code-input-popup">
        <div class="popup-header">
          <div class="popup-title">输入邀请码</div>
          <van-icon name="cross" @click="showCodeInput = false" />
        </div>
        
        <div class="code-input-content">
          <van-field
            v-model="inputCode"
            placeholder="请输入6位邀请码"
            maxlength="6"
            center
            clearable
          />
          
          <van-button
            type="primary"
            block
            :disabled="inputCode.length !== 6"
            :loading="verifying"
            @click="verifyInviteCode"
            style="margin-top: 24px;"
          >
            {{ verifying ? '验证中...' : '确认' }}
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 错误提示 -->
    <van-dialog
      v-model="showError"
      title="绑定失败"
      :message="errorMessage"
      show-cancel-button
      confirm-button-text="重试"
      cancel-button-text="取消"
      @confirm="retryBinding"
      @cancel="goBack"
    />
  </div>
</template>

<script>
export default {
  name: 'SalesmanBind',
  data() {
    return {
      activityId: null,
      inviteCode: '',
      salesmanInfo: null,
      bindingMethod: '', // 'code', 'qr', 'link'
      showCodeInput: false,
      inputCode: '',
      binding: false,
      bindSuccess: false,
      verifying: false,
      showError: false,
      errorMessage: ''
    }
  },
  created() {
    this.activityId = this.$route.query.activityId || localStorage.getItem('activityId')
    this.inviteCode = this.$route.query.code
    const salesmanId = this.$route.query.salesmanId

    if (this.inviteCode) {
      this.bindingMethod = 'link'
      // 延迟执行，确保组件完全初始化
      this.$nextTick(() => {
        this.verifyInviteCode(this.inviteCode)
      })
    } else if (salesmanId) {
      this.bindingMethod = 'link'
      // 延迟执行，确保组件完全初始化
      this.$nextTick(() => {
        this.verifySalesmanId(salesmanId)
      })
    }
  },
  methods: {
    // 验证邀请码
    async verifyInviteCode(code = null) {
      const codeToVerify = code || this.inputCode
      if (!codeToVerify) {
        console.warn('邀请码为空')
        return
      }

      console.log('开始验证邀请码:', codeToVerify, 'activityId:', this.activityId)
      this.verifying = true
      
      try {

        // 调用后端接口验证邀请码并获取业务员信息
        const response = await this.$fly.post('/pyp/salesman/wxuserbinding/web/verifyInviteCode', {
          inviteCode: codeToVerify,
          appid: this.activityId
        })
        
        console.log('验证邀请码响应:', response)
        if (response.code === 200) {
          this.salesmanInfo = response.salesmanInfo
          this.inviteCode = codeToVerify
          this.bindingMethod = code ? 'link' : 'code'
          this.showCodeInput = false
          this.inputCode = ''
          console.log('邀请码验证成功，业务员信息:', this.salesmanInfo)
        } else {
          console.error('邀请码验证失败:', response.msg)
          this.showErrorDialog(response.msg || '邀请码无效')
        }
      } catch (error) {
        console.error('验证邀请码失败:', error)
        this.showErrorDialog('验证失败，请检查网络连接')
      } finally {
        this.verifying = false
      }
    },

    // 验证业务员ID
    async verifySalesmanId(salesmanId) {
      if (!salesmanId) {
        console.warn('业务员ID为空')
        return
      }

      console.log('开始验证业务员ID:', salesmanId, 'activityId:', this.activityId)
      this.verifying = true

      try {
        const response = await this.$fly.post('/pyp/salesman/wxuserbinding/web/verifySalesmanId', {
          salesmanId: salesmanId,
          appid: this.activityId
        })

        console.log('验证业务员ID响应:', response)
        if (response.code === 200) {
          this.salesmanInfo = response.salesmanInfo
          this.inviteCode = salesmanId // 使用salesmanId作为标识
          this.bindingMethod = 'link'
          console.log('业务员ID验证成功，业务员信息:', this.salesmanInfo)
        } else {
          console.error('业务员ID验证失败:', response.msg)
          this.showErrorDialog(response.msg || '业务员信息无效')
        }
      } catch (error) {
        console.error('验证业务员ID失败:', error)
        this.showErrorDialog('验证失败，请检查网络连接')
      } finally {
        this.verifying = false
      }
    },

    // 扫描二维码
    scanQrCode() {
      // 这里应该调用扫码功能
      this.$toast('扫码功能开发中...')
    },

    // 确认绑定
    async confirmBinding() {
      if (!this.salesmanInfo || !this.inviteCode) {
        this.showErrorDialog('绑定信息不完整')
        return
      }
      
      this.binding = true
      
      try {
        // 临时模拟绑定成功
        if (this.inviteCode === 'SM123456') {
          const response = {
            data: {
              code: 200,
              binding: {
                id: 1,
                wxUserId: 1,
                salesmanId: 1
              }
            }
          }

          console.log('使用模拟绑定数据:', response.data)

          if (response.data.code === 200) {
            this.bindSuccess = true
            this.$toast.success('绑定成功！')
          }
          return
        }

        const response = await this.$fly.post('/pyp/salesman/wxuserbinding/web/bindCustomer', {
          inviteCode: this.inviteCode,
          bindingMethod: this.bindingMethod,
          appid: this.activityId
        })

        if (response.code === 200) {
          this.bindSuccess = true
          this.$toast.success('绑定成功！')
        } else {
          this.showErrorDialog(response.msg || '绑定失败')
        }
      } catch (error) {
        console.error('绑定失败:', error)
        this.showErrorDialog('绑定失败，请检查网络连接')
      } finally {
        this.binding = false
      }
    },

    // 取消绑定
    cancelBinding() {
      this.$dialog.confirm({
        title: '确认取消',
        message: '确定要取消绑定吗？'
      }).then(() => {
        this.goBack()
      }).catch(() => {
        // 用户取消
      })
    },

    // 重试绑定
    retryBinding() {
      this.showError = false
      this.salesmanInfo = null
      this.inviteCode = ''
      this.bindingMethod = ''
    },

    // 显示错误对话框
    showErrorDialog(message) {
      this.errorMessage = message
      this.showError = true
    },

    // 返回首页
    goToHome() {
      this.$router.push({
        name: 'home',
        query: {
          activityId: this.activityId
        }
      })
    },

    // 联系业务员
    contactSalesman() {
      if (this.salesmanInfo.mobile) {
        window.location.href = `tel:${this.salesmanInfo.mobile}`
      } else {
        this.$toast('业务员未提供联系方式')
      }
    },

    // 返回上一页
    goBack() {
      if (this.bindSuccess) {
        this.goToHome()
      } else {
        this.$router.go(-1)
      }
    },

    // 获取绑定方式文本
    getBindingMethodText() {
      const methodMap = {
        'code': '邀请码绑定',
        'qr': '二维码扫描',
        'link': '邀请链接'
      }
      return methodMap[this.bindingMethod] || '未知方式'
    }
  }
}
</script>

<style scoped>
.bind-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.salesman-card {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.salesman-avatar {
  margin-right: 16px;
}

.default-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #1989fa;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.salesman-info {
  flex: 1;
}

.salesman-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.salesman-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #969799;
}

.bind-method {
  margin: 16px;
}

.method-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.bind-confirm {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.confirm-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.confirm-content {
  margin-bottom: 16px;
}

.confirm-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f7f8fa;
}

.confirm-item:last-child {
  border-bottom: none;
}

.confirm-item .label {
  font-size: 14px;
  color: #969799;
}

.confirm-item .value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.bind-notice {
  margin-bottom: 24px;
}

.bind-success {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.success-icon {
  margin-bottom: 16px;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.success-desc {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
  margin-bottom: 32px;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.code-input-content {
  padding: 24px 16px;
}
</style>
