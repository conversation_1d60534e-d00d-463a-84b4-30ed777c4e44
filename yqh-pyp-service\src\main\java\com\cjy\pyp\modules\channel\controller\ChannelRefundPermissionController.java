package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.channel.service.ChannelRefundPermissionService;
import com.cjy.pyp.modules.channel.service.ChannelRefundQuotaRecordService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 渠道退款权限管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/channel/refund-permission")
public class ChannelRefundPermissionController {

    @Autowired
    private ChannelRefundPermissionService channelRefundPermissionService;

    @Autowired
    private ChannelRefundQuotaRecordService channelRefundQuotaRecordService;

    /**
     * 获取渠道退款名额使用情况
     */
    @GetMapping("/quota-usage/{channelId}")
    @RequiresPermissions("channel:refund:view")
    public R getQuotaUsage(@PathVariable("channelId") Long channelId) {
        Map<String, Object> usage = channelRefundPermissionService.getChannelRefundQuotaUsage(channelId);
        return R.ok().put("data", usage);
    }

    /**
     * 批量更新渠道退款权限
     */
    @PostMapping("/batch-update/{channelId}")
    @RequiresPermissions("channel:refund:update")
    @SysLog("批量更新渠道退款权限")
    public R batchUpdatePermissions(@PathVariable("channelId") Long channelId) {
        return channelRefundPermissionService.batchUpdateRefundPermissions(channelId);
    }

    /**
     * 更新渠道退款名额设置
     */
    @PostMapping("/update-quota")
    @RequiresPermissions("channel:refund:update")
    @SysLog("更新渠道退款名额设置")
    public R updateQuota(@RequestBody Map<String, Object> params) {
        Long channelId = Long.valueOf(params.get("channelId").toString());
        Integer refundQuota = params.get("refundQuota") != null ? 
            Integer.valueOf(params.get("refundQuota").toString()) : 0;
        Boolean enabled = params.get("enabled") != null ? 
            Boolean.valueOf(params.get("enabled").toString()) : false;

        return channelRefundPermissionService.updateChannelRefundQuota(channelId, refundQuota, enabled);
    }

    /**
     * 获取渠道内具有退款权限的订单列表
     */
    @GetMapping("/eligible-orders/{channelId}")
    @RequiresPermissions("channel:refund:view")
    public R getEligibleOrders(@PathVariable("channelId") Long channelId) {
        return R.ok().put("data", channelRefundQuotaRecordService.getChannelEligibleOrders(channelId));
    }

    /**
     * 分页查询退款名额使用记录
     */
    @GetMapping("/quota-records")
    @RequiresPermissions("channel:refund:view")
    public R getQuotaRecords(@RequestParam Map<String, Object> params) {
        PageUtils page = channelRefundQuotaRecordService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 检查订单退款权限
     */
    @GetMapping("/check-permission/{orderId}")
    @RequiresPermissions("channel:refund:view")
    public R checkPermission(@PathVariable("orderId") Long orderId) {
        return channelRefundPermissionService.checkRefundPermissionWithDetails(orderId);
    }

    /**
     * 手动分配退款权限
     */
    @PostMapping("/assign-permission/{orderId}")
    @RequiresPermissions("channel:refund:update")
    @SysLog("手动分配退款权限")
    public R assignPermission(@PathVariable("orderId") Long orderId) {
        return channelRefundPermissionService.assignRefundPermission(orderId);
    }

    /**
     * 手动释放退款权限
     */
    @PostMapping("/release-permission/{orderId}")
    @RequiresPermissions("channel:refund:update")
    @SysLog("手动释放退款权限")
    public R releasePermission(@PathVariable("orderId") Long orderId) {
        return channelRefundPermissionService.releaseRefundPermission(orderId);
    }

    /**
     * 获取订单退款权限排序
     */
    @GetMapping("/quota-sequence/{orderId}")
    @RequiresPermissions("channel:refund:view")
    public R getQuotaSequence(@PathVariable("orderId") Long orderId) {
        Integer sequence = channelRefundPermissionService.getOrderRefundQuotaSequence(orderId);
        Long channelId = channelRefundPermissionService.getChannelIdByOrderId(orderId);
        
        return R.ok()
            .put("sequence", sequence)
            .put("channelId", channelId)
            .put("hasPermission", sequence != null);
    }
}
