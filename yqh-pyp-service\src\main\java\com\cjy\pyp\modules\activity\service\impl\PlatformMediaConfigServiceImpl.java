package com.cjy.pyp.modules.activity.service.impl;

import com.cjy.pyp.modules.activity.service.PlatformMediaConfigService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 平台媒体配置服务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-30
 */
@Service("platformMediaConfigService")
public class PlatformMediaConfigServiceImpl implements PlatformMediaConfigService {
    
    // 平台配置数据
    private static final Map<String, Map<String, Object>> PLATFORM_CONFIGS = new HashMap<>();
    
    static {
        // 抖音配置
        Map<String, Object> douyinConfig = new HashMap<>();
        douyinConfig.put("code", "douyin");
        douyinConfig.put("name", "抖音");
        douyinConfig.put("supportedMediaTypes", Arrays.asList("video", "image"));
        douyinConfig.put("defaultImageCount", 3);
        douyinConfig.put("videoRatio", "9:16");
        douyinConfig.put("imageRatio", "9:16");
        PLATFORM_CONFIGS.put("douyin", douyinConfig);
        
        // 小红书配置
        Map<String, Object> xiaohongshuConfig = new HashMap<>();
        xiaohongshuConfig.put("code", "xiaohongshu");
        xiaohongshuConfig.put("name", "小红书");
        xiaohongshuConfig.put("supportedMediaTypes", Arrays.asList("video", "image"));
        xiaohongshuConfig.put("defaultImageCount", 3);
        xiaohongshuConfig.put("videoRatio", "3:4");
        xiaohongshuConfig.put("imageRatio", "3:4");
        PLATFORM_CONFIGS.put("xiaohongshu", xiaohongshuConfig);
        
        // 快手配置
        Map<String, Object> kuaishouConfig = new HashMap<>();
        kuaishouConfig.put("code", "kuaishou");
        kuaishouConfig.put("name", "快手");
        kuaishouConfig.put("supportedMediaTypes", Arrays.asList("video", "image"));
        kuaishouConfig.put("defaultImageCount", 3);
        kuaishouConfig.put("videoRatio", "9:16");
        kuaishouConfig.put("imageRatio", "9:16");
        PLATFORM_CONFIGS.put("kuaishou", kuaishouConfig);
        
        // 微信朋友圈配置
        Map<String, Object> weixinConfig = new HashMap<>();
        weixinConfig.put("code", "weixin");
        weixinConfig.put("name", "微信朋友圈");
        weixinConfig.put("supportedMediaTypes", Arrays.asList("image"));
        weixinConfig.put("defaultImageCount", 3);
        weixinConfig.put("imageRatio", "1:1");
        PLATFORM_CONFIGS.put("weixin", weixinConfig);
        
        // 大众点评配置
        Map<String, Object> dianpingConfig = new HashMap<>();
        dianpingConfig.put("code", "dianping");
        dianpingConfig.put("name", "大众点评");
        dianpingConfig.put("supportedMediaTypes", Arrays.asList("image"));
        dianpingConfig.put("defaultImageCount", 3);
        dianpingConfig.put("imageRatio", "4:3");
        PLATFORM_CONFIGS.put("dianping", dianpingConfig);
        
        // 美团点评配置
        Map<String, Object> meituanConfig = new HashMap<>();
        meituanConfig.put("code", "meituan");
        meituanConfig.put("name", "美团点评");
        meituanConfig.put("supportedMediaTypes", Arrays.asList("image"));
        meituanConfig.put("defaultImageCount", 3);
        meituanConfig.put("imageRatio", "4:3");
        PLATFORM_CONFIGS.put("meituan", meituanConfig);
        
        // 抖音点评配置
        Map<String, Object> douyinReviewConfig = new HashMap<>();
        douyinReviewConfig.put("code", "douyin_review");
        douyinReviewConfig.put("name", "抖音点评");
        douyinReviewConfig.put("supportedMediaTypes", Arrays.asList("image"));
        douyinReviewConfig.put("defaultImageCount", 3);
        douyinReviewConfig.put("videoRatio", "9:16");
        douyinReviewConfig.put("imageRatio", "9:16");
        PLATFORM_CONFIGS.put("douyin_review", douyinReviewConfig);
    }
    
    @Override
    public List<Map<String, Object>> getAllPlatformConfigs() {
        List<Map<String, Object>> configs = new ArrayList<>();
        for (Map<String, Object> config : PLATFORM_CONFIGS.values()) {
            Map<String, Object> platformInfo = new HashMap<>();
            platformInfo.put("code", config.get("code"));
            platformInfo.put("name", config.get("name"));
            platformInfo.put("supportedMediaTypes", config.get("supportedMediaTypes"));
            platformInfo.put("defaultImageCount", config.get("defaultImageCount"));
            configs.add(platformInfo);
        }
        return configs;
    }
    
    @Override
    public List<Map<String, Object>> getPlatformMediaTypes(String platform) {
        Map<String, Object> config = PLATFORM_CONFIGS.get(platform);
        if (config == null) {
            return new ArrayList<>();
        }
        
        @SuppressWarnings("unchecked")
        List<String> supportedTypes = (List<String>) config.get("supportedMediaTypes");
        List<Map<String, Object>> mediaTypes = new ArrayList<>();
        
        for (String type : supportedTypes) {
            Map<String, Object> mediaType = new HashMap<>();
            mediaType.put("code", type);
            if ("video".equals(type)) {
                mediaType.put("name", "视频");
                mediaType.put("icon", "video-o");
            } else if ("image".equals(type)) {
                mediaType.put("name", "图片");
                mediaType.put("icon", "photo-o");
            }
            mediaTypes.add(mediaType);
        }
        
        return mediaTypes;
    }
    
    @Override
    public Integer getPlatformDefaultImageCount(String platform) {
        Map<String, Object> config = PLATFORM_CONFIGS.get(platform);
        if (config == null) {
            return 3; // 默认3张
        }
        return (Integer) config.get("defaultImageCount");
    }
    
    @Override
    public boolean isValidPlatformMediaType(String platform, String mediaType) {
        Map<String, Object> config = PLATFORM_CONFIGS.get(platform);
        if (config == null) {
            return false;
        }
        
        @SuppressWarnings("unchecked")
        List<String> supportedTypes = (List<String>) config.get("supportedMediaTypes");
        return supportedTypes.contains(mediaType);
    }
}
