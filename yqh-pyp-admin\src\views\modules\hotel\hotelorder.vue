<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.contact" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.remarks" placeholder="备注" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="订单状态" filterable>
          <el-option label="全部(订单状态)" value=""></el-option>
          <el-option v-for="item in orderStatus" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.source" placeholder="支付来源" filterable>
          <el-option label="全部(支付来源)" value=""></el-option>
          <el-option v-for="item in sources" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="exportHandle()" type="success">导出</el-button>
        <el-button v-if="isAuth('hotel:hotelorder:save')" type="primary" @click="addOrUpdateHandle()">添加订单</el-button>
        <el-button @click="downloadDemo()" type="success">导入模板</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/hotel/hotelorder/importExcel?activityId=' + dataForm.activityId"
            :name="'酒店订单导入'"></Upload>
        </el-button>
        <!-- <el-button v-if="isAuth('hotel:hotelorder:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :default-expand-all="isExpand" height="800px" :data="dataList" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%">
      <el-table-column type="expand">
        <template slot-scope="props" v-if="props.row.hotelOrderDetailEntities">
          <el-table :data="props.row.hotelOrderDetailEntities" border v-loading="dataListLoading"
            @selection-change="selectionChangeHandle" style="width: 100%">
            <el-table-column prop="hotelName" show-overflow-tooltip  header-align="center" align="center" label="酒店名称">
            </el-table-column>
            <el-table-column prop="roomName" header-align="center" align="center" label="房型名称">
            </el-table-column>
            <el-table-column prop="roomType" header-align="center" align="center" label="房间类型">
              <div slot-scope="scope">
                <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.roomType)">{{ 
                  roomType[scope.row.roomType].value}}</el-tag>
              </div>
            </el-table-column>
            <el-table-column prop="number" header-align="center" align="center" label="数量">
            </el-table-column>
            <el-table-column prop="inDate" header-align="center" align="center" label="入住时间">
              <div slot-scope="scope">
                <span>{{ scope.row.inDate | dateFilter }}</span>
              </div>
            </el-table-column>
            <el-table-column prop="outDate" header-align="center" align="center" label="退房时间">
              <div slot-scope="scope">
                <span>{{ scope.row.outDate | dateFilter }}</span>
              </div>
            </el-table-column>
            <el-table-column prop="dayNumber" header-align="center" align="center" label="总天数">
            </el-table-column>
            <el-table-column prop="price" header-align="center" align="center" label="价格">
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="contact" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="email" header-align="center" align="center" label="邮箱">
      </el-table-column>
      <el-table-column prop="orderSn" show-overflow-tooltip header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="订单状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.status)">{{ scope.row.status | statusFilter
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="source" header-align="center" align="center" label="支付来源">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.source)">{{ scope.row.source | sourceFilter
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="hotelName" header-align="center" show-overflow-tooltip  align="center" label="酒店名称">
        <div slot-scope="scope">
          {{ scope.row.hotelOrderDetailEntities[0].hotelName }}
        </div>
      </el-table-column>
      <el-table-column prop="roomName" header-align="center" align="center" label="房型名称">
        <div slot-scope="scope">
          {{ scope.row.hotelOrderDetailEntities[0].roomName }}
        </div>
      </el-table-column><el-table-column prop="roomType" header-align="center" align="center" label="房间类型">
        <div slot-scope="scope">
          <el-tag type="primary"
            :class="'tag-color tag-color-' + (scope.row.hotelOrderDetailEntities[0].roomType)">{{ 
              roomType[scope.row.hotelOrderDetailEntities[0].roomType].value}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="number" header-align="center" align="center" label="数量">
        <div slot-scope="scope">
          {{ scope.row.hotelOrderDetailEntities[0].number }}
        </div>
      </el-table-column>
      <el-table-column prop="inDate" header-align="center" align="center" label="入住时间">
        <div slot-scope="scope">
          <span>{{ scope.row.hotelOrderDetailEntities[0].inDate | dateFilter }}</span>
        </div>
      </el-table-column>
      <el-table-column prop="outDate" header-align="center" align="center" label="退房时间">
        <div slot-scope="scope">
          <span>{{ scope.row.hotelOrderDetailEntities[0].outDate | dateFilter }}</span>
        </div>
      </el-table-column>
      <el-table-column prop="dayNumber" header-align="center" align="center" label="总天数">
        <div slot-scope="scope">
          {{ scope.row.hotelOrderDetailEntities[0].dayNumber }}
        </div>
      </el-table-column>
      <el-table-column prop="totalAmount" header-align="center" align="center" label="订单总金额">
      </el-table-column>
      <el-table-column prop="payAmount" header-align="center" align="center" label="实付金额">
      </el-table-column>
      <el-table-column prop="payAccount" header-align="center" align="center" label="付款人账号">
      </el-table-column>
      <el-table-column prop="payTransaction" header-align="center" align="center" label="支付流水号">
      </el-table-column>
      <el-table-column prop="payTime" header-align="center" align="center" label="支付时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="remarks" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button> -->
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button> -->
          <el-button type="text" size="small" v-if="isAuth('hotel:hotelorder:cancel')"
            @click="cancelHandle(scope.row.id)">取消订单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from "./hotelorder-add-or-update";
import { sources, orderStatus } from '@/data/common';
import {
  roomType,
  roomTypeFjsd
} from "@/data/room.js";
export default {
  data() {
    return {
      appid: '',
      isExpand: false,
      dataForm: {
        contact: "",
        mobile: "",
        remarks: "",
        status: '',
        source: '',
        activityId: undefined,
      },
      sources,
      orderStatus: orderStatus,
      roomType,
      roomTypeFjsd,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    };
  },
  components: {
    AddOrUpdate,
    Upload: () => import('@/components/upload')
  },
  filters: {
    statusFilter(v) {
      let data = orderStatus.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    sourceFilter(v) {
      let data = sources.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    dateFilter(v) {
      return v.substring(0, 10);
    },
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.getDataList();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/hotel/hotelorder/list"),
        method: "get",
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'activityId': this.dataForm.activityId,
          'contact': this.dataForm.contact,
          'mobile': this.dataForm.mobile,
          'remarks': this.dataForm.remarks,
          'source': this.dataForm.source,
          'status': this.dataForm.status,
        }),
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/hotel/hotelorder/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "activityId=" + this.dataForm.activityId,
        "contact=" + this.dataForm.contact,
        "mobile=" + this.dataForm.mobile,
        "remarks=" + this.dataForm.remarks,
        "source=" + this.dataForm.source,
        "status=" + this.dataForm.status,
      ].join('&'));
      window.open(url);
    },
    downloadDemo() {
      var url = this.$http.adornUrl("/hotel/hotelorder/downloadDemo?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle() {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId);
      });
    },
    // 删除
    deleteHandle(id) {
      var ids = id ?
        [id] :
        this.dataListSelections.map((item) => {
          return item.id;
        });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/hotel/hotelorder/delete"),
          method: "post",
          data: this.$http.adornData(ids, false),
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    cancelHandle(id) {
      this.$confirm(
        `确定对取消订单操作?`,
        "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/hotel/hotelorder/cancel"),
          method: "get",
          params: this.$http.adornParams({
            orderId: id
          }),
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
  },
};
</script>
