-- 团购券系统数据库变更

-- 1. 创建团购券表
CREATE TABLE `group_buying_coupon` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `platform_type` varchar(20) NOT NULL COMMENT '平台类型：douyin-抖音团购，meituan-美团团购，dianping-大众点评团购',
  `coupon_name` varchar(100) NOT NULL COMMENT '团购券名称',
  `coupon_description` text COMMENT '团购券描述',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `group_price` decimal(10,2) NOT NULL COMMENT '团购价',
  `discount_info` varchar(100) DEFAULT NULL COMMENT '优惠信息',
  `coupon_url` varchar(500) NOT NULL COMMENT '团购券链接',
  `coupon_id` varchar(100) DEFAULT NULL COMMENT '团购券ID（平台方提供）',
  `qr_code_url` varchar(500) DEFAULT NULL COMMENT '二维码链接',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `total_count` int(11) DEFAULT NULL COMMENT '总数量（NULL表示不限量）',
  `sold_count` int(11) DEFAULT '0' COMMENT '已售数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序（数字越大越靠前）',
  `remarks` text COMMENT '备注信息',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_platform_type` (`platform_type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团购券表';

-- 2. 为活动表添加团购券相关字段
ALTER TABLE `tb_activity` ADD COLUMN `show_group_buying` tinyint(1) DEFAULT 0 COMMENT '是否显示团购券：0-不显示，1-显示';
ALTER TABLE `tb_activity` ADD COLUMN `shop_description` varchar(100) DEFAULT '商户专属店铺' COMMENT '小店描述';

-- 3. 添加索引
ALTER TABLE `tb_activity` ADD INDEX `idx_show_group_buying` (`show_group_buying`);

-- 4. 创建团购券平台配置表（用于存储各平台的配置信息）
CREATE TABLE `group_buying_platform_config` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `platform_type` varchar(20) NOT NULL COMMENT '平台类型：douyin、meituan、dianping',
  `platform_name` varchar(50) NOT NULL COMMENT '平台名称',
  `icon_url` varchar(500) DEFAULT NULL COMMENT '平台图标URL',
  `url_scheme` varchar(100) DEFAULT NULL COMMENT 'APP跳转URL Scheme模板',
  `web_url_template` varchar(200) DEFAULT NULL COMMENT '网页跳转URL模板',
  `api_config` text COMMENT 'API配置信息（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_on` datetime NOT NULL COMMENT '创建时间',
  `update_on` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_platform_type` (`platform_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团购券平台配置表';

-- 5. 插入默认平台配置
INSERT INTO `group_buying_platform_config` (`id`, `platform_type`, `platform_name`, `icon_url`, `url_scheme`, `web_url_template`, `status`, `sort_order`, `create_on`) VALUES
(1, 'douyin', '抖音团购', 'https://pyp.yqihua.com/shop/static/icons/douyin-group.png', 'snssdk1128://groupon/detail?id={coupon_id}', 'https://www.douyin.com/groupon/{coupon_id}', 1, 1, NOW()),
(2, 'meituan', '美团团购', 'https://pyp.yqihua.com/shop/static/icons/meituan-group.png', 'imeituan://www.meituan.com/deal/{coupon_id}', 'https://www.meituan.com/deal/{coupon_id}', 1, 2, NOW()),
(3, 'dianping', '大众点评团购', 'https://pyp.yqihua.com/shop/static/icons/dianping-group.png', 'dianping://shopinfo?shopid={coupon_id}', 'https://www.dianping.com/shop/{coupon_id}', 1, 3, NOW());
