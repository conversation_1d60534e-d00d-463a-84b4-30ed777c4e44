<template>
    <div :class="isMobilePhone ? '' : 'pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <!-- <van-search v-model="dataForm.title" placeholder="请输入关键词搜索" show-action shape="round">
            <div slot="action" @click="onSearch" class="search-text">搜索</div>
        </van-search>

        <div style="margin-top: 8px" class="nav-title">
            <div class="color"></div>
            <div class="text">导航列表</div>
        </div> -->
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad"
            style="display: flex;flex-wrap: wrap;justify-content: space-between;">
            <div class="card" v-for="item in dataList" :key="item.id">
                <div class="header">
                    <div>{{ item.title }}</div>
                    <van-button @click.native="map(item.longitude,item.latitude)" size="small" round type="primary"
                        v-show="item.longitude != null && item.latitude != null">点击导航</van-button>
                </div>
                <div class="content">
                    <div v-html="item.content"></div>
                </div>
            </div>
        </van-list>
        <!-- 返回按钮 -->
        <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    </div>
</template>
  
<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
    components: { pcheader },
    data() {
        return {
            isMobilePhone: isMobilePhone(),
            activityInfo: {},
            flag: false,
            openid: undefined,
            activityId: undefined,
            dataForm: {
                title: "",
                status: 1,
            },
            loading: false,
            finished: false,
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
        };
    },
    mounted() {
        this.activityId = this.$route.query.id;
        this.openid = this.$cookie.get("openid");
        this.getActivityInfo();
    },
    methods: {
        onSearch() {
            this.pageIndex = 1;
            this.dataList = [];
            this.getActivityList();
        },
        onLoad() {
            if (!this.flag) {
                this.getActivityList();
            }
        },
        getActivityInfo() {
            this.$fly
                .get(`/pyp/activity/activity/info/${this.activityId}`)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.activityInfo = res.activity;
                        this.activityInfo.backImg =
                            this.activityInfo.backImg ||
                            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
                        document.title = this.activityInfo.name + "-导航";
                        let startTime = date.formatDate.format(
                            new Date(this.activityInfo.startTime),
                            "yyyy年MM月dd日"
                        );
                        let endTime = date.formatDate.format(
                            new Date(this.activityInfo.endTime),
                            "MM月dd日"
                        );
                        if (startTime.includes(endTime)) {
                            let desc =
                                "时间:" +
                                startTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                "展商列表-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        } else {
                            let desc =
                                "时间:" +
                                startTime +
                                "-" +
                                endTime +
                                "\n地址:" +
                                this.activityInfo.address;
                            this.$wxShare(
                                "展商列表-" + this.activityInfo.name,
                                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                                desc
                            ); //加载微信分享
                        }
                    } else {
                        vant.Toast(res.msg);
                        this.activityInfo = {};
                    }
                });
        },
        getActivityList() {
            this.flag = true;
            this.$fly
                .get("/pyp/web/activityNav/list", {
                    page: this.pageIndex,
                    limit: this.pageSize,
                    activityId: this.activityId,
                    title: this.dataForm.title,
                })
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.flag = false;
                        if (res.page.list && res.page.list.length > 0) {
                            res.page.list.forEach((e) => {
                                this.dataList.push(e);
                            });
                            this.totalPage = res.page.totalPage;
                            this.pageIndex++;
                            this.loading = false;
                            if (this.totalPage < this.pageIndex) {
                                this.finished = true;
                            } else {
                                this.finished = false;
                            }
                        } else {
                            this.finished = true;
                        }
                    } else {
                        vant.Toast(res.msg);
                        this.dataList = [];
                        this.totalPage = 0;
                        this.finished = true;
                    }
                });
        },
        //   turnDetail(item) {
        //       if(item.url) {
        //           location.href = item.url;
        //       } else {
        //       this.$router.push({
        //           name: 'merchantDetail',
        //           query: { merchantId: item.id, id: item.activityId },
        //         })

        //       }
        //   },
        cmsTurnBack() {
            if (this.activityInfo.backUrl) {
                window.open(this.activityInfo.backUrl);
            } else {
                this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
            }
        },
        map(longitude, latitude) {
            console.log(1)
            wx.openLocation({
                latitude: parseFloat(longitude), // 纬度，浮点数，范围为90 ~ -90
                longitude: parseFloat(latitude), // 经度，浮点数，范围为180 ~ -180。
                name: '导航目的地', // 位置名
                address: '导航目的地', // 地址详情说明
                scale: 20, // 地图缩放级别,整形值,范围从1~28。默认为最大
                infoUrl: 'https://weixin.qq.com' // 在查看位置界面底部显示的超链接,可点击跳转
            });
            // Toast("功能正在开发中..")
        },
        // 用经纬度设置地图中心点
        theLocation(longitude, latitude) {
            if (longitude != "" && latitude != "") {
                var center = new qq.maps.LatLng(longitude, latitude);
                var map = new qq.maps.Map(document.getElementById('allmap'), {
                    center: center,
                    zoom: 18
                });
                //创建marker
                var marker = new qq.maps.Marker({
                    position: center,
                    map: map
                });
            }
        }
    },
};
</script>
  
<style lang="less" scoped>
.card {
    width: 94%;
    margin-left: 3%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
    background-color: white;
    margin-bottom: 20px;
    margin-top: 20px;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
    }
    .content {
        padding: 0 20px 20px 20px;

    }
}
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;

  /deep/ p {
    width: 100%;
  }

  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-list /deep/ .van-list__finished-text {
  width: 100%;
  text-align: center;
  visibility: hidden;
}
</style>
  