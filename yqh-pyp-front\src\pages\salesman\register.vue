<template>
  <div :class="isMobilePhone ? 'page' : 'page pc-container'">
    <pcheader v-if="!isMobilePhone" />
    
    <!-- 页面标题 -->
    <van-nav-bar
      title="业务员注册"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 加载状态 -->
    <div v-if="checkingStatus" style="padding: 50px; text-align: center;">
      <van-loading size="24px" vertical>正在验证身份...</van-loading>
    </div>

    <!-- 主要内容 - 只有非业务员才显示 -->
    <div v-else-if="!isSalesman">
      <!-- 邀请信息 -->
      <van-card v-if="inviterInfo || channelInfo" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 20px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); color: white;">
        <div slot="title" style="padding-top: 10px;padding-left: 10px;font-size: 18px; font-weight: bold; color: white;">
          <van-icon :name="inviteType === 'salesman' ? 'user-o' : 'shop-o'" style="margin-right: 8px;" />
          邀请信息
        </div>
        <div slot="desc" style="padding: 10px;">
          <div class="invite-info" v-if="inviteType === 'salesman' && inviterInfo">
            <p>{{ inviterInfo.name }} 邀请您成为业务员</p>
            <p>手机号：{{ inviterInfo.mobile }}</p>
            <p>成为业务员后，您将获得推广权限和佣金收益</p>
          </div>
          <div class="invite-info" v-if="inviteType === 'channel' && channelInfo">
            <p>{{ channelInfo.name }} 邀请您成为业务员</p>
            <p>联系人：{{ channelInfo.contactName }}</p>
            <p v-if="channelInfo.contactMobile">联系电话：{{ channelInfo.contactMobile }}</p>
            <p>成为业务员后，您将获得推广权限和佣金收益</p>
          </div>
        </div>
      </van-card>

      <!-- 注册表单 -->
      <div style="margin: 20px;">
      <van-form @submit="onSubmit">
        <van-field
          v-model="form.name"
          name="name"
          label="姓名"
          placeholder="请输入您的姓名"
          :rules="[{ required: true, message: '请输入姓名' }]"
        />
        
        <van-field
          v-model="form.mobile"
          name="mobile"
          label="手机号"
          placeholder="请输入手机号"
          :rules="[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
          ]"
        />
        
        <van-field
          v-model="form.email"
          name="email"
          label="邮箱"
          placeholder="请输入邮箱（可选）"
        />
        
        <van-field
          v-model="form.department"
          name="department"
          label="部门"
          placeholder="请输入所属部门（可选）"
        />
        
        <van-field
          v-model="form.position"
          name="position"
          label="职位"
          placeholder="请输入职位（可选）"
        />

        <div style="margin: 30px 0;">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="submitting"
          >
            {{ submitting ? '注册中...' : '确认注册' }}
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 注册说明 -->
    <van-card style="background: white; margin: 20px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
      <div slot="title" style="padding-top: 10px;padding-left: 10px;font-size: 16px; font-weight: bold; color: #333;">
        <van-icon name="info-o" style="margin-right: 8px; color: #1989fa;" />
        业务员权益
      </div>
      <div slot="desc" style="padding: 10px;">
        <div class="benefits">
          <div class="benefit-item">
            <van-icon name="gold-coin-o" color="#ff976a" />
            <span>推广佣金：获得推广订单的佣金收益</span>
          </div>
          <div class="benefit-item">
            <van-icon name="friends-o" color="#07c160" />
            <span>团队建设：可以邀请下级业务员</span>
          </div>
          <div class="benefit-item">
            <van-icon name="chart-trending-o" color="#1989fa" />
            <span>业绩统计：实时查看个人和团队业绩</span>
          </div>
          <div class="benefit-item">
            <van-icon name="gift-o" color="#ee0a24" />
            <span>奖励机制：多种奖励和激励政策</span>
          </div>
        </div>
      </div>
    </van-card>
    </div>

    <!-- 成功注册弹窗 -->
    <van-dialog
      v-model="showSuccess"
      title="注册成功"
      :show-cancel-button="false"
      confirm-button-text="开始使用"
      @confirm="goToSalesmanCenter"
    >
      <div class="success-content">
        <van-icon name="success" size="60" color="#07c160" style="display: block; margin: 20px auto;" />
        <p>恭喜您成功注册为业务员！</p>
        <p>您现在可以开始推广并获得佣金收益了</p>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  isMobilePhone
} from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";

export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      inviterId: null,
      channelId: null,
      inviterInfo: null,
      channelInfo: null,
      inviteType: 'salesman', // 'salesman' 或 'channel'
      submitting: false,
      showSuccess: false,
      userInfo: null,
      isSalesman: false,
      checkingStatus: true,
      form: {
        name: '',
        mobile: '',
        email: '',
        department: '',
        position: ''
      }
    }
  },
  mounted() {
    document.title = "业务员注册";
    this.inviterId = this.$route.query.inviterId;
    this.channelId = this.$route.query.channelId;

    // 判断邀请类型
    if (this.inviterId) {
      this.inviteType = 'salesman';
    } else if (this.channelId) {
      this.inviteType = 'channel';
    } else {
      vant.Toast('邀请链接无效');
      this.$router.go(-1);
      return;
    }

    // 先获取用户信息，再加载邀请信息
    this.getUserInfo();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;

          // 自动填充用户信息
          this.fillUserInfo();

          // 检查业务员身份
          this.checkSalesmanStatus();

          // 加载邀请信息
          this.loadInviterInfo();
        } else {
        }
      }).catch((error) => {
        console.error("获取用户信息失败:", error);
        vant.Toast("获取用户信息失败，请重试");
      });
    },

    // 自动填充用户信息
    fillUserInfo() {
      if (this.userInfo) {
        this.form.name = this.userInfo.nickname || '';
        this.form.mobile = this.userInfo.mobile || '';
      }
    },

    // 检查业务员身份
    async checkSalesmanStatus() {
      try {
        const res = await this.$fly.get('/pyp/web/salesman/scanByAuth');

        if (res.code === 200) {
          // 如果接口返回成功，说明已经是业务员
          this.isSalesman = true;

          // 显示提示并跳转
          vant.Dialog.alert({
            title: '提示',
            message: '您已经是业务员，无需重复注册',
            confirmButtonText: '确定'
          }).then(() => {
            // 跳转到业务员页面
            this.$router.push('/salesman/qrcode');
          });
          return;
        } else {
          // 不是业务员，可以继续注册
          this.isSalesman = false;
        }
      } catch (error) {
        console.error('检查业务员状态失败:', error);
        // 如果接口调用失败，假设不是业务员，允许继续注册
        this.isSalesman = false;
      } finally {
        this.checkingStatus = false;
      }
    },

    // 加载邀请人信息
    async loadInviterInfo() {
      try {
        if (this.inviteType === 'salesman') {
          const res = await this.$fly.get(`/pyp/web/salesman/info/${this.inviterId}`);

          if (res.code === 200) {
            this.inviterInfo = res.result;
          } else {
            vant.Toast('获取邀请人信息失败');
          }
        } else if (this.inviteType === 'channel') {
          const res = await this.$fly.get(`/pyp/web/channel/info/${this.channelId}`);

          if (res.code === 200) {
            this.channelInfo = res.result;
          } else {
            vant.Toast('获取渠道信息失败');
          }
        }
      } catch (error) {
        console.error('获取邀请信息失败:', error);
        vant.Toast('获取邀请信息失败');
      }
    },

    // 提交注册
    async onSubmit() {
      try {
        this.submitting = true;

        let requestData = {
          name: this.form.name,
          mobile: this.form.mobile,
          email: this.form.email,
          department: this.form.department,
          position: this.form.position
        };

        let apiUrl;
        if (this.inviteType === 'salesman') {
          apiUrl = '/pyp/web/salesman/processInvite';
          requestData.inviterId = this.inviterId;
        } else if (this.inviteType === 'channel') {
          apiUrl = '/pyp/web/salesman/processChannelInvite';
          requestData.channelId = this.channelId;
        }

        const res = await this.$fly.get(apiUrl, requestData);

        if (res.code === 200) {
          this.showSuccess = true;
        } else {
          vant.Toast(res.msg || '注册失败');
        }
      } catch (error) {
        console.error('注册失败:', error);
        vant.Toast('注册失败，请重试');
      } finally {
        this.submitting = false;
      }
    },

    // 跳转到业务员中心
    goToSalesmanCenter() {
      this.$router.replace({
        name: 'salesmanQrcode'
      });
    }
  }
}
</script>

<style lang="less" scoped>
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.invite-info {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.invite-info p {
  margin: 8px 0;
}

.benefits {
  padding: 10px 0;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #333;
}

.benefit-item:last-child {
  margin-bottom: 0;
}

.benefit-item .van-icon {
  margin-right: 10px;
  font-size: 18px;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-content p {
  margin: 10px 0;
  font-size: 14px;
  color: #666;
}
</style>
