/**
 * 文件大小格式化工具
 * 统一将文件大小格式化为MB单位，保留2位小数
 */

/**
 * 格式化文件大小为MB单位
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串，如 "1.25 MB"
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0.00 MB'
  const mb = bytes / (1024 * 1024)
  return mb.toFixed(2) + ' MB'
}

/**
 * 通过HTTP HEAD请求获取文件大小
 * @param {string} url - 文件URL
 * @returns {Promise<number>} 文件大小（字节）
 */
export function getFileSizeFromUrl(url) {
  return fetch(url, { method: 'HEAD' })
    .then(response => {
      const contentLength = response.headers.get('Content-Length')
      return contentLength ? parseInt(contentLength) : 0
    })
    .catch(err => {
      console.warn('无法获取文件大小:', err)
      return 0
    })
}

/**
 * 批量获取多个文件的大小
 * @param {string[]} urls - 文件URL数组
 * @returns {Promise<Object[]>} 包含URL和文件大小的对象数组
 */
export function getBatchFileSizes(urls) {
  const promises = urls.map(url => 
    getFileSizeFromUrl(url).then(size => ({ url, size }))
  )
  return Promise.all(promises)
}

/**
 * 文件大小验证
 * @param {number} bytes - 文件大小（字节）
 * @param {number} maxMB - 最大允许大小（MB）
 * @returns {boolean} 是否在允许范围内
 */
export function validateFileSize(bytes, maxMB = 2) {
  const mb = bytes / (1024 * 1024)
  return mb <= maxMB
}

/**
 * 获取文件大小的描述信息
 * @param {number} bytes - 文件大小（字节）
 * @returns {Object} 包含各种单位的文件大小信息
 */
export function getFileSizeInfo(bytes) {
  if (!bytes || bytes === 0) {
    return {
      bytes: 0,
      kb: 0,
      mb: 0,
      gb: 0,
      formatted: '0.00 MB',
      description: '空文件'
    }
  }

  const kb = bytes / 1024
  const mb = bytes / (1024 * 1024)
  const gb = bytes / (1024 * 1024 * 1024)

  let description = ''
  if (mb < 0.01) {
    description = '很小的文件'
  } else if (mb < 1) {
    description = '小文件'
  } else if (mb < 10) {
    description = '中等文件'
  } else if (mb < 100) {
    description = '大文件'
  } else {
    description = '很大的文件'
  }

  return {
    bytes: bytes,
    kb: parseFloat(kb.toFixed(2)),
    mb: parseFloat(mb.toFixed(2)),
    gb: parseFloat(gb.toFixed(2)),
    formatted: formatFileSize(bytes),
    description: description
  }
}

// 默认导出主要的格式化函数
export default formatFileSize
