package com.cjy.pyp.modules.activity.controller;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理端文案信息接口
 */
@RestController
@RequestMapping("activity/text")
public class ActivityTextInfoController extends AbstractController {
    
    @Autowired
    private ActivityTextService activityTextService;

    /**
     * 获取文案信息
     */
    @GetMapping("/info")
    public R getTextInfo(@RequestParam("textId") Long textId) {
        try {
            ActivityTextEntity textEntity = activityTextService.getById(textId);
            
            if (textEntity == null) {
                return R.error("文案不存在");
            }
            
            Map<String, Object> textInfo = new HashMap<>();
            textInfo.put("id", textEntity.getId());
            textInfo.put("title", textEntity.getTitle()); // 提示词字段
            textInfo.put("content", textEntity.getResult()); // 文案内容字段
            textInfo.put("topics", ""); // 话题字段暂时为空，需要从result中解析
            textInfo.put("promptKeyword", textEntity.getQuery()); // 自定义输入作为提示词
            textInfo.put("adType", textEntity.getAdType());
            textInfo.put("createOn", textEntity.getCreateOn());
            textInfo.put("name", textEntity.getName()); // 标题字段
            
            return R.ok().put("textInfo", textInfo);
            
        } catch (Exception e) {
            return R.error("获取文案信息失败: " + e.getMessage());
        }
    }
}
