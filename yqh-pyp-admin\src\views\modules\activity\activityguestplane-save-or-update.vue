<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="联系人姓名" prop="name">
        <el-autocomplete style="width: 100%;" v-model="dataForm.name" :fetch-suggestions="search" label="name"
          placeholder="请输入内容" @select="selectRsult">
          <div slot-scope="scope">
            <span style="float: left">{{ scope.item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ scope.item.unit }}</span>
          </div>
        </el-autocomplete></el-form-item>
      <el-form-item label="联系人电话" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="联系人电话"></el-input>
      </el-form-item>
      <el-form-item label="工作单位" prop="unit">
        <el-input v-model="dataForm.unit" placeholder="工作单位"></el-input>
      </el-form-item>
      <el-form-item label="来程类型" prop="inType">
        <el-select v-model="dataForm.inType" placeholder="来程类型" filterable>
          <el-option v-for="item in guestGoType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="来程日期" prop="inDate">
        <el-date-picker v-model="dataForm.inDate" style="width: 100%" type="date"
          value-format="yyyy/MM/dd"  placeholder="开始日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="来程航班/火车号" prop="inNumber">
        <el-input v-model="dataForm.inNumber" placeholder="来程航班/火车号"></el-input>
      </el-form-item>
      <el-form-item label="返程类型" prop="outType">
        <el-select v-model="dataForm.outType" placeholder="返程类型" filterable>
          <el-option v-for="item in guestGoType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="返程日期" prop="outDate">
        <el-date-picker v-model="dataForm.outDate" style="width: 100%" type="date"
          value-format="yyyy/MM/dd"  placeholder="开始日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="返程航班/火车号" prop="outNumber">
        <el-input v-model="dataForm.outNumber" placeholder="返程航班/火车号"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
import { guestGoType } from '@/data/activity'
export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
  },
  data() {
    return {
      guestGoType,
      visible: false,
      url: "",
      loading: false,
      searchResult: [],
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        mobile: "",
        unit: "",
        wxUserId: "",
        inType: "",
        inDate: "",
        inNumber: "",
        outType: "",
        outDate: "",
        outNumber: "",
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "联系人姓名不能为空", trigger: "blur" },
        ],
      },
      timeout: null,
    };
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.content = "";
      this.dataForm.id = id || 0;
      this.visible = true;
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.activityId = data.activityGuest.activityId;
              this.dataForm.name = data.activityGuest.name;
              this.dataForm.mobile = data.activityGuest.mobile;
              this.dataForm.unit = data.activityGuest.unit;
              this.dataForm.wxUserId = data.activityGuest.wxUserId;
              this.dataForm.inType = data.activityGuest.inType;
              this.dataForm.inDate = data.activityGuest.inDate;
              this.dataForm.inNumber = data.activityGuest.inNumber;
              this.dataForm.outType = data.activityGuest.outType;
              this.dataForm.outDate = data.activityGuest.outDate;
              this.dataForm.outNumber = data.activityGuest.outNumber;
              this.dataForm.isSave = false;
            }
          });
        }
      });
    },
    search(query, cb) {
      // if(!this.dataForm.name) {
      //   this.$message.error("请输入姓名后查找");
      //   return false;
      // }
      if (query !== '') {
        this.loading = true;
        this.$http({
          url: this.$http.adornUrl(
            `/activity/guest/findByName`
          ),
          method: "get",
          params: this.$http.adornParams({
            name: query,
          }),
        }).then(({ data }) => {
          this.loading = false;
          if (data && data.code === 200) {
            // if(data.result.length == 1) {
            //   this.dataForm.name = data.result[0].name;
            //   this.dataForm.mobile = data.result[0].mobile;
            //   this.dataForm.unit = data.result[0].unit;
            //   this.dataForm.duties = data.result[0].duties;
            //   this.dataForm.wxUserId = data.result[0].wxUserId;
            //   this.dataForm.avatar = data.result[0].avatar;
            //   this.dataForm.content = data.result[0].content;
            //   this.dataForm.orderBy = data.result[0].orderBy;
            //   this.dataForm.idCardZheng = data.result[0].idCardZheng;
            //   this.dataForm.idCardFan = data.result[0].idCardFan;
            //   this.dataForm.bank = data.result[0].bank;
            //   this.dataForm.kaihuhang = data.result[0].kaihuhang;
            // } else {
            this.searchResult = data.result;
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
              cb(this.searchResult);
            }, 100 * Math.random());
            // }
          } else {
            this.$message.error(data.msg)
          }
        });
      }
    },
    createStateFilter(queryString) {
      return (state) => {
        return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    selectRsult(result) {
      this.dataForm.name = result.name;
      this.dataForm.mobile = result.mobile;
      this.dataForm.unit = result.unit;
      this.dataForm.duties = result.duties;
      this.dataForm.wxUserId = result.wxUserId;
      this.dataForm.avatar = result.avatar;
      this.dataForm.content = result.content;
      this.dataForm.orderBy = result.orderBy;
      this.dataForm.idCardZheng = result.idCardZheng;
      this.dataForm.idCardFan = result.idCardFan;
      this.dataForm.bank = result.bank;
      this.dataForm.kaihuhang = result.kaihuhang;
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activityguest/${!this.dataForm.id ? "save" : "update"}`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              activityId: this.dataForm.activityId,
              name: this.dataForm.name,
              mobile: this.dataForm.mobile,
              unit: this.dataForm.unit,
              inType: this.dataForm.inType,
              inDate: this.dataForm.inDate,
              inNumber: this.dataForm.inNumber,
              outType: this.dataForm.outType,
              outDate: this.dataForm.outDate,
              outNumber: this.dataForm.outNumber,
              wxUserId: this.dataForm.wxUserId,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else if (data.code == 405) {
              this.$confirm(data.msg, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }).then(() => {
                this.dataForm.isSave = true;
                this.dataFormSubmit();
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    // 上传之前
    // 上传之前
    checkFileSize: function (file) {
      if (file.size / 1024 / 1024 > 6) {
        this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
        return false
      }
      if (file.size / 1024 > 100) {
        // 100kb不压缩
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            
            success(result) {
              resolve(result)
            }
          })
        })
      }
      return true
    },
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.avatar = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    // idCardZheng公众号轮播图上传成功
    idCardZhengSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.idCardZheng = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    // idCardFan公众号轮播图上传成功
    idCardFanSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.idCardFan = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
  },
};
</script>
