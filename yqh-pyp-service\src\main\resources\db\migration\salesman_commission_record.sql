-- 业务员佣金记录表
CREATE TABLE `salesman_commission_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `salesman_id` bigint(20) NOT NULL COMMENT '业务员ID',
  `wx_user_id` bigint(20) DEFAULT NULL COMMENT '微信用户ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `order_type` tinyint(4) NOT NULL COMMENT '订单类型：1-充值订单，2-活动订单，3-转发操作',
  `commission_config_id` bigint(20) NOT NULL COMMENT '佣金配置ID',
  `commission_type` tinyint(4) NOT NULL COMMENT '佣金类型：1-充值佣金，2-活动佣金，3-转发佣金',
  `calculation_type` tinyint(4) NOT NULL COMMENT '计算方式：1-按比例，2-固定金额',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例（%）',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际佣金金额（扣除限制后）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-待结算，2-已结算，3-已取消',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settlement_batch` varchar(50) DEFAULT NULL COMMENT '结算批次号',
  `description` varchar(500) DEFAULT NULL COMMENT '描述说明',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `appid` varchar(50) NOT NULL COMMENT '应用ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_salesman_id` (`salesman_id`),
  KEY `idx_wx_user_id` (`wx_user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_appid` (`appid`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务员佣金记录表';

-- 为订单表添加业务员ID字段
ALTER TABLE `activity_recharge_record` 
ADD COLUMN `salesman_id` bigint(20) DEFAULT NULL COMMENT '关联业务员ID' AFTER `wx_user_id`,
ADD KEY `idx_salesman_id` (`salesman_id`);
