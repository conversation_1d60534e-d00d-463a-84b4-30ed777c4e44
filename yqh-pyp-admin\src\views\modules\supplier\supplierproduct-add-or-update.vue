<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="name">
              <el-input v-model="dataForm.name" placeholder="产品名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="描述" prop="brief">
              <el-input v-model="dataForm.brief" placeholder="描述"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成本价" prop="price">
              <el-input v-model="dataForm.price" placeholder="描述"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对外售价" prop="sellPrice">
              <el-input v-model="dataForm.sellPrice" placeholder="描述"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="dataForm.unit" filterable>
                <el-option v-for="item in productUnit" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplierId">
              <div style="display: flex">
                <el-select v-model="dataForm.supplierId" filterable>
                  <el-option v-for="item in suppliers" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-button type="text" @click="supplierAddHandle">快速新增</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类别" prop="productTypeId">
              <el-select v-model="dataForm.productTypeId" filterable>
                <el-option v-for="item in productTypes" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图片" prop="picUrl">
              <el-upload class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                accept=".jpg, .jpeg, .png, .gif" :on-success="appSuccessHandle" :action="url">
                <img width="100px" v-if="dataForm.picUrl" :src="dataForm.picUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="内容" prop="content">
          <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :loading="loading">确定</el-button>
      </span>
    </el-dialog>
    <supplieradd v-if="supplieraddVisible" ref="supplieradd" @refreshDataList="findSupplier"></supplieradd>
  </div>
</template>

<script>
import supplieradd from './supplier-add-or-update'
export default {
  components: {
    TinymceEditor: () => import("@/components/tinymce-editor"),
    tagsEditor: () => import("@/components/tags-editor"),
    OssUploader: () => import('../oss/oss-uploader'),
    supplieradd,
  },
  data() {
    return {
      company: {},
      contractPriceConfig: [],
      supplieraddVisible: false,
      loading: false,
      visible: false,
      url: '',
      productUnit: [],
      dataForm: {
        id: 0,
        name: '',
        brief: '',
        content: '',
        picUrl: '',
        supplierId: '',
        productTypeId: '',
        productType: 1,
        inTaxRate: 0,
        outTaxRate: 0,
        unit: '',
        price: '',
        sellPrice: '',
        repeatToken: '',
      },
      suppliers: [],
      productTypes: [],
      dataRule: {
        name: [
          { required: true, message: '产品名称不能为空', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '供应商id不能为空', trigger: 'blur' }
        ],
        contractPriceConfigId: [
          { required: true, message: '产品科目不能为空', trigger: 'blur' }
        ],
        contractPriceConfigPid: [
          { required: true, message: '产品科目不能为空', trigger: 'blur' }
        ],
        // productTypeId: [
        //   { required: true, message: '产品类别不能为空', trigger: 'blur' }
        // ],
        productType: [
          { required: true, message: '产品类型不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    supplierAddHandle() {
      this.supplieraddVisible = true
      this.$nextTick(() => {
        this.$refs.supplieradd.init()
      })
    },
    init(id, productType, supplierId) {
      this.dataForm.id = id || 0
      this.url = this.$http.adornUrl(
        `/sys/oss/upload?token=${this.$cookie.get("token")}`
      );
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/supplier/supplierproduct/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.supplierProduct.name
              this.dataForm.brief = data.supplierProduct.brief
              this.dataForm.content = data.supplierProduct.content
              this.dataForm.picUrl = data.supplierProduct.picUrl
              this.dataForm.supplierId = data.supplierProduct.supplierId
              this.dataForm.productType = data.supplierProduct.productType
              this.dataForm.price = data.supplierProduct.price
              this.dataForm.productTypeId = data.supplierProduct.productTypeId
              this.dataForm.sellPrice = data.supplierProduct.sellPrice
              this.dataForm.unit = data.supplierProduct.unit
              this.dataForm.inTaxRate = data.supplierProduct.inTaxRate
              this.dataForm.outTaxRate = data.supplierProduct.outTaxRate
              this.dataForm.contractPriceConfigId = data.supplierProduct.contractPriceConfigId
              this.dataForm.contractPriceConfigPid = data.supplierProduct.contractPriceConfigPid
            }
          })
        } else {
          this.dataForm.productType = productType
          this.dataForm.supplierId = supplierId
        }
      })
      this.getResult();
      this.findSupplier();
      this.findProductType();
      this.getToken();
    },
    getResult() {
      this.$http({
        url: this.$http.adornUrl(`/sys/config/findOnlyParamKey`),
        method: "get",
        params: this.$http.adornParams({
          paramKey: 'productUnit',
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.productUnit = data.result.paramValue ? data.result.paramValue.split(',') : [];
        }
      });
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    findSupplier(v) {
      this.$http({
        url: this.$http.adornUrl(`/supplier/supplier/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.suppliers = data.result;
          if (v) {
            this.dataForm.supplierId = v;
            this.getToken();
          }
        }
      })
    },
    findProductType() {
      this.$http({
        url: this.$http.adornUrl(`/supplier/producttype/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.productTypes = data.result;
        }
      })
    },
    imgUploadSuccess(response, file, fileList) {
      console.log(response);
      if (response.code == 200) {
        this.dataForm.picUrl = response.data;
        console.log("this.article", this.article);
      } else {
        this.$message.warning(response.msg);
      }
    },
    // 上传之前
    beforeUploadHandle(file) {
      if (
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "image/png" &&
        file.type !== "image/gif"
      ) {
        this.$message.error("只支持jpg、png、gif格式的图片！");
        return false;
      }
    },
    // app公众号轮播图上传成功
    appSuccessHandle(response, file, fileList) {
      if (response && response.code === 200) {
        this.dataForm.picUrl = response.url;
      } else {
        this.$message.error(response.msg);
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: this.$http.adornUrl(`/supplier/supplierproduct/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'brief': this.dataForm.brief,
              'content': this.dataForm.content,
              'picUrl': this.dataForm.picUrl,
              'supplierId': this.dataForm.supplierId,
              'productTypeId': this.dataForm.productTypeId,
              'productType': this.dataForm.productType,
              'price': this.dataForm.price,
              'sellPrice': this.dataForm.sellPrice,
              'unit': this.dataForm.unit,
              'inTaxRate': this.dataForm.inTaxRate,
              'outTaxRate': this.dataForm.outTaxRate,
              'contractPriceConfigId': this.dataForm.contractPriceConfigId,
              'contractPriceConfigPid': this.dataForm.contractPriceConfigPid,
              'repeatToken': this.dataForm.repeatToken,
              appid: this.$cookie.get("appid"),
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.result)
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
            this.loading = false;
          })
        }
      })
    }
  }
}
</script>
