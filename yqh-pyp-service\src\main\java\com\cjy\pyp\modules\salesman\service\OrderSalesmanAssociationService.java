package com.cjy.pyp.modules.salesman.service;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * 订单业务员关联服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public interface OrderSalesmanAssociationService {

    /**
     * 为充值订单关联业务员
     * 在创建充值记录时调用，自动关联用户绑定的业务员
     * 
     * @param rechargeRecordId 充值记录ID
     * @param userId 用户ID
     * @param appid 应用ID
     * @return 关联的业务员ID，如果没有绑定业务员则返回null
     */
    Long associateSalesmanForRechargeOrder(Long rechargeRecordId, Long userId, String appid);

    /**
     * 根据用户ID查找关联的业务员ID
     * 
     * @param userId 用户ID（微信用户ID）
     * @param appid 应用ID
     * @return 业务员ID，如果没有绑定则返回null
     */
    Long findSalesmanByUser(Long userId, String appid);

    /**
     * 手动为订单关联业务员
     * 
     * @param rechargeRecordId 充值记录ID
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 是否关联成功
     */
    boolean manualAssociateSalesman(Long rechargeRecordId, Long salesmanId, String appid);

    /**
     * 取消订单的业务员关联
     * 
     * @param rechargeRecordId 充值记录ID
     * @param appid 应用ID
     * @return 是否取消成功
     */
    boolean disassociateSalesman(Long rechargeRecordId, String appid);

    /**
     * 批量为历史订单关联业务员
     * 根据当前的用户业务员绑定关系，为历史订单补充业务员信息
     * 
     * @param appid 应用ID
     * @return 关联的订单数量
     */
    Integer batchAssociateHistoricalOrders(String appid);

    /**
     * 验证业务员关联的有效性
     * 检查订单关联的业务员是否与用户当前绑定的业务员一致
     *
     * @param rechargeRecordId 充值记录ID
     * @param appid 应用ID
     * @return 验证结果消息，null表示验证通过
     */
    String validateSalesmanAssociation(Long rechargeRecordId, String appid);

    /**
     * 查询订单列表
     * @param params 查询参数
     * @return 分页结果
     */
    List<ActivityRechargeRecordEntity> queryOrderList(Map<String, Object> params);

    /**
     * 获取关联统计数据
     * @param params 查询参数（包含appid和筛选条件）
     * @return 统计数据
     */
    Map<String, Object> getAssociationStats(Map<String, Object> params);

    /**
     * 关联业务员
     * @param rechargeRecordId 订单ID
     * @param salesmanId 业务员ID
     * @param reason 关联原因
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean associateSalesman(Long rechargeRecordId, Long salesmanId, String reason, String appid);

    /**
     * 更换业务员
     * @param rechargeRecordId 订单ID
     * @param salesmanId 新业务员ID
     * @param reason 更换原因
     * @param appid 应用ID
     * @return 是否成功
     */
    boolean changeSalesman(Long rechargeRecordId, Long salesmanId, String reason, String appid);

    /**
     * 验证订单关联
     * @param rechargeRecordId 订单ID
     * @param appid 应用ID
     * @return 验证结果消息，null表示验证通过
     */
    String validateOrderAssociation(Long rechargeRecordId, String appid);

    /**
     * 批量验证一致性
     * @param appid 应用ID
     * @return 不一致的数量
     */
    Integer batchValidateConsistency(String appid);

    /**
     * 批量修复不一致
     * @param appid 应用ID
     * @return 修复的数量
     */
    Integer batchRepairInconsistency(String appid);

    /**
     * 处理业务员关联变更后的佣金调整
     * @param rechargeRecordId 充值记录ID
     * @param oldSalesmanId 原业务员ID（可为null）
     * @param newSalesmanId 新业务员ID（可为null）
     * @param reason 变更原因
     * @param appid 应用ID
     * @return 是否处理成功
     */
    boolean handleCommissionAdjustment(Long rechargeRecordId, Long oldSalesmanId, Long newSalesmanId, String reason, String appid);
}
