<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="onSearch()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="视频名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="onSearch()">查询</el-button>
        <el-button v-if="isAuth('activity:activityvideo:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityvideo:save')" type="primary"
          @click="generateVideoHandle()">生成混剪视频</el-button>
        <el-button v-if="isAuth('activity:activityvideo:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>


    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" width="200" label="视频名称">
      </el-table-column>
      <!-- <el-table-column prop="mediaUrl" header-align="center" align="center" width="120" label="视频预览">
        <template slot-scope="scope">
          <div v-if="scope.row.mediaUrl" class="video-preview">
            <video :src="scope.row.mediaUrl" width="120" height="120" controls preload="metadata"
              @click="previewVideo(scope.row.mediaUrl)" class="preview-video"></video>
          </div>
          <span v-else class="no-video">无视频</span>
        </template>
      </el-table-column> -->
      <el-table-column prop="fileSize" header-align="center" align="center"  label="文件大小">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
      <el-table-column prop="duration" header-align="center" align="center"  label="时长">
        <template slot-scope="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="fileId" header-align="center" align="center"  label="文件ID">
      </el-table-column>
      <el-table-column prop="frameRate" header-align="center" align="center" label="帧率">
      </el-table-column> -->
      <!-- <el-table-column prop="paixu" header-align="center" align="center" label="排序">
      </el-table-column> -->
      <el-table-column prop="useCount" header-align="center" align="center"  label="使用次数">
      </el-table-column>
      <el-table-column prop="createOn" :show-overflow-tooltip="true" header-align="center" align="center" width="150"
        label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" :show-overflow-tooltip="true" header-align="center" align="center" width="150"
        label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="220" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="previewVideo(scope.row.mediaUrl)"
            :disabled="!scope.row.mediaUrl">
            <i class="el-icon-video-play"></i> 预览
          </el-button>
          <el-button type="text" size="small" @click="viewRelatedVideos(scope.row)"
            v-if="scope.row.type === 1">
            <i class="el-icon-connection"></i> 关联素材
          </el-button>
          <el-button type="text" size="small" @click="viewRelatedVideos(scope.row)"
            v-if="scope.row.type === 0">
            <i class="el-icon-connection"></i> 关联成品
          </el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <!-- 视频预览弹窗 -->
    <el-dialog :visible.sync="previewVisible" width="70%" append-to-body title="视频预览">
      <div style="text-align: center;">
      <video style="height: 800px;" :src="previewVideoUrl" controls autoplay >
        您的浏览器不支持视频播放
      </video></div>
    </el-dialog>

    <!-- 关联视频查看弹窗 -->
    <el-dialog :visible.sync="relatedVideosVisible" width="90%" append-to-body :title="relatedVideosTitle">
      <el-table :data="flattenedRelatedList" border v-loading="relatedVideosLoading" style="width: 100%;">
        <!-- 类型 -->
        <el-table-column header-align="center" align="center" width="80" label="类型">
          <template slot-scope="scope">
            <el-tag :type="scope.row.itemType === 'video' ? 'primary' : 'success'" size="small">
              {{ scope.row.itemType === 'video' ? '视频' : '图片' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 预览 -->
        <el-table-column header-align="center" align="center" width="100" label="预览">
          <template slot-scope="scope">
            <div v-if="scope.row.itemType === 'video'" class="video-preview">
              <video width="60" height="40" :src="scope.row.mediaUrl" style="object-fit: cover; border-radius: 4px;">
                您的浏览器不支持视频播放
              </video>
            </div>
            <div v-else class="image-preview">
              <img :src="scope.row.mediaUrl" :alt="scope.row.name"
                @click="previewImage(scope.row.mediaUrl)" class="preview-img" />
            </div>
          </template>
        </el-table-column>

        <!-- 名称 -->
        <el-table-column header-align="center" align="center" width="200" label="名称">
          <template slot-scope="scope">
            {{ scope.row.name || '未知' }}
          </template>
        </el-table-column>

        <!-- 文件大小 -->
        <el-table-column header-align="center" align="center" label="文件大小">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>

        <!-- 时长/尺寸 -->
        <el-table-column header-align="center" align="center" label="时长/尺寸">
          <template slot-scope="scope">
            <span v-if="scope.row.itemType === 'video'">{{ formatDuration(scope.row.duration) }}</span>
            <span v-else>{{ scope.row.width }}×{{ scope.row.height }}</span>
          </template>
        </el-table-column>

        <!-- 使用次数 -->
        <el-table-column header-align="center" align="center" label="使用次数">
          <template slot-scope="scope">
            {{ scope.row.useCount || 0 }}
          </template>
        </el-table-column>

        <!-- 排序 -->
        <el-table-column prop="paixu" header-align="center" align="center" width="80" label="排序">
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column header-align="center" align="center" width="150" label="创建时间">
          <template slot-scope="scope">
            {{ scope.row.createOn }}
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column fixed="right" header-align="center" align="center" width="120" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="previewMedia(scope.row)"
              :disabled="!scope.row.mediaUrl">
              <i :class="scope.row.itemType === 'video' ? 'el-icon-video-play' : 'el-icon-view'"></i>
              {{ scope.row.itemType === 'video' ? '预览' : '查看' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="flattenedRelatedList.length === 0 && !relatedVideosLoading" class="empty-state">
        <i class="el-icon-video-camera-solid"></i>
        <p>暂无关联内容</p>
      </div>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="imagePreviewVisible" width="60%" append-to-body title="图片预览">
      <img width="100%" :src="previewImageUrl" alt="图片预览">
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './activityvideo-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        name: '',
        appid: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activityId: '',
      previewVisible: false,
      previewVideoUrl: '',
      activeTab: 'material', // 固定为素材视频
      // 关联视频相关
      relatedVideosVisible: false,
      relatedVideosList: [],
      flattenedRelatedList: [], // 扁平化的关联列表（包含视频和图片）
      relatedVideosLoading: false,
      relatedVideosTitle: '',
      currentVideo: null,
      // 图片预览相关
      imagePreviewVisible: false,
      previewImageUrl: ''
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.activityId = this.$route.query.activityId || ''
    this.getDataList()
  },
  methods: {
    generateVideoHandle() {
      // 直接调用接口
      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/submitVideoEdit'),
        method: 'get',
        params: this.$http.adornParams({
          activityId: this.activityId,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg || '操作失败')
        }
      })
    },
    onSearch() {
      this.pageIndex = 1;
      this.getDataList();
    },

    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      // 素材视频固定type为0
      const type = 0

      this.$http({
        url: this.$http.adornUrl('/activity/activityvideo/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'activityId': this.activityId,
          'appid': this.$cookie.get('appid'),
          'type': type // 添加type参数来过滤
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        // 素材视频固定type为0
        const defaultType = 0
        this.$refs.addOrUpdate.init(id, this.activityId, defaultType)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityvideo/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 预览视频
    previewVideo(url) {
      if (!url) {
        this.$message.warning('视频地址不存在')
        return
      }
      this.previewVideoUrl = url
      this.previewVisible = true
    },

    // 查看关联视频
    viewRelatedVideos(video) {
      this.currentVideo = video
      this.relatedVideosVisible = true
      this.relatedVideosLoading = true

      // 根据当前视频类型确定要查找的关联类型和标题
      if (video.type === 0) {
        // 素材视频获取关联的成品视频
        this.relatedVideosTitle = '关联成品视频'
        this.getFinishedVideosByMaterial(video.id)
      } else {
        // 成品视频获取关联的素材视频
        this.relatedVideosTitle = '关联素材视频'
        this.getMaterialVideosByFinished(video.id)
      }
    },

    // 成品视频获取关联的素材视频
    getMaterialVideosByFinished(activityVideoId) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activityvideoconnect/findByConnectActivityVideoId/${activityVideoId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.relatedVideosLoading = false
        if (data && data.code === 200) {
          // 直接使用返回的连接关系数据，包含了完整的视频和图片信息
          this.relatedVideosList = data.result || []
          // 生成扁平化列表
          this.generateFlattenedList()
        } else {
          this.relatedVideosList = []
          this.flattenedRelatedList = []
          this.$message.error(data.msg || '获取关联素材视频失败')
        }
      }).catch((error) => {
        this.relatedVideosLoading = false
        console.error('获取关联素材视频失败:', error)
        this.$message.error('获取关联素材视频失败')
        this.relatedVideosList = []
        this.flattenedRelatedList = []
      })
    },

    // 素材视频获取关联的成品视频
    getFinishedVideosByMaterial(connectActivityVideoId) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activityvideoconnect/findByActivityVideoId/${connectActivityVideoId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.relatedVideosLoading = false
        if (data && data.code === 200) {
          // 直接使用返回的连接关系数据，包含了完整的视频信息
          this.relatedVideosList = data.result || []
          // 生成扁平化列表
          this.generateFlattenedList()
        } else {
          this.relatedVideosList = []
          this.flattenedRelatedList = []
          this.$message.error(data.msg || '获取关联成品视频失败')
        }
      }).catch((error) => {
        this.relatedVideosLoading = false
        console.error('获取关联成品视频失败:', error)
        this.$message.error('获取关联成品视频失败')
        this.relatedVideosList = []
        this.flattenedRelatedList = []
      })
    },

    // 格式化文件大小 - 固定显示为MB，保留2位小数
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    },

    // 格式化视频时长
    formatDuration(seconds) {
      if (!seconds || seconds === 0) return '0秒'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else if (minutes > 0) {
        return `${minutes}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${secs}秒`
      }
    },

    // 从连接关系数据中获取视频名称
    getVideoName(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的名称
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.name : '未知视频'
      } else {
        // 素材视频查看成品：返回targetVideo的名称
        return connectionRow.targetVideo ? connectionRow.targetVideo.name : '未知视频'
      }
    },

    // 从连接关系数据中获取视频URL
    getVideoUrl(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的URL
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.mediaUrl : ''
      } else {
        // 素材视频查看成品：返回targetVideo的URL
        return connectionRow.targetVideo ? connectionRow.targetVideo.mediaUrl : ''
      }
    },

    // 从连接关系数据中获取文件大小
    getVideoFileSize(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的文件大小
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.fileSize : 0
      } else {
        // 素材视频查看成品：返回targetVideo的文件大小
        return connectionRow.targetVideo ? connectionRow.targetVideo.fileSize : 0
      }
    },

    // 从连接关系数据中获取视频时长
    getVideoDuration(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的时长
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.duration : 0
      } else {
        // 素材视频查看成品：返回targetVideo的时长
        return connectionRow.targetVideo ? connectionRow.targetVideo.duration : 0
      }
    },

    // 从连接关系数据中获取使用次数
    getVideoUseCount(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的使用次数
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.useCount : 0
      } else {
        // 素材视频查看成品：返回targetVideo的使用次数
        return connectionRow.targetVideo ? connectionRow.targetVideo.useCount : 0
      }
    },

    // 从连接关系数据中获取创建时间
    getVideoCreateTime(connectionRow) {
      if (this.currentVideo && this.currentVideo.type === 1) {
        // 成品视频查看素材：返回sourceVideo的创建时间
        return connectionRow.sourceVideo ? connectionRow.sourceVideo.createOn : ''
      } else {
        // 素材视频查看成品：返回targetVideo的创建时间
        return connectionRow.targetVideo ? connectionRow.targetVideo.createOn : ''
      }
    },

    // 生成扁平化的关联列表（包含视频和图片）
    generateFlattenedList() {
      this.flattenedRelatedList = []

      this.relatedVideosList.forEach(connection => {
        // 添加视频信息
        let video = null
        if (this.currentVideo && this.currentVideo.type === 1) {
          // 成品视频查看素材：使用sourceVideo
          video = connection.sourceVideo
        } else {
          // 素材视频查看成品：使用targetVideo
          video = connection.targetVideo
        }

        if (video) {
          this.flattenedRelatedList.push({
            ...video,
            itemType: 'video',
            paixu: connection.paixu,
            connectionId: connection.id
          })
        }

        // 添加关联图片信息（仅在成品视频查看素材时显示）
        if (this.currentVideo && this.currentVideo.type === 1 && connection.sourceImage) {
          this.flattenedRelatedList.push({
            ...connection.sourceImage,
            itemType: 'image',
            paixu: connection.paixu,
            connectionId: connection.id
          })
        }
      })

      // 按排序字段排序
      this.flattenedRelatedList.sort((a, b) => (a.paixu || 0) - (b.paixu || 0))
    },

    // 预览媒体（视频或图片）
    previewMedia(item) {
      if (item.itemType === 'video') {
        this.previewVideo(item.mediaUrl)
      } else {
        this.previewImage(item.mediaUrl)
      }
    },

    // 预览图片
    previewImage(url) {
      if (!url) {
        this.$message.warning('图片地址不存在')
        return
      }
      this.previewImageUrl = url
      this.imagePreviewVisible = true
    }
  }
}
</script>

<style scoped>
.video-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-video {
  border-radius: 4px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-video:hover {
  transform: scale(1.05);
  border-color: #409EFF;
}

.no-video {
  color: #999;
  font-size: 12px;
}

.upload-demo {
  display: inline-block;
  margin-left: 10px;
}

/* Tab样式优化 */
::v-deep .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
}

::v-deep .el-tabs__item i {
  margin-right: 5px;
}

::v-deep .el-tabs__nav-wrap::after {
  background-color: #e4e7ed;
}

::v-deep .el-tabs__active-bar {
  background-color: #409EFF;
}

::v-deep .el-tabs__item.is-active {
  color: #409EFF;
}

/* 关联视频弹窗样式 */
::v-deep .el-dialog__header {
  background-color: #f5f7fa;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
}

::v-deep .el-dialog__title {
  font-weight: 600;
  color: #303133;
}

/* 操作按钮样式优化 */
.el-button--text {
  padding: 5px 8px;
  margin: 0 2px;
}

.el-button--text i {
  margin-right: 3px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
  color: #c0c4cc;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 图片预览样式 */
.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-img:hover {
  transform: scale(1.1);
  border-color: #409EFF;
}

.no-image {
  color: #999;
  font-size: 12px;
}
</style>
