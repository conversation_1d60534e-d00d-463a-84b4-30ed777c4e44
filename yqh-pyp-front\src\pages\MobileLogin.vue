<template>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>

        <!-- 顶部logo和标题 -->
        <div class="login-header">
            <div class="logo-container">
                <div class="logo-icon">
                    <img src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20250618/fb791c64adc6424883781564ec4de7f9.png" style="width: 60px;" alt="">
                    <!-- <van-icon name="shield-o" size="48" color="#1989fa" /> -->
                </div>
                <h1 class="app-title">易企化AI爆店码</h1>
                <p class="app-subtitle">AI助力商家营销获客</p>
            </div>
        </div>

        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- 手机验证码登录 -->
            <div class="login-section">
                <div class="section-header">
                    <van-icon name="phone-o" size="20" color="#1989fa" />
                    <span class="section-title">手机验证码登录</span>
                </div>

                <div class="form-container">
                    <div class="input-group">
                        <van-field
                            v-model="mobile"
                            name="手机号"
                            placeholder="请输入手机号"
                            :border="false"
                            class="custom-field"
                        >
                            <template #left-icon>
                                <van-icon name="phone-o" color="#969799" />
                            </template>
                        </van-field>
                    </div>

                    <div class="input-group">
                        <van-field
                            v-model="code"
                            center
                            clearable
                            maxlength="6"
                            placeholder="请输入短信验证码"
                            :border="false"
                            class="custom-field"
                        >
                            <template #left-icon>
                                <van-icon name="shield-o" color="#969799" />
                            </template>
                            <template #button>
                                <van-button
                                    size="small"
                                    :type="waiting ? 'default' : 'primary'"
                                    :disabled="waiting"
                                    @click="doSendSmsCode()"
                                    class="code-btn"
                                    round
                                >
                                    <span v-if="waiting">{{ waitingTime }}s</span>
                                    <span v-else>获取验证码</span>
                                </van-button>
                            </template>
                        </van-field>
                    </div>

                    <div class="login-btn-container">
                        <van-button
                            type="primary"
                            size="large"
                            @click="mobileLogin"
                            :loading="loginLoading"
                            round
                            block
                            class="login-btn"
                        >
                            <van-icon name="arrow" />
                            <span style="margin-left: 8px;">立即登录</span>
                        </van-button>
                    </div>
                </div>
            </div>

            <!-- 微信授权登录 (仅在微信环境中显示) -->
            <!-- <div v-if="isWeixin" class="wx-login-section"> -->
            <div class="wx-login-section">
                <div class="divider">
                    <span>其他登录方式</span>
                </div>
                <van-button
                    color="linear-gradient(135deg, #07c160, #1aad19)"
                    size="large"
                    @click="wxLogin"
                    :loading="wxLoginLoading"
                    :disabled="wxLoginLoading"
                    round
                    block
                    class="wx-login-btn"
                >
                    <van-icon name="wechat" size="20" />
                    <span style="margin-left: 8px;">
                        {{ wxLoginLoading ? '授权中...' : '微信授权登录' }}
                    </span>
                </van-button>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="footer-info">
            <p>登录即表示同意<span class="link" @click="goToUserAgreement">《用户协议》</span>和<span class="link" @click="goToPrivacyPolicy">《隐私政策》</span></p>
        </div>
    </div>
</template>

<script>
import { isMobile, isWeixin } from "@/js/validate";
import wxAuth from '../js/wxAuth.js';

export default {
    name: "MobileLogin",
    data() {
        return {
            mobile: "",
            code: "",
            waiting: false,
            waitingTime: 60,
            loginLoading: false,
            wxLoginLoading: false,
            returnUrl: '',
            isWeixin: isWeixin(),
            activityId: undefined
        };
    },
    mounted() {
        document.title = "登录";
        // 简单处理returnUrl
        let returnUrl = this.$route.query.returnUrl || '/';

        console.log("=== MobileLogin URL处理开始 ===");
        console.log("原始 $route.query.returnUrl:", returnUrl);
        console.log("当前页面完整URL:", window.location.href);
        console.log("当前页面search:", window.location.search);

        // 如果returnUrl被多重编码，进行解码
        try {
            // 最多解码3次，处理常见的嵌套编码情况
            for (let i = 0; i < 3; i++) {
                let decoded = decodeURIComponent(returnUrl);
                console.log(`第${i+1}次解码:`, decoded);
                if (decoded === returnUrl) {
                    console.log(`第${i+1}次解码无变化，停止解码`);
                    break;
                }
                returnUrl = decoded;
            }
        } catch (e) {
            console.log("URL解码完成或出错:", e);
        }

        this.returnUrl = returnUrl;
        this.activityId = this.$route.query.id;

        console.log("最终处理后的returnUrl:", this.returnUrl);

        // 检查URL中是否有微信授权的code参数
        this.checkWxAuthCode();

        console.log("=== MobileLogin URL处理结束 ===");
    },

    methods: {
        // 手机验证码登录
        mobileLogin() {
            if (!this.mobile) {
                vant.Toast("请输入手机号");
                return false;
            }
            if (!isMobile(this.mobile)) {
                vant.Toast("手机号格式错误");
                return false;
            }
            if (!this.code) {
                vant.Toast("请输入验证码");
                return false;
            }
            if (!/^\d{6}$/.test(this.code)) {
                vant.Toast("验证码格式错误");
                return false;
            }
            
            this.loginLoading = true;
            this.$fly.post("/pyp/web/user/pcLogin", {
                mobile: this.mobile,
                code: this.code,
            }).then((res) => {
                this.loginLoading = false;
                if (res && res.code === 200) {
                    vant.Toast("登录成功");
                    this.$store.commit("user/update", res.userInfo);
                    // 跳转回原页面
                    location.href = this.returnUrl;
                } else {
                    vant.Toast(res.msg || "登录失败");
                }
            }).catch(() => {
                this.loginLoading = false;
                vant.Toast("登录失败，请重试");
            });
        },
        
        // 微信授权登录 - 完全参考WxLogin的简洁实现
        wxLogin() {
            // 防止重复点击
            if (this.wxLoginLoading) {
                return;
            }

            this.wxLoginLoading = true;

            // 完全参考WxLogin.vue的实现方式
            wxAuth(this.returnUrl).then(() => {
                // 授权成功，直接跳转到目标页面
                location.href = this.returnUrl;
            }).catch((error) => {
                this.wxLoginLoading = false;
                console.error("微信授权失败:", error);
                vant.Toast("微信授权失败，请重试");
            });
        },
        
        // 发送短信验证码
        doSendSmsCode() {
            if (!this.mobile) {
                vant.Toast("请输入手机号");
                return false;
            }
            if (!isMobile(this.mobile)) {
                vant.Toast("手机号格式错误");
                return false;
            }
            
            this.$fly.post("/pyp/sms/sms/send", {
                mobile: this.mobile,
                activityId: this.activityId,
            }).then((res) => {
                if (res && res.code === 200) {
                    this.countdown();
                    vant.Toast("发送验证码成功");
                } else {
                    vant.Toast(res.msg || "发送失败");
                }
            });
        },
        
        // 倒计时
        countdown() {
            this.waiting = true;
            let clock = window.setInterval(() => {
                this.waitingTime--;
                if (this.waitingTime < 0) {
                    window.clearInterval(clock);
                    this.waitingTime = 60;
                    this.waiting = false;
                }
            }, 1000);
        },

        // 获取URL参数
        getUrlParam(key) {
            const reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
            const result = window.location.search.substring(1).match(reg);
            return result ? decodeURIComponent(result[2]) : null;
        },

        // 检查URL中的微信授权code参数
        checkWxAuthCode() {
            // 解析URL中的code参数
            const code = this.getUrlParam('code');
            const state = this.getUrlParam('state');

            console.log("检查微信授权参数 - code:", code, "state:", state);
            console.log("当前search参数:", window.location.search);

            if (code && state !== null) {
                console.log("检测到微信授权回调，自动处理授权");
                // 显示加载状态
                this.wxLoginLoading = true;

                // 自动调用微信授权处理
                wxAuth(this.returnUrl).then(() => {
                    console.log("自动微信授权成功，跳转到目标页面");
                    this.wxLoginLoading = false;
                    // 授权成功，直接跳转到目标页面
                    location.href = this.returnUrl;
                }).catch((error) => {
                    this.wxLoginLoading = false;
                    console.error("自动微信授权失败:", error);
                    vant.Toast("微信授权失败，请重试");
                });
            } else {
                console.log("未检测到微信授权回调参数");
            }
        },

        // 跳转到用户协议
        goToUserAgreement() {
            this.$router.push({ name: 'userAgreement' });
        },

        // 跳转到隐私政策
        goToPrivacyPolicy() {
            this.$router.push({ name: 'privacyPolicy' });
        },
    },
};
</script>

<style scoped>
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 120px;
    height: 120px;
    top: 10%;
    left: -60px;
    animation-delay: 0s;
}

.circle-2 {
    width: 80px;
    height: 80px;
    top: 20%;
    right: -40px;
    animation-delay: 2s;
}

.circle-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 头部区域 */
.login-header {
    text-align: center;
    margin-bottom: 40px;
    padding-top: 60px;
    position: relative;
    z-index: 2;
}

.logo-container {
    margin-bottom: 20px;
}

.logo-icon {
    margin-bottom: 16px;
}

.app-title {
    color: white;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
    font-weight: 300;
}

/* 登录卡片 */
.login-card {
    max-width: 380px;
    margin: 0 auto;
    background: white;
    border-radius: 24px;
    padding: 32px 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.login-section {
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.section-title {
    margin-left: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
}

.form-container {
    margin-top: 20px;
}

.input-group {
    margin-bottom: 16px;
    background: #f8f9fa;
    border-radius: 16px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.input-group:focus-within {
    background: #f0f7ff;
    box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.1);
}

.custom-field {
    background: transparent !important;
}

.custom-field :deep(.van-cell) {
    padding: 16px;
    background: transparent;
    border-radius: 0;
    min-height: 56px;
    display: flex;
    align-items: center;
}

.custom-field :deep(.van-field__left-icon) {
    margin-right: 12px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.custom-field :deep(.van-field__body) {
    display: flex;
    align-items: center;
    flex: 1;
}

.custom-field :deep(.van-field__control) {
    font-size: 16px;
    border: none;
    background: transparent;
    flex: 1;
    height: auto;
    line-height: 1.5;
    color: #323233;
}

.custom-field :deep(.van-field__control::placeholder) {
    color: #c8c9cc;
    font-size: 16px;
}

.custom-field :deep(.van-field__button) {
    margin-left: 12px;
    flex-shrink: 0;
}

.code-btn {
    font-size: 12px;
    min-width: 80px;
    height: 36px;
    padding: 0 12px;
    background: #1989fa;
    color: white;
    border: none;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.code-btn:disabled {
    background: #c8c9cc;
    color: #969799;
}

.login-btn-container {
    margin-top: 32px;
}

.login-btn {
    height: 52px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #1989fa, #1976d2);
    border: none;
    box-shadow: 0 8px 20px rgba(25, 137, 250, 0.3);
    transition: all 0.3s ease;
}

.login-btn:active {
    transform: translateY(1px);
    box-shadow: 0 4px 12px rgba(25, 137, 250, 0.4);
}

/* 微信登录区域 */
.wx-login-section {
    margin-top: 24px;
}

.divider {
    text-align: center;
    margin: 24px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e5e5, transparent);
}

.divider span {
    background: white;
    color: #969799;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
}

.wx-login-btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 6px 16px rgba(7, 193, 96, 0.2);
    transition: all 0.3s ease;
}

.wx-login-btn:active {
    transform: translateY(1px);
}

/* 底部信息 */
.footer-info {
    text-align: center;
    margin-top: 32px;
    position: relative;
    z-index: 2;
}

.footer-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin: 0;
    line-height: 1.5;
}

.link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
    cursor: pointer;
    transition: all 0.3s ease;
}

.link:hover {
    color: white;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.link:active {
    transform: scale(0.95);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }

    .login-header {
        padding-top: 40px;
        margin-bottom: 32px;
    }

    .app-title {
        font-size: 28px;
    }

    .login-card {
        padding: 24px 20px;
        border-radius: 20px;
    }
}
</style>
