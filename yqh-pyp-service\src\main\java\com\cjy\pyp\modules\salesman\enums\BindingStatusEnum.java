package com.cjy.pyp.modules.salesman.enums;

/**
 * 绑定状态枚举
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public enum BindingStatusEnum {
    
    /**
     * 已失效
     */
    EXPIRED(0, "已失效"),
    
    /**
     * 有效
     */
    ACTIVE(1, "有效"),
    
    /**
     * 已解绑
     */
    UNBOUND(2, "已解绑");
    
    private final Integer code;
    private final String desc;
    
    BindingStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static BindingStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BindingStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(Integer code) {
        BindingStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知";
    }
}
