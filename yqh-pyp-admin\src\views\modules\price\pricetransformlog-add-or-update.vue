<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="到款金额" prop="price">
        <el-input v-model="dataForm.price" placeholder="到款金额"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
      </el-form-item>
      <el-form-item label="往来ID" prop="priceTransformId">
        <el-input v-model="dataForm.priceTransformId" placeholder="往来ID"></el-input>
      </el-form-item>
      <el-form-item label="银行账户ID" prop="priceBankId">
        <el-input v-model="dataForm.priceBankId" placeholder="银行账户ID"></el-input>
      </el-form-item>
      <el-form-item label="支付时间" prop="payTime">
        <el-date-picker v-model="dataForm.payTime" type="datetime" placeholder="请选择支付时间"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,

        price: '',

        remarks: '',

        priceTransformId: '',

        priceBankId: '',

        payTime: ''
      },
      dataRule: {
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        remarks: [
          { required: true, message: '备注不能为空', trigger: 'blur' }
        ],
        priceTransformId: [
          { required: true, message: '往来ID不能为空', trigger: 'blur' }
        ],
        priceBankId: [
          { required: true, message: '银行账户ID不能为空', trigger: 'blur' }
        ],
        payTime: [
          { required: true, message: '支付时间不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.getToken();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricetransformlog/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.price = data.priceTransformLog.price
              this.dataForm.remarks = data.priceTransformLog.remarks
              this.dataForm.priceTransformId = data.priceTransformLog.priceTransformId
              this.dataForm.priceBankId = data.priceTransformLog.priceBankId
              this.dataForm.payTime = data.priceTransformLog.payTime
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricetransformlog/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'price': this.dataForm.price,
              'remarks': this.dataForm.remarks,
              'appid': this.$cookie.get('appid'),

              'priceTransformId': this.dataForm.priceTransformId,
              'priceBankId': this.dataForm.priceBankId,
              'payTime': this.dataForm.payTime,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
