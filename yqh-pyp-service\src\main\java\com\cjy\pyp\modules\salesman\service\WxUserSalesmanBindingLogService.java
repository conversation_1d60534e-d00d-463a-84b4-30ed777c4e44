package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;

import java.util.List;
import java.util.Map;

/**
 * 微信用户业务员绑定变更记录服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public interface WxUserSalesmanBindingLogService extends IService<WxUserSalesmanBindingLogEntity> {

    /**
     * 分页查询绑定变更记录
     * @param params 查询参数
     * @return 分页结果
     */
    List<WxUserSalesmanBindingLogEntity> queryPage(Map<String, Object> params);

    /**
     * 根据微信用户ID查询绑定变更历史
     * @param wxUserId 微信用户ID
     * @param appid 应用ID
     * @return 变更记录列表
     */
    List<WxUserSalesmanBindingLogEntity> getLogsByWxUser(Long wxUserId, String appid);

    /**
     * 根据业务员ID查询相关的绑定变更记录
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 变更记录列表
     */
    List<WxUserSalesmanBindingLogEntity> getLogsBySalesman(Long salesmanId, String appid);

    /**
     * 记录绑定变更日志
     * @param wxUserId 微信用户ID
     * @param oldSalesmanId 原业务员ID
     * @param newSalesmanId 新业务员ID
     * @param operationType 操作类型
     * @param reason 操作原因
     * @param bindingSource 绑定来源
     * @param operatorId 操作人ID
     * @param operatorType 操作人类型
     * @param appid 应用ID
     */
    void recordLog(Long wxUserId, Long oldSalesmanId, Long newSalesmanId, Integer operationType,
                   String reason, String bindingSource, Long operatorId, Integer operatorType, String appid);
}
