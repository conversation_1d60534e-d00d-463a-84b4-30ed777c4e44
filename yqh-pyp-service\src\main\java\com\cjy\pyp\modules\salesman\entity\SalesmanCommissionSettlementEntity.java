package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务员佣金结算批次实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Data
@TableName("salesman_commission_settlement")
@Accessors(chain = true)
public class SalesmanCommissionSettlementEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 结算批次号
     */
    private String batchNo;

    /**
     * 结算日期
     */
    private Date settlementDate;

    /**
     * 结算业务员数量
     */
    private Integer salesmanCount;

    /**
     * 结算记录数量
     */
    private Integer recordCount;

    /**
     * 结算总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态：0-待结算，1-已结算，2-已取消
     */
    private Integer status;

    /**
     * 结算类型：1-手动结算，2-自动结算
     */
    private Integer settlementType;

    /**
     * 结算完成时间
     */
    private Date settlementTime;

    /**
     * 结算备注
     */
    private String remarks;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    // 关联查询字段
    /**
     * 状态描述
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 结算类型描述
     */
    @TableField(exist = false)
    private String settlementTypeDesc;
}
