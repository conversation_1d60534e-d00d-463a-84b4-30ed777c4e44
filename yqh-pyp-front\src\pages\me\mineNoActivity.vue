<template>
    <div :class="isMobilePhone ? 'page' : 'page pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <van-card style="background: white" :thumb="userInfo.headimgurl ? userInfo.headimgurl : 'photo-o'">
            <div slot="title" style="padding-top: 10px;padding-left: 10px;font-size: 18px">{{userInfo.nickname}}</div>
            <div slot="desc" style="padding-top: 10px;padding-left: 10px;font-size: 14px;color:grey">{{userInfo.mobile}}</div>

        </van-card>

        <van-grid style="margin-top:20px" :column-num="3" icon-size="50px" :gutter="8">
            <van-grid-item icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230110/a162a57b18304c32af3334075ec19dc8.png" @click="$router.push({ name: 'meApplyList' })" text="我的会议" />
            <van-grid-item icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230110/efb283dcc4a745aeae429f3101b2e89a.png" @click="$router.push({ name: 'help' })" text="系统说明" />
            <van-grid-item icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230110/40e379749bac45eb93adc0ed9be95482.png" @click="$router.push({ name: 'contact' })" text="联系我们" />
        </van-grid>
    </div>
</template>

<script>
    import {
        isMobilePhone
    } from "@/js/validate";
    import pcheader from "@/pages/cms/components/pcheader.vue";
    export default {
        components: {
            pcheader,
        },
        data() {
            return {
                isMobilePhone: isMobilePhone(),
                userInfo: {}
            }
        },
        mounted() {
            document.title = "我的"
    this.$wxShare(
      this.$cookie.get("accountName"),
      this.$cookie.get("logo"),
      this.$cookie.get("slog")
    );
            this.getUserInfo();
        },
        methods: {

    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
        } else {
          vant.Toast(res.msg);
        }
      });
    },
        }
    }
</script>

<style lang="less" scoped>
    .template5 {
        margin-top: 20px;
        background: #f6f6f6;
    }
    .template5 /deep/ .van-grid-item__content {
        background: transparent;
        padding: 4px 6px;
    }
    .template5 /deep/ .van-grid-item__icon {
        width: 100%;
        height: auto;
    }
    .template5 /deep/ .van-icon__image {
        width: 100%;
        height: auto;
        object-fit: fill;
    }
</style>