<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-select v-model="dataForm.hotelActivityId" @change="hotelChange">
          <el-option label="全部(酒店)" value=""></el-option>
          <el-option v-for="item in hotels" :key="item.id" :label="item.hotelName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.hotelActivityRoomId">
          <el-option label="全部(房型)" value=""></el-option>
          <el-option v-for="item in rooms" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.number" placeholder="房间号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button :disabled="!dataForm.hotelActivityRoomId" v-if="isAuth('hotel:hotelactivityroomnumber:save')"
          type="primary" @click="addnumberHandle()">新增</el-button>
        <!-- <el-button v-if="isAuth('hotel:hotelactivityroomnumber:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
        <el-button :disabled="!dataForm.hotelActivityRoomId" @click="exportHandle()" type="success">导出</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table size="mini" :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="roomName" header-align="center" align="center" label="房型名称">
      </el-table-column>
      <el-table-column prop="number" header-align="center" align="center" label="房号">
      </el-table-column>
      <el-table-column prop="isAssign" header-align="center" align="center" label="是否分配">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + scope.row.isAssign">{{
            yesOrNo[scope.row.isAssign].value }}</el-tag>
        </div>
      </el-table-column>
      <div v-if="dataList.length > 0">
        <el-table-column header-align="center" align="center" v-for="(item, index) in dataList[0].assignVos"
          :key="index">
          <template slot="header">
            {{ item.date }}
          </template>
          <!-- index对应下面动态列（answerList）的索引，取出值渲染 -->
          <template slot-scope="scope">{{ scope.row.assignVos[index].number }}
            <span v-if="scope.row.assignVos[index].number > 0">({{ scope.row.assignVos[index].number < 1 ?
              (
              roomType[scope.row.assignVos[index].roomType].value) : '满' }})</span>
          </template>
        </el-table-column>
      </div>
      <el-table-column prop="createOn" show-overflow-tooltip header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" show-overflow-tooltip header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="180" label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button> -->
          <el-button style="color: red" type="text" size="small" @click="assignHandle(scope.row)">分配入住</el-button>
          <el-button style="color: red" type="text" size="small" @click="assignPeopleHandle(scope.row)">查看入住</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <addnumber v-if="addnumberVisible" ref="addnumber" @refreshDataList="getDataList"></addnumber>
    <assign v-if="assignVisible" ref="assign" @refreshDataList="getDataList"></assign>
    <assignpeople v-if="assignpeopleVisible" ref="assignpeople" @refreshDataList="getDataList"></assignpeople>
  </div>
</template>

<script>
import { roomType } from '@/data/room'
import AddOrUpdate from './hotelactivityroomnumber-add-or-update'
import addnumber from './hotelactivityroomnumber-addnumber'
import assign from './hotelactivityroomnumber-assign'
import assignpeople from './hotelactivityroomnumber-assignpeople'
import { yesOrNo } from '@/data/common'
export default {
  data() {
    return {
      appid: '',
      roomType,
      hotels: [],
      rooms: [],
      yesOrNo,
      dataForm: {
        activityId: '',
        hotelActivityId: '',
        hotelActivityRoomId: '',
        number: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      addnumberVisible: false,
      assignVisible: false,
      assignpeopleVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    addnumber,
    assign,
    assignpeople,
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.dataForm.hotelActivityId = this.$route.query.hotelActivityId;
    this.dataForm.hotelActivityRoomId = this.$route.query.hotelActivityRoomId;
    if (this.dataForm.hotelActivityId) {
      this.findRoom(this.dataForm.hotelActivityId);
    }
    this.getDataList()
    this.findHotel()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hotel/hotelactivityroomnumber/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'number': this.dataForm.number,
          'activityId': this.dataForm.activityId,
          'hotelActivityId': this.dataForm.hotelActivityId,
          'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/hotel/hotelactivityroomnumber/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "activityId=" + this.dataForm.activityId,
        "hotelActivityId=" + this.dataForm.hotelActivityId,
        "hotelActivityRoomId=" + this.dataForm.hotelActivityRoomId,
        "number=" + this.dataForm.number,
      ].join('&'));
      window.open(url);
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    addnumberHandle() {
      this.addnumberVisible = true
      this.$nextTick(() => {
        this.$refs.addnumber.init(this.dataForm.hotelActivityRoomId)
      })
    },
    findHotel() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivity/findByActivityId/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.hotels = data.result
        }
      })
    },
    hotelChange(v) {
      this.dataForm.hotelActivityRoomId = '';
      this.findRoom(v);
    },
    findRoom(v) {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroom/findByHotelActivityId/${v}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.rooms = data.result
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hotel/hotelactivityroomnumber/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    assignHandle(v) {
      this.assignVisible = true
      this.$nextTick(() => {
        this.$refs.assign.init(v.id, this.dataForm.activityId, v.hotelActivityRoomId)
      })
    },
    assignPeopleHandle(v) {
      this.assignpeopleVisible = true
      this.$nextTick(() => {
        this.$refs.assignpeople.init(v.id)
      })
    },
  }
}
</script>
