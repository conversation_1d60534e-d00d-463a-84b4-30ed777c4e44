<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-card style="background: white;"
      :thumb="guestInfo.avatar ? guestInfo.avatar : 'http://mpjoy.oss-cn-beijing.aliyuncs.com/20240917/d9301e81ab674112b3acf4a84aff4e96.png'">
      <div slot="title" style="font-size: 18px">{{ guestInfo.name }}</div>
      <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.unit" size="medium" round type="primary" plain>{{
          guestInfo.unit }}</van-tag>
        <van-tag style="margin: 5px 10px 5px 0px" v-if="guestInfo.duties" size="medium" round type="warning" plain>{{
          guestInfo.duties }}</van-tag>
      </div>
    </van-card>
    <van-collapse v-model="activeName" style="margin-top: 10px;padding-bottom: 100px;">
      <van-collapse-item title="嘉宾介绍" name="1">
        <div class="content"
          style="white-space: pre-wrap; word-wrap: break-word;table-layout: fixed;word-break: break-all"
          v-if="guestInfo.content" v-html="guestInfo.content"></div>
        <div v-else>暂无介绍</div>
      </van-collapse-item>
      <van-collapse-item v-if="topicSpeaker && topicSpeaker.length > 0" class="transparent" title="负责主题主席任务" name="2">
        <van-card v-for="item in topicSpeaker" :key="item.id" style="background: white;border-radius: 10px">
          <!-- :thumb="item.imageUrl
          ? item.imageUrl
          : activityInfo.mobileBanner
            ? activityInfo.mobileBanner.split(',')[0]
            : 'van-icon'
          " -->
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" round type="primary" style="margin-left: 10px;" @click="confirm(1,item.flagId,'topicSpeaker')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click="confirm(2,item.flagId,'topicSpeaker')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
      <van-collapse-item v-if="topic && topic.length > 0" class="transparent" title="负责主题主持任务" name="3">
        <van-card v-for="item in topic" :key="item.id" style="background: white;border-radius: 10px">
          <!-- :thumb="item.imageUrl
          ? item.imageUrl
          : activityInfo.mobileBanner
            ? activityInfo.mobileBanner.split(',')[0]
            : 'van-icon'
          " -->
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" round type="primary"  style="margin-left: 10px;" @click="confirm(1,item.flagId,'topic')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click="confirm(2,item.flagId,'topic')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
      <van-collapse-item v-if="topicDiscuss && topicDiscuss.length > 0" class="transparent" title="负责主题讨论任务" name="3">
        <van-card v-for="item in topicDiscuss" :key="item.id" style="background: white;border-radius: 10px">
          <!-- :thumb="item.imageUrl
          ? item.imageUrl
          : activityInfo.mobileBanner
            ? activityInfo.mobileBanner.split(',')[0]
            : 'van-icon'
          " -->
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey;">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" style="margin-left: 10px;" round type="primary" @click="confirm(1,item.flagId,'topicDiscuss')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click="confirm(2,item.flagId,'topicDiscuss')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
      <van-collapse-item v-if="schedule && schedule.length > 0" class="transparent" title="负责讲课任务" name="4">
        <van-card v-for="item in schedule" :key="item.id" style="background: white;border-radius: 10px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" round type="primary" style="margin-left: 10px;" @click.native="confirm(1,item.flagId,'schedule')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click.native="confirm(2,item.flagId,'schedule')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
      <van-collapse-item v-if="scheduleSpeaker && scheduleSpeaker.length > 0" class="transparent" title="负责主持任务"
        name="5">
        <van-card v-for="item in scheduleSpeaker" :key="item.id" style="background: white;border-radius: 10px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" round type="primary" style="margin-left: 10px;" @click="confirm(1,item.flagId,'scheduleSpeaker')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click="confirm(2,item.flagId,'scheduleSpeaker')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
      <van-collapse-item v-if="scheduleDiscuss && scheduleDiscuss.length > 0" class="transparent" title="负责讲课讨论任务"
        name="6">
        <van-card v-for="item in scheduleDiscuss" :key="item.id" style="background: white;border-radius: 10px">
          <div slot="title" style="font-size: 18px">{{ item.name }}</div>
          <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
            <!-- <van-tag style="margin: 5px 10px 5px 0px" v-for="item1 in item.activityGuests" :key="item1.id" size="medium"
              round type="primary" plain>{{ item1.name }}</van-tag> -->
            <div v-if="isConfirm" style="display: flex;align-items: center;justify-content: space-between;">
              <div>
              </div>
              <div style="display: flex;align-items: center;">  
                <div v-if="item.confirmStatus" :style="item.confirmStatus == 1 ? 'color: green' :'color: red'">{{ item.confirmStatus == 1 ? '已确认' :'已拒绝' }}</div>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 2" round type="primary" style="margin-left: 10px;" @click="confirm(1,item.flagId,'scheduleDiscuss')">确认任务</van-tag>
                <van-tag size="large" v-if="!item.confirmStatus || item.confirmStatus == 1" style="margin-left: 10px;" round type="danger"  @click="confirm(2,item.flagId,'scheduleDiscuss')">拒绝任务</van-tag>
              </div>
            </div>
            <div>
              {{ item.startTime }} ~
              {{ item.endTime.substring(11, 19) }}
            </div>
            <div>主题：{{ item.placeTopicName }}</div>
            <div>会场：{{ item.placeName }}</div>
          </div>
        </van-card>
      </van-collapse-item>
    </van-collapse>
    <!-- Add before bottom buttons -->
    <div v-if="isConfirm" class="fixed-bottom">
      <div v-if="!guestInfo.isSchedule" class="notice-bar">
        如果任务时间您已经知悉并确认可以到场完成任务，请点击确认按钮。
      </div>
      <div class="bottom-buttons">
        <van-button v-if="!guestInfo.isSchedule" round block type="info" @click.native="confirm(1,id,'all')" :loading="loading"
          loading-text="提交中" class="submit-btn">一键确认</van-button>
        <van-button round block type="primary" @click="$router.replace({
          path: '/schedules/expertIndex',
          query: { detailId: id },
        })" class="back-btn">返回上一页面</van-button>
      </div>
    </div>
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    <van-popup v-model="areaShow" position="center" :style="{ width: '90%', maxWidth: '400px' }">
        <van-cell-group>
            <van-field name="radio" label="填写方式">
                <template #input>
                    <van-radio-group v-model="confirmForm.status" direction="horizontal">
                        <van-radio :name="1" :key="1">确认无误</van-radio>
                        <van-radio :name="2" :key="2">有异议</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <div v-show="confirmForm.status == 1 && confirmForm.method !== 'all'">
                <van-field v-model="confirmForm.topicName" name="课题名称" label="课题名称" placeholder="请确认课题名称"
                    required :rules="[{ required: true, message: '请确认课题名称' }]" class="topic-name-field">
                    <template #input>
                        <textarea v-model="confirmForm.topicName" rows="3" class="topic-input" placeholder="请确认课题名称"></textarea>
                    </template>
                </van-field>
            </div>
            <div v-show="confirmForm.status == 2">
                <van-field name="reasonType" label="异议原因">
                    <template #input>
                        <van-radio-group v-model="confirmForm.reasonType" direction="vertical">
                            <van-radio :name="1" :key="1">其他会议时间冲突，无法到场</van-radio>
                            <van-radio :name="2" :key="2">家中有事，无法到场</van-radio>
                            <van-radio :name="3" :key="3">自定义原因</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field v-show="confirmForm.reasonType == 3" v-model="confirmForm.customReason" name="自定义原因" label="自定义原因" placeholder="请输入异议原因"
                    required :rules="[{ required: true, message: '请输入异议原因' }]">
                </van-field>
            </div>
        </van-cell-group>
        <div style="margin: 16px;display: flex;gap: 10px">
            <van-button round block type="info" @click="areaShow = false">取消</van-button>
            <van-button round color="#DD5C5F" block type="info" @click="confirmDo" :loading="loading"
                loading-text="提交中">提交</van-button>
        </div>
    </van-popup>
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from '@/js/validate'
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: { pcheader },
  data() {
    return {
      areaShow: false,
      confirmForm: {
        status: 1,
        reason: '',
        reasonType: 1,
        customReason: '',
        flagId: '',
        method: '',
        topicName: ''
      },
      isConfirm: 0,
      loading: false,
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activeName: [ "2", "3", '4', '5', '6'],
      activityId: undefined,
      id: undefined,
      guestInfo: {},
      schedule: [],
      scheduleDiscuss: [],
      scheduleSpeaker: [],
      topic: [],
      topicSpeaker: [],
      topicDiscuss: [],
      activityInfo: {},
    };
  },
  watch: {
    'confirmForm.status': function(newVal, oldVal) {
      // 强制重新渲染表单
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          // 在下一个DOM更新周期执行
          console.log('状态已切换:', newVal);
        });
      }
    }
  },
  mounted() {
    // this.activityId = this.$route.query.id;
    this.id = this.$route.query.detailId;
    this.isConfirm = this.$route.query.c || 0;
    this.openid = this.$cookie.get("openid")
    this.getActivityList();
    this.getTopicAndSchedule();
  },
  methods: {
    confirm(status, flagId, method) {
      // 先设置弹窗为可见
      this.areaShow = true;
      
      // 在下一个DOM更新周期设置表单值，确保UI已经渲染
      this.$nextTick(() => {
        this.confirmForm.flagId = flagId;
        this.confirmForm.status = status;
        this.confirmForm.method = method;
        this.confirmForm.reason = '';
        this.confirmForm.reasonType = 1;
        this.confirmForm.customReason = '';
        
        // 根据不同的任务类型设置默认的课题名称
        let defaultTopicName = '';
        if (method === 'topicSpeaker' || method === 'topic' || method === 'topicDiscuss') {
          // 查找对应的主题信息
          const items = method === 'topicSpeaker' ? this.topicSpeaker : 
                        method === 'topic' ? this.topic : this.topicDiscuss;
          const item = items.find(item => item.flagId === flagId);
          if (item) {
            defaultTopicName = item.name || '';
          }
        } else if (method === 'schedule' || method === 'scheduleSpeaker' || method === 'scheduleDiscuss') {
          // 查找对应的日程信息
          const items = method === 'schedule' ? this.schedule : 
                        method === 'scheduleSpeaker' ? this.scheduleSpeaker : this.scheduleDiscuss;
          const item = items.find(item => item.flagId === flagId);
          if (item) {
            defaultTopicName = item.name || '';
          }
        }
        
        this.confirmForm.topicName = defaultTopicName;
      });
    },
    confirmDo() {
      this.loading = true;
      
      // 根据选择的原因类型设置最终提交的原因
      if (this.confirmForm.status == 2) {
        if (this.confirmForm.reasonType == 1) {
          this.confirmForm.reason = "其他会议时间冲突，无法到场";
        } else if (this.confirmForm.reasonType == 2) {
          this.confirmForm.reason = "家中有事，无法到场";
        } else if (this.confirmForm.reasonType == 3) {
          this.confirmForm.reason = this.confirmForm.customReason;
        }
        
        // 验证自定义原因是否填写
        if (this.confirmForm.reasonType == 3 && !this.confirmForm.customReason) {
          vant.Toast('请填写自定义异议原因');
          this.loading = false;
          return;
        }
      } else if (this.confirmForm.status == 1 && this.confirmForm.method !== 'all') {
        // 验证课题名称是否填写（一键确认除外）
        if (!this.confirmForm.topicName) {
          vant.Toast('请确认课题名称');
          this.loading = false;
          return;
        }
      }
      
      this.$fly
        .post(`/pyp/web/activity/activityguest/confirm`, this.confirmForm)
        .then((res) => {
          this.loading = false;
          if (res && res.code === 200) {
            vant.Toast('操作成功');
            this.areaShow = false;
            this.getTopicAndSchedule();
            this.getActivityList();
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = "嘉宾介绍-" + this.guestInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-嘉宾详情-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                this.guestInfo.name + "-嘉宾详情-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getTopicAndSchedule() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getTopicAndSchedule/${this.id}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.topic = res.result.topic;
            this.topicDiscuss = res.result.topicDiscuss;
            this.topicSpeaker = res.result.topicSpeaker;
            this.schedule = res.result.schedule;
            this.scheduleSpeaker = res.result.scheduleSpeaker;
            this.scheduleDiscuss = res.result.scheduleDiscuss;
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityList() {
      this.$fly
        .get(`/pyp/web/activity/activityguest/getById/${this.id}`)
        .then((res) => {
          if (res.code == 200) {
            this.guestInfo = res.result;
            this.activityId = res.result.activityId;
            this.getActivityInfo();
          } else {
            vant.Toast(res.msg);
            this.guestInfo = {};
          }
        });
    },
    cmsTurnBack() {
      // this.$router.replace({ name: 'schedulesIndex',query: { id: this.activityInfo.id } })
      this.$router.go(-1)
    },
    submit() {
      this.guestInfo.isSchedule = 1;
      this.guestInfo.isScheduleTime = date.formatDate.format(new Date(), "yyyy/MM/dd hh:mm:ss");
      this.loading = true;
      // 保存
      this.$fly
        .post(
          "/pyp/web/activity/activityguest/updateInfo",
          this.guestInfo
        )
        .then((res) => {
          this.loading = false;
          if (res && res.code === 200) {
            vant.Dialog.confirm({
              title: "更新成功",
              message: "点击确定，返回继续完善其他信息",
            })
              .then(() => {
                this.$router.replace({
                  path: '/schedules/expertIndex',
                  query: { detailId: this.id },
                });
              })
              .catch(() => {
                this.getActivityList();
              });
            // 绑定手机
          } else {
            vant.Toast(res.msg);
          }
        });
    }
  },
};
</script>
<style lang="less" scoped>
.transparent {
  /deep/.van-collapse-item__content {
    background: transparent;
  }
}

.content {
  /deep/ img {
    max-width: 100%;
    height: auto;
  }
}

.van-card__thumb /deep/ img {
  object-fit: contain !important;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 99;

}

.notice-bar {
  background-color: #fff7e6;
  color: #666;
  font-size: 14px;
  text-align: left;
  border-top: 1px solid #ffe7ba;
  padding: 6px 16px;
}

.bottom-buttons {
  display: flex;
  gap: 10px;
  padding: 6px 16px;
}

.submit-btn,
.back-btn {
  flex: 1;
}
/deep/ .van-card__thumb  img {
    object-fit: contain !important;
}

.topic-name-field {
  /deep/ .van-field__control {
    min-height: 80px;
  }
}

.topic-input {
  width: 100%;
  min-height: 80px;
  border: 1px solid #ebedf0;
  border-radius: 4px;
  padding: 8px;
  font-size: 14px;
  resize: none;
}
</style>