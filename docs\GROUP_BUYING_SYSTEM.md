# 团购券系统实现文档

## 功能概述

为活动管理系统添加团购券功能，支持3种团购平台：抖音团购、美团团购、大众点评团购。用户可以在九宫格页面点击团购券按钮，跳转到对应平台的团购页面。

## 系统架构

### 数据库设计

#### 1. 团购券表 (group_buying_coupon)
- `id`: 主键ID
- `activity_id`: 活动ID
- `platform_type`: 平台类型 (douyin/meituan/dianping)
- `coupon_name`: 团购券名称
- `coupon_description`: 团购券描述
- `original_price`: 原价
- `group_price`: 团购价
- `discount_info`: 优惠信息
- `coupon_url`: 团购券链接
- `coupon_id`: 团购券ID（平台方提供）
- `qr_code_url`: 二维码链接
- `cover_image`: 封面图片
- `start_time`: 开始时间
- `end_time`: 结束时间
- `total_count`: 总数量
- `sold_count`: 已售数量
- `status`: 状态 (0-下架，1-上架)
- `sort_order`: 排序
- `remarks`: 备注信息

#### 2. 团购券平台配置表 (group_buying_platform_config)
- `id`: 主键ID
- `platform_type`: 平台类型
- `platform_name`: 平台名称
- `icon_url`: 平台图标URL
- `url_scheme`: APP跳转URL Scheme模板
- `web_url_template`: 网页跳转URL模板
- `api_config`: API配置信息
- `status`: 状态
- `sort_order`: 排序

#### 3. 活动表扩展字段
- `show_group_buying`: 是否显示团购券 (0-不显示，1-显示)
- `group_buying_title`: 团购券按钮标题

### 后端实现

#### 1. 实体类
- `GroupBuyingCouponEntity`: 团购券实体类
- `GroupBuyingPlatformConfigEntity`: 平台配置实体类

#### 2. 服务层
- `GroupBuyingCouponService`: 团购券服务接口
- `GroupBuyingCouponServiceImpl`: 团购券服务实现
- `GroupBuyingPlatformConfigService`: 平台配置服务接口
- `GroupBuyingPlatformConfigServiceImpl`: 平台配置服务实现

#### 3. 控制器
- `GroupBuyingCouponController`: 管理后台API
- `WebGroupBuyingController`: 前端Web API

### 前端实现

#### 1. 管理后台页面
- `groupbuying-coupon.vue`: 团购券列表页面
- `groupbuying-coupon-add-or-update.vue`: 团购券添加/编辑页面

#### 2. 移动端功能
- 在九宫格页面添加团购券按钮
- 实现团购券选择和跳转逻辑

## API接口

### 管理后台接口

#### 1. 团购券管理
- `GET /groupbuying/coupon/list`: 获取团购券列表
- `GET /groupbuying/coupon/info/{id}`: 获取团购券详情
- `POST /groupbuying/coupon/save`: 保存团购券
- `POST /groupbuying/coupon/update`: 更新团购券
- `POST /groupbuying/coupon/delete`: 删除团购券
- `POST /groupbuying/coupon/updateStatus`: 批量更新状态

### 前端Web接口

#### 1. 团购券查询
- `GET /web/groupbuying/coupons`: 获取活动团购券列表
- `GET /web/groupbuying/coupon/{id}`: 获取团购券详情
- `GET /web/groupbuying/platforms`: 获取平台配置列表

#### 2. 跳转和统计
- `GET /web/groupbuying/jumpUrl`: 生成跳转链接
- `POST /web/groupbuying/click`: 记录团购券点击
- `GET /web/groupbuying/statistics`: 获取统计信息

## 功能特性

### 1. 多平台支持
- **抖音团购**: 支持抖音APP和网页跳转
- **美团团购**: 支持美团APP和网页跳转
- **大众点评团购**: 支持大众点评APP和网页跳转

### 2. 智能跳转
- **环境检测**: 自动检测用户设备环境
- **APP优先**: 优先使用APP跳转，提升用户体验
- **降级处理**: APP跳转失败时自动降级到网页

### 3. 灵活配置
- **开关控制**: 活动级别的团购券显示开关
- **自定义标题**: 支持自定义团购券按钮标题
- **时间控制**: 支持设置团购券有效期
- **库存管理**: 支持设置总数量和已售数量

### 4. 用户体验
- **单券直跳**: 只有一个团购券时直接跳转
- **多券选择**: 多个团购券时显示选择列表
- **加载提示**: 提供友好的加载和错误提示
- **统计记录**: 记录用户点击行为

## 使用说明

### 管理员操作

#### 1. 活动配置
1. 在活动管理后台编辑活动
2. 开启"是否显示团购券"开关
3. 设置团购券按钮标题（可选）
4. 保存活动配置

#### 2. 团购券管理
1. 进入团购券管理页面
2. 点击"新增"按钮创建团购券
3. 填写团购券信息：
   - 选择活动和平台类型
   - 设置团购券名称和描述
   - 配置价格信息（原价、团购价、优惠信息）
   - 设置跳转链接或平台ID
   - 配置有效期和库存（可选）
4. 保存团购券

#### 3. 批量操作
- 支持批量上架/下架团购券
- 支持批量删除团购券
- 支持按条件筛选和搜索

### 用户操作

#### 1. 访问团购券
1. 用户访问活动页面
2. 在九宫格中看到团购券按钮
3. 点击团购券按钮

#### 2. 选择和跳转
1. 如果只有一个团购券，直接跳转
2. 如果有多个团购券，显示选择列表
3. 选择后自动跳转到对应平台

## 平台跳转配置

### URL Scheme模板

#### 抖音团购
- **APP**: `snssdk1128://groupon/detail?id={coupon_id}`
- **网页**: `https://www.douyin.com/groupon/{coupon_id}`

#### 美团团购
- **APP**: `imeituan://www.meituan.com/deal/{coupon_id}`
- **网页**: `https://www.meituan.com/deal/{coupon_id}`

#### 大众点评团购
- **APP**: `dianping://shopinfo?shopid={coupon_id}`
- **网页**: `https://www.dianping.com/shop/{coupon_id}`

### 环境检测规则

```javascript
// 检测是否在对应APP内
function isInApp(platformType, userAgent) {
  switch (platformType) {
    case 'douyin':
      return userAgent.includes('aweme') || userAgent.includes('tiktok');
    case 'meituan':
      return userAgent.includes('meituan');
    case 'dianping':
      return userAgent.includes('dianping');
    default:
      return false;
  }
}
```

## 扩展功能

### 1. 统计分析
- 团购券点击统计
- 平台分布统计
- 转化率分析

### 2. 营销功能
- 限时团购券
- 限量团购券
- 会员专享团购券

### 3. 个性化推荐
- 基于用户行为推荐
- 地理位置相关推荐
- 历史购买偏好推荐

## 注意事项

### 1. 平台限制
- 各平台的URL Scheme可能会变化
- 需要定期验证跳转链接的有效性
- 部分平台可能有反爬虫机制

### 2. 用户体验
- 确保跳转链接的准确性
- 提供清晰的错误提示
- 考虑网络环境较差的情况

### 3. 数据安全
- 团购券信息的保密性
- 防止恶意刷量
- 用户隐私保护

## 技术优化

### 1. 性能优化
- 团购券列表缓存
- 图片懒加载
- 接口响应优化

### 2. 容错处理
- 网络异常处理
- 平台服务异常处理
- 降级方案设计

### 3. 监控告警
- 跳转成功率监控
- 接口响应时间监控
- 异常情况告警
