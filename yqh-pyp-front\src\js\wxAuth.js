import fly from '../js/request';
const APPID = process.env.VUE_APP_WX_APPID;
const WX_AUTH_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + APPID +
    '&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_userinfo&state=0#wechat_redirect';

// 防止重复调用的标志
let isAuthInProgress = false;

/**
 * 微信授权
 */
export default function wxAuth(url) {
    return new Promise((resolve, reject) => {
        // 防止重复调用
        if (isAuthInProgress) {
            console.log("微信授权正在进行中，请勿重复操作");
            reject(new Error("授权正在进行中"));
            return;
        }

        isAuthInProgress = true;

        if (process.env.NODE_ENV === 'development') {
            // 测试环境模拟调用
            fly.post('/pyp/wxAuth/devGetUserInfo').then(res => {
                isAuthInProgress = false;
                if (res.code == 200) {
                    console.log("微信授权完成");
                    resolve(res.data);
                } else {
                    console.log("换取openid失败:", res.msg);
                    reject(new Error(res.msg || "开发环境授权失败"));
                }
            }).catch(error => {
                isAuthInProgress = false;
                console.error("开发环境授权请求失败:", error);
                reject(error);
            });
        } else {
            // 正式环境正式调用
            let code = getUrlParam('code');
            console.log("获取到的code:", code);
            if (!code) {
                // 未经过微信授权，跳转到微信授权页面
                console.log("未获取到code，跳转微信授权");
                isAuthInProgress = false;
                let currentUrl = encodeURIComponent(url || window.location.href);
                // 立即跳转到微信授权页面
                window.location.href = WX_AUTH_URL.replace('REDIRECT_URI', currentUrl);
                return; // 确保不会继续执行
            } else {
                console.log("获取到code，开始换取用户信息:", code);
                fly.post('/pyp/wxAuth/codeToUserInfo', {
                    code: code
                }).then(res => {
                    isAuthInProgress = false;
                    if (res.code == 200) {
                        console.log("微信授权完成");
                        resolve(res.data);
                    } else {
                        console.log("换取openid失败:", res.msg);
                        reject(new Error(res.msg || "换取用户信息失败"));
                    }
                }).catch(error => {
                    isAuthInProgress = false;
                    console.error("微信授权请求失败:", error);
                    reject(error);
                });
            }
        }
    });
}

function getUrlParam(key) { //获取当前页面url中的参数
    var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    var result = window.location.search.substring(1).match(reg);
    return result ? decodeURIComponent(result[2]) : '';
}