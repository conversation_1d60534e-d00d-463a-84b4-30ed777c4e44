<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="类型" prop="type">
            <el-select v-model="dataForm.type" placeholder="类型" filterable>
              <el-option v-for="item in tripType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { tripType } from '@/data/activity'
export default {
  data() {
    return {
      times: [],
      tripType,
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        type: 0,
      },
      dataRule: {
        type: [
          { required: true, message: '类型不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
      dateChange(v) {
        this.dataForm.inStartDate = v[0];
        this.dataForm.inEndDate = v[1];
      },
    init(id,activityId,activityGuestId) {
      this.getToken();
      this.dataForm.id = id || 0
      this.dataForm.activityGuestId = activityGuestId || 0
      this.dataForm.activityId = activityId || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityguesttrip/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.type = data.activityGuestTrip.type
            }
          })
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/activity/activityguesttrip/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'type': this.dataForm.type,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
