<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="参会表id" prop="activityUserId">
      <el-input v-model="dataForm.activityUserId" placeholder="参会表id"></el-input>
    </el-form-item>
    <el-form-item label="签到or签退：0-未签到，1-已签到，2-已签退" prop="signType">
      <el-input v-model="dataForm.signType" placeholder="签到or签退：0-未签到，1-已签到，2-已签退"></el-input>
    </el-form-item>
    <el-form-item label="签到时间" prop="signTime">
      <el-input v-model="dataForm.signTime" placeholder="签到时间"></el-input>
    </el-form-item>
    <el-form-item label="签到方式：0-后台签到，1-微信扫码签到" prop="signModel">
      <el-input v-model="dataForm.signModel" placeholder="签到方式：0-后台签到，1-微信扫码签到"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          userId: '',
          activityUserId: '',
          signType: '',
          signTime: '',
          signModel: ''
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          activityUserId: [
            { required: true, message: '参会表id不能为空', trigger: 'blur' }
          ],
          signType: [
            { required: true, message: '签到or签退：0-未签到，1-已签到，2-已签退不能为空', trigger: 'blur' }
          ],
          signTime: [
            { required: true, message: '签到时间不能为空', trigger: 'blur' }
          ],
          signModel: [
            { required: true, message: '签到方式：0-后台签到，1-微信扫码签到不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityusersign/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityUserSign.activityId
                this.dataForm.createOn = data.activityUserSign.createOn
                this.dataForm.createBy = data.activityUserSign.createBy
                this.dataForm.updateOn = data.activityUserSign.updateOn
                this.dataForm.updateBy = data.activityUserSign.updateBy
                this.dataForm.userId = data.activityUserSign.userId
                this.dataForm.activityUserId = data.activityUserSign.activityUserId
                this.dataForm.signType = data.activityUserSign.signType
                this.dataForm.signTime = data.activityUserSign.signTime
                this.dataForm.signModel = data.activityUserSign.signModel
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityusersign/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'userId': this.dataForm.userId,
                'activityUserId': this.dataForm.activityUserId,
                'signType': this.dataForm.signType,
                'signTime': this.dataForm.signTime,
                'signModel': this.dataForm.signModel
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
