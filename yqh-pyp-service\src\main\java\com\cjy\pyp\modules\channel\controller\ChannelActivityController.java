package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.ActivityUserService;
import com.cjy.pyp.modules.channel.utils.ChannelPermissionUtils;
import com.cjy.pyp.modules.sys.controller.AbstractController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 渠道活动查看控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/channel/activity")
@Api(tags = "渠道活动管理")
public class ChannelActivityController extends AbstractController {
    
    @Autowired
    private ActivityService activityService;

    @Autowired
    private ActivityUserService activityUserService;

    @Autowired
    private ChannelPermissionUtils channelPermissionUtils;

    /**
     * 列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("channel:activity:list")
    @ApiOperation(value = "渠道活动列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看相关的活动
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        PageUtils pageUtils = activityService.queryPage(params);

        return R.ok().put("page", pageUtils);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    // @RequiresPermissions("channel:activity:info")
    @ApiOperation(value = "活动信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        // 检查权限
        if (!channelPermissionUtils.canAccessActivity(getUser(), id)) {
            return R.error("无权限访问该活动信息");
        }
        
        ActivityEntity activity = activityService.getById(id);
        return R.ok().put("activity", activity);
    }

    /**
     * 活动参与者列表
     */
    @RequestMapping("/participants/{activityId}")
    // @RequiresPermissions("channel:activity:info")
    @ApiOperation(value = "活动参与者列表", notes = "")
    public R participants(@PathVariable("activityId") Long activityId, 
                         @RequestParam Map<String, Object> params) {
        // 检查权限
        if (!channelPermissionUtils.canAccessActivity(getUser(), activityId)) {
            return R.error("无权限查看该活动参与者");
        }
        
        params.put("activityId", activityId);
        
        // 渠道管理员只能查看自己渠道业务员推荐的参与者
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 调用活动用户服务获取参与者列表
        PageUtils participants = activityUserService.queryPage(params);

        return R.ok().put("page", participants);
    }

    /**
     * 活动统计信息
     */
    @RequestMapping("/stats/{activityId}")
    // @RequiresPermissions("channel:activity:info")
    @ApiOperation(value = "活动统计信息", notes = "")
    public R stats(@PathVariable("activityId") Long activityId) {
        // 检查权限
        if (!channelPermissionUtils.canAccessActivity(getUser(), activityId)) {
            return R.error("无权限查看该活动统计");
        }
        
        // 渠道管理员只能查看自己渠道相关的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("activityId", activityId);
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 调用统计服务获取活动统计数据
        Map<String, Object> stats = getActivityStatsByChannel(activityId, accessibleChannelIds);

        return R.ok().put("stats", stats);
    }

    /**
     * 渠道活动总体统计
     */
    @RequestMapping("/overview")
    // @RequiresPermissions("channel:activity:list")
    @ApiOperation(value = "渠道活动总体统计", notes = "")
    public R overview(@CookieValue String appid) {
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("appid", appid);
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 调用统计服务获取总体统计数据
        Map<String, Object> overview = getActivityOverviewByChannel(accessibleChannelIds, appid);

        return R.ok().put("overview", overview);
    }

    /**
     * 获取活动统计数据（按渠道）
     * @param activityId 活动ID
     * @param channelIds 可访问的渠道ID列表
     * @return 统计数据
     */
    private Map<String, Object> getActivityStatsByChannel(Long activityId, List<Long> channelIds) {
        Map<String, Object> stats = new java.util.HashMap<>();

        try {
            // 查询活动基本信息
            ActivityEntity activity = activityService.getById(activityId);
            if (activity != null) {
                stats.put("activityName", activity.getName());
                stats.put("activityCode", activity.getCode());
                stats.put("startTime", activity.getStartTime());
                stats.put("endTime", activity.getEndTime());
            }

            // 查询参与者统计
            Map<String, Object> userParams = new java.util.HashMap<>();
            userParams.put("activityId", activityId);
            if (channelIds != null) {
                userParams.put("channelIds", channelIds);
            }

            // 这里可以调用具体的统计查询
            // 暂时返回模拟数据
            stats.put("totalParticipants", 0);
            stats.put("activeParticipants", 0);
            stats.put("completedTasks", 0);
            stats.put("totalRevenue", 0.0);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return stats;
    }

    /**
     * 获取渠道活动总体统计
     * @param channelIds 可访问的渠道ID列表
     * @param appid 应用ID
     * @return 统计数据
     */
    private Map<String, Object> getActivityOverviewByChannel(List<Long> channelIds, String appid) {
        Map<String, Object> overview = new java.util.HashMap<>();

        try {
            // 查询活动总数
            Map<String, Object> activityParams = new java.util.HashMap<>();
            activityParams.put("appid", appid);
            if (channelIds != null) {
                activityParams.put("channelIds", channelIds);
            }

            // 这里可以调用具体的统计查询
            // 暂时返回模拟数据
            overview.put("totalActivities", 0);
            overview.put("activeActivities", 0);
            overview.put("totalParticipants", 0);
            overview.put("totalRevenue", 0.0);
            overview.put("averageParticipants", 0.0);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return overview;
    }
}
