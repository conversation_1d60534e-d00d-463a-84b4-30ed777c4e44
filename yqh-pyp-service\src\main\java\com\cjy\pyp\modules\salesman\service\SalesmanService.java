package com.cjy.pyp.modules.salesman.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;

import java.util.List;
import java.util.Map;

/**
 * 业务员服务接口
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
public interface SalesmanService extends IService<SalesmanEntity> {

    /**
     * 分页查询业务员列表
     * @param params 查询参数
     * @return 分页结果
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 根据应用ID查询业务员列表
     * @param appid 应用ID
     * @return 业务员列表
     */
    List<SalesmanEntity> findByAppid(String appid);

    /**
     * 根据业务员编号查询业务员
     * @param code 业务员编号
     * @param appid 应用ID
     * @return 业务员信息
     */
    SalesmanEntity findByCode(String code, String appid);

    /**
     * 根据手机号查询业务员
     * @param mobile 手机号
     * @param appid 应用ID
     * @return 业务员信息
     */
    SalesmanEntity findByMobile(String mobile, String appid);

    /**
     * 检查业务员编号是否存在
     * @param code 业务员编号
     * @param appid 应用ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否存在
     */
    boolean existsByCode(String code, String appid, Long excludeId);

    /**
     * 检查手机号是否存在
     * @param mobile 手机号
     * @param appid 应用ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否存在
     */
    boolean existsByMobile(String mobile, String appid, Long excludeId);

    /**
     * 分页查询业务员列表（包含统计信息）
     * @param params 查询参数
     * @return 分页结果
     */
    List<SalesmanEntity> queryPageWithStats(Map<String, Object> params);

    /**
     * 计算业务员层级
     * @param salesmanId 业务员ID
     * @return 层级级别
     */
    Integer calculateLevel(Long salesmanId);

    /**
     * 更新业务员层级
     * @param salesmanId 业务员ID
     */
    void updateLevel(Long salesmanId);

    /**
     * 搜索业务员
     * @param keyword 搜索关键词
     * @param limit 返回数量限制
     * @param appid 应用ID
     * @return 业务员列表
     */
    List<SalesmanEntity> searchSalesmen(String keyword, Integer limit, String appid);

    /**
     * 获取业务员订单总体统计
     * @param params 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOrderStats(Map<String, Object> params);
}
