package com.cjy.pyp.modules.wx.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信小程序URL Scheme生成表单
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "微信小程序URL Scheme生成表单")
public class WxMiniProgramSchemeForm {

    private Long activityId;
    @ApiModelProperty(value = "小程序页面路径，必须是已经发布的小程序存在的页面，不可携带query")
    private String path;

    @ApiModelProperty(value = "小程序页面query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符")
    private String query;

    @ApiModelProperty(value = "要打开的小程序版本。正式版为release，体验版为trial，开发版为develop", example = "trial")
    private String envVersion = "trial";

    @ApiModelProperty(value = "生成的scheme码类型，到期失效：true，永久有效：false", example = "true")
    private Boolean isExpire = true;

    @ApiModelProperty(value = "到期失效的scheme码的失效时间，为Unix时间戳。生成的到期失效scheme码在该时间前有效。最长有效期为30天")
    private Long expireTime;

    @ApiModelProperty(value = "跳转到的目标小程序信息")
    private JumpWxa jumpWxa;

    @Data
    @ApiModel(value = "跳转目标小程序信息")
    public static class JumpWxa {
        @ApiModelProperty(value = "通过scheme码进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带query")
        private String path;

        @ApiModelProperty(value = "通过scheme码进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符")
        private String query;

        @ApiModelProperty(value = "要打开的小程序版本。正式版为release，体验版为trial，开发版为develop")
        private String envVersion;
    }
}
