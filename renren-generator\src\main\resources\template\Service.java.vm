package ${package}.${moduleName}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import ${mainPath}.common.utils.PageUtils;
import ${package}.${moduleName}.entity.${className}Entity;

import java.util.Map;
import java.util.ArrayList;
import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
public interface ${className}Service extends IService<${className}Entity> {

    PageUtils queryPage(Map<String, Object> params);

    List<${className}Entity> findByIds(List<Long> ids);
}

