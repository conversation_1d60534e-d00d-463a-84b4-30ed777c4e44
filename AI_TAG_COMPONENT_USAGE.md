# AI标签组件使用说明

## 组件更新

已将AI标签配置从普通输入框更新为`tags-editorlist`组件，提供更好的用户体验。

## 主要改动

### 1. 组件导入
```javascript
components: {
  TagsEditorlist: () => import("@/components/tags-editorlist")
}
```

### 2. 模板更新
```vue
<!-- AI标签配置 -->
<el-row>
  <el-col :span="24">
    <el-form-item label="AI标签" prop="aiTag">
      <tags-editorlist 
        v-model="aiTagList"
        placeholder="添加AI标签，如：男、女、儿童、老人等"
        :max-tags="8"
        :max-length="10"
        @change="onAiTagChange">
      </tags-editorlist>
      <div style="margin-top: 5px; font-size: 12px; color: #909399;">
        <i class="el-icon-info"></i>
        AI标签用于生成针对特定受众的文案。用户在"换一篇"时可以选择不同标签生成对应的文案，建议不超过8个标签
      </div>
    </el-form-item>
  </el-col>
</el-row>
```

### 3. 数据结构
```javascript
data() {
  return {
    // 原有的aiTag字段保持不变，用于与后端交互
    dataForm: {
      aiTag: ''
    },
    // 新增aiTagList数组，用于tags-editorlist组件
    aiTagList: []
  }
}
```

### 4. 数据转换
```javascript
methods: {
  // 获取数据时：字符串转数组
  getInfo(id) {
    // ... 获取数据后
    this.dataForm.aiTag = data.activity.aiTag || ''
    this.aiTagList = data.activity.aiTag ? 
      data.activity.aiTag.split(',').map(tag => tag.trim()).filter(tag => tag) : []
  },
  
  // 提交数据时：数组转字符串
  dataFormSubmit() {
    // ...
    'aiTag': this.aiTagList.join(',')
  },
  
  // 标签变化处理
  onAiTagChange(tags) {
    this.aiTagList = tags;
    this.dataForm.aiTag = tags.join(',');
  }
}
```

## 组件特性

### tags-editorlist 组件属性
- `v-model`: 绑定标签数组
- `placeholder`: 输入提示文字
- `max-tags`: 最大标签数量（建议8个）
- `max-length`: 单个标签最大长度（建议10个字符）
- `@change`: 标签变化事件

### 用户体验提升
1. **可视化标签**: 标签以卡片形式显示，更直观
2. **便捷编辑**: 支持点击删除、拖拽排序
3. **输入验证**: 自动去重、长度限制
4. **实时预览**: 输入时实时显示效果

## 使用示例

### 理发店配置
```
标签: [男] [女] [儿童] [老人]
```

### 餐厅配置
```
标签: [情侣] [家庭] [商务] [朋友聚会]
```

### 健身房配置
```
标签: [减肥] [增肌] [塑形] [康复训练]
```

## 注意事项

1. **标签数量**: 建议不超过8个标签，避免选择过于复杂
2. **标签长度**: 每个标签建议不超过10个字符
3. **标签命名**: 使用简洁明了的词汇
4. **数据同步**: 组件会自动同步aiTagList和dataForm.aiTag

## 兼容性

- 保持与原有API的完全兼容
- 数据库存储格式不变（逗号分隔的字符串）
- 前端显示和编辑体验大幅提升

## 测试建议

1. **添加标签**: 测试添加不同长度的标签
2. **删除标签**: 测试点击删除功能
3. **数量限制**: 测试超过最大数量时的处理
4. **数据保存**: 测试保存后重新打开的数据一致性
5. **特殊字符**: 测试包含特殊字符的标签处理
