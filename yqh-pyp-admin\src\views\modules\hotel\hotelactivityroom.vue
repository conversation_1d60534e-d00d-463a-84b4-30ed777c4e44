<template>
  <div class="mod-config">
    <div style="text-align: center;padding: 20px;font-weight: bold;font-size: 28px">{{hotelActivityInfo.hotelName}}的房型列表</div>
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="房型名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('hotel:hotelactivityroom:save')" type="primary" @click="addOrUpdateHandle(undefined,dataForm.activityId,dataForm.hotelActivityId,hotelActivityInfo.hotelId)">新增</el-button>
        <el-button v-if="isAuth('hotel:hotelactivityroom:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :default-expand-all="true" :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="expand">
      <template slot-scope="props" v-if="props.row.hotelActivityRoomStockEntities">
        <el-table :data="props.row.hotelActivityRoomStockEntities" border v-loading="dataListLoading" style="width: 100%;">
          <el-table-column
            prop="stockDate"
            header-align="center"
            align="center"
            label="库存日期">
          </el-table-column>
          <el-table-column
            prop="number"
            header-align="center"
            align="center"
            label="总库存">
          </el-table-column>
          <el-table-column
            prop="spareNumber"
            header-align="center"
            align="center"
            label="剩余库存">
          </el-table-column>
          <el-table-column
            prop="alreadyNumber"
            header-align="center"
            align="center"
            label="已售库存">
          </el-table-column>
          <el-table-column
            prop="price"
            header-align="center"
            align="center"
            label="单价">
          </el-table-column>
          <el-table-column
            prop="bedPrice"
            header-align="center"
            align="center"
            label="床位价格">
          </el-table-column>
          <el-table-column
            fixed="right"
            header-align="center"
            align="center"
            width="200"
            label="操作">
            <template slot-scope="scopeExt">
              <el-button type="text" size="small" v-if="scopeExt.$index == 0" @click="stockBeforeHandle(props.row.id,scopeExt.row.stockDate)">往前加一天</el-button>
              <el-button type="text" size="small" v-if="scopeExt.$index == props.row.hotelActivityRoomStockEntities.length -1 " @click="stockAfterHandle(props.row.id,scopeExt.row.stockDate)">往后加一天</el-button>
              <el-button type="text" size="small" @click="stockUpdateHandle(scopeExt.row.id)">修改</el-button>
              <el-button type="text" style="color: red" size="small" v-if="scopeExt.$index == props.row.hotelActivityRoomStockEntities.length -1 || scopeExt.$index == 0" @click="stockDeleteHandle(scopeExt.row.id)">删除</el-button>
          </template>
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="房型名称">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="销售状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-'+(scope.row.status)">{{scope.row.status | statusFilter}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="bedStatus" header-align="center" align="center" label="床位销售状态">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-'+(scope.row.bedStatus)">{{scope.row.bedStatus | statusFilter}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="price" header-align="center" align="center" label="单价">
      </el-table-column>
      <el-table-column prop="bedPrice" header-align="center" align="center" label="床位价格">
      </el-table-column>
      <el-table-column prop="bedNumber" header-align="center" align="center" label="床位数量">
      </el-table-column>
      <el-table-column prop="inDate" header-align="center" align="center" label="库存开始时间">
      </el-table-column>
      <el-table-column prop="outDate" header-align="center" align="center" label="库存结束时间">
      </el-table-column>
      <el-table-column prop="orderBy" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
            <el-button type="text" size="small" @click="number(scope.row.id)">房号管理</el-button>
            <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id,dataForm.activityId,dataForm.hotelActivityId,hotelActivityInfo.hotelId)">修改</el-button>
            <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <stock-add-or-update v-if="stockAddOrUpdateVisible" ref="stockAddOrUpdate" @refreshDataList="getDataList"></stock-add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './hotelactivityroom-add-or-update'
  import StockAddOrUpdate from './hotelactivityroomstock-add-or-update'
  import {saleStatus} from '@/data/room.js'
  import {addDays} from "@/utils/date.js";
  export default {
    data() {
      return {
        dataForm: {
          name: '',
          activityId: '',
          hotelActivityId: ''
        },
        dataList: [],
        hotelActivityInfo: {},
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        stockAddOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate,StockAddOrUpdate
    },
    activated() {
      this.dataForm.activityId = this.$route.query.activityId;
      this.dataForm.hotelActivityId = this.$route.query.hotelActivityId;
      this.getDataList()
      this.getHotelActivityInfo()
    },
    filters: {
      statusFilter(v) {
        let data = saleStatus.filter(item => item.key === v)
        if (data.length >= 1) {
          return data[0].value;
        }
      },
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/hotel/hotelactivityroom/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'activityId': this.dataForm.activityId,
            'hotelActivityId': this.dataForm.hotelActivityId,
            'name': this.dataForm.name
          })
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle(id,activityId,hotelActivityId,hotelId) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id,activityId,hotelActivityId,hotelId)
        })
      },
      stockUpdateHandle(id) {
        this.stockAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.stockAddOrUpdate.init(id,undefined,undefined)
        })
      },
      stockBeforeHandle(roomId,stockDate) {
        var date = addDays(stockDate,-1);
        this.stockAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.stockAddOrUpdate.init(undefined,roomId,date)
        })
      },
      stockAfterHandle(roomId,stockDate) {
        var date = addDays(stockDate,1);
        this.stockAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.stockAddOrUpdate.init(undefined,roomId,date)
        })
      },
      // 删除
      deleteHandle(id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/hotel/hotelactivityroom/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({
            data
          }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      stockDeleteHandle(id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定删除操作操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/hotel/hotelactivityroomstock/delete'),
            method: 'post',
            data: this.$http.adornData(ids, false)
          }).then(({data}) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      getHotelActivityInfo() {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivity/info/${this.dataForm.hotelActivityId}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.hotelActivityInfo = data.hotelActivity;
          }
        })
      },
      number(v) {

        this.$router.push({
              name: 'hotelactivityroomnumber',
              query: {
                  activityId: this.dataForm.activityId,
                  hotelActivityRoomId: v
              }
          })
      }
    }
  }
</script>
