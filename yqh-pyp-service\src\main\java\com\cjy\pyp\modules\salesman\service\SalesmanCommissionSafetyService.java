package com.cjy.pyp.modules.salesman.service;

import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;

import java.math.BigDecimal;

/**
 * 业务员佣金安全处理服务接口
 * 提供防重复、异常处理、数据一致性保障等功能
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
public interface SalesmanCommissionSafetyService {

    /**
     * 安全生成创建活动佣金
     * 包含防重复检查、异常处理、事务回滚等安全机制
     * 
     * @param salesmanId 业务员ID
     * @param rechargeRecordId 充值记录ID
     * @param orderAmount 订单金额
     * @param appid 应用ID
     * @return 佣金记录，如果已存在或生成失败则返回null
     */
    SalesmanCommissionRecordEntity safeGenerateCreateActivityCommission(Long salesmanId, Long rechargeRecordId, 
                                                                        BigDecimal orderAmount, String appid);

    /**
     * 安全生成充值次数佣金
     * 包含防重复检查、异常处理、事务回滚等安全机制
     * 
     * @param salesmanId 业务员ID
     * @param rechargeRecordId 充值记录ID
     * @param orderAmount 订单金额
     * @param appid 应用ID
     * @return 佣金记录，如果已存在或生成失败则返回null
     */
    SalesmanCommissionRecordEntity safeGenerateRechargeCountCommission(Long salesmanId, Long rechargeRecordId, 
                                                                       BigDecimal orderAmount, String appid);

    /**
     * 安全生成用户转发佣金
     * 包含防重复检查、异常处理、事务回滚等安全机制
     * 
     * @param salesmanId 业务员ID
     * @param usageRecordId 使用记录ID
     * @param appid 应用ID
     * @return 佣金记录，如果已存在或生成失败则返回null
     */
    SalesmanCommissionRecordEntity safeGenerateUserForwardCommission(Long salesmanId, Long usageRecordId, String appid);

    /**
     * 检查佣金记录是否已存在
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param appid 应用ID
     * @return 是否已存在
     */
    boolean isCommissionExists(String businessType, Long businessId, String appid);

    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁键
     * @param expireTime 过期时间（秒）
     * @return 是否获取成功
     */
    boolean acquireLock(String lockKey, int expireTime);

    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁键
     * @return 是否释放成功
     */
    boolean releaseLock(String lockKey);

    /**
     * 佣金计算异常补偿
     * 当佣金计算失败时，记录失败信息并尝试补偿
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param salesmanId 业务员ID
     * @param errorMessage 错误信息
     * @param appid 应用ID
     */
    void recordCommissionFailure(String businessType, Long businessId, Long salesmanId, 
                                String errorMessage, String appid);

    /**
     * 重试失败的佣金计算
     * 
     * @param appid 应用ID
     * @return 重试成功的数量
     */
    Integer retryFailedCommissions(String appid);

    /**
     * 验证佣金计算的数据一致性
     * 检查佣金记录与业务记录的一致性
     * 
     * @param commissionRecordId 佣金记录ID
     * @return 验证结果消息，null表示验证通过
     */
    String validateCommissionConsistency(Long commissionRecordId);

    /**
     * 修复数据不一致的佣金记录
     * 
     * @param commissionRecordId 佣金记录ID
     * @return 是否修复成功
     */
    boolean repairInconsistentCommission(Long commissionRecordId);

    /**
     * 批量验证佣金数据一致性
     * 
     * @param appid 应用ID
     * @return 不一致的记录数量
     */
    Integer batchValidateCommissionConsistency(String appid);

    /**
     * 生成佣金计算的唯一标识
     * 用于防重复处理
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 唯一标识
     */
    String generateCommissionUniqueKey(String businessType, Long businessId, Long salesmanId, String appid);

    /**
     * 清理过期的锁和临时数据
     * 
     * @param appid 应用ID
     * @return 清理的数量
     */
    Integer cleanupExpiredData(String appid);
}
