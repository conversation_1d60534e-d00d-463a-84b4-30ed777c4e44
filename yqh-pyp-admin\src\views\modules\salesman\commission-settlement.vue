<template>
  <div class="mod-commission-settlement">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.totalBatches || 0 }}</div>
            <div class="stats-label">总批次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.totalAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">总结算金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.completedBatches || 0 }}</div>
            <div class="stats-label">已完成批次</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.pendingBatches || 0 }}</div>
            <div class="stats-label">待处理批次</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.batchNo" placeholder="批次号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="待结算" :value="0"></el-option>
          <el-option label="已结算" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="createBatchHandle()">创建结算批次</el-button>
        <el-button @click="getStats()">刷新统计</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="batchNo" header-align="center" align="center" label="批次号">
      </el-table-column>
      <el-table-column prop="settlementDate" header-align="center" align="center" label="结算日期">
      </el-table-column>
      <el-table-column prop="salesmanCount" header-align="center" align="center" label="业务员数量">
      </el-table-column>
      <el-table-column prop="recordCount" header-align="center" align="center" label="记录数量">
      </el-table-column>
      <el-table-column prop="totalAmount" header-align="center" align="center" label="结算金额">
        <template slot-scope="scope">
          ¥{{ scope.row.totalAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="statusDesc" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ scope.row.statusDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementTime" header-align="center" align="center" width="150" label="完成时间">
        <template slot-scope="scope">
          {{ scope.row.settlementTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetails(scope.row)">详情</el-button>
          <el-button v-if="scope.row.status === 0" type="text" size="small" @click="executeSettlement(scope.row)">
            执行结算
          </el-button>
          <el-button v-if="scope.row.status !== 2" type="text" size="small" @click="cancelSettlement(scope.row)">
            取消
          </el-button>
          <el-button v-if="scope.row.status === 2" type="text" size="small" @click="deleteHandle(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 创建结算批次弹窗 -->
    <el-dialog title="创建结算批次" :visible.sync="createBatchDialogVisible" width="50%">
      <el-form :model="batchForm" :rules="batchRule" ref="batchForm" label-width="120px">
        <el-form-item label="业务员" prop="salesmanIds">
          <el-select v-model="batchForm.salesmanIds" multiple placeholder="请选择业务员（可多选）" style="width: 100%;">
            <el-option v-for="salesman in salesmanList" :key="salesman.id"
              :label="salesman.name + '(' + salesman.code + ')'" :value="salesman.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker v-model="batchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="batchForm.remarks" type="textarea" placeholder="结算备注"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="createBatchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createBatchSubmit()">确定</el-button>
      </span>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog title="结算批次详情" :visible.sync="detailsDialogVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="批次号">{{ selectedBatch.batchNo }}</el-descriptions-item>
        <el-descriptions-item label="结算日期">{{ selectedBatch.settlementDate }}</el-descriptions-item>
        <el-descriptions-item label="业务员数量">{{ selectedBatch.salesmanCount }}</el-descriptions-item>
        <el-descriptions-item label="记录数量">{{ selectedBatch.recordCount }}</el-descriptions-item>
        <el-descriptions-item label="结算金额">¥{{ (selectedBatch.totalAmount || 0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(selectedBatch.status)">
            {{ selectedBatch.statusDesc }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="结算类型">{{ selectedBatch.settlementTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ selectedBatch.settlementTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ selectedBatch.createOn }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ selectedBatch.remarks || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        batchNo: '',
        status: ''
      },
      dateRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      stats: {},
      createBatchDialogVisible: false,
      detailsDialogVisible: false,
      selectedBatch: {},
      batchForm: {
        salesmanIds: [],
        dateRange: [],
        remarks: ''
      },
      batchRule: {
        salesmanIds: [
          { required: true, message: '请选择业务员', trigger: 'change' }
        ],
        dateRange: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ]
      },
      salesmanList: []
    }
  },
  activated() {
    this.getDataList()
    this.getStats()
    this.getSalesmanList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'batchNo': this.dataForm.batchNo,
        'status': this.dataForm.status
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.startDate = this.dateRange[0]
        params.endDate = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/settlement/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取统计数据
    getStats() {
      let params = {}

      if (this.dateRange && this.dateRange.length === 2) {
        params.startDate = this.dateRange[0]
        params.endDate = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/settlement/stats'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.stats = data.stats
        }
      })
    },
    // 获取业务员列表
    getSalesmanList() {
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.salesmanList = data.page.list
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 创建结算批次
    createBatchHandle() {
      this.createBatchDialogVisible = true
      this.$nextTick(() => {
        this.$refs['batchForm'].resetFields()
      })
    },
    // 提交创建结算批次
    createBatchSubmit() {
      this.$refs['batchForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/salesman/commission/settlement/createBatch'),
            method: 'post',
            data: this.$http.adornData({
              salesmanIds: this.batchForm.salesmanIds,
              startTime: this.batchForm.dateRange[0],
              endTime: this.batchForm.dateRange[1],
              remarks: this.batchForm.remarks
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '创建结算批次成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.createBatchDialogVisible = false
                  this.getDataList()
                  this.getStats()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 执行结算
    executeSettlement(row) {
      this.$confirm(`确定执行批次[${row.batchNo}]的结算操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/commission/settlement/execute'),
          method: 'post',
          params: this.$http.adornParams({
            batchNo: row.batchNo
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '执行结算成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.getStats()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 取消结算
    cancelSettlement(row) {
      this.$confirm(`确定取消批次[${row.batchNo}]的结算?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/commission/settlement/cancel'),
          method: 'post',
          params: this.$http.adornParams({
            batchNo: row.batchNo
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '取消结算成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.getStats()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 查看详情
    viewDetails(row) {
      this.selectedBatch = row
      this.detailsDialogVisible = true
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定删除该结算批次?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/commission/settlement/delete'),
          method: 'post',
          data: this.$http.adornData([id], false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.getStats()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'warning'  // 待结算
        case 1: return 'success'  // 已结算
        case 2: return 'danger'   // 已取消
        default: return 'info'
      }
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}
</style>
