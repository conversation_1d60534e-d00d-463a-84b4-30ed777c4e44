<template>
  <div class="finished-video">
    <!-- 顶部背景装饰 -->
    <div class="page-header-bg"></div>

    <van-nav-bar
      title="成品视频"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    />

    <!-- 功能区域 -->
    <div class="function-section">
      <div class="function-bar">
        <div class="function-item ai-generate" @click="showGenerateDialog = true">
          <div class="function-icon">
            <van-icon name="video-o" />
          </div>
          <div class="function-content">
            <span class="function-title">AI生成视频</span>
          </div>
          <van-icon name="arrow" class="function-arrow" />
        </div>

        <!-- 搜索框 -->
        <div class="search-compact">
          <van-search
            v-model="searchKeyword"
            placeholder="搜索视频..."
            @search="onSearch"
            @clear="onSearch"
            shape="round"
            class="compact-search"
          />
        </div>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-list">
      <div class="list-header" v-if="videoList.length > 0">
        <span class="list-count">共 {{ videoList.length }} 个视频</span>
        <van-button size="mini" icon="refresh" @click="onSearch">刷新</van-button>
      </div>

      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        class="custom-list"
      >
        <div
          v-for="item in videoList"
          :key="item.id"
          class="video-item"
        >
          <div class="video-preview" @click="previewVideo(item)">
            <div class="video-container">
              <div v-if="item.mediaType === 'image'" class="image-container">
                <img
                  v-if="item.mediaUrl"
                  :src="item.mediaUrl"
                  class="image-element"
                  alt="成品图片"
                />
                <div v-else class="no-image">
                  <van-icon name="photo-o" size="40" />
                  <p>图片处理中...</p>
                </div>
                <div class="image-overlay">
                  <div class="image-count" v-if="item.imageCount > 1">
                    {{ item.imageCount }}张
                  </div>
                </div>
              </div>
              <div v-else class="video-container">
                <video
                  v-if="item.mediaUrl"
                  :src="item.mediaUrl"
                  preload="metadata"
                  :poster="item.coverUrl"
                  class="video-element"
                />
                <div v-else class="no-video">
                  <div class="processing-animation">
                    <van-loading size="24px" color="#1989fa" />
                  </div>
                  <p class="processing-text">视频处理中...</p>
                </div>
                <div class="play-overlay">
                  <div class="play-button">
                    <van-icon name="play" size="24" />
                  </div>
                </div>
                <div class="video-duration" v-if="item.duration">
                  {{ formatDuration(item.duration) }}
                </div>
              </div>
            </div>
          </div>
          <div class="video-info">
            <div class="video-header">
              <h3 class="video-title">{{ item.name }}</h3>
              <div class="video-badges">
                <van-tag
                  :type="item.mediaType === 'video' ? 'success' : 'primary'"
                  size="small"
                  class="video-tag"
                >
                  {{ item.mediaType === 'video' ? '成品视频' : '成品图片' }}
                </van-tag>
                <van-tag
                  v-if="item.platform"
                  color="#667eea"
                  size="small"
                  class="platform-tag"
                >
                  {{ getPlatformName(item.platform) }}
                </van-tag>
                <van-tag v-if="item.useCount > 0" color="#f39c12" size="small">
                  热门
                </van-tag>
              </div>
            </div>
            <div class="video-meta">
              <div class="meta-item">
                <van-icon name="description" />
                <span>{{ formatFileSize(item.fileSize) }}</span>
              </div>
              <div class="meta-item">
                <van-icon name="clock-o" />
                <span>{{ formatDuration(item.duration) }}</span>
              </div>
              <div class="meta-item">
                <van-icon name="eye-o" />
                <span>{{ item.useCount || 0 }} 次使用</span>
              </div>
            </div>
            <div class="video-actions">
              <van-button
                size="small"
                type="primary"
                @click="previewVideo(item)"
                :disabled="!item.mediaUrl"
                icon="play-circle-o"
              >
                预览
              </van-button>
              <!-- <van-button
                size="small"
                @click="editVideo(item)"
                icon="edit"
              >
                编辑
              </van-button> -->
              <van-button
                size="small"
                type="danger"
                @click="deleteVideo(item)"
                icon="delete-o"
              >
                删除
              </van-button>
            </div>
          </div>
        </div>
      </van-list>

      <!-- 空状态 -->
      <div v-if="!loading && videoList.length === 0" class="empty-state">
        <div class="empty-content">
          <van-icon name="video-o" size="80" class="empty-icon" />
          <h3 class="empty-title">暂无成品视频</h3>
          <p class="empty-desc">开始创建您的第一个成品视频吧</p>
          <div class="empty-actions">
            <van-button type="primary" @click="showGenerateDialog = true" icon="video-o">
              AI生成视频
            </van-button>
            <van-button @click="showUploadDialog = true" icon="plus">
              上传视频
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- AI智能生成弹窗 -->
    <van-dialog
      v-model="showGenerateDialog"
      title=""
      show-cancel-button
      @confirm="handleGenerate"
      confirm-button-text="立即生成"
      class="custom-dialog generate-dialog"
      width="90%"
    >
      <div class="generate-form">
        <div class="dialog-header">
          <div class="header-icon">
            <van-icon name="video-o" size="32" />
          </div>
          <h3 class="dialog-title">AI智能生成</h3>
          <p class="dialog-subtitle">为不同平台创建专业的成品内容</p>
        </div>

        <!-- 生成配置 -->
        <div class="generate-config">
          <!-- <van-field
            v-model="generateForm.name"
            label="内容名称"
            placeholder="请输入生成内容的名称"
            required
            class="custom-field"
          /> -->

          <van-field
            readonly
            clickable
            label="选择平台"
            :value="selectedPlatformName"
            placeholder="请选择目标平台"
            @click="showPlatformPicker = true"
            required
            class="custom-field"
            right-icon="arrow-down"
          />

          <van-field
            readonly
            clickable
            label="内容类型"
            :value="selectedMediaTypeName"
            placeholder="请选择内容类型"
            @click="showMediaTypePicker = true"
            required
            class="custom-field"
            right-icon="arrow-down"
          />

          <van-field
            v-if="generateForm.mediaType === 'image'"
            v-model="generateForm.imageCount"
            label="图片数量"
            type="number"
            placeholder="请输入图片数量"
            class="custom-field"
          />
        </div>

        <div class="generate-features">
          <div class="feature-item" v-if="generateForm.mediaType === 'video'">
            <van-icon name="video-o" class="feature-icon" />
            <div class="feature-content">
              <span class="feature-title">智能剪辑</span>
              <span class="feature-desc">自动识别精彩片段</span>
            </div>
          </div>
          <div class="feature-item" v-if="generateForm.mediaType === 'video'">
            <van-icon name="music-o" class="feature-icon" />
            <div class="feature-content">
              <span class="feature-title">配乐配音</span>
              <span class="feature-desc">智能匹配背景音乐</span>
            </div>
          </div>
          <div class="feature-item" v-if="generateForm.mediaType === 'image'">
            <van-icon name="photo-o" class="feature-icon" />
            <div class="feature-content">
              <span class="feature-title">智能选图</span>
              <span class="feature-desc">从素材库精选图片</span>
            </div>
          </div>
          <div class="feature-item">
            <van-icon name="star-o" class="feature-icon" />
            <div class="feature-content">
              <span class="feature-title">文案匹配</span>
              <span class="feature-desc">关联对应平台文案</span>
            </div>
          </div>
        </div>

        <div class="generate-tips">
          <div class="tip-header">
            <van-icon name="info-o" />
            <span>温馨提示</span>
          </div>
          <ul class="tip-list">
            <li v-if="generateForm.mediaType === 'video'">AI将基于您的活动素材自动生成成品视频</li>
            <li v-if="generateForm.mediaType === 'image'">AI将从图片素材中精选内容生成成品图片</li>
            <li>生成过程需要3-5分钟，请耐心等待</li>
            <li>生成完成后会在列表中显示</li>
          </ul>
        </div>
      </div>
    </van-dialog>

    <!-- 平台选择器 -->
    <van-popup v-model="showPlatformPicker" position="bottom">
      <van-picker
        :columns="platformColumns"
        @confirm="onPlatformConfirm"
        @cancel="showPlatformPicker = false"
        show-toolbar
        title="选择平台"
      />
    </van-popup>

    <!-- 媒体类型选择器 -->
    <van-popup v-model="showMediaTypePicker" position="bottom">
      <van-picker
        :columns="mediaTypeColumns"
        @confirm="onMediaTypeConfirm"
        @cancel="showMediaTypePicker = false"
        show-toolbar
        title="选择内容类型"
      />
    </van-popup>

    <!-- 上传视频弹窗 -->
    <van-dialog
      v-model="showUploadDialog"
      title=""
      show-cancel-button
      @confirm="confirmUpload"
      :confirm-button-loading="uploading"
      confirm-button-text="开始上传"
      class="custom-dialog upload-dialog"
      width="90%"
    >
      <div class="upload-form">
        <div class="dialog-header">
          <div class="header-icon upload-icon">
            <van-icon name="plus" size="32" />
          </div>
          <h3 class="dialog-title">上传成品视频</h3>
          <p class="dialog-subtitle">导入您的本地视频文件</p>
        </div>

        <van-field
          v-model="uploadForm.name"
          label="批次名称"
          placeholder="请输入批次名称（可选）"
          class="custom-field"
        />

        <div class="upload-section">
          <div class="upload-header">
            <span class="upload-title">选择视频文件</span>
            <span class="upload-count">{{ fileList.length }}/3</span>
          </div>
          <van-uploader
            v-model="fileList"
            :max-count="3"
            :after-read="afterRead"
            :before-delete="beforeDelete"
            accept="video/*"
            :max-size="500 * 1024 * 1024"
            @oversize="onOversize"
            multiple
            :preview-size="80"
            upload-text="选择视频"
            class="custom-uploader"
          />
        </div>

        <div class="upload-tips">
          <div class="tip-header">
            <van-icon name="info-o" />
            <span>上传须知</span>
          </div>
          <div class="tip-grid">
            <div class="tip-item">
              <van-icon name="video-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">支持格式</span>
                <span class="tip-desc">MP4、AVI、MOV、WMV、FLV</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="description" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">文件大小</span>
                <span class="tip-desc">单个文件不超过500MB</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="apps-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">数量限制</span>
                <span class="tip-desc">最多可选择3个文件</span>
              </div>
            </div>
            <div class="tip-item">
              <van-icon name="star-o" class="tip-icon" />
              <div class="tip-content">
                <span class="tip-title">推荐内容</span>
                <span class="tip-desc">完整的宣传视频</span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="uploadProgress.length > 0" class="upload-progress">
          <div class="progress-header">
            <van-icon name="clock-o" />
            <span class="progress-title">上传进度</span>
          </div>
          <div v-for="(item, index) in uploadProgress" :key="index" class="progress-item">
            <div class="progress-info">
              <span class="progress-name">{{ item.name }}</span>
              <span class="progress-status">{{ item.status }}</span>
            </div>
            <van-progress :percentage="item.progress" :color="item.color" stroke-width="6" />
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 预览弹窗 -->
    <van-dialog
      v-model="showPreviewDialog"
      title=""
      :show-confirm-button="false"
      show-cancel-button
      cancel-button-text="关闭"
      width="95%"
      class="custom-dialog preview-dialog"
    >
      <div class="preview-container" v-if="currentPreviewItem">
        <div class="preview-header">
          <van-icon
            :name="currentPreviewItem.mediaType === 'video' ? 'play-circle-o' : 'photo-o'"
            size="24"
          />
          <span class="preview-title">
            {{ currentPreviewItem.mediaType === 'video' ? '视频预览' : '图片预览' }}
          </span>
          <span class="preview-subtitle">{{ currentPreviewItem.name }}</span>
        </div>

        <!-- 视频预览 -->
        <div v-if="currentPreviewItem.mediaType === 'video'" class="video-wrapper">
          <video
            v-if="currentPreviewItem.mediaUrl"
            :src="currentPreviewItem.mediaUrl"
            controls
            autoplay
            class="preview-video"
            controlslist="nodownload"
          />
          <div v-else class="no-preview">
            <van-icon name="video-o" size="48" />
            <p>视频处理中，暂无预览</p>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-else class="image-wrapper">
          <van-swipe
            v-if="previewImages.length > 0"
            :autoplay="0"
            :show-indicators="previewImages.length > 1"
            indicator-color="white"
            class="preview-swipe"
            @change="onSwipeChange"
          >
            <van-swipe-item
              v-for="(image, index) in previewImages"
              :key="index"
              class="swipe-item"
            >
              <img
                :src="image.mediaUrl"
                :alt="`图片 ${index + 1}`"
                class="preview-image"
                @load="onImageLoaded"
              />
            </van-swipe-item>
          </van-swipe>
          <div v-else class="no-preview">
            <van-icon name="photo-o" size="48" />
            <p>图片处理中，暂无预览</p>
          </div>

          <!-- 图片计数器 -->
          <div v-if="previewImages.length > 1" class="image-counter">
            {{ currentImageIndex + 1 }} / {{ previewImages.length }}
          </div>
        </div>

        <!-- 预览信息 -->
        <div class="preview-info">
          <div class="info-item">
            <span class="info-label">平台:</span>
            <span class="info-value">{{ getPlatformName(currentPreviewItem.platform) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">类型:</span>
            <span class="info-value">{{ currentPreviewItem.mediaType === 'video' ? '视频' : '图片' }}</span>
          </div>
          <div class="info-item" v-if="currentPreviewItem.mediaType === 'image'">
            <span class="info-label">数量:</span>
            <span class="info-value">{{ currentPreviewItem.imageCount || 1 }}张</span>
          </div>
        </div>

        <!-- 文案信息 -->
        <div v-if="currentTextInfo" class="text-info">
          <!-- <div class="text-header">
            <van-icon name="edit" />
            <span class="text-title">关联文案</span>
          </div> -->

          <div class="text-content">
            <!-- 标题 -->
            <div class="text-item" v-if="currentTextInfo.title">
              <div class="text-label">
                <span>标题</span>
                <van-button
                  size="mini"
                  type="primary"
                  plain
                  @click="copyText(currentTextInfo.title)"
                  class="copy-btn"
                >
                  复制
                </van-button>
              </div>
              <div class="text-value">{{ currentTextInfo.title }}</div>
            </div>

            <!-- 提示词 -->
            <div class="text-item" v-if="currentTextInfo.promptKeyword">
              <div class="text-label">
                <span>提示词</span>
                <van-button
                  size="mini"
                  type="primary"
                  plain
                  @click="copyText(currentTextInfo.promptKeyword)"
                  class="copy-btn"
                >
                  复制
                </van-button>
              </div>
              <div class="text-value">{{ currentTextInfo.promptKeyword }}</div>
            </div>

            <!-- 文案内容 -->
            <div class="text-item" v-if="currentTextInfo.content">
              <div class="text-label">
                <span>文案内容</span>
                <van-button
                  size="mini"
                  type="primary"
                  plain
                  @click="copyText(currentTextInfo.content)"
                  class="copy-btn"
                >
                  复制
                </van-button>
              </div>
              <div class="text-value content-text">{{ currentTextInfo.content }}</div>
            </div>

            <!-- 话题标签 -->
            <div class="text-item" v-if="currentTextInfo.topics">
              <div class="text-label">
                <span>话题标签</span>
                <van-button
                  size="mini"
                  type="primary"
                  plain
                  @click="copyText(currentTextInfo.topics)"
                  class="copy-btn"
                >
                  复制
                </van-button>
              </div>
              <div class="text-value topics-text">{{ currentTextInfo.topics }}</div>
            </div>

            <!-- 一键复制全部 -->
            <div class="copy-all-section">
              <van-button
                type="info"
                size="small"
                block
                @click="copyAllText"
                icon="notes-o"
              >
                一键复制全部文案
              </van-button>
            </div>
          </div>
        </div>

        <!-- 加载文案信息 -->
        <div v-else-if="loadingTextInfo" class="loading-text">
          <van-loading size="16px" />
          <span>加载文案信息中...</span>
        </div>

        <!-- <div class="preview-actions">
          <van-button size="small" icon="download-o" @click="downloadContent">下载</van-button>
          <van-button size="small" icon="share-o" @click="shareContent">分享</van-button>
          <van-button size="small" icon="delete-o" @click="deleteContent" type="danger">删除</van-button>
        </div> -->
      </div>
    </van-dialog>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script>
export default {
  name: 'FinishedVideo',
  data() {
    return {
      activityId: null,
      fileList: [],
      videoList: [],
      loading: false,
      finished: false,
      page: 1,
      pageSize: 10,
      searchKeyword: '',
      
      // 弹窗控制
      showGenerateDialog: false,
      showUploadDialog: false,
      showPreviewDialog: false,
      previewVideoUrl: '',
      uploading: false,

      // 预览相关
      currentPreviewItem: null,
      previewImages: [],
      currentImageIndex: 0,
      currentTextInfo: null,
      loadingTextInfo: false,

      // 生成表单
      generateForm: {
        name: '',
        platform: 'douyin',
        mediaType: 'video',
        imageCount: 3
      },

      // 平台和媒体类型
      platformOptions: [],
      platformColumns: [],
      mediaTypeColumns: [],
      showPlatformPicker: false,
      showMediaTypePicker: false,

      // 上传表单
      uploadForm: {
        name: ''
      },

      // 上传进度
      uploadProgress: []
    }
  },

  computed: {
    selectedPlatformName() {
      const platform = this.platformOptions.find(p => p.code === this.generateForm.platform)
      return platform ? platform.name : ''
    },
    selectedMediaTypeName() {
      if (this.generateForm.mediaType === 'video') return '视频'
      if (this.generateForm.mediaType === 'image') return '图片'
      return ''
    }
  },

  mounted() {
    document.title = '成品视频'

    // 优先使用URL参数中的activityId，如果没有则使用缓存的活动ID
    const urlActivityId = this.$route.query.activityId
    if (urlActivityId) {
      this.activityId = urlActivityId
    } else {
      // 如果URL中没有activityId，尝试从store中获取当前选中的活动ID
      const currentSelectedId = this.$store.state.activity.selectedActivityId
      if (currentSelectedId) {
        this.activityId = currentSelectedId
      }
    }

    if (!this.activityId) {
      this.$toast.fail('活动ID不能为空，请先选择活动')
      this.$router.push({ name: 'index' })
      return
    }
    this.loadPlatformConfigs()
    this.loadVideoList()
  },
  methods: {
    // 加载平台配置
    loadPlatformConfigs() {
      this.$fly.get('/pyp/web/activity/video/platforms').then(res => {
        if (res.code === 200) {
          this.platformOptions = res.platforms || []
          this.platformColumns = this.platformOptions.map(p => ({
            text: p.name,
            value: p.code
          }))
        }
      }).catch(error => {
        console.error('加载平台配置失败:', error)
      })
    },

    // 平台选择确认
    onPlatformConfirm(value) {
      this.generateForm.platform = value.value
      this.showPlatformPicker = false
      this.loadMediaTypes(value.value)
    },

    // 加载媒体类型
    loadMediaTypes(platform) {
      if (!platform) return

      this.$fly.get(`/pyp/web/activity/video/platforms/${platform}/mediaTypes`).then(res => {
        if (res.code === 200) {
          const mediaTypes = res.mediaTypes || []
          this.mediaTypeColumns = mediaTypes.map(m => ({
            text: m.name,
            value: m.code
          }))

          // 如果当前选择的媒体类型不支持，重置为第一个
          const supportedTypes = mediaTypes.map(m => m.code)
          if (!supportedTypes.includes(this.generateForm.mediaType)) {
            this.generateForm.mediaType = supportedTypes[0] || 'video'
          }
        }
      }).catch(error => {
        console.error('加载媒体类型失败:', error)
      })
    },

    // 媒体类型选择确认
    onMediaTypeConfirm(value) {
      this.generateForm.mediaType = value.value
      this.showMediaTypePicker = false

      // 如果选择图片，设置默认数量
      if (value.value === 'image') {
        const platform = this.platformOptions.find(p => p.code === this.generateForm.platform)
        if (platform && platform.defaultImageCount) {
          this.generateForm.imageCount = platform.defaultImageCount
        }
      }
    },

    onSearch() {
      this.page = 1
      this.videoList = []
      this.finished = false
      this.loadVideoList()
    },
    
    onLoad() {
      this.loadVideoList()
    },

    loadVideoList() {
      this.loading = true
      const params = {
        page: this.page,
        limit: this.pageSize,
        activityId: this.activityId,
        type: 1 // 固定为成品视频
      }
      
      if (this.searchKeyword) {
        params.name = this.searchKeyword
      }
      
      this.$fly.get('/pyp/web/activity/activityvideo/list', params).then(res => {
        this.loading = false
        if (res.code === 200) {
          const newVideos = res.page.list || []
          
          if (this.page === 1) {
            this.videoList = newVideos
          } else {
            this.videoList = this.videoList.concat(newVideos)
          }
          
          this.page++
          this.finished = this.videoList.length >= res.page.totalCount
        } else {
          this.$toast.fail(res.msg || '获取视频列表失败')
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.$toast.fail('获取视频列表失败')
        this.finished = true
      })
    },

    // 处理智能生成
    handleGenerate() {
      if (!this.generateForm.platform) {
        this.$toast.fail('请选择平台')
        return
      }
      if (!this.generateForm.mediaType) {
        this.$toast.fail('请选择内容类型')
        return
      }
      // if (!this.generateForm.name.trim()) {
      //   this.$toast.fail('请输入内容名称')
      //   return
      // }

      // 显示加载提示
      const loadingToast = this.$toast.loading({
        message: '正在生成中...',
        forbidClick: true,
        duration: 0
      })

      const params = {
        activityId: this.activityId,
        platform: this.generateForm.platform,
        mediaType: this.generateForm.mediaType,
        name: this.generateForm.name
      }

      if (this.generateForm.mediaType === 'image') {
        params.imageCount = this.generateForm.imageCount
      }

      const apiUrl = this.generateForm.mediaType === 'video'
        ? '/pyp/web/activity/activityvideo/submitVideoEdit'
        : '/pyp/web/activity/video/generateImages'

      const method = this.generateForm.mediaType === 'video' ? 'get' : 'post'

      this.$fly[method](apiUrl,  params).then(res => {
        loadingToast.clear()
        if (res.code === 200) {
          const contentType = this.generateForm.mediaType === 'video' ? '视频' : '图片'
          this.$toast.success({
            message: `� 成品${contentType}生成任务已提交！\n后台正在处理中，请稍后在列表中查看结果`,
            duration: 3000
          })
          this.showGenerateDialog = false
          this.resetGenerateForm()
          this.onSearch() // 刷新列表
        } else {
          this.$toast.fail(res.msg || '生成失败')
        }
      }).catch(error => {
        loadingToast.clear()
        console.error('生成失败:', error)
        this.$toast.fail('生成失败')
      })
    },

    // 重置生成表单
    resetGenerateForm() {
      this.generateForm = {
        name: '',
        platform: '',
        mediaType: 'video',
        imageCount: 3
      }
    },

    // 获取平台名称
    getPlatformName(platformCode) {
      const platform = this.platformOptions.find(p => p.code === platformCode)
      return platform ? platform.name : platformCode
    },

    // 文件上传
    afterRead(file) {
      console.log('上传文件:', file)
    },
    
    onOversize() {
      this.$toast.fail('文件大小不能超过100MB')
    },
    
    confirmUpload() {
      if (!this.uploadForm.name) {
        this.$toast.fail('请输入视频名称')
        return
      }
      
      if (this.fileList.length === 0) {
        this.$toast.fail('请选择视频文件')
        return
      }
      
      this.$toast.loading('上传中...')
      
      // 这里应该调用真实的上传API
      setTimeout(() => {
        this.$toast.success('上传成功')
        this.showUploadDialog = false
        this.uploadForm = { name: '' }
        this.fileList = []
        this.onSearch()
      }, 2000)
    },

    // 预览视频
    previewVideo(item) {
      this.currentPreviewItem = item
      this.currentTextInfo = null
      this.loadingTextInfo = false

      if (item.mediaType === 'video') {
        // 视频预览
        if (!item.mediaUrl) {
          this.$toast.fail('视频还在处理中，请稍后再试')
          return
        }
        this.previewVideoUrl = item.mediaUrl
      } else {
        // 图片预览 - 需要获取关联的图片列表
        this.loadPreviewImages(item.id)
      }

      // 加载关联的文案信息
      this.loadTextInfo(item.activityTextId)

      this.showPreviewDialog = true
    },

    // 加载预览图片
    loadPreviewImages(videoId) {
      this.$fly.get('/pyp/web/activity/video/images', {
        videoId: videoId 
      }).then(res => {
        if (res.code === 200) {
          this.previewImages = res.images || []
          this.currentImageIndex = 0

          if (this.previewImages.length === 0) {
            // 如果没有关联图片，使用主图片
            if (this.currentPreviewItem.mediaUrl) {
              this.previewImages = [{
                mediaUrl: this.currentPreviewItem.mediaUrl
              }]
            }
          }
        } else {
          this.$toast.fail('加载图片失败')
          this.previewImages = []
        }
      }).catch(error => {
        console.error('加载预览图片失败:', error)
        // 使用主图片作为备选
        if (this.currentPreviewItem.mediaUrl) {
          this.previewImages = [{
            mediaUrl: this.currentPreviewItem.mediaUrl
          }]
        } else {
          this.previewImages = []
        }
      })
    },

    // 轮播图切换
    onSwipeChange(index) {
      this.currentImageIndex = index
    },

    // 图片加载完成
    onImageLoaded() {
      // 图片加载完成的处理
    },

    // 下载内容
    downloadContent() {
      if (this.currentPreviewItem.mediaType === 'video') {
        if (this.currentPreviewItem.mediaUrl) {
          window.open(this.currentPreviewItem.mediaUrl, '_blank')
        } else {
          this.$toast.fail('视频还在处理中，无法下载')
        }
      } else {
        // 图片下载 - 下载当前显示的图片
        if (this.previewImages.length > 0) {
          const currentImage = this.previewImages[this.currentImageIndex]
          if (currentImage && currentImage.mediaUrl) {
            window.open(currentImage.mediaUrl, '_blank')
          } else {
            this.$toast.fail('图片无法下载')
          }
        }
      }
    },

    // 分享内容
    shareContent() {
      this.$toast('分享功能开发中')
    },

    // 删除内容
    deleteContent() {
      this.$dialog.confirm({
        title: '确认删除',
        message: `确定要删除这个${this.currentPreviewItem.mediaType === 'video' ? '视频' : '图片'}吗？`,
      }).then(() => {
        this.deleteVideo(this.currentPreviewItem)
        this.showPreviewDialog = false
      }).catch(() => {
        // 用户取消
      })
    },

    // 加载文案信息
    loadTextInfo(textId) {
      if (!textId) {
        this.currentTextInfo = null
        return
      }

      this.loadingTextInfo = true

      this.$fly.get('/pyp/web/activity/text/info', {
         textId: textId 
      }).then(res => {
        this.loadingTextInfo = false
        if (res.code === 200) {
          this.currentTextInfo = res.textInfo || null
        } else {
          this.currentTextInfo = null
          console.error('加载文案信息失败:', res.msg)
        }
      }).catch(error => {
        this.loadingTextInfo = false
        this.currentTextInfo = null
        console.error('加载文案信息失败:', error)
      })
    },

    // 复制文本
    copyText(text) {
      if (!text) {
        this.$toast.fail('没有可复制的内容')
        return
      }

      // 使用现代浏览器的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          this.$toast.success('复制成功')
        }).catch(() => {
          this.fallbackCopyText(text)
        })
      } else {
        this.fallbackCopyText(text)
      }
    },

    // 备用复制方法
    fallbackCopyText(text) {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          this.$toast.success('复制成功')
        } else {
          this.$toast.fail('复制失败，请手动复制')
        }
      } catch (err) {
        this.$toast.fail('复制失败，请手动复制')
      }
    },

    // 一键复制全部文案
    copyAllText() {
      if (!this.currentTextInfo) {
        this.$toast.fail('没有可复制的文案')
        return
      }

      let allText = ''

      if (this.currentTextInfo.title) {
        allText += `标题：${this.currentTextInfo.title}\n\n`
      }

      if (this.currentTextInfo.promptKeyword) {
        allText += `提示词：${this.currentTextInfo.promptKeyword}\n\n`
      }

      if (this.currentTextInfo.content) {
        allText += `文案内容：${this.currentTextInfo.content}\n\n`
      }

      if (this.currentTextInfo.topics) {
        allText += `话题标签：${this.currentTextInfo.topics}\n\n`
      }

      if (allText.trim()) {
        this.copyText(allText.trim())
      } else {
        this.$toast.fail('没有可复制的文案内容')
      }
    },
    
    editVideo(item) {
      this.$toast('编辑视频功能待开发')
      console.log('编辑视频:', item)
    },
    
    deleteVideo(item) {
      this.$dialog.confirm({
        title: '确认删除',
        message: '确定要删除这个成品视频吗？'
      }).then(() => {
        this.$fly.post('/pyp/web/activity/activityvideo/delete', [item.id]).then(res => {
          if (res.code === 200) {
            this.$toast.success('删除成功')
            this.onSearch()
          } else {
            this.$toast.fail(res.msg || '删除失败')
          }
        }).catch(() => {
          this.$toast.fail('删除失败')
        })
      }).catch(() => {
        // 用户取消
      })
    },
    
    // 工具方法
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    },
    
    formatDuration(duration) {
      if (!duration) return '00:00'
      const minutes = Math.floor(duration / 60)
      const seconds = Math.floor(duration % 60)
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  }
}
</script>

<style lang="less" scoped>
// 页面整体样式
.finished-video {
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
  min-height: 100vh;
  position: relative;
  padding-bottom: 60px;
}

.page-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;
}

.safe-area-bottom {
  height: 20px;
}

// 自定义导航栏
.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

// 功能区域样式 - 紧凑版
.function-section {
  padding: 6px 15px;
  margin-bottom: 8px;
  position: relative;
  z-index: 10;

  .function-bar {
    background: white;
    border-radius: 16px;
    padding: 6px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 12px;

    .function-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px 12px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      flex-shrink: 0;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      .function-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;

        .van-icon {
          font-size: 16px;
          color: white;
        }
      }

      .function-content {
        .function-title {
          font-size: 14px;
          font-weight: 600;
          color: white;
          white-space: nowrap;
        }
      }

      .function-arrow {
        margin-left: 6px;
        color: rgba(255, 255, 255, 0.7);
        font-size: 12px;
        transition: all 0.3s ease;
      }

      &:hover .function-arrow {
        color: white;
        transform: translateX(2px);
      }
    }

    .search-compact {
      flex: 1;
      min-width: 0;

      .compact-search {
        :deep(.van-search__content) {
          background: #f8faff;
          border-radius: 12px;
          padding: 6px 12px;
        }

        :deep(.van-field__control) {
          font-size: 14px;
          color: #333;
        }

        :deep(.van-search__action) {
          display: none;
        }

        :deep(.van-field__left-icon) {
          color: #999;
        }
      }
    }
  }
}



// 视频列表样式
.video-list {
  padding: 0 15px;
  position: relative;
  z-index: 10;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 5px;

    .list-count {
      font-size: 14px;
      color: white;
      font-weight: 500;
    }
  }

  .custom-list {
    .van-list__finished-text {
      color: #999;
      font-size: 13px;
    }
  }
}

.video-item {
  background: white;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);

    &::before {
      opacity: 1;
    }
  }
}

// 视频预览样式
.video-preview {
  position: relative;
  margin-bottom: 16px;
  cursor: pointer;
  border-radius: 16px;
  overflow: hidden;

  .video-container {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: #f8faff;

    .video-element {
      width: 100%;
      height: 220px;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .no-video {
      height: 220px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);

      .processing-animation {
        margin-bottom: 12px;
      }

      .processing-text {
        margin: 0;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .play-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
      transition: all 0.3s ease;

      .play-button {
        width: 64px;
        height: 64px;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        .van-icon {
          margin-left: 2px;
        }
      }
    }

    .video-duration {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
    }

    &:hover {
      .video-element {
        transform: scale(1.02);
      }

      .play-overlay {
        opacity: 1;

        .play-button {
          transform: scale(1.1);
          background: rgba(102, 126, 234, 0.9);
        }
      }
    }
  }

  .image-container {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: #f8faff;

    .image-element {
      width: 100%;
      height: 220px;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .no-image {
      height: 220px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);
      color: #666;

      .van-icon {
        margin-bottom: 8px;
        color: #999;
      }

      p {
        font-size: 14px;
        font-weight: 500;
        margin: 0;
      }
    }

    .image-overlay {
      position: absolute;
      top: 12px;
      right: 12px;

      .image-count {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    &:hover {
      .image-element {
        transform: scale(1.02);
      }
    }
  }
}

// 视频信息样式
.video-info {
  position: relative;
  z-index: 2;

  .video-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .video-title {
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      color: #333;
      flex: 1;
      margin-right: 12px;
      line-height: 1.4;
    }

    .video-badges {
      display: flex;
      gap: 6px;
      flex-shrink: 0;

      .video-tag {
        border-radius: 8px;
        font-weight: 500;
      }

      .platform-tag {
        border-radius: 8px;
        font-weight: 500;
      }
    }
  }

  .video-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .meta-item {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666;
      font-weight: 500;

      .van-icon {
        margin-right: 4px;
        color: #999;
        font-size: 14px;
      }
    }
  }

  .video-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .van-button {
      border-radius: 12px;
      font-weight: 500;
      transition: all 0.3s ease;

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
      }

      &--danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }
      }

      &:not(.van-button--primary):not(.van-button--danger) {
        background: #f8faff;
        color: #667eea;
        border: 1px solid #e6f3ff;

        &:hover {
          background: #e6f3ff;
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  padding: 60px 20px;
  text-align: center;

  .empty-content {
    background: white;
    border-radius: 20px;
    padding: 40px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

    .empty-icon {
      color: #ddd;
      margin-bottom: 20px;
    }

    .empty-title {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .empty-desc {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }

    .empty-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;

      .van-button {
        border-radius: 12px;
        font-weight: 500;

        &--primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }
}

// 弹窗样式
.custom-dialog {
  :deep(.van-dialog) {
    border-radius: 20px;
    overflow: hidden;
  }

  :deep(.van-dialog__header) {
    padding: 0;
  }

  :deep(.van-dialog__content) {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0;
  }

  :deep(.van-dialog__footer) {
    // padding: 20px;
    border-top: 1px solid #f0f0f0;

    .van-button {
      border-radius: 12px;
      font-weight: 500;

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
      }
    }
  }
}

.generate-form, .upload-form {
  padding: 24px;

  .dialog-header {
    text-align: center;
    margin-bottom: 24px;

    .header-icon {
      width: 64px;
      height: 64px;
      border-radius: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      color: white;
    }

    &.upload-icon .header-icon {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    }

    .dialog-title {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 700;
      color: #333;
    }

    .dialog-subtitle {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }

  .generate-config {
    margin-bottom: 24px;

    .custom-field {
      margin-bottom: 16px;

      :deep(.van-field__label) {
        font-weight: 600;
        color: #333;
      }

      :deep(.van-field__control) {
        font-size: 16px;
      }

      :deep(.van-field__right-icon) {
        color: #999;
      }
    }
  }
}
// 生成功能样式
.generate-features {
  margin: 20px 0;

  .feature-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .feature-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: #f8faff;
      color: #667eea;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 16px;
    }

    .feature-content {
      flex: 1;

      .feature-title {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .feature-desc {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.generate-tips {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f3ff 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;

  .tip-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #0369a1;
    font-weight: 600;
    font-size: 14px;

    .van-icon {
      margin-right: 6px;
    }
  }

  .tip-list {
    margin: 0;
    padding-left: 16px;
    color: #0369a1;
    font-size: 13px;
    line-height: 1.6;

    li {
      margin-bottom: 4px;
    }
  }
}

// 上传相关样式
.custom-field {
  margin-bottom: 16px;

  :deep(.van-field__control) {
    font-size: 14px;
  }
}

.upload-section {
  margin: 20px 0;

  .upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .upload-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .upload-count {
      font-size: 12px;
      color: #999;
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 6px;
    }
  }

  .custom-uploader {
    :deep(.van-uploader__upload) {
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      background: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        border-color: #667eea;
        background: #f8faff;
      }
    }
  }
}

.upload-tips {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;

  .tip-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #666;
    font-weight: 600;
    font-size: 14px;

    .van-icon {
      margin-right: 6px;
    }
  }

  .tip-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .tip-item {
      display: flex;
      align-items: flex-start;

      .tip-icon {
        margin-right: 8px;
        margin-top: 2px;
        color: #999;
        font-size: 14px;
        flex-shrink: 0;
      }

      .tip-content {
        .tip-title {
          display: block;
          font-size: 12px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
        }

        .tip-desc {
          font-size: 11px;
          color: #999;
          line-height: 1.4;
        }
      }
    }
  }
}

.upload-progress {
  margin-top: 20px;
  padding: 16px;
  background: #f8faff;
  border-radius: 12px;

  .progress-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #667eea;
    font-weight: 600;
    font-size: 14px;

    .van-icon {
      margin-right: 6px;
    }
  }

  .progress-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .progress-name {
        font-size: 13px;
        color: #333;
        font-weight: 500;
      }

      .progress-status {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

// 预览弹窗样式
.preview-dialog {
  .preview-container {
    padding: 10px;

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      color: #333;
      font-weight: 600;

      .van-icon {
        margin-right: 8px;
        color: #667eea;
      }

      .preview-title {
        font-size: 16px;
        flex: 1;
      }

      .preview-subtitle {
        font-size: 14px;
        color: #666;
        margin-left: 8px;
      }
    }

    .video-wrapper {
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 16px;
      background: #000;
      height: 300px;

      .preview-video {
        width: 100%;
        max-height: 300px;
        display: block;
      }

      .no-preview {
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #999;
        background: #f5f5f5;

        .van-icon {
          margin-bottom: 12px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }

    .image-wrapper {
      margin-bottom: 16px;
      border-radius: 12px;
      overflow: hidden;
      position: relative;

      .preview-swipe {
        height: 200px;
        border-radius: 12px;
        overflow: hidden;

        .swipe-item {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;

          .preview-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
          }
        }

        :deep(.van-swipe__indicator) {
          background: rgba(255, 255, 255, 0.5);

          &.van-swipe__indicator--active {
            background: white;
          }
        }
      }

      .no-preview {
        height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #999;
        background: #f5f5f5;

        .van-icon {
          margin-bottom: 12px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .image-counter {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .preview-info {
      margin-bottom: 5px;
      padding: 6px;
      background: #f8f9fa;
      border-radius: 8px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          font-weight: 600;
        }
      }
    }

    .text-info {
      // margin-bottom: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e9ecef;

      .text-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;

        .van-icon {
          margin-right: 8px;
          color: #667eea;
          font-size: 18px;
        }

        .text-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .text-content {
        .text-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .text-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            span {
              font-size: 14px;
              font-weight: 600;
              color: #666;
            }

            .copy-btn {
              font-size: 12px;
              height: 24px;
              padding: 0 8px;
            }
          }

          .text-value {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
            padding: 6px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            word-break: break-all;

            &.content-text {
              white-space: pre-wrap;
              max-height: 200px;
              overflow-y: auto;
            }

            &.topics-text {
              color: #667eea;
              font-weight: 500;
            }
          }
        }

        .copy-all-section {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #e9ecef;
        }
      }
    }

    .loading-text {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #999;
      font-size: 14px;

      .van-loading {
        margin-right: 8px;
      }
    }

    .preview-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .van-button {
        border-radius: 8px;
        font-size: 12px;
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-item {
  animation: fadeInUp 0.6s ease-out;
}

.function-item {
  animation: fadeInUp 0.6s ease-out;
}

.search-card {
  animation: fadeInUp 0.6s ease-out;
}
</style>
