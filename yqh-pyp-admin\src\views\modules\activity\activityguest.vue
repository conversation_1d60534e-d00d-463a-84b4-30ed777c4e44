<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:activityguest:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('activity:activityguest:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button v-if="activityInfo.isFirstChar" type="danger" @click="updateIsFirstChar(0)">关闭嘉宾首字母排序</el-button>
        <el-button v-else type="success" @click="updateIsFirstChar(1)">开启嘉宾首字母排序</el-button>
        <el-button @click="exportHandle()" type="success">导出</el-button>
        
        <el-tooltip placement="right">
                <div slot="content">
                  <img style="height: 600px;" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20241231/cd8c299ac6ad4fcc9ab9470bbdb906e0.png" alt="">
                </div>
                <el-button type="primary" @click="sendIndexHandle()" :disabled="dataListSelections.length <= 0">专家首页通知</el-button>
              </el-tooltip>
        <el-button @click="activityguestplane()" type="success">来程返程信息</el-button>
        <el-button @click="activityconfigupdateTurn()" type="primary">任务规则配置</el-button>
        <el-button @click="downloadDemo()" type="success">导入模板</el-button>
        <el-button @click="exportIndex()" type="success">导出首页通知短信</el-button>
        <el-button type="primary">
          <Upload @uploaded="getDataList" :url="'/activity/activityguest/importExcel?activityId=' + dataForm.activityId + '&appid=' + appid"
            :name="'专家导入'"></Upload>
        </el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="联系人姓名">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系人电话">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="工作单位">
      </el-table-column>
      <el-table-column prop="duties" header-align="center" align="center" label="职称">
      </el-table-column>
      <el-table-column prop="idCard" header-align="center" align="center" label="身份证">
      </el-table-column>
      <el-table-column prop="avatar" header-align="center" align="center" label="头像">
        <div slot-scope="scope">
          <img class="image-sm" style="height: 80px" :src="scope.row.avatar" />
        </div>
      </el-table-column>
      <!-- <el-table-column prop="wxUserId" header-align="center" align="center" label="关联用户ID">
      </el-table-column> -->
      <el-table-column prop="idCardZheng" header-align="center" align="center" label="身份证正面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardZheng)" :src="scope.row.idCardZheng" />
          <a :href="scope.row.idCardZheng" target="_blank" v-else>{{ scope.row.idCardZheng }}</a>
        </div>
      </el-table-column>
      <el-table-column prop="idCardFan" header-align="center" align="center" label="身份证反面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardFan)" :src="scope.row.idCardFan" />
          <a :href="scope.row.idCardFan" target="_blank" v-else>{{ scope.row.idCardFan }}</a>
        </div>
      </el-table-column>
      <!-- <el-table-column prop="bank" header-align="center" align="center" label="银行卡号">
      </el-table-column>
      <el-table-column prop="kaihuhang" header-align="center" align="center" label="开户行">
      </el-table-column> -->
      <!-- <el-table-column prop="sendTask" header-align="center" align="center" label="学术短信发送">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendTask == 1 ? 'success' : 'danger'">{{ scope.row.sendTask == 1 ? '已发送' : '未发送'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendTaskTime" header-align="center" align="center" label="发送时间">
      </el-table-column> -->
      <el-table-column prop="isAttend" header-align="center" align="center" label="是否参会">
        <div slot-scope="scope">
          <el-tag :type="scope.row.isAttend == 1 ? 'success' : 'danger'">{{ scope.row.isAttend == 1 ? '参会' : '不参会'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendIndex" header-align="center" align="center" label="专家通知">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendIndex == 1 ? 'success' : 'danger'">{{ scope.row.sendIndex == 1 ? '已发送' : '未发送'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendIndexTime" header-align="center" align="center" label="发送时间">
      </el-table-column>
      <el-table-column prop="sendIndexTime" header-align="center" align="center" label="专家简介">
        <div slot-scope="scope">
          <el-button @click="downloadFile(scope.row.contentFile)" size="mini" :type="!scope.row.contentFile ? 'danger' : 'success'">{{ !scope.row.contentFile ? '未上传' : '已上传(下载)'
            }}</el-button>
        </div>
      </el-table-column>
      <el-table-column prop="orderBy" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="sendTaskHandle(scope.row.id)">专家议程确认通知</el-button>
          <el-button type="text" size="small" @click="sendIndexHandle(scope.row.id)">专家首页通知</el-button>
          <el-button type="text" size="small" @click="showTaskHandle(scope.row.id)">学术任务</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="copyResult(scope.row.id)">复制首页通知</el-button>
          <el-button type="text" size="small" @click="activityconfigupdate(scope.row.id)">规则配置</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <showtask v-if="showtaskVisible" ref="showtask" @refreshDataList="getDataList"></showtask>
    <activity-guest-config 
      v-if="configVisible" 
      ref="guestConfig" 
      @refreshDataList="getDataList">
    </activity-guest-config>
  </div>
</template>

<script>
import AddOrUpdate from './activityguest-add-or-update'
import showtask from './activityguest-showtask'
import ActivityGuestConfig from './activityguest-config'
export default {
  data() {
    return {
      appid: '',
      dataForm: {
        name: '',
        mobile: '',
        isFirstChar: 0,
        activityId: undefined
      },
      activityInfo: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      showtaskVisible: false,
      configVisible: false
    }
  },
  components: {
    AddOrUpdate,
    showtask,
    Upload: () => import('@/components/upload'),
    ActivityGuestConfig
  },
  activated() {
    this.appid = this.$cookie.get("appid");
    this.dataForm.activityId = this.$route.query.activityId;
    this.getActivity();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'activityId': this.dataForm.activityId,
          'isFirstChar': this.activityInfo.isFirstChar,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendTaskHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送学术通知短信' : '批量发送学术通知短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendTask'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendIndexHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送专家通知短信' : '批量发送专家通知短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendIndex'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    copyResult(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/copyIndex'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {

            const result = data.result;
          this.copyToClipboard(result);
          } else {
            this.$message.error(data.msg)
          }
        })
    },
    copyToClipboard(text) {
      const input = document.createElement('input');
      input.setAttribute('value', text);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message({
        message: '复制成功',
        type: 'success',
      });
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
          this.getDataList()
        }
      });
    },
    updateIsFirstChar(v) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/updateIsFirstChar`),
        method: "post",
        data: this.$http.adornData({
          id: this.dataForm.activityId,
          isFirstChar: v,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message.success("操作成功");
          this.getActivity();
          this.pageIndex = 1;
          this.getDataList()
        } else {
          this.$message.error(data.msg)

        }
      });
    },
    showTaskHandle(v) {
      this.showtaskVisible = true
      this.$nextTick(() => {
        this.$refs.showtask.init(v)
      })
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activityguest/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "activityId=" + this.dataForm.activityId,
        "isFirstChar=" + this.activityInfo.isFirstChar
      ].join('&'));
      window.open(url);
    },
    downloadFile(e) {
      if (!e) {
        this.$message.error("未上传文件");
        return;
      }
      window.open(e);
    },
    activityguestplane() {
      this.$router.push({
        name :'activityguestplane',
        query: {
          activityId: this.dataForm.activityId,
        },
      })
    },
    downloadDemo() {
      var url = this.$http.adornUrl("/activity/activityguest/downloadDemo?" + [
        "token=" + this.$cookie.get('token'),
      ].join('&'));
      window.open(url);
    },
    exportIndex() {
      var url = this.$http.adornUrl("/activity/activityguest/exportIndex?" + [
        "token=" + this.$cookie.get('token'),
        "activityId=" + this.dataForm.activityId,
      ].join('&'));
      window.open(url);
    },
    activityconfigupdate(id) {
      this.configVisible = true
      this.$nextTick(() => {
        this.$refs.guestConfig.init(id)
      })
    },
    activityconfigupdateTurn() {
      this.$router.push({
        name :'activityconfigupdate',
        query: {
          activityId: this.dataForm.activityId,
        },
      })

    },
  }
}
</script>
