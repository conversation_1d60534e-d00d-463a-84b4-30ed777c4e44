<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="酒店名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="酒店名称"></el-input>
    </el-form-item>
    <el-form-item label="联系方式" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="星级" prop="star">
          <template>
            <el-rate  v-model="dataForm.star" :colors="['#99A9BF', '#F7BA2A', '#FF9900']">
            </el-rate>
          </template>
    </el-form-item>
      <el-form-item label="所在城市" prop="cityId">
        <el-select v-model="dataForm.provinceId" placeholder="省" @change="provinceChange" filterable>
          <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select style="margin-left: 10px;" v-model="dataForm.cityId" placeholder="市" @change="getCityName" filterable>
          <el-option v-for="item in cities" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    <el-form-item label="详细地址" prop="address">
      <el-input v-model="dataForm.address" placeholder="详细地址"></el-input>
    </el-form-item>
    <el-form-item label="图片" prop="imageUrl">
          <el-upload :before-upload="checkFileSize" class="avatar-uploader" list-type="picture-card" :show-file-list="false"
                      accept=".jpg, .jpeg, .png, .gif" :on-success="appSuccessHandle" :action="url">
              <img width="100px" v-if="dataForm.imageUrl" :src="dataForm.imageUrl" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
    </el-form-item>
    <el-form-item label="经度" prop="longitude">
      <el-input v-model="dataForm.longitude" placeholder="经度"></el-input>
    </el-form-item>
    <el-form-item label="纬度" prop="latitude">
      <el-input v-model="dataForm.latitude" placeholder="纬度"></el-input>
    </el-form-item>
        <a style="color:red;margin-left:50px"  target="_blank" href="https://lbs.qq.com/tool/getpoint/index.html">腾讯地图坐标拾取工具</a>
    
        <!-- <el-form-item label="内容" prop="content">
        <tinymce-editor ref="editor" v-model="dataForm.content"></tinymce-editor>
    </el-form-item> -->
      </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs';
  export default {
    data () {
      return {
        visible: false,
        provinces: [],
        cities: [],
        cityName: "",
        url: "",
        dataForm: {
          id: 0,
          name: '',
          star: '',
          mobile: '',
          provinceId: '',
          cityId: '',
          address: '',
          longitude: '',
          latitude: '',
          appid: '',
          imageUrl: ''
        },
        dataRule: {
          name: [
            { required: true, message: '酒店名称不能为空', trigger: 'blur' }
          ],
          // star: [
          //   { required: true, message: '星级不能为空', trigger: 'blur' }
          // ],
          cityId: [
            { required: true, message: '城市不能为空', trigger: 'blur' }
          ],
          address: [
            { required: true, message: '详细地址不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    components: {
      TinymceEditor: () =>
        import ("@/components/tinymce-editor"),
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.url = this.$http.adornUrl(
          `/sys/oss/upload?token=${this.$cookie.get("token")}`
        );
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.getProvinces();
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotel/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.name = data.hotel.name
                this.dataForm.star = data.hotel.star
                this.dataForm.mobile = data.hotel.mobile
                this.dataForm.provinceId = data.hotel.provinceId
                this.dataForm.cityId = data.hotel.cityId
                this.dataForm.address = data.hotel.address
                this.dataForm.longitude = data.hotel.longitude
                this.dataForm.latitude = data.hotel.latitude
                this.dataForm.imageUrl = data.hotel.imageUrl
                this.dataForm.appid = data.hotel.appid
                this.$http({
                  url: this.$http.adornUrl(
                    `/sys/region/pid/${this.dataForm.provinceId}`
                  ),
                  method: "get",
                  params: this.$http.adornParams()
                }).then(({
                  data
                }) => {
                  if (data && data.code === 200) {
                    this.cities = data.list;
                  } else {
                    this.cities = [];
                  }
                });
              }
            })
          } else {
            
            this.dataForm.appid = this.$cookie.get("appid");
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotel/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'star': this.dataForm.star,
                'mobile': this.dataForm.mobile,
                'provinceId': this.dataForm.provinceId,
                'cityId': this.dataForm.cityId,
                'address': this.dataForm.address,
                'longitude': this.dataForm.longitude,
                'latitude': this.dataForm.latitude,
                'appid': this.dataForm.appid,
                'imageUrl': this.dataForm.imageUrl
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      
      getProvinces() {
        this.$http({
          url: this.$http.adornUrl("/sys/region/pid/100000"),
          method: "get",
          params: this.$http.adornParams()
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.provinces = data.list;
          } else {
            this.provinces = [];
          }
        });
      },
      provinceChange(val) {
        if (val === undefined) {
          return;
        }
        this.cities = {};
        this.dataForm.cityId = undefined;
        this.dataForm.jieSongCityName = [];
        this.$http({
          url: this.$http.adornUrl(`/sys/region/pid/${val}`),
          method: "get",
          params: this.$http.adornParams()
        }).then(({
          data
        }) => {
          if (data && data.code === 200) {
            this.cities = data.list;
          } else {
            this.cities = [];
          }
        });
      },
      getCityName(v) {
        var obj = this.cities.find((item) => { //这里的userList就是上面遍历的数据源
          return item.id === v; //筛选出匹配数据
        });
        this.cityName = obj.name.replace("市", ""); //我这边的name就是对应label的
      },
      // 上传之前
      // 上传之前
      checkFileSize: function(file) {
        if (file.size / 1024 / 1024   > 6) {
          this.$message.error(`${file.name}文件大于6MB，请选择小于6MB大小的图片`)
          return false
        }
        if(file.size / 1024 > 100) {
          // 100kb不压缩
          return new Promise((resolve, reject) => {
          new Compressor(file, {
              quality: 0.8,
              
              success(result) {
            resolve(result)
              }
            })
          })
        }
        return true
      },
      beforeUploadHandle(file) {
        if (
          file.type !== "image/jpg" &&
          file.type !== "image/jpeg" &&
          file.type !== "image/png" &&
          file.type !== "image/gif"
        ) {
          this.$message.error("只支持jpg、png、gif格式的图片！");
          return false;
        }
      },
      // app公众号轮播图上传成功
      appSuccessHandle(response, file, fileList) {
        if (response && response.code === 200) {
            this.dataForm.imageUrl = response.url;
        } else {
          this.$message.error(response.msg);
        }
      },
    }
  }
</script>
