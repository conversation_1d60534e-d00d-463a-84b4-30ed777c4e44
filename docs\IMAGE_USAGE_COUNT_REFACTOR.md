# 图片使用次数统计重构文档

## 重构概述

将图片平台使用次数的增加操作从数据库层面的 `INSERT ... ON DUPLICATE KEY UPDATE` 改为 Java 代码实现。

## 修改原因

1. **代码可读性**：Java 代码比 SQL 语句更容易理解和维护
2. **调试便利**：可以在 Java 代码中添加日志和断点进行调试
3. **业务逻辑集中**：将业务逻辑从数据库层移到服务层
4. **扩展性**：便于后续添加额外的业务逻辑（如缓存、事件通知等）

## 技术实现

### 原实现（数据库层）

**DAO 方法**：
```java
int incrementUsageCount(@Param("imageId") Long imageId, 
                       @Param("platform") String platform, 
                       @Param("activityId") Long activityId);
```

**SQL 实现**：
```sql
INSERT INTO activity_image_platform_usage 
(activity_id, image_id, platform, use_count, first_used_time, last_used_time, create_time, update_time)
VALUES (#{activityId}, #{imageId}, #{platform}, 1, NOW(), NOW(), NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    use_count = use_count + 1,
    last_used_time = NOW(),
    update_time = NOW()
```

### 新实现（Java 代码）

**Service 方法**：
```java
@Override
public void incrementUseCountByPlatform(Long imageId, String platform, Long activityId) {
    // 1. 先查询是否已存在记录
    QueryWrapper<ActivityImagePlatformUsageEntity> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("image_id", imageId).eq("platform", platform);
    
    ActivityImagePlatformUsageEntity existingRecord = 
        activityImagePlatformUsageDao.selectOne(queryWrapper);
    
    if (existingRecord != null) {
        // 2. 如果存在，更新使用次数
        existingRecord.setUseCount(existingRecord.getUseCount() + 1);
        existingRecord.setLastUsedTime(new Date());
        existingRecord.setUpdateTime(new Date());
        activityImagePlatformUsageDao.updateById(existingRecord);
    } else {
        // 3. 如果不存在，插入新记录
        ActivityImagePlatformUsageEntity newRecord = new ActivityImagePlatformUsageEntity();
        newRecord.setActivityId(activityId);
        newRecord.setImageId(imageId);
        newRecord.setPlatform(platform);
        newRecord.setUseCount(1);
        newRecord.setFirstUsedTime(new Date());
        newRecord.setLastUsedTime(new Date());
        newRecord.setCreateTime(new Date());
        newRecord.setUpdateTime(new Date());
        activityImagePlatformUsageDao.insert(newRecord);
    }
}
```

## 修改文件

### 1. Service 实现类
**文件**：`ActivityImageServiceImpl.java`
- 重写 `incrementUseCountByPlatform` 方法
- 添加必要的导入：`ActivityImagePlatformUsageEntity` 和 `Date`

### 2. DAO 接口
**文件**：`ActivityImagePlatformUsageDao.java`
- 注释掉 `incrementUsageCount` 方法声明

### 3. Mapper XML
**文件**：`ActivityImagePlatformUsageDao.xml`
- 注释掉 `incrementUsageCount` 的 SQL 实现

## 功能对比

| 特性 | 原实现（SQL） | 新实现（Java） |
|------|---------------|----------------|
| 原子性 | ✅ 数据库保证 | ⚠️ 需要事务保证 |
| 性能 | ✅ 单次数据库操作 | ⚠️ 可能需要两次操作 |
| 可读性 | ❌ SQL 语法复杂 | ✅ Java 代码清晰 |
| 调试性 | ❌ 难以调试 | ✅ 容易调试 |
| 扩展性 | ❌ 难以扩展 | ✅ 容易扩展 |
| 日志记录 | ❌ 无法记录详细日志 | ✅ 可以添加详细日志 |

## 注意事项

### 1. 事务处理
新实现可能需要在调用方法时确保事务一致性：
```java
@Transactional
public void someBusinessMethod() {
    // 业务逻辑
    activityImageService.incrementUseCountByPlatform(imageId, platform, activityId);
    // 其他操作
}
```

### 2. 并发处理
在高并发场景下，可能需要考虑乐观锁或悲观锁：
```java
// 可以考虑添加版本号字段进行乐观锁控制
@Version
private Integer version;
```

### 3. 性能优化
如果性能成为瓶颈，可以考虑：
- 使用缓存减少数据库查询
- 批量处理多个图片的使用次数更新
- 异步处理使用次数统计

## 测试验证

### 1. 单元测试
```java
@Test
public void testIncrementUseCountByPlatform() {
    // 测试新增记录
    activityImageService.incrementUseCountByPlatform(1L, "douyin", 100L);
    
    // 测试更新记录
    activityImageService.incrementUseCountByPlatform(1L, "douyin", 100L);
    
    // 验证使用次数
    Integer count = activityImageService.getUsageCountByPlatform(1L, "douyin");
    assertEquals(2, count.intValue());
}
```

### 2. 集成测试
验证在实际业务场景中的正确性：
- 点评功能中的图片使用次数统计
- 多平台图片使用次数独立计算
- 并发操作的数据一致性

## 回滚方案

如果需要回滚到原实现：
1. 恢复 DAO 接口中的 `incrementUsageCount` 方法
2. 恢复 Mapper XML 中的 SQL 实现
3. 将 Service 中的实现改回调用 DAO 方法

```java
@Override
public void incrementUseCountByPlatform(Long imageId, String platform, Long activityId) {
    activityImagePlatformUsageDao.incrementUsageCount(imageId, platform, activityId);
}
```
