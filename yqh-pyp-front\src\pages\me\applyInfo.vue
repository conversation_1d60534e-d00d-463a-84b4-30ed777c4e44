<template>
  <div class="page">
    <van-form>
      <div v-show="channelVisible">

        <div style="margin-top: 8px" class="nav-title">
          <div class="color"></div>
          <div class="text">报名通道选择</div>
        </div>
        <van-radio-group v-model="channelId">
          <van-cell-group inset>
            <van-cell v-for="(item, index) in channelList" :key="item.id" :title="item.name" clickable
              @click="chooseChannel(item.id, index)">
              <template v-if="!item.children" #right-icon>
                <van-radio :name="item.id" />
              </template>
              <div slot="label">
                <div v-if="item.children">
                  <van-radio-group v-model="channelId">
                    <van-cell-group>
                      <van-cell v-for="(item1, index1) in item.children" :key="item1.id" :title="item1.name" clickable
                        @click.stop="chooseChannelChild(item1.id, index, index1)">
                        <template #right-icon>
                          <van-radio :name="item1.id" />
                        </template>
                        <div slot="label">
                          <div v-if="item1.description">{{ item1.description }}</div>
                          <div v-if="item1.price > 0">￥{{ item1.price }}元</div>
                        </div>
                      </van-cell>
                    </van-cell-group>
                  </van-radio-group>
                </div>
                <div v-else>
                  <div v-if="item.description">{{ item.description }}</div>
                  <div v-if="item.price > 0">￥{{ item.price }}元</div>
                </div>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">报名信息修改</div>
      </div>
      <div class="dyn-item">
        <van-field v-model="userInfo.contact" name="姓名" label="姓名" :required="true" placeholder="姓名" :rules="[
          {
            required: true,
            message: '请填写姓名',
          },
        ]" />
      </div>
      <div class="dyn-item">
        <van-field :disabled="true" v-model="userInfo.mobile" name="手机" label="手机" :required="true" placeholder="手机"
          :rules="[
            {
              required: true,
              message: '请填写手机',
            },
          ]" />
      </div>
      <!-- 如果不是扩展字段 -->
      <div class="dyn-item" v-for="item in applyActivityConfigList" :key="item.id">
        <!-- 如果是填空 -->
        <div v-if="item.type == 0">
          <van-field v-model="userInfo[item.applyConfigFieldName]" :name="item.finalName" :label="item.finalName"
            :required="item.required == 1 ? true : false" :placeholder="item.finalName" :rules="[
              {
                required: item.required == 1 ? true : false,
                message: '请填写' + item.finalName,
              },
            ]" />
        </div>
        <!-- 如果是单选 -->
        <div v-else-if="item.type == 1">
          <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
            :title="item.finalName" is-link @click="showRadio(item)" />
        </div>
        <!-- 如果是多选 -->
        <div v-else-if="item.type == 2">
          <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
            :title="item.finalName" is-link @click.native="showCheckBox(item)" />
        </div>
            <!-- 如果是日期 -->
            <div v-else-if="item.type == 5">
              <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
                :title="item.finalName" is-link @click.native="showDate(item)" />
            </div>
        <!-- 如果是特殊字段 -->
        <div v-else-if="item.type == 4">
          <van-cell v-if="item.applyConfigFieldName == 'area'" v-model="userInfo.areaName"
            :required="item.required == 1 ? true : false" :title="item.finalName" is-link @click="showArea(item)" />
        </div>
      </div>
      <!-- 如果是扩展字段 -->
      <div class="dyn-item" v-for="(extraItem, index) in extraResult" :key="index">
        <!-- 如果是填空(扩展字段) -->
        <div v-if="extraItem.type == 0">
          <van-field v-model="extraItem.value" :name="extraItem.finalName" :label="extraItem.finalName"
            :required="extraItem.required == 1 ? true : false" :placeholder="extraItem.finalName" :rules="[
              {
                required: extraItem.required == 1 ? true : false,
                message: '请填写' + extraItem.finalName,
              },
            ]" />
        </div>
        <!-- 如果是单选(扩展字段) -->
        <div v-else-if="extraItem.type == 1">
          <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
            :title="extraItem.finalName" is-link @click="showExtraRadio(extraItem)" />
        </div>
        <!-- 如果是多选(扩展字段) -->
        <div v-else-if="extraItem.type == 2">
          <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
            :title="extraItem.finalName" is-link @click.native="showExtraCheckBox(extraItem)" />
        </div>
            <div v-else-if="extraItem.type == 5">
              <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
                :title="extraItem.finalName" is-link @click.native="showExtraDate(extraItem)" />
            </div>
      </div>
          <div v-if="indexChannel.isVerify">
            <van-cell :title="indexChannel.verifyName ? indexChannel.verifyName : '审核材料'" required
              :rules="[{ required: true, message: '请上传' + (indexChannel.verifyName ? indexChannel.verifyName : '审核材料') }]">
              <van-uploader :after-read="afterRead" name="credit" :before-read="beforeRead" accept="image/*">
                <van-icon v-if="!userInfo.credit" slot="default" name="add-o" size="50px"
                  style="margin-left: 5px"></van-icon>
                <van-image v-else height="50px" :src="userInfo.credit" fit="contain" />
              </van-uploader>
            </van-cell>
          </div>
      <div v-if="indexChannel.isInvoice">
        <div style="margin-top: 8px" class="nav-title">
          <div class="color"></div>
          <div class="text">发票信息填写</div>
        </div>
        <van-cell-group>
          <van-field required v-model="userInfo.invoiceName" label="发票抬头" placeholder="请输入发票抬头" @clear="cancel"
            clearable @input="autoCompleteList" :rules="[{ required: true, message: '请填写发票抬头' }]" />
          <van-cell title="" v-show="autoCompleteListView" style="height:62px">
            <template>
              <div v-for="item in invoicelist" :key="item.id" :title="item.invoiceName" clearable
                @click="invoiceChoose(item)">
                <div style="flex:3"></div>
                <div style="flex:6;height: 23px;overflow: hidden;line-height: 22px;">{{ item.invoiceName }}</div>
                <div style="font-size: 12px;color: #949494;line-height: 17px;">{{ item.invoiceCode }}</div>
              </div>
            </template>
          </van-cell>
          <van-field v-model="userInfo.invoiceCode" label="纳税人识别号" clearable required placeholder="请输入纳税人识别号"
            :rules="[{ required: true, message: '请输入纳税人识别号' }]" />
          <van-field v-model="userInfo.invoiceAddress" label="注册地址(专票)" clearable placeholder="请输入注册地址(专票)" />
          <van-field v-model="userInfo.invoiceMobile" label="注册电话(专票)" clearable placeholder="请输入注册电话(专票)" />
          <van-field v-model="userInfo.invoiceBank" label="开户银行(专票)" clearable placeholder="请输入开户银行(专票)" />
          <van-field v-model="userInfo.invoiceAccount" label="银行账户(专票)" clearable placeholder="请输入银行账户(专票)" />
        </van-cell-group>
      </div>
      <div style="margin: 16px;display:flex">
        <van-button style="flex: 1" round block type="danger" @click="cancelApply">取消报名</van-button>
        <van-button style="flex: 1;margin-left:10px" round block type="info" native-type="submit"
          @click="onSubmit">提交</van-button>
      </div>
    </van-form>
    <!-- 单选弹窗 -->
    <van-popup v-model="radioVisible" closeable position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ chooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-radio-group v-model="userInfo[chooseResult.applyConfigFieldName]">
          <van-cell-group>
            <van-cell v-for="item in chooseResult.selectData" :key="item" :title="item" clickable @click="choose(item)">
              <template #right-icon>
                <van-radio :name="item" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
    <!-- 多选弹窗 -->
    <van-popup v-model="checkBoxVisible" closeable @click-close-icon="chooseConfirm"
      close-icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png"
      position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ chooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-checkbox-group v-model="checkBoxResult" change="onChange">
          <van-cell-group>
            <van-cell v-for="(item, index) in chooseResult.selectData" :key="item" :title="item" clickable
              :data-index="index" @click="toggle">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxes"></van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
    <!-- 单选弹窗（扩展字段） -->
    <van-popup v-model="extraRadioVisible" closeable position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ extraChooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-radio-group v-model="extraChooseResult.value">
          <van-cell-group>
            <van-cell v-for="item in extraChooseResult.selectData" :key="item" :title="item" clickable
              @click="chooseExtra(item)">
              <template #right-icon>
                <van-radio :name="item" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
    <!-- 多选弹窗（扩展字段） -->
    <van-popup v-model="extraCheckBoxVisible" closeable @click-close-icon="chooseExtraConfirm"
      close-icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png"
      position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ extraChooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-checkbox-group v-model="extraCheckBoxResult" change="onChangeExtra">
          <van-cell-group>
            <van-cell v-for="(item, index) in extraChooseResult.selectData" :key="item" :title="item" clickable
              :data-index="index" @click="toggleExtra">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxesExtra"></van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
    <!-- 日期选择 -->
    <van-calendar v-model="dateVisible" @confirm="dateSelect"  :min-date="minDate" :max-date="maxDate"/>
    <van-calendar v-model="extraDateVisible" @confirm="dateExtraSelect"  :min-date="minDate" :max-date="maxDate"/>
    <!-- 省市区弹窗 -->
    <van-popup v-model="areaVisible" position="bottom" :style="{ height: '45%' }">
      <van-area @cancel="areaVisible = false" @confirm="areaSelect" title="省市区选择" :area-list="areaList"
        :value="areaCode" />
    </van-popup>
  </div>
</template>

<script>
import { isMobile } from "@/js/validate";
import { areaList } from "@vant/area-data";
import date from "@/js/date.js";
export default {
  components: {},
  data() {
    return {
      minDate: new Date(2023, 0, 1),
      maxDate: new Date(2028, 0, 31),
      dateVisible: false,
      extraDateVisible: false,
      autoCompleteListView: false,
      invoicelist: [],
      activityId: undefined,
      channelId: undefined,
      areaVisible: false,
      areaCode: '110101',
      isPay: 0,
      userInfo: {
        invoiceName: '',
        invoiceCode: '',
        invoiceBank: '',
        invoiceAddress: '',
        invoiceMobile: '',
        invoiceAccount: '',
      },
      channelList: [],
      applyActivityConfigList: [],
      extraResult: [],
      channelVisible: false,
      radioVisible: false,
      checkBoxVisible: false,
      indexChannel: {},
      chooseResult: {},
      checkBoxResult: [],
      extraRadioVisible: false,
      extraCheckBoxVisible: false,
      extraChooseResult: {},
      extraCheckBoxResult: [],
      areaList
    };
  },
  mounted() {
    document.title = "报名信息填写";
    this.activityId = this.$route.query.id;
    this.getUserActivityInfo();
    // this.getApplyActivityChannelConfig();
  },
  methods: {
    showDate(v) {
      this.chooseResult = v;
      this.dateVisible = true;
    },
    // 展示日期（扩展字段）
    showExtraDate(v) {
      this.extraChooseResult = v;
      this.extraDateVisible = true;
    },
    // 日期选择
    dateSelect(v) {
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, date.formatDate.format(
              new Date(v),
              "yyyy/MM/dd"
            ));
      this.dateVisible = false;
    },
    dateExtraSelect(v) {
      console.log(v)
      this.extraResult.forEach((item) => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = date.formatDate.format(
              new Date(v),
              "yyyy/MM/dd"
            );
        }
      });
      this.extraDateVisible = false;
    },
    autoCompleteList() {
      if (this.userInfo.invoiceName) {
        this.$fly
          .get(`/pyp/web/invoice/findByInvoiceName/`, {
            invoiceName: this.userInfo.invoiceName
          })
          .then((res) => {
            if (res.code == 200) {
              if (res.result.length != 0) {
                this.invoicelist = res.result
                this.autoCompleteListView = true
              } else {
                this.invoicelist = []
              }
            }
          });
      }
    },
    invoiceChoose(item) {
      this.userInfo.invoiceName = item.invoiceName
      this.userInfo.invoiceCode = item.invoiceCode
      this.userInfo.invoiceAccount = item.invoiceAccount
      this.userInfo.invoiceBank = item.invoiceBank
      this.userInfo.invoiceAddress = item.invoiceAddress
      this.userInfo.invoiceMobile = item.invoiceMobile
      this.autoCompleteListView = false
    },
    cancel() {
      this.autoCompleteListView = false
      this.invoicelist = []
    },
    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/web/activity/activityuserapplyorder/applyUserInfo/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.activityUserEntity;
            if (this.userInfo.applyActivityChannelConfigId) {
              this.getApplyActivityChannelConfig(this.userInfo.applyActivityChannelConfigId);
            }
            this.applyActivityConfigList = res.applyActivityConfigEntities;
            this.extraResult = res.extraConfig;
            this.applyActivityConfigList.forEach((item) => {
              if (item.type == 1) {
                // 单选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
              } else if (item.type == 2) {
                // 多选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
                // this.userInfo[item.applyConfigFieldName] = this.userInfo[item.applyConfigFieldName] ? this.userInfo[item.applyConfigFieldName].replace(/，/ig, ',').split(",") : [];
              }
            });
            // 扩展
            if (this.extraResult) {
              console.log(1)
              this.extraResult.forEach((extraItem) => {
                if (extraItem.type == 1) {
                  // 单选
                  extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                } else if (extraItem.type == 2) {
                  // 多选
                  extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                  // extraItem.value = extraItem.value ? extraItem.value.replace(/，/ig, ',').split(",") : [];
                }
              })
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getApplyActivityChannelConfig(v) {
      this.$fly
        .get(
          `/pyp/apply/applyactivitychannelconfig/info/${v}`
        )
        .then((res) => {
          if (res.code == 200) {
            this.indexChannel = res.applyActivityChannelConfig;
          }
        })
    },
    // getApplyActivityChannelConfig() {
    //   this.$fly
    //     .get(
    //       `/pyp/apply/applyactivitychannelconfig/findByActivityId/${this.activityId}`
    //     )
    //     .then((res) => {
    //       if (res.code == 200) {
    //         this.channelList = res.result;
    //       } else {
    //         vant.Toast(res.msg);
    //       }
    //     });
    // },
    //选择报名通道
    chooseChannel(v) {
      this.channelId = v;
      this.getApplyActivityConfig();
    },
    // 单选选择
    choose(v) {
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, v)
      this.radioVisible = false;
    },
    // 展示单选
    showRadio(v) {
      this.chooseResult = v;
      this.radioVisible = true;
    },
    // 多选框改变事件
    onChange(event) {
      this.setData({
        checkBoxResult: event.detail
      })
    },
    // 多选选择
    toggle(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxes[index].toggle();
    },
    // 多选选择确定
    chooseConfirm() {
      var searchResult = ''
      if (this.checkBoxResult.length > 1) {
        for (let i = 0; i < this.checkBoxResult.length; i++) {
          if (i === this.checkBoxResult.length - 1) {
            searchResult = searchResult + this.checkBoxResult[i]
          } else {
            searchResult = searchResult + this.checkBoxResult[i] + ','
          }
        }
      } else {
        searchResult = this.checkBoxResult[0]
      }
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, searchResult)
      this.checkBoxVisible = false
    },
    // 展示多选
    showCheckBox(v) {
      this.chooseResult = v;
      var defaultResult = this.userInfo[this.chooseResult.applyConfigFieldName];
      this.checkBoxResult = !defaultResult ? [] : defaultResult.split(",");
      this.checkBoxVisible = true;
    },
    // 单选选择（扩展字段）
    chooseExtra(v) {
      this.extraResult.forEach(item => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = v;
        }
      })
      this.extraRadioVisible = false;
    },
    // 展示单选（扩展字段）
    showExtraRadio(v) {
      this.extraChooseResult = v;
      this.extraRadioVisible = true;
    },
    // 多选框改变事件（扩展字段）
    onChangeExtra(event) {
      this.setData({
        extraCheckBoxResult: event.detail
      })
    },
    // 多选选择（扩展字段）
    toggleExtra(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxesExtra[index].toggle();
    },
    // 多选选择确定（扩展字段）
    chooseExtraConfirm() {
      var searchResult = ''
      if (this.extraCheckBoxResult.length > 1) {
        for (let i = 0; i < this.extraCheckBoxResult.length; i++) {
          if (i === this.extraCheckBoxResult.length - 1) {
            searchResult = searchResult + this.extraCheckBoxResult[i]
          } else {
            searchResult = searchResult + this.extraCheckBoxResult[i] + ','
          }
        }
      } else {
        searchResult = this.extraCheckBoxResult[0]
      }
      this.extraResult.forEach(item => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = searchResult;
        }
      })
      this.extraRadioVisible = false
    },
    // 展示多选（扩展字段）
    showExtraCheckBox(v) {
      this.extraChooseResult = v;
      var defaultResult = v.value;
      this.extraCheckBoxResult = !defaultResult ? [] : defaultResult.split(",");
      this.extraCheckBoxVisible = true;
    },
    // 展示特殊配置===省市区选择====
    showArea(v) {
      this.chooseResult = v;
      this.areaCode = this.userInfo.area ? this.userInfo.area.split(",")[2] : '110101'
      this.areaVisible = true;
    },
    areaSelect(v) {
      console.log(v)
      let areaCode = v[0].code + ',' + v[1].code + ',' + v[2].code;
      let areaName = v[0].name + ',' + v[1].name + ',' + v[2].name;
      this.areaCode = v[2].code;
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, areaCode);
      this.$set(this.userInfo, 'areaName', areaName);
      this.areaVisible = false;
    },
    getApplyActivityConfig() {
      this.$fly
        .get(`/pyp/apply/applyactivityconfig/findByChannelId/${this.channelId}`)
        .then((res) => {
          if (res.code == 200) {
            this.applyActivityConfigList = res.result;
            this.applyActivityConfigList.forEach((item) => {
              if (item.type == 1) {
                // 单选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
              } else if (item.type == 2) {
                // 多选
                item.selectData = item.selectData.replace(/，/ig, ',').split(",");
              } else if (item.type == 3) {
                this.extraResult = JSON.parse(item.extra);
                this.extraResult.forEach((extraItem) => {
                  if (extraItem.type == 1) {
                    // 单选
                    extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                  } else if (extraItem.type == 2) {
                    // 多选
                    extraItem.selectData = extraItem.selectData.replace(/，/ig, ',').split(",");
                  }
                })
              }
            });
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    // 修改报名信息
    onSubmit() {
      // 基础信息校验
      this.applyActivityConfigList.forEach((item) => {
        if (item.type != 3 && item.required && !this.userInfo[item.applyConfigFieldName]) {
          vant.Toast("请输入" + item.finalName);
          return;
        }
      })
      // 如果存在扩展字段
      if (this.extraResult) {
        // 扩展字段校验
        this.extraResult.forEach((item) => {
          item.selectData = !item.selectData ? null : item.selectData.toString();
          if (item.type != 3 && item.required && !item.value) {
            vant.Toast("请输入" + item.finalName);
            return;
          }
        })
      }
      // 全部校验通过，开始传送数据
      this.userInfo.activityId = this.activityId;
      this.userInfo.applyActivityChannelConfigId = this.channelId;
      this.userInfo.applyExtraVos = this.extraResult;

      // 保存
      this.$fly
        .post("/pyp/activity/activityuser/update",
          this.userInfo
        )
        .then((res) => {
          if (res && res.code === 200) {
            vant.Toast("更新成功");
            this.$router.go(-1)
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    afterRead(e, name) {
      let filedName = name.name;
      e.status = "uploading";
      e.message = "上传中...";
      let formData = new FormData();
      // formData.append("pushKey", this.pushKey);
      // formData.append("activityId", this.activityId);
      formData.append("file", e.file);

      this.$fly.post("/pyp/web/upload", formData).then((res) => {
        if (res && res.code === 200) {
          this.$set(this.userInfo, filedName, res.result);
        }
      });
    },
    beforeRead(file) {
      // if (file.type !== 'image/jpeg') {
      //   Toast('请上传 jpg 格式图片');
      //   return false;
      // }
      return true;
    },
    // 取消报名
    cancelApply() {
      vant.Dialog.confirm({
        title: '提示',
        message: '确认取消报名?',
      }).then(() => {
        this.$fly
          .post("/pyp/web/activity/activityuserapplyorder/cancelOrder", {
            id: this.userInfo.id
          })
          .then((res) => {
            if (res && res.code === 200) {
              vant.Toast("取消成功");
              this.$router.go(-1)
            } else {
              vant.Toast(res.msg);
            }
          });
      }).catch(() => {

      });
    }
  },
};
</script>

<style lang="less">
@popup-close-icon-size: 40px;
@popup-close-icon-color: black;

.page {
  background-color: #f0f3f5;
}

.dyn-item {
  margin-top: 5px;
}

.popup-title {

  font-weight: bold;
  font-size: 15px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  padding: 0 50px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>