<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
    <el-form-item label="文件地址" prop="url">
              <el-input v-model="dataForm.url" placeholder="文件地址"></el-input>
          </el-form-item>
    <el-form-item label="专家ID" prop="activityGuestId">
              <el-input v-model="dataForm.activityGuestId" placeholder="专家ID"></el-input>
          </el-form-item>
    <el-form-item label="活动ID" prop="activityId">
              <el-input v-model="dataForm.activityId" placeholder="活动ID"></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
                    
          url: '',
    
          activityGuestId: '',
    
          activityId: ''
        },
        dataRule: {
          url: [
            { required: true, message: '文件地址不能为空', trigger: 'blur' }
          ],
          activityGuestId: [
            { required: true, message: '专家ID不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '活动ID不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/log/logpdfservicefee/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.url = data.logPdfServiceFee.url
                this.dataForm.activityGuestId = data.logPdfServiceFee.activityGuestId
                this.dataForm.activityId = data.logPdfServiceFee.activityId
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/log/logpdfservicefee/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                                                                            'url': this.dataForm.url,
                            'activityGuestId': this.dataForm.activityGuestId,
                            'activityId': this.dataForm.activityId
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
