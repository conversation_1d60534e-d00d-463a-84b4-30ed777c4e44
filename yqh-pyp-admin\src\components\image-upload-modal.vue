<template>
  <el-dialog
    title="选择图片"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
    append-to-body
    :modal-append-to-body="false"
    class="image-upload-dialog"
  >
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 素材库选择 -->
      <el-tab-pane label="素材库" name="library">
        <div class="library-content">
          <div class="search-bar">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索图片..." 
              @input="handleSearch"
              style="width: 300px; margin-bottom: 20px;"
            >
              <i slot="prefix" class="el-icon-search"></i>
            </el-input>
          </div>
          
          <div v-loading="libraryLoading" class="image-grid">
            <div 
              v-for="item in libraryImages" 
              :key="item.id" 
              class="image-item"
              :class="{ 'selected': isSelected(item) }"
              @click="toggleSelect(item)"
            >
              <img :src="item.url" :alt="item.url" />
              <div class="image-overlay">
                <i v-if="isSelected(item)" class="el-icon-check selected-icon"></i>
              </div>
            </div>
          </div>
          
          <el-pagination
            v-if="libraryTotal > 0"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[12, 24, 48, 96]"
            :page-size="pageSize"
            :total="libraryTotal"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; text-align: center;"
          />
        </div>
      </el-tab-pane>
      
      <!-- 本地上传 -->
      <el-tab-pane label="本地上传" name="upload">
        <div class="upload-content">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="uploadFileList"
            :multiple="multiple"
            :limit="maxCount"
            :on-exceed="handleExceed"
            list-type="picture-card"
            accept="image/*"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tips">
            <p>支持格式：JPG、PNG、GIF</p>
            <p>单张图片大小不超过 {{ maxSize }}MB</p>
            <p v-if="multiple">最多可上传 {{ maxCount }} 张图片</p>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 已选择的图片预览 -->
    <div v-if="selectedImages.length > 0" class="selected-preview">
      <h4>已选择 {{ selectedImages.length }} 张图片：</h4>
      <div class="selected-images">
        <div 
          v-for="(image, index) in selectedImages" 
          :key="image.id || image.url" 
          class="selected-item"
        >
          <img :src="image.url" :alt="image.url" />
          <div class="remove-btn" @click="removeSelected(index)">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="selectedImages.length === 0"
      >
        确定选择 ({{ selectedImages.length }})
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import Compressor from 'compressorjs'

export default {
  name: 'ImageUploadModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    maxCount: {
      type: Number,
      default: 9
    },
    maxSize: {
      type: Number,
      default: 2 // MB
    },
    defaultImages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'library',
      searchKeyword: '',
      libraryLoading: false,
      libraryImages: [],
      libraryTotal: 0,
      currentPage: 1,
      pageSize: 12,
      selectedImages: [],
      uploadFileList: [],
      uploadUrl: '',
      uploadHeaders: {}
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.init()
        this.$nextTick(() => {
          this.setDialogZIndex()
        })
      }
    },
    defaultImages: {
      handler(val) {
        this.selectedImages = [...val]
      },
      immediate: true
    }
  },
  mounted() {
    this.initUploadConfig()
    this.setDialogZIndex()
  },
  methods: {
    init() {
      // 清空所有旧数据
      this.selectedImages = [...this.defaultImages]
      this.uploadFileList = []
      this.activeTab = 'library'
      this.searchKeyword = ''
      this.currentPage = 1
      this.libraryImages = []
      this.libraryTotal = 0

      // 清空上传组件的文件列表
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })

      // 加载素材库图片
      if (this.activeTab === 'library') {
        this.loadLibraryImages()
      }
    },
    
    initUploadConfig() {
      this.uploadUrl = this.$http.adornUrl(`/sys/oss/upload?token=${this.$cookie.get("token")}`)
      this.uploadHeaders = {
        'token': this.$cookie.get("token")
      }
    },

    setDialogZIndex() {
      // 动态设置弹窗层级，确保显示在最上层
      this.$nextTick(() => {
        const dialogWrapper = document.querySelector('.image-upload-dialog .el-dialog__wrapper')
        if (dialogWrapper) {
          dialogWrapper.style.zIndex = '3000'
        }
        const overlay = document.querySelector('.image-upload-dialog .el-overlay')
        if (overlay) {
          overlay.style.zIndex = '2999'
        }
      })
    },
    
    handleTabClick(tab) {
      if (tab.name === 'library') {
        this.loadLibraryImages()
      }
    },
    
    // 加载素材库图片
    loadLibraryImages() {
      this.libraryLoading = true
      this.$http({
        url: this.$http.adornUrl('/sys/oss/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.currentPage,
          'limit': this.pageSize,
          'appid': this.$cookie.get('appid'),
          'sidx': 'id',
          'order': 'desc'
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          // 只显示图片文件
          this.libraryImages = data.page.list.filter(item => this.isImageUrl(item.url))
          this.libraryTotal = data.page.totalCount
        } else {
          this.libraryImages = []
          this.libraryTotal = 0
        }
        this.libraryLoading = false
      }).catch(() => {
        this.libraryLoading = false
      })
    },
    
    // 搜索处理
    handleSearch() {
      // 这里可以添加搜索逻辑
      this.currentPage = 1
      this.loadLibraryImages()
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadLibraryImages()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadLibraryImages()
    },
    
    // 判断是否为图片URL
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    },
    
    // 选择/取消选择图片
    toggleSelect(image) {
      const index = this.selectedImages.findIndex(item => 
        (item.id && item.id === image.id) || item.url === image.url
      )
      
      if (index > -1) {
        this.selectedImages.splice(index, 1)
      } else {
        if (!this.multiple) {
          this.selectedImages = [image]
        } else if (this.selectedImages.length < this.maxCount) {
          this.selectedImages.push(image)
        } else {
          this.$message.warning(`最多只能选择 ${this.maxCount} 张图片`)
        }
      }
    },
    
    // 判断图片是否已选择
    isSelected(image) {
      return this.selectedImages.some(item => 
        (item.id && item.id === image.id) || item.url === image.url
      )
    },
    
    // 移除已选择的图片
    removeSelected(index) {
      this.selectedImages.splice(index, 1)
    },
    
    // 上传前检查
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < this.maxSize
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error(`图片大小不能超过 ${this.maxSize}MB!`)
        return false
      }
      
      // 图片压缩
      if (file.size / 1024 > 100) {
        return new Promise((resolve, reject) => {
          new Compressor(file, {
            quality: 0.8,
            success(result) {
              resolve(result)
            },
            error(err) {
              reject(err)
            }
          })
        })
      }
      
      return true
    },
    
    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response && response.code === 200) {
        const newImage = {
          id: Date.now(), // 临时ID
          url: response.url,
          createDate: new Date().toISOString(),
          fileSize: file.size, // 保存文件大小（字节）
          fileName: file.name // 保存文件名
        }

        if (!this.multiple) {
          this.selectedImages = [newImage]
        } else if (this.selectedImages.length < this.maxCount) {
          this.selectedImages.push(newImage)
        }

        this.$message.success('上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },
    
    // 上传失败
    handleUploadError(err, file, fileList) {
      this.$message.error('上传失败: ' + err.message)
    },
    
    // 超出文件数量限制
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传 ${this.maxCount} 张图片`)
    },
    
    // 确认选择
    handleConfirm() {
      this.$emit('confirm', this.selectedImages)
      this.handleClose()
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')

      // 关闭时清空数据，避免下次打开时显示旧数据
      this.$nextTick(() => {
        this.selectedImages = []
        this.uploadFileList = []
        this.searchKeyword = ''
        this.currentPage = 1
        this.libraryImages = []
        this.libraryTotal = 0
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    },

    // 格式化文件大小 - 固定显示为MB，保留2位小数
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0.00 MB'
      const mb = bytes / (1024 * 1024)
      return mb.toFixed(2) + ' MB'
    }
  }
}
</script>

<style scoped>
.library-content {
  min-height: 400px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s;
}

.image-item:hover {
  border-color: #409EFF;
}

.image-item.selected {
  border-color: #409EFF;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-overlay,
.image-item.selected .image-overlay {
  opacity: 1;
}

.selected-icon {
  color: #409EFF;
  font-size: 24px;
  background: white;
  border-radius: 50%;
  padding: 4px;
}

.upload-content {
  min-height: 400px;
  text-align: center;
}

.upload-tips {
  margin-top: 20px;
  color: #999;
}

.upload-tips p {
  margin: 5px 0;
}

.selected-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.selected-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.selected-item {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

.selected-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.remove-btn:hover {
  background: #f78989;
}

/* 确保弹窗显示在最上层 */
.image-upload-dialog {
  z-index: 3000 !important;
}

.image-upload-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.image-upload-dialog .el-overlay {
  z-index: 2999 !important;
}
</style>

<style>
/* 全局样式，确保图片上传弹窗显示在最上层 */
.image-upload-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.image-upload-dialog .el-overlay {
  z-index: 2999 !important;
}

.image-upload-dialog .el-dialog {
  z-index: 3001 !important;
}

/* 确保弹窗内的元素也有正确的层级 */
.image-upload-dialog .el-tabs {
  z-index: 3002 !important;
}

.image-upload-dialog .el-upload {
  z-index: 3003 !important;
}

.image-upload-dialog .el-pagination {
  z-index: 3002 !important;
}
</style>
