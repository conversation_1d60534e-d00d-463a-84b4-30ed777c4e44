<template>
  <div :class="isMobilePhone ? '' : 'pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <van-search v-model="dataForm.name" placeholder="请输入您要搜索的专家名称" show-action shape="round">
      <div slot="action" @click="onSearch" class="search-text">搜索</div>
    </van-search>
    <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
      <van-tabs v-model="dateActive" :ellipsis="false" @click="dateOnClick">
        <van-tab title="全部日期" name=""></van-tab>
        <van-tab v-for="item in activityDateBetween" :key="item.realDate" :name="item.realDate"
          :title="item.textDate"></van-tab>
      </van-tabs>
      <div style="display: flex">
        <van-sidebar @change="placeOnChange" v-model="placeActive" style="width: 20%" v-if="placeList.length > 1">
          <van-sidebar-item title="全部场地" />
          <van-sidebar-item v-for="item in placeList" :key="item.id" :title="item.name" />
        </van-sidebar>
        <div class="data" :style="placeList.length > 1 ? 'width: 80%' : 'width: 100%'">
          <van-collapse accordion v-model="schedulesActive">
            <van-collapse-item v-for="item in dataList" :key="item.id" :name="item.id" style="margin-bottom: 10px">
              <van-card slot="title" style="background: white" :thumb="item.imageUrl
                  ? item.imageUrl
                  : activityInfo && activityInfo.mobileBanner
                    ? activityInfo.mobileBanner.split(',')[0]
                    : 'van-icon'
                " :class="hight(item)">
                <div slot="title" style="font-size: 14px">{{ item.name }}</div>
                <div slot="desc" style="padding-top: 10px; font-size: 12px; color: grey">
                  <div v-if="item.startTime">
                    {{ item.startTime ? item.startTime.substring(5, 16) : '' }}
                    <span v-if="item.endTime">~ {{ item.endTime ? item.endTime.substring(11, 16) : '' }}</span>
                  </div>
                  <div v-if="item.activitySpeakers && item.activitySpeakers.length > 0">
                    {{ item.aliasSpeakerName || '主席' }}：
                    <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                      v-for="item1 in item.activitySpeakers" :key="item1.id" size="medium" round type="primary" plain>{{
                      item1.name }}</van-tag>
                  </div>
                  <div v-if="item.activityGuests && item.activityGuests.length > 0">
                    {{ item.aliasGuestName || '主持' }}：
                    <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                      v-for="item1 in item.activityGuests" :key="item1.id" size="medium" round type="primary" plain>{{
                      item1.name }}</van-tag>
                  </div>
                <div v-if="item.activityDiscuss && item.activityDiscuss.length > 0">
                  {{ item.aliasDiscussName || '讨论' }}：
                  <van-tag @click="goExpertDetail(item1.id)" style="margin: 5px 10px 5px 0px"
                    v-for="item1 in item.activityDiscuss" :key="item1.id" size="medium" round type="primary" plain>{{
                    item1.name }}</van-tag>
                </div>
                </div>
              </van-card>
              <van-steps active="-1" direction="vertical">
                <van-step v-for="item2 in item.placeActivityTopicScheduleEntities" :key="item2.id"
                  :class="hight(item2)">
                  <div>
                    <div style="color: black">{{ item2.name }}</div>
                    <div v-if="
                      item2.activitySpeakers &&
                      item2.activitySpeakers.length > 0
                    ">
                      {{ item2.aliasSpeakerName || '主持' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activitySpeakers" :key="item3.id" round plain size="medium"
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div v-if="
                      item2.activityGuests && item2.activityGuests.length > 0
                    ">
                      {{ item2.aliasGuestName || '讲者' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityGuests" :key="item3.id" size="medium" round plain
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div v-if="
                      item2.activityDiscuss &&
                      item2.activityDiscuss.length > 0
                    ">
                      {{ item2.aliasDiscussName || '讨论' }}：
                      <van-tag @click="goExpertDetail(item3.id)" style="margin: 5px 10px 5px 0px"
                        v-for="item3 in item2.activityDiscuss" :key="item3.id" size="medium" round plain
                        type="warning">{{ item3.name }}</van-tag>
                    </div>
                    <div>
                      {{ item2.startTime ? item2.startTime.substring(5, 16) : '' }} ~
                      {{ item2.endTime ? item2.endTime.substring(11, 16) : '' }}
                    </div>
                  </div>
                </van-step>
              </van-steps>
            </van-collapse-item>
          </van-collapse>
        </div>
      </div>
    </van-list>
    <!-- 返回按钮 -->
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
  </div>
</template>

<script>
import date from "@/js/date.js";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: { pcheader },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      activityId: undefined,
      dataForm: {
        name: "",
        placeId: "",
        date: "",
      },
      activityInfo: {},
      loading: false,
      finished: false,
      flag: false,
      dataList: [],
      placeList: [],
      activityDateBetween: [],
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      placeActive: 0,
      dateActive: "",
      schedulesActive: "",
    };
  },
  mounted() {
    this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid");
    this.getActivityInfo();
    this.getActivityDateBetween();
    this.getPlace();
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      if (!this.flag) {
        this.getActivityList();
      }
    },
    onLoad() {
      if (!this.flag) {
        this.getActivityList();
      }
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.activityInfo = res.activity;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            document.title = this.activityInfo.name;
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (startTime.includes(endTime)) {
              let desc =
                "时间:" +
                startTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            } else {
              let desc =
                "时间:" +
                startTime +
                "-" +
                endTime +
                "\n地址:" +
                this.activityInfo.address;
              this.$wxShare(
                "日程列表-" + this.activityInfo.name,
                (this.activityInfo.shareUrl ? this.activityInfo.shareUrl : this.activityInfo.mobileBanner.split(",")[0]),
                desc
              ); //加载微信分享
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getActivityDateBetween() {
      this.$fly
        .get(`/pyp/activity/activity/dateBetween/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.activityDateBetween = res.result;
          } else {
            vant.Toast(res.msg);
            this.activityDateBetween = [];
          }
        });
    },
    getPlace() {
      this.$fly
        .get(`/pyp/place/placeactivity/findByActivityId/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.placeList = res.result;
          } else {
            vant.Toast(res.msg);
            this.placeList = [];
          }
        });
    },
    getActivityList() {
      this.flag = true;
      this.$fly
        .get("/pyp/web/place/placeactivitytopic/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          placeId: this.dataForm.placeId,
          date: this.dataForm.date,
          name: this.dataForm.name,
        })
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.flag = false;
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.schedulesActive = this.dataList[0].id;
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              this.loading = false;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    dateOnClick(name, title) {
      this.dataForm.date = name;
      this.onSearch();
    },
    placeOnChange(index) {
      if (index == 0) {
        this.dataForm.placeId = "";
      } else {
        this.placeList.forEach((e, index1) => {
          var realIndex = index1 + 1;
          if (realIndex == index) {
            this.dataForm.placeId = e.id;
          }
        });
      }
      this.onSearch();
    },
    goExpertDetail(v) {
      this.$router.push({
        name: "schedulesExpertDetail",
        query: { detailId: v, id: this.activityId },
      });
    },
    hight(item) {
      if (this.dataForm.name) {
        // 存在搜索才去高亮
        if (item.activityGuests && item.activityGuests.length > 0) {
          const flag = item.activityGuests.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
        if (item.activitySpeakers && item.activitySpeakers.length > 0) {
          const flag = item.activitySpeakers.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
        if (item.activityDiscuss && item.activityDiscuss.length > 0) {
          const flag = item.activityDiscuss.filter(e => e.name.includes(this.dataForm.name)).length > 0;
          if (flag) {
            return "show";
          }
        }
      }
    },
    cmsTurnBack() {
      if (this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
  },
};
</script>
<style lang="less" scoped>
.data {
  /deep/ .van-cell {
    padding: 0;
    align-items: center;
  }

  /deep/ .van-cell__right-icon {
    margin-right: 20px;
  }
}

.van-sidebar-item {
  padding: 20px 10px;
}

.show {
  background: #bae2ff !important;
}
</style>