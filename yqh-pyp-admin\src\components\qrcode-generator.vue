<template>
  <div class="qrcode-generator">
    <vue-qrcode
      ref="qrcode"
      :value="value"
      :options="qrcodeOptions"
      @ready="onQrcodeReady"
    ></vue-qrcode>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode'

export default {
  name: 'QrcodeGenerator',
  components: {
    VueQrcode
  },
  props: {
    value: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      default: 200
    },
    level: {
      type: String,
      default: 'M'
    }
  },
  computed: {
    qrcodeOptions() {
      return {
        width: this.size,
        height: this.size,
        errorCorrectionLevel: this.level,
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      }
    }
  },
  methods: {
    // 二维码准备就绪回调
    onQrcodeReady() {
      // 可以在这里添加准备就绪后的逻辑
    },

    // 获取二维码数据URL
    getDataURL() {
      if (this.$refs.qrcode && this.$refs.qrcode.$el) {
        const canvas = this.$refs.qrcode.$el.querySelector('canvas')
        if (canvas) {
          return canvas.toDataURL('image/png')
        }
      }
      return null
    },

    // 下载二维码
    download(filename = 'qrcode.png') {
      const dataURL = this.getDataURL()
      if (dataURL) {
        const link = document.createElement('a')
        link.download = filename
        link.href = dataURL
        link.click()
      } else {
        console.error('无法获取二维码数据')
      }
    }
  }
}
</script>

<style scoped>
.qrcode-generator {
  display: inline-block;
}

.qrcode-generator :deep(canvas) {
  border: 1px solid #ddd;
  border-radius: 4px;
}

.qrcode-generator :deep(img) {
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
