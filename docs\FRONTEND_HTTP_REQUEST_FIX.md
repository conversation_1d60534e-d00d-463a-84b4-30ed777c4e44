# 前端HTTP请求方式修正文档

## 问题说明

在前端项目中，原本使用了 `this.$http.get` 和 `this.$http.post` 的请求方式，但这种写法在当前项目中不被支持。需要修改为项目中使用的 `this.$fly` 请求方式。

## 修改内容

### 1. getGroupBuyingCoupons 方法

**修改前：**
```javascript
async getGroupBuyingCoupons() {
  if (!this.activityInfo.showGroupBuying) {
    return;
  }

  const response = await this.$http.get('/pyp/web/groupbuying/coupons', {
    params: {
      activityId: this.activityId
    }
  });

  if (response.data.code === 200) {
    this.groupBuyingCoupons = response.data.coupons || [];
  }
}
```

**修改后：**
```javascript
getGroupBuyingCoupons() {
  if (!this.activityInfo.showGroupBuying) {
    return;
  }

  this.$fly.get('/pyp/web/groupbuying/coupons', {
    activityId: this.activityId
  }).then((res) => {
    if (res.code === 200) {
      this.groupBuyingCoupons = res.coupons || [];
    }
  }).catch((error) => {
    console.error('获取团购券失败:', error);
  });
}
```

### 2. goGroupBuying 方法

**修改前：**
```javascript
async goGroupBuying() {
  try {
    vant.Toast("正在加载团购券...");
    
    const response = await this.$http.get('/pyp/web/groupbuying/coupons', {
      params: {
        activityId: this.activityId
      }
    });

    if (response.data.code !== 200) {
      vant.Toast(response.data.msg || "获取团购券失败");
      return;
    }

    const coupons = response.data.coupons;
    // ... 其他逻辑
  } catch (error) {
    console.error('获取团购券失败:', error);
    vant.Toast("获取团购券失败，请稍后重试");
  }
}
```

**修改后：**
```javascript
goGroupBuying() {
  vant.Toast("正在加载团购券...");
  
  this.$fly.get('/pyp/web/groupbuying/coupons', {
    activityId: this.activityId
  }).then((res) => {
    if (res.code !== 200) {
      vant.Toast(res.msg || "获取团购券失败");
      return;
    }

    const coupons = res.coupons;
    // ... 其他逻辑
  }).catch((error) => {
    console.error('获取团购券失败:', error);
    vant.Toast("获取团购券失败，请稍后重试");
  });
}
```

### 3. jumpToGroupBuying 方法

**修改前：**
```javascript
async jumpToGroupBuying(coupon) {
  try {
    vant.Toast("正在跳转...");

    // 记录点击
    this.$http.post('/pyp/web/groupbuying/click', {
      couponId: coupon.id
    }).catch(() => {});

    // 获取跳转链接
    const response = await this.$http.get('/pyp/web/groupbuying/jumpUrl', {
      params: {
        couponId: coupon.id,
        isApp: isApp
      }
    });

    if (response.data.code !== 200) {
      vant.Toast(response.data.msg || "获取跳转链接失败");
      return;
    }

    const jumpUrl = response.data.jumpUrl;
    // ... 其他逻辑
  } catch (error) {
    console.error('跳转团购券失败:', error);
    vant.Toast("跳转失败，请稍后重试");
  }
}
```

**修改后：**
```javascript
jumpToGroupBuying(coupon) {
  vant.Toast("正在跳转...");

  // 记录点击
  this.$fly.post('/pyp/web/groupbuying/click', {
    couponId: coupon.id
  }).catch(() => {});

  // 检测设备环境
  const userAgent = navigator.userAgent.toLowerCase();
  const isApp = this.isInApp(coupon.platformType, userAgent);

  // 获取跳转链接
  this.$fly.get('/pyp/web/groupbuying/jumpUrl', {
    couponId: coupon.id,
    isApp: isApp
  }).then((res) => {
    if (res.code !== 200) {
      vant.Toast(res.msg || "获取跳转链接失败");
      return;
    }

    const jumpUrl = res.jumpUrl;
    // ... 其他逻辑
  }).catch((error) => {
    console.error('跳转团购券失败:', error);
    vant.Toast("跳转失败，请稍后重试");
  });
}
```

## 主要变化

### 1. 请求库变更
- **从**: `this.$http` 
- **到**: `this.$fly`

### 2. 参数传递方式
- **GET请求参数**:
  - 从: `{ params: { key: value } }`
  - 到: `{ key: value }`

### 3. 响应数据结构
- **响应数据访问**:
  - 从: `response.data.code`、`response.data.coupons`
  - 到: `res.code`、`res.coupons`

### 4. 异步处理方式
- **从**: `async/await` 语法
- **到**: `Promise.then().catch()` 语法

## 项目中的 $fly 使用规范

### GET 请求
```javascript
this.$fly.get('/api/endpoint', {
  param1: 'value1',
  param2: 'value2'
}).then((res) => {
  if (res.code === 200) {
    // 处理成功响应
    console.log(res.data);
  }
}).catch((error) => {
  // 处理错误
  console.error('请求失败:', error);
});
```

### POST 请求
```javascript
this.$fly.post('/api/endpoint', {
  field1: 'value1',
  field2: 'value2'
}).then((res) => {
  if (res.code === 200) {
    // 处理成功响应
    console.log(res.data);
  }
}).catch((error) => {
  // 处理错误
  console.error('请求失败:', error);
});
```

## 注意事项

### 1. 参数传递
- GET 请求的参数直接作为第二个参数传递，不需要包装在 `params` 对象中
- POST 请求的数据直接作为第二个参数传递

### 2. 响应数据
- 响应数据直接通过 `res` 访问，不需要 `res.data`
- 状态码通过 `res.code` 访问
- 业务数据直接通过 `res.字段名` 访问

### 3. 错误处理
- 使用 `.catch()` 方法处理请求错误
- 统计类请求可以忽略错误：`.catch(() => {})`

### 4. 异步处理
- 项目中统一使用 Promise 链式调用
- 避免使用 async/await 语法

## 验证方法

### 1. 浏览器开发者工具
- 打开 Network 面板
- 查看请求是否正常发送
- 检查请求参数和响应数据

### 2. 控制台日志
- 查看是否有请求相关的错误信息
- 确认响应数据结构是否正确

### 3. 功能测试
- 测试团购券列表是否正常加载
- 测试团购券点击跳转是否正常
- 测试错误情况的提示是否正确

## 总结

通过将 `this.$http` 请求方式修改为 `this.$fly`，解决了前端请求不支持的问题。主要变化包括：

1. 请求库从 `$http` 改为 `$fly`
2. GET 请求参数传递方式简化
3. 响应数据访问路径简化
4. 异步处理从 async/await 改为 Promise 链式调用

这些修改确保了团购券功能在前端项目中能够正常工作，符合项目的技术规范和编码标准。
