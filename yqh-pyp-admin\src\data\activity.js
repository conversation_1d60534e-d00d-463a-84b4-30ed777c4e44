// 签到方式
export const signModel = [
    {
        key: 0,
        value: "后台签到",
    },
    {
        key: 1,
        value: "微信扫码签到",
    },
];

// 签到状态
export const signType = [
    {
        key: 0,
        value: "未签到",
    },
    {
        key: 1,
        value: "已签到",
    },
    {
        key: 2,
        value: "已签退",
    },
];

// 报名状态
export const userStatus = [
    {
        key: 0,
        value: "未报名",
    },
    {
        key: 1,
        value: "已报名",
    },
    {
        key: 2,
        value: "已取消",
    },
    {
        key: 3,
        value: "退款中",
    },
    {
        key: 4,
        value: "退款成功",
    },
    {
        key: 5,
        value: "退款失败",
    },
];

export const needInvite = [
    {
        key: 0,
        value: "无需",
    },
    {
        key: 1,
        value: "统一",
    },
    {
        key: 2,
        value: "随机",
    },
    {
        key: 3,
        value: "统一(多个)",
    },
];
export const verifyStatus = [
    {
        key: 0,
        value: "未审核",
    },
    {
        key: 1,
        value: "审核通过",
    },
    {
        key: 2,
        value: "审核不通过",
    },
];
export const guestGoType = [
    {
        key: 0,
        value: "飞机",
    },
    {
        key: 1,
        value: "火车",
    },
    {
        key: 2,
        value: "其他",
    },
];
export const tripType = [
    {
        key: 0,
        value: "会务组出票",
    },
    {
        key: 1,
        value: "自行出票",
    },
];
export const activityRole = [
    {
        key: 0,
        value: "主控",
    },
    {
        key: 1,
        value: "学员管理",
    },
    {
        key: 2,
        value: "专家管理",
    },
    {
        key: 3,
        value: "技术",
    },
    {
        key: 4,
        value: "业务员",
    },
    {
        key: 5,
        value: "兼职",
    },
];
export const settleType = [
    {
        key: 0,
        value: "项目收支",
    },
    {
        key: 1,
        value: "服务费",
    },
];
export const ticketType = [
    {
        key: 0,
        value: "大会出票",
    },
    {
        key: 1,
        value: "自行购买，凭票大会报销",
    },
];
export const isBuy = [
    {
        key: 0,
        value: "未出票",
    },
    {
        key: 1,
        value: "已出票",
    },
];
export const tripPlaneStatus = [
    {
        key: 0,
        value: "已提交",
    },
    {
        key: 1,
        value: "已预订",
    },
    {
        key: 2,
        value: "已支付,待出票",
    },
    {
        key: 4,
        value: "已出票",
    },
    {
        key: 5,
        value: "不能出票待退款",
    },
    {
        key: 7,
        value: "不能出票已退款",
    },
    {
        key: 13,
        value: "(退票)审核不通过交易结束",
    },
    {
        key: 15,
        value: "(退票)申请中",
    },
    {
        key: 16,
        value: "(退票)已退款交易结束",
    },
    {
        key: 22,
        value: "(改签)审核不通过交易结束",
    },
    {
        key: 23,
        value: "(改签)需补款待支付",
    },
    {
        key: 26,
        value: "(改签)无法改签已退款交易结束",
    },
    {
        key: 27,
        value: "(改签)改签成功交易结束",
    },
    {
        key: 28,
        value: "(改签)改签已取消交易结束",
    },
    {
        key: 29,
        value: "(改签)改签处理中",
    },
    {
        key: 99,
        value: "已取消",
    },
];
export const tripTrainStatus = [
    {
        key: 0,
        value: "已提交",
    },
    {
        key: 1,
        value: "已预订",
    },
    {
        key: 2,
        value: "占位成功",
    },
    {
        key: 4,
        value: "占位失败",
    },
    {
        key: 5,
        value: "已取消",
    },
    {
        key: 8,
        value: "出票成功",
    },
    {
        key: 9,
        value: "出票失败",
    },
    {
        key: 31,
        value: "改签占位成功",
    },
    {
        key: 32,
        value: "改签占位失败",
    },
    {
        key: 34,
        value: "改签确认出票成功",
    },
    {
        key: 35,
        value: "改签确认出票失败",
    },
    {
        key: 37,
        value: "改签已取消",
    },
    // =21,=, = 
    {
        key: 21,
        value: "退票成功",
    },
    {
        key: 22,
        value: "退票失败",
    },
    {
        key: 23,
        value: "已退票未退款",
    },
];

// 0-专家信息，1-专家任务确认，2-专家行程，3-专家接送，4-专家劳务费信息，5-专家劳务费签字，6-其他，7-活动即将过期，8-活动已过期，9-活动续费成功
export const activityNotifyType = [
    {
        key: 0,
        value: "专家信息",
    },
    {
        key: 1,
        value: "专家任务确认",
    },
    {
        key: 2,
        value: "专家行程",
    },
    {
        key: 3,
        value: "专家接送",
    },
    {
        key: 4,
        value: "专家劳务费信息",
    },
    {
        key: 5,
        value: "专家劳务费签字",
    },
    {
        key: 6,
        value: "其他",
    },
    {
        key: 7,
        value: "活动即将过期",
    },
    {
        key: 8,
        value: "活动已过期",
    },
    {
        key: 9,
        value: "活动续费成功",
    },
];

export const isRead = [
    {
        key: 0,
        value: "未读",
    },
    {
        key: 1,
        value: "已读",
    },
];
