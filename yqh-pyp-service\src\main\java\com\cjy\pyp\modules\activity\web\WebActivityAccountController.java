package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargePackageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.vo.RechargeOrderVo;
import com.cjy.pyp.modules.activity.vo.CreateActivityPackageOrderVo;
import com.cjy.pyp.modules.sys.controller.AbstractController;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Web端账户页面控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("web/account")
public class WebActivityAccountController extends AbstractController {
    
    @Autowired
    private ActivityService activityService;
    
    @Autowired
    private ActivityRechargePackageService activityRechargePackageService;
    
    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService activityRechargeUsageService;

    @Autowired
    private com.cjy.pyp.modules.channel.service.ChannelRefundPermissionService channelRefundPermissionService;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取账户基础信息
     */
    @GetMapping("/getOrderInfo")
    public R getOrderInfo(@RequestParam("orderId") Long orderId) {
        // 直接从activity表获取allCount和useCount
        ActivityRechargeRecordEntity orderInfo = activityRechargeRecordService.getById(orderId);
        if (orderInfo == null) {
            return R.error("订单不存在");
        }
        ActivityRechargePackageEntity packageInfo = activityRechargePackageService.getById(orderInfo.getPackageId());
        Map<String,Object> result = new HashMap<>();
        result.put("orderInfo", orderInfo);
        result.put("packageInfo", packageInfo);
        return R.ok(result);
    }
    @GetMapping("/info")
    public R getAccountInfo(@RequestParam("activityId") Long activityId) {
        // 获取活动信息
        ActivityEntity activity = activityService.getById(activityId);
        if (activity == null) {
            return R.error("活动不存在");
        }
        if(!getUserId().equals(1L)) {

        // 验证活动是否属于当前用户
        if (StringUtils.isNotEmpty(activity.getMobile()) &&
        !activity.getMobile().equals(getUser().getMobile())) {
            return R.error("无权查看此活动信息");
        }
        }

        // 获取已支付且未过期订单的总次数
        Integer paidTotalCount = activityRechargeRecordService.getPaidTotalCountByActivity(activityId);

        // 获取有效的已使用次数（只统计来自有效充值记录的使用次数）
        Integer useCount = activityRechargeUsageService.getValidUsedCountByActivity(activityId);

        // 计算剩余次数
        Integer availableCount = paidTotalCount - useCount;

        Map<String, Object> accountInfo = new HashMap<>();
        accountInfo.put("activityId", activityId);
        accountInfo.put("activityName", activity.getName());
        accountInfo.put("allCount", paidTotalCount);
        accountInfo.put("useCount", useCount);
        accountInfo.put("availableCount", availableCount);
        accountInfo.put("userId", getUserId());

        return R.ok().put("accountInfo", accountInfo);
    }

    /**
     * 获取充值套餐列表（用于账户页面）
     */
    @GetMapping("/packages")
    public R getPackages(@CookieValue String appid, @RequestParam(value = "packageType", required = false) Integer packageType) {
        List<ActivityRechargePackageEntity> packages;
        
        if (packageType != null) {
            // 根据类型获取套餐
            packages = activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, packageType);
        } else {
            // 获取所有启用的套餐
            packages = activityRechargePackageService.getEnabledPackagesByAppid(appid);
        }
        
        return R.ok().put("packages", packages);
    }

    /**
     * 获取推荐套餐列表
     */
    @GetMapping("/packages/recommended")
    public R getRecommendedPackages(@CookieValue String appid) {
        List<ActivityRechargePackageEntity> packages = activityRechargePackageService.getRecommendedPackagesByAppid(appid);
        return R.ok().put("packages", packages);
    }

    /**
     * 获取热门套餐列表
     */
    @GetMapping("/packages/hot")
    public R getHotPackages(@CookieValue String appid) {
        List<ActivityRechargePackageEntity> packages = activityRechargePackageService.getHotPackagesByAppid(appid);
        return R.ok().put("packages", packages);
    }

    /**
     * 获取充值次数套餐列表
     */
    @GetMapping("/packages/rechargeCount")
    public R getRechargeCountPackages(@CookieValue String appid) {
        List<ActivityRechargePackageEntity> packages = activityRechargePackageService.getRechargeCountPackagesByAppid(appid);
        return R.ok().put("packages", packages);
    }

    /**
     * 获取创建活动套餐列表
     */
    @GetMapping("/packages/createActivity")
    public R getCreateActivityPackages(@CookieValue String appid) {
        List<ActivityRechargePackageEntity> packages = activityRechargePackageService.getCreateActivityPackagesByAppid(appid);
        return R.ok().put("packages", packages);
    }

    /**
     * 创建充值订单
     */
    @SysLog("用户创建充值订单")
    @PostMapping("/recharge")
    @Transactional(rollbackFor = Exception.class)
    public R createRechargeOrder(@Valid @RequestBody RechargeOrderVo rechargeOrderVo, @CookieValue String appid) {
        // 原子性操作验证和删除令牌
        // Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
        //         Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), rechargeOrderVo.getRepeatToken());
        // if (result == 0L) {
        //     throw new RRException("不能重复提交");
        // }
        
        return activityRechargeRecordService.createRechargeOrder(rechargeOrderVo, getUserId());
    }

    /**
     * 创建活动套餐订单
     */
    @SysLog("用户创建活动套餐订单")
    @PostMapping("/createActivityPackage")
    @Transactional(rollbackFor = Exception.class)
    public R createActivityPackageOrder(@Valid @RequestBody CreateActivityPackageOrderVo orderVo, @CookieValue String appid) {
        // 原子性操作验证和删除令牌
        // Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
        //         Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), orderVo.getRepeatToken());
        // if (result == 0L) {
        //     throw new RRException("不能重复提交");
        // }

        return activityRechargeRecordService.createActivityPackageOrder(orderVo, getUserId());
    }

    /**
     * 获取用户充值记录
     */
    @GetMapping("/records")
    public R getRechargeRecords(@RequestParam("activityId") Long activityId,
                               @RequestParam(value = "page", defaultValue = "1") Integer page,
                               @RequestParam(value = "limit", defaultValue = "10") Integer limit,
                               @RequestParam(value = "status", required = false) Integer status,
                               @RequestParam(value = "statusList", required = false) String statusList) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", getUserId());
        params.put("activityId", activityId);
        params.put("page", String.valueOf(page));
        params.put("limit", String.valueOf(limit));

        // 添加状态筛选 - 优先使用statusList，如果没有则使用status
        if (StringUtils.isNotEmpty(statusList)) {
            // 将字符串分割为整数列表
            String[] statusArray = statusList.split(",");
            List<Integer> statusIntList = new ArrayList<>();
            for (String statusStr : statusArray) {
                try {
                    statusIntList.add(Integer.parseInt(statusStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的数字
                }
            }
            if (!statusIntList.isEmpty()) {
                params.put("statusList", statusIntList);
            }
        } else if (status != null) {
            params.put("status", status);
        }

        List<ActivityRechargeRecordEntity> list = activityRechargeRecordService.queryPage(params);
        return R.okList(list);
    }

    /**
     * 检查退款权限
     */
    @GetMapping("/checkRefundPermission")
    public R checkRefundPermission(@RequestParam("orderId") Long orderId) {
        try {
            R result = channelRefundPermissionService.checkRefundPermissionWithDetails(orderId);
            return result;
        } catch (Exception e) {
            return R.error("检查退款权限失败：" + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @SysLog("用户申请退款")
    @PostMapping("/refund")
    @Transactional(rollbackFor = Exception.class)
    public R applyRefund(@RequestBody Map<String, Object> params) {
        Long rechargeRecordId = Long.valueOf(params.get("rechargeRecordId").toString());
        String orderSn = (String) params.get("orderSn");

        return activityRechargeRecordService.applyRefund(rechargeRecordId, orderSn, getUserId());
    }

    /**
     * 获取账户页面所有数据（一次性获取）
     */
    @GetMapping("/dashboard")
    public R getAccountDashboard(@RequestParam("activityId") Long activityId, @CookieValue String appid) {
        // 获取账户基础信息
        R accountInfoResult = getAccountInfo(activityId);
        if (!accountInfoResult.get("code").equals(200)) {
            return accountInfoResult;
        }
        
        // 获取推荐套餐
        List<ActivityRechargePackageEntity> recommendedPackages = activityRechargePackageService.getRecommendedPackagesByAppid(appid);
        
        // 获取热门套餐
        List<ActivityRechargePackageEntity> hotPackages = activityRechargePackageService.getHotPackagesByAppid(appid);
        
        // 获取充值次数套餐
        List<ActivityRechargePackageEntity> rechargeCountPackages = activityRechargePackageService.getRechargeCountPackagesByAppid(appid);
        
        // 获取创建活动套餐
        List<ActivityRechargePackageEntity> createActivityPackages = activityRechargePackageService.getCreateActivityPackagesByAppid(appid);
        
        Map<String, Object> dashboard = new HashMap<>();
        dashboard.put("accountInfo", accountInfoResult.get("accountInfo"));
        dashboard.put("recommendedPackages", recommendedPackages);
        dashboard.put("hotPackages", hotPackages);
        dashboard.put("rechargeCountPackages", rechargeCountPackages);
        dashboard.put("createActivityPackages", createActivityPackages);
        
        return R.ok().put("dashboard", dashboard);
    }
}
