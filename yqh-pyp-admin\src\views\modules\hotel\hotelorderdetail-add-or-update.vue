<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议酒店房型id" prop="hotelActivityRoomId">
      <el-input v-model="dataForm.hotelActivityRoomId" placeholder="会议酒店房型id"></el-input>
    </el-form-item>
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="酒店id" prop="hotelId">
      <el-input v-model="dataForm.hotelId" placeholder="酒店id"></el-input>
    </el-form-item>
    <el-form-item label="会议酒店id" prop="hotelActivityId">
      <el-input v-model="dataForm.hotelActivityId" placeholder="会议酒店id"></el-input>
    </el-form-item>
    <el-form-item label="酒店订单表id" prop="hotelOrderId">
      <el-input v-model="dataForm.hotelOrderId" placeholder="酒店订单表id"></el-input>
    </el-form-item>
    <el-form-item label="房间类型" prop="roomType">
      <el-input v-model="dataForm.roomType" placeholder="房间类型"></el-input>
    </el-form-item>
    <el-form-item label="数量" prop="number">
      <el-input v-model="dataForm.number" placeholder="数量"></el-input>
    </el-form-item>
    <el-form-item label="入住日期" prop="inDate">
      <el-input v-model="dataForm.inDate" placeholder="入住日期"></el-input>
    </el-form-item>
    <el-form-item label="退房日期" prop="outDate">
      <el-input v-model="dataForm.outDate" placeholder="退房日期"></el-input>
    </el-form-item>
    <el-form-item label="总天数" prop="dayNumber">
      <el-input v-model="dataForm.dayNumber" placeholder="总天数"></el-input>
    </el-form-item>
    <el-form-item label="价格" prop="price">
      <el-input v-model="dataForm.price" placeholder="价格"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          hotelActivityRoomId: '',
          activityId: '',
          hotelId: '',
          hotelActivityId: '',
          hotelOrderId: '',
          roomType: '',
          number: '',
          inDate: '',
          outDate: '',
          dayNumber: '',
          price: '',
        },
        dataRule: {
          hotelActivityRoomId: [
            { required: true, message: '会议酒店房型id不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          hotelId: [
            { required: true, message: '酒店id不能为空', trigger: 'blur' }
          ],
          hotelActivityId: [
            { required: true, message: '会议酒店id不能为空', trigger: 'blur' }
          ],
          hotelOrderId: [
            { required: true, message: '酒店订单表id不能为空', trigger: 'blur' }
          ],
          roomType: [
            { required: true, message: '房间类型：0-整间，1-男床位，2-女床位不能为空', trigger: 'blur' }
          ],
          number: [
            { required: true, message: '数量不能为空', trigger: 'blur' }
          ],
          inDate: [
            { required: true, message: '入住日期不能为空', trigger: 'blur' }
          ],
          outDate: [
            { required: true, message: '退房日期不能为空', trigger: 'blur' }
          ],
          dayNumber: [
            { required: true, message: '总天数不能为空', trigger: 'blur' }
          ],
          price: [
            { required: true, message: '价格不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelorderdetail/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.hotelActivityRoomId = data.hotelOrderDetail.hotelActivityRoomId
                this.dataForm.activityId = data.hotelOrderDetail.activityId
                this.dataForm.hotelId = data.hotelOrderDetail.hotelId
                this.dataForm.hotelActivityId = data.hotelOrderDetail.hotelActivityId
                this.dataForm.hotelOrderId = data.hotelOrderDetail.hotelOrderId
                this.dataForm.roomType = data.hotelOrderDetail.roomType
                this.dataForm.number = data.hotelOrderDetail.number
                this.dataForm.inDate = data.hotelOrderDetail.inDate
                this.dataForm.outDate = data.hotelOrderDetail.outDate
                this.dataForm.dayNumber = data.hotelOrderDetail.dayNumber
                this.dataForm.price = data.hotelOrderDetail.price
                this.dataForm.createOn = data.hotelOrderDetail.createOn
                this.dataForm.createBy = data.hotelOrderDetail.createBy
                this.dataForm.updateOn = data.hotelOrderDetail.updateOn
                this.dataForm.updateBy = data.hotelOrderDetail.updateBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/hotel/hotelorderdetail/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'hotelActivityRoomId': this.dataForm.hotelActivityRoomId,
                'activityId': this.dataForm.activityId,
                'hotelId': this.dataForm.hotelId,
                'hotelActivityId': this.dataForm.hotelActivityId,
                'hotelOrderId': this.dataForm.hotelOrderId,
                'roomType': this.dataForm.roomType,
                'number': this.dataForm.number,
                'inDate': this.dataForm.inDate,
                'outDate': this.dataForm.outDate,
                'dayNumber': this.dataForm.dayNumber,
                'price': this.dataForm.price,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
