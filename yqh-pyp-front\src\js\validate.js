/**
 * 邮箱
 * @param {*} s
 */
export function isEmail(s) {
    return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile(s) {
    return /^1[0-9]{10}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone(s) {
    return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
    return /^http[s]?:\/\/.*/.test(s)
}

/**
 *  最多保留两位小数
 * @param s
 * @returns {boolean}
 */
export function isTwoDouble(s) {
    return /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(s)
}

/**
 * 身份证号
 * @param {*} s
 */
export function isIdCard(s) {
    // 身份证号校验
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(s)
}

/**
 * 数字校验
 * @param {*} s
 */
export function isNumber(s) {
    return /^([1-9][0-9]*)+(.[0-9]{1,2})?$/.test(s)
}


/**
 * 正整数校验
 * @param {*} s
 */
export function isInteger(s) {
    return /^[1-9]\d*$/.test(s)
}

/**
 * 整数（正负以及0）校验
 * @param {*} s
 */
export function isAllInteger(s) {
    return /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/.test(s)
}

/**
 * 至少包含数字跟字母，可以有字符，长度6-20
 * @param {*} s
 */
export function isPassword(s) {
    return /(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{6,20}$/.test(s)
}


/**
 * 考试成绩校验  一位小数
 * @param {*} s
 * @Author:Senc
 */
export function isGrades(s) {
    return /^(100|[1-9]?[0-9](\.[1-9])?)$/.test(s)
}

/**
 * 1位小数校验
 * @param s
 * @returns {*|boolean}
 */
export function isDecimalByOne(s) {
    return /^([\+ \-]?(([1-9]\d*)|(0)))([.]\d{0,1})?$/.test(s)
}

/**
 * 2位小数校验
 * @param s
 * @returns {*|boolean}
 */
export function isDecimalByTwo(s) {
    return /^([\+ \-]?(([1-9]\d*)|(0)))([.]\d{0,2})?$/.test(s)
}

export function isCarNumber(value) {
    return /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1})|(([0-9]{5}[DF])|([DF][A-HJ-NP-Z0-9][0-9]{4})))$/.test(value)
}

/**
 * 判断是否为移动设备
 * @returns {*|boolean}
 */
export function isMobilePhone() {
    // 获取访问链接
    let href = window.location.href;
    if (href.includes("pc=true")) {
        return false;
    }
    if (href.includes("pc=false")) {
        return true;
    }
    //获取访问的user-agent
    let ua = window.navigator.userAgent.toLowerCase();
    //判断user-agent
    // isWX = /MicroMessenger/i.test(ua); //微信端
    // isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族
    // isAndroid = /(android|nexus)/i.test(ua); //安卓家族
    // isWindows = /(Windows Phone|windows[\s+]phone)/i.test(ua); //微软家族
    return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|MicroMessenger)/i.test(ua)

}
export function isWeixin() {
    let ua = navigator.userAgent || window.navigator.userAgent;
    return /micromessenger/ig.test(ua);
}