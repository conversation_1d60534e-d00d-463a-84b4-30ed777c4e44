<template>
  <div>

    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
        label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="类型" prop="settleType">
              <el-select v-model="dataForm.settleType" placeholder="类型" filterable @change="settleTypeChange">
                <el-option v-for="item in settleType" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item></el-col>
          <el-col :span="24">
            <el-form-item label="摘要" prop="name">
              <el-input v-model="dataForm.name" placeholder="摘要"></el-input>
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="收款or付款" prop="type">
              <el-select v-model="dataForm.type" placeholder="收款or付款" filterable :disabled="dataForm.settleType == 1">
                <el-option v-for="item in contractTypeSimple" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.type == 0">
            <el-form-item label="供应商" prop="supplierId">
              <div style="display: flex">
                <el-select v-model="dataForm.supplierId" filterable>
                  <el-option v-for="item in suppliers" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-button type="text" @click="supplierAddHandle">快速新增</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-else>
            <el-form-item label="客户" prop="clientId">
              <div style="display: flex">
                <el-select v-model="dataForm.clientId" filterable>
                  <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-button type="text" @click="clientAddHandle">快速新增</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收/付款金额" prop="price">
              <el-input v-model="dataForm.price" placeholder="收/付款金额" @change="priceChange"></el-input>
            </el-form-item>
          </el-col><el-col :span="12" v-if="dataForm.type == 0">
            <el-form-item label="客户付" prop="clientPrice">
              <el-input v-model="dataForm.clientPrice" placeholder="客户付"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.type == 0 ? '预计支付时间' : '预计收款时间'" prop="payTime">
              <el-date-picker v-model="dataForm.payTime" type="datetime" :placeholder="dataForm.type == 0 ? '预计支付时间' : '预计收款时间'"
                value-format="yyyy/MM/dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item :label="dataForm.type == 0 ? '是否开票' : '是否开票'" prop="isInvoice">
            <el-select v-model="dataForm.isInvoice" :placeholder="dataForm.type == 0 ? '是否开票' : '是否开票'" filterable>
              <el-option v-for="item in isInvoice" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dataForm.isInvoice == 1">
          <el-form-item :label="dataForm.type == 0 ? '收票类型' : '开票类型'" prop="invoiceType">
            <el-select v-model="dataForm.invoiceType" :placeholder="dataForm.type == 0 ? '收票类型' : '开票类型'" filterable>
              <el-option v-for="item in contractInvoiceType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12"  v-if="dataForm.isInvoice == 1">
          <el-form-item :label="dataForm.type == 0 ? '发票类型' : '发票类型'" prop="invoiceBillType">
            <el-select v-model="dataForm.invoiceBillType" :placeholder="dataForm.type == 0 ? '发票类型' : '发票类型'" filterable>
              <el-option v-for="item in contractInvoiceBillType" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
    <supplieradd v-if="supplieraddVisible" ref="supplieradd" @refreshDataList="findSupplier"></supplieradd>
    <clientadd v-if="clientaddVisible" ref="clientadd" @refreshDataList="findClient"></clientadd>
  </div>
</template>

<script>
import supplieradd from '@/views/modules/supplier/supplier-add-or-update'
import clientadd from '@/views/modules/client/client-add-or-update'
import { format } from "@/utils/date.js";
import {
  contractPriceStatus,
  contractTypeSimple,
  isInvoice,
  contractInvoiceType,
  contractInvoiceStatus,
  contractInvoiceSellBuy,
  contractInvoiceBillType,
} from "@/data/price";
import {
  settleType,
} from "@/data/activity";
export default {
  components: {
    supplieradd,
    clientadd,
  },
  data() {
    return {
      supplieraddVisible: false,
      clientaddVisible: false,
      client: [],
      suppliers: [],
      priceConfig: [],
      settleType,
      contractPriceStatus,
      contractTypeSimple,
      isInvoice,
      contractInvoiceType,
      contractInvoiceStatus,
      contractInvoiceSellBuy,
      contractInvoiceBillType,
      visible: false,
      dataForm: {
        repeatToken: "",
        id: 0,
        name: "",
        price: 0,
        type: 0,
        payTime: "",
        clientPrice: 0,
        settleType: 0,
        activityId: "",
        priceStatus: "",
        arrivePrice: "",
        invoiceStatus: "",
        invoice: "",
        arriveInvoice: "",
        remarks: "",
        isInvoice: 0,
        invoiceType: 0,
        invoiceBillType: 0,
        refundPrice: "",
        redInvoice: "",
        priceConfigId: "",
        clientId: "",
        supplierId: "",
      },
      dataRule: {
        name: [
          {
            required: true,
            message: "名称不能为空",
            trigger: "blur",
          },
        ],
        // supplierId: [
        //   {
        //     required: true,
        //     message: "供应商不能为空",
        //     trigger: "blur",
        //   },
        // ],
        // clientId: [
        //   {
        //     required: true,
        //     message: "客户不能为空",
        //     trigger: "blur",
        //   },
        // ],
        priceConfigId: [
          {
            required: true,
            message: "科目不能为空",
            trigger: "blur",
          },
        ],
        settleType: [
          {
            required: true,
            message: "科目不能为空",
            trigger: "blur",
          },
        ],
        price: [
          {
            required: true,
            message: "到款金额不能为空",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "收款or付款不能为空",
            trigger: "blur",
          },
        ],
        // payTime: [
        //   {
        //     required: true,
        //     message: "预计支付时间不能为空",
        //     trigger: "blur",
        //   },
        // ],
        clientPrice: [
          {
            required: true,
            message: "客户付不能为空",
            trigger: "blur",
          },
        ],
        activityId: [
          {
            required: true,
            message: "活动ID不能为空",
            trigger: "blur",
          },
        ],
        priceStatus: [
          {
            required: true,
            message: "收/付款状态不能为空",
            trigger: "blur",
          },
        ],
        arrivePrice: [
          {
            required: true,
            message: "到款金额不能为空",
            trigger: "blur",
          },
        ],
        invoiceStatus: [
          {
            required: true,
            message: "开票状态不能为空",
            trigger: "blur",
          },
        ],
        invoice: [
          {
            required: true,
            message: "开票金额不能为空",
            trigger: "blur",
          },
        ],
        arriveInvoice: [
          {
            required: true,
            message: "已开票金额不能为空",
            trigger: "blur",
          },
        ],
        isInvoice: [
          {
            required: true,
            message: "是否发票不能为空",
            trigger: "blur",
          },
        ],
        invoiceType: [
          {
            required: true,
            message: "发票类型不能为空",
            trigger: "blur",
          },
        ],
        refundPrice: [
          {
            required: true,
            message: "退款金额不能为空",
            trigger: "blur",
          },
        ],
        redInvoice: [
          {
            required: true,
            message: "已红冲金额不能为空",
            trigger: "blur",
          },
        ],
        invoiceBillType: [
          {
            required: true,
            message: "开票类型不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    settleTypeChange(v) {
      this.dataForm.type = v;
      if (v == 0) {
        this.dataForm.name = '';
      } else {
        this.dataForm.name = '服务费';
      }
    },
    supplierAddHandle() {
      this.supplieraddVisible = true
      this.$nextTick(() => {
        this.$refs.supplieradd.init()
      })
    },
    clientAddHandle() {
      this.clientaddVisible = true
      this.$nextTick(() => {
        this.$refs.clientadd.init()
      })
    },
    init(id, activityId) {
      this.getToken();
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.dataForm.activityId = activityId;
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activitysettle/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.dataForm.name = data.activitySettle.name;
              this.dataForm.price = data.activitySettle.price;
              this.dataForm.type = data.activitySettle.type;
              this.dataForm.payTime = data.activitySettle.payTime;
              this.dataForm.clientPrice =
                data.activitySettle.clientPrice;
              this.dataForm.activityId =
                data.activitySettle.activityId;
              this.dataForm.priceStatus =
                data.activitySettle.priceStatus;
              this.dataForm.arrivePrice =
                data.activitySettle.arrivePrice;
              this.dataForm.invoiceStatus =
                data.activitySettle.invoiceStatus;
              this.dataForm.invoice = data.activitySettle.invoice;
              this.dataForm.arriveInvoice =
                data.activitySettle.arriveInvoice;
              this.dataForm.remarks = data.activitySettle.remarks;
              this.dataForm.isInvoice =
                data.activitySettle.isInvoice;
              this.dataForm.invoiceType =
                data.activitySettle.invoiceType;
              this.dataForm.refundPrice =
                data.activitySettle.refundPrice;
              this.dataForm.redInvoice =
                data.activitySettle.redInvoice;
              this.dataForm.invoiceBillType =
                data.activitySettle.invoiceBillType;
              this.dataForm.priceConfigId =
                data.activitySettle.priceConfigId;
              this.dataForm.settleType =
                data.activitySettle.settleType;
              this.dataForm.clientId =
                data.activitySettle.clientId;
              this.dataForm.supplierId =
                data.activitySettle.supplierId;
            }
          });
        } else {

          this.dataForm.payTime = format(new Date(), "yyyy/MM/dd hh:mm:ss");
        }
      });
      this.getPriceConfig();
      this.findClient();
      this.findSupplier();
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataForm.repeatToken = data.result;
        }
      });
    },
    findClient(v) {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
            if (v) {
              this.dataForm.clientId = v;
              this.getToken();
            }
          }
        })
    },
    findSupplier(v) {
      this.$http({
        url: this.$http.adornUrl(`/supplier/supplier/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.suppliers = data.result;
          if (v) {
            this.dataForm.supplierId = v;
            this.getToken();
          }
        }
      })
    },
    getPriceConfig() {
      this.$http({
        url: this.$http.adornUrl("/price/priceconfig/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceConfig = data.result;
          }
        })
    },
    priceChange(v) {
      this.dataForm.invoice = v;
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activity/activitysettle/${!this.dataForm.id ? "save" : "update"
              }`
            ),
            method: "post",
            data: this.$http.adornData({
              repeatToken: this.dataForm.repeatToken,
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              price: this.dataForm.price,
              type: this.dataForm.type,
              payTime: this.dataForm.payTime,
              clientPrice: this.dataForm.clientPrice,
              appid: this.$cookie.get("appid"),

              activityId: this.dataForm.activityId,
              priceStatus: this.dataForm.priceStatus,
              arrivePrice: this.dataForm.arrivePrice,
              invoiceStatus: this.dataForm.invoiceStatus,
              invoice: this.dataForm.invoice,
              arriveInvoice: this.dataForm.arriveInvoice,
              remarks: this.dataForm.remarks,
              isInvoice: this.dataForm.isInvoice,
              invoiceType: this.dataForm.invoiceType,
              refundPrice: this.dataForm.refundPrice,
              redInvoice: this.dataForm.redInvoice,
              invoiceBillType: this.dataForm.invoiceBillType,
              priceConfigId: this.dataForm.priceConfigId,
              settleType: this.dataForm.settleType,
              clientId: this.dataForm.clientId,
              supplierId: this.dataForm.supplierId,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
              if (data.msg != "不能重复提交") {
                this.getToken();
              }
            }
          });
        }
      });
    },
  },
};
</script>
