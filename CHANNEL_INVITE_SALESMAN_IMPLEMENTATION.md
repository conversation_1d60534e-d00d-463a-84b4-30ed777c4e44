# 渠道邀请业务员功能实现

## 概述

在原有的业务员邀请子业务员功能基础上，新增了渠道邀请业务员的逻辑。现在支持两种邀请方式：
1. 业务员邀请子业务员（原有功能）
2. 渠道邀请业务员（新增功能）

## 前端修改

### 1. 业务员注册页面改造 (`yqh-pyp-front/src/pages/salesman/register.vue`)

#### 主要修改：
- **数据结构扩展**：
  - 新增 `channelId` 字段用于存储渠道ID
  - 新增 `channelInfo` 字段用于存储渠道信息
  - 新增 `inviteType` 字段区分邀请类型（'salesman' 或 'channel'）

- **邀请信息显示**：
  - 根据邀请类型显示不同的邀请信息
  - 业务员邀请：显示邀请人姓名、手机号
  - 渠道邀请：显示渠道名称、联系人、联系电话

- **参数处理逻辑**：
  - 支持 `inviterId` 参数（业务员邀请）
  - 支持 `channelId` 参数（渠道邀请）
  - 根据参数自动判断邀请类型

- **信息加载**：
  - 业务员邀请：调用 `/pyp/web/salesman/info/{id}` 获取业务员信息
  - 渠道邀请：调用 `/pyp/web/channel/info/{id}` 获取渠道信息

- **注册提交**：
  - 业务员邀请：调用 `/pyp/web/salesman/processInvite` 接口
  - 渠道邀请：调用 `/pyp/web/salesman/processChannelInvite` 接口

### 2. 测试页面 (`yqh-pyp-front/src/pages/test/channel-invite.vue`)

创建了一个测试页面，提供快速测试两种邀请方式的入口：
- 测试渠道邀请（channelId=1）
- 测试业务员邀请（inviterId=1）

### 3. 路由配置 (`yqh-pyp-front/src/router.js`)

添加了测试页面的路由配置：
```javascript
{
    path: '/test/channel-invite',
    name: 'testChannelInvite',
    component: () => import('@/pages/test/channel-invite'),
    meta: { title: '渠道邀请测试' }
}
```

## 后端修改

### 1. 渠道Web接口 (`yqh-pyp-service/src/main/java/com/cjy/pyp/modules/channel/web/WebChannelController.java`)

新创建的渠道Web接口控制器，提供：
- `GET /pyp/web/channel/info/{id}` - 获取渠道信息接口
- 验证渠道状态（必须为启用状态）
- 返回渠道详细信息

### 2. 业务员Web接口扩展 (`yqh-pyp-service/src/main/java/com/cjy/pyp/modules/salesman/web/WebSalesmanController.java`)

#### 新增接口：
- `POST /pyp/web/salesman/processChannelInvite` - 处理渠道邀请注册

#### 功能特点：
- 验证渠道有效性（存在、启用、应用ID匹配）
- 检查手机号唯一性
- 创建业务员时设置：
  - `parentId` = null（渠道邀请的业务员没有上级业务员）
  - `channelId` = 邀请渠道ID
  - `level` = 1（渠道邀请的业务员为一级业务员）
- 自动生成业务员编号

#### 依赖注入：
- 添加了 `ChannelService` 的依赖注入
- 添加了必要的导入语句

## 使用方式

### 1. 渠道邀请链接格式：
```
https://domain.com/#/salesman/register?channelId={渠道ID}
```

### 2. 业务员邀请链接格式（原有）：
```
https://domain.com/#/salesman/register?inviterId={业务员ID}
```

### 3. 测试页面访问：
```
http://localhost:8023/#/test/channel-invite
```

## 数据库关系

- 渠道邀请的业务员：
  - `channel_id` = 邀请渠道的ID
  - `parent_id` = null（没有上级业务员）
  - `level` = 1（一级业务员）

- 业务员邀请的子业务员：
  - `channel_id` = 继承邀请人的渠道ID
  - `parent_id` = 邀请人的业务员ID
  - `level` = 邀请人的level + 1

## 注意事项

1. 渠道必须处于启用状态才能邀请业务员
2. 手机号在同一应用下必须唯一
3. 渠道邀请的业务员直接归属于该渠道，没有上级业务员关系
4. 前端会根据URL参数自动判断邀请类型，无需额外配置

## 测试建议

1. 确保数据库中存在有效的渠道记录（ID=1，状态=1）
2. 测试渠道邀请流程：访问测试页面 → 点击"测试渠道邀请" → 填写注册信息 → 提交
3. 测试业务员邀请流程：访问测试页面 → 点击"测试业务员邀请" → 填写注册信息 → 提交
4. 验证数据库中业务员记录的 `channel_id`、`parent_id`、`level` 字段是否正确设置
