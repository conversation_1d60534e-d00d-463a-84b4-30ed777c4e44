<template>
    <div class="agreement-container">
        <!-- 顶部导航 -->
        <div class="header">
            <van-nav-bar
                title="用户协议"
                left-text="返回"
                left-arrow
                @click-left="$router.go(-1)"
                class="custom-nav-bar"
            />
        </div>

        <!-- 协议内容 -->
        <div class="content">
            <div class="agreement-content">
                <h1>易企化AI爆店码用户协议</h1>
                
                <div class="update-time">
                    <p>更新时间：2024年12月30日</p>
                    <p>生效时间：2024年12月30日</p>
                </div>

                <div class="section">
                    <h2>1. 协议的范围</h2>
                    <p>1.1 本协议是您与易企化AI爆店码平台（以下简称"本平台"）之间关于您使用本平台服务所订立的协议。</p>
                    <p>1.2 本协议描述本平台与用户之间关于"易企化AI爆店码"软件服务（以下简称"服务"）的权利义务。</p>
                    <p>1.3 "用户"是指注册、登录、使用本服务的个人或组织。</p>
                </div>

                <div class="section">
                    <h2>2. 服务内容</h2>
                    <p>2.1 本平台为用户提供AI助力商家营销获客的数字化解决方案。</p>
                    <p>2.2 服务包括但不限于：</p>
                    <ul>
                        <li>活动管理和创建</li>
                        <li>视频、图片、文本等营销素材生成</li>
                        <li>AI智能营销工具</li>
                        <li>数据分析和统计</li>
                        <li>其他相关增值服务</li>
                    </ul>
                </div>

                <div class="section">
                    <h2>3. 账户注册</h2>
                    <p>3.1 用户需要注册账户才能使用本服务。</p>
                    <p>3.2 用户应提供真实、准确、完整的个人信息。</p>
                    <p>3.3 用户有义务及时更新注册信息，确保其真实有效。</p>
                    <p>3.4 用户应妥善保管账户信息，对账户下的所有活动承担责任。</p>
                </div>

                <div class="section">
                    <h2>4. 用户行为规范</h2>
                    <p>4.1 用户在使用服务时应遵守法律法规，不得：</p>
                    <ul>
                        <li>发布违法、有害、威胁、辱骂、骚扰、侵权的内容</li>
                        <li>传播虚假信息或进行欺诈活动</li>
                        <li>侵犯他人知识产权</li>
                        <li>恶意攻击或破坏平台系统</li>
                        <li>进行其他违法违规行为</li>
                    </ul>
                </div>

                <div class="section">
                    <h2>5. 知识产权</h2>
                    <p>5.1 本平台拥有服务中包含的所有内容（包括但不限于文字、图片、音频、视频、软件等）的知识产权。</p>
                    <p>5.2 未经本平台书面许可，用户不得复制、传播、修改或商业性使用上述内容。</p>
                    <p>5.3 用户上传的内容，用户保证拥有相应权利，并授权本平台在服务范围内使用。</p>
                </div>

                <div class="section">
                    <h2>6. 隐私保护</h2>
                    <p>6.1 本平台重视用户隐私保护，具体内容请参见《隐私政策》。</p>
                    <p>6.2 本平台将按照隐私政策收集、使用、存储和保护用户信息。</p>
                </div>

                <div class="section">
                    <h2>7. 服务变更与终止</h2>
                    <p>7.1 本平台有权根据业务发展需要修改或终止服务。</p>
                    <p>7.2 如服务发生重大变更，本平台将提前通知用户。</p>
                    <p>7.3 用户违反本协议的，本平台有权暂停或终止提供服务。</p>
                </div>

                <div class="section">
                    <h2>8. 免责声明</h2>
                    <p>8.1 本平台不对因不可抗力、网络故障、系统维护等原因导致的服务中断承担责任。</p>
                    <p>8.2 用户理解并同意，使用本服务存在一定风险，应自行承担相应后果。</p>
                </div>

                <div class="section">
                    <h2>9. 协议修改</h2>
                    <p>9.1 本平台有权随时修改本协议条款。</p>
                    <p>9.2 协议修改后，将在平台内公布，用户继续使用服务即视为同意修改后的协议。</p>
                </div>

                <div class="section">
                    <h2>10. 联系我们</h2>
                    <p>如您对本协议有任何疑问，请通过以下方式联系我们：</p>
                    <p>客服电话：15880293295</p>
                    <p>邮箱：<EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "UserAgreement",
    mounted() {
        document.title = "用户协议";
    }
};
</script>

<style scoped>
.agreement-container {
    min-height: 100vh;
    background-color: #f8f9fa;
}

.header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-nav-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-nav-bar :deep(.van-nav-bar__title) {
    color: white;
    font-weight: 600;
}

.custom-nav-bar :deep(.van-nav-bar__text) {
    color: white;
}

.custom-nav-bar :deep(.van-icon) {
    color: white;
}

.content {
    padding: 20px;
}

.agreement-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.agreement-content h1 {
    text-align: center;
    color: #323233;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #1989fa;
}

.update-time {
    text-align: center;
    margin-bottom: 30px;
    padding: 12px;
    background: #f0f7ff;
    border-radius: 8px;
}

.update-time p {
    margin: 4px 0;
    color: #646566;
    font-size: 14px;
}

.section {
    margin-bottom: 24px;
}

.section h2 {
    color: #1989fa;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    padding-left: 12px;
    border-left: 4px solid #1989fa;
}

.section p {
    color: #646566;
    line-height: 1.6;
    margin-bottom: 8px;
    text-align: justify;
}

.section ul {
    margin: 12px 0;
    padding-left: 20px;
}

.section li {
    color: #646566;
    line-height: 1.6;
    margin-bottom: 6px;
    list-style-type: disc;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .content {
        padding: 16px;
    }
    
    .agreement-content {
        padding: 20px;
    }
    
    .agreement-content h1 {
        font-size: 20px;
    }
    
    .section h2 {
        font-size: 16px;
    }
}
</style>
