<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
    <el-form-item label="会议id" prop="activityId">
      <el-input v-model="dataForm.activityId" placeholder="会议id"></el-input>
    </el-form-item>
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="状态：0-未报名，1-已报名，2-已取消" prop="status">
      <el-input v-model="dataForm.status" placeholder="状态：0-未报名，1-已报名，2-已取消"></el-input>
    </el-form-item>
    <el-form-item label="姓名" prop="contact">
      <el-input v-model="dataForm.contact" placeholder="姓名"></el-input>
    </el-form-item>
    <el-form-item label="手机" prop="mobile">
      <el-input v-model="dataForm.mobile" placeholder="手机"></el-input>
    </el-form-item>
    <el-form-item label="性别" prop="gender">
      <el-input v-model="dataForm.gender" placeholder="性别"></el-input>
    </el-form-item>
    <el-form-item label="身份证" prop="idCard">
      <el-input v-model="dataForm.idCard" placeholder="身份证"></el-input>
    </el-form-item>
    <el-form-item label="职称" prop="duties">
      <el-input v-model="dataForm.duties" placeholder="职称"></el-input>
    </el-form-item>
    <el-form-item label="单位" prop="unit">
      <el-input v-model="dataForm.unit" placeholder="单位"></el-input>
    </el-form-item>
    <el-form-item label="扩展字段" prop="extra">
      <el-input v-model="dataForm.extra" placeholder="扩展字段"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          activityId: '',
          userId: '',
          status: '',
          contact: '',
          mobile: '',
          gender: '',
          idCard: '',
          duties: '',
          unit: '',
          extra: '',
        },
        dataRule: {
          activityId: [
            { required: true, message: '会议id不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态：0-未报名，1-已报名，2-已取消不能为空', trigger: 'blur' }
          ],
          contact: [
            { required: true, message: '姓名不能为空', trigger: 'blur' }
          ],
          mobile: [
            { required: true, message: '手机不能为空', trigger: 'blur' }
          ],
          gender: [
            { required: true, message: '性别不能为空', trigger: 'blur' }
          ],
          idCard: [
            { required: true, message: '身份证不能为空', trigger: 'blur' }
          ],
          duties: [
            { required: true, message: '职称不能为空', trigger: 'blur' }
          ],
          unit: [
            { required: true, message: '单位不能为空', trigger: 'blur' }
          ],
          extra: [
            { required: true, message: '扩展字段不能为空', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuser/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.activityId = data.activityUser.activityId
                this.dataForm.userId = data.activityUser.userId
                this.dataForm.status = data.activityUser.status
                this.dataForm.contact = data.activityUser.contact
                this.dataForm.mobile = data.activityUser.mobile
                this.dataForm.gender = data.activityUser.gender
                this.dataForm.idCard = data.activityUser.idCard
                this.dataForm.duties = data.activityUser.duties
                this.dataForm.unit = data.activityUser.unit
                this.dataForm.extra = data.activityUser.extra
                this.dataForm.createOn = data.activityUser.createOn
                this.dataForm.createBy = data.activityUser.createBy
                this.dataForm.updateOn = data.activityUser.updateOn
                this.dataForm.updateBy = data.activityUser.updateBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activity/activityuser/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'activityId': this.dataForm.activityId,
                'userId': this.dataForm.userId,
                'status': this.dataForm.status,
                'contact': this.dataForm.contact,
                'mobile': this.dataForm.mobile,
                'gender': this.dataForm.gender,
                'idCard': this.dataForm.idCard,
                'duties': this.dataForm.duties,
                'unit': this.dataForm.unit,
                'extra': this.dataForm.extra,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
