package com.cjy.pyp.modules.salesman.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionSettlementEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 业务员佣金结算批次DAO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Mapper
public interface SalesmanCommissionSettlementDao extends BaseMapper<SalesmanCommissionSettlementEntity> {

    /**
     * 分页查询结算批次列表
     * @param params 查询参数
     * @return 结算批次列表
     */
    List<SalesmanCommissionSettlementEntity> queryPage(Map<String, Object> params);

    /**
     * 根据批次号查询结算批次
     * @param batchNo 批次号
     * @param appid 应用ID
     * @return 结算批次
     */
    SalesmanCommissionSettlementEntity getByBatchNo(@Param("batchNo") String batchNo, 
                                                    @Param("appid") String appid);

    /**
     * 查询结算统计信息
     * @param params 查询参数
     * @return 统计信息
     */
    Map<String, Object> getSettlementStats(Map<String, Object> params);
}
