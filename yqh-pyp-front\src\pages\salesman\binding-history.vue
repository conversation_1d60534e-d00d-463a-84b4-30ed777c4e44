<template>
  <div class="binding-history-page">
    <!-- 头部 -->
    <div class="header">
      <van-nav-bar
        title="绑定历史"
        left-text="返回"
        left-arrow
        @click-left="$router.go(-1)"
      />
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 当前绑定状态 -->
      <div class="current-status">
        <div class="status-card">
          <div class="status-header">
            <van-icon name="user-o" size="20" />
            <span>当前绑定状态</span>
          </div>
          <div v-if="currentBinding" class="current-binding">
            <div class="binding-info">
              <div class="salesman-name">{{ currentBinding.salesmanName }}</div>
              <div class="salesman-code">编号：{{ currentBinding.salesmanCode }}</div>
            </div>
            <div class="binding-status">
              <van-tag type="success">已绑定</van-tag>
            </div>
          </div>
          <div v-else class="no-binding">
            <span>暂未绑定业务员</span>
            <van-button type="primary" size="small" @click="goToBind">立即绑定</van-button>
          </div>
        </div>
      </div>

      <!-- 历史记录 -->
      <div class="history-section">
        <div class="section-title">
          <van-icon name="clock-o" />
          <span>变更历史</span>
        </div>

        <div v-if="!loading">
          <div v-if="historyList.length > 0" class="history-list">
            <div
              v-for="(item, index) in historyList"
              :key="item.id"
              class="history-item"
            >
              <div class="timeline-dot" :class="getTimelineDotClass(item.operationType)"></div>
              <div class="timeline-line" v-if="index < historyList.length - 1"></div>
              
              <div class="history-content">
                <div class="history-header">
                  <div class="operation-type">
                    <van-tag :type="getOperationTagType(item.operationType)" size="medium">
                      {{ getOperationTypeText(item.operationType) }}
                    </van-tag>
                  </div>
                  <div class="operation-time">{{ formatDateTime(item.createOn) }}</div>
                </div>

                <div class="history-details">
                  <!-- 新增绑定 -->
                  <div v-if="item.operationType === 1" class="detail-content">
                    <p><strong>绑定业务员：</strong>{{ item.newSalesmanName }}</p>
                    <p v-if="item.bindingSource"><strong>绑定来源：</strong>{{ item.bindingSource }}</p>
                  </div>

                  <!-- 更换业务员 -->
                  <div v-else-if="item.operationType === 2" class="detail-content">
                    <p><strong>原业务员：</strong>{{ item.oldSalesmanName || '无' }}</p>
                    <p><strong>新业务员：</strong>{{ item.newSalesmanName }}</p>
                    <p v-if="item.operationReason"><strong>更换原因：</strong>{{ item.operationReason }}</p>
                  </div>

                  <!-- 解除绑定 -->
                  <div v-else-if="item.operationType === 3" class="detail-content">
                    <p><strong>解绑业务员：</strong>{{ item.oldSalesmanName }}</p>
                    <p v-if="item.operationReason"><strong>解绑原因：</strong>{{ item.operationReason }}</p>
                  </div>

                  <!-- 系统自动解绑 -->
                  <div v-else-if="item.operationType === 4" class="detail-content">
                    <p><strong>系统解绑：</strong>{{ item.oldSalesmanName }}</p>
                    <p v-if="item.operationReason"><strong>解绑原因：</strong>{{ item.operationReason }}</p>
                  </div>

                  <!-- 操作人信息 -->
                  <div class="operator-info">
                    <span class="operator-label">操作人：</span>
                    <span class="operator-text">{{ getOperatorTypeText(item.operatorType) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 无历史记录 -->
          <div v-else class="no-history">
            <van-empty
              image="https://img.yzcdn.cn/vant/custom-empty-image.png"
              description="暂无绑定历史记录"
            />
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else class="loading-state">
          <van-loading type="spinner" color="#1989fa">加载中...</van-loading>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && !loading" class="load-more">
        <van-button
          type="default"
          size="large"
          @click="loadMore"
          :loading="loadingMore"
          block
        >
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BindingHistory',
  data() {
    return {
      loading: true,
      loadingMore: false,
      currentBinding: null,
      historyList: [],
      page: 1,
      pageSize: 10,
      hasMore: true
    }
  },
  mounted() {
    this.getCurrentBinding()
    this.getHistoryList()
  },
  methods: {
    // 获取当前绑定状态
    async getCurrentBinding() {
      try {
        const response = await this.$fly.get('/pyp/web/salesman/binding/myBinding')
        if (response.code ===200) {
          this.currentBinding = response.binding
        }
      } catch (error) {
        // 忽略错误，可能是未绑定
      }
    },

    // 获取绑定历史
    async getHistoryList(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.loading = true
          this.page = 1
        } else {
          this.loadingMore = true
        }

        const response = await this.$fly.get('/pyp/web/salesman/binding/getBindingHistory', {
          page: this.page,
          limit: this.pageSize
        })

        if (response.code ===200) {
          const newList = response.page.list || []
          
          if (isLoadMore) {
            this.historyList = [...this.historyList, ...newList]
          } else {
            this.historyList = newList
          }

          this.hasMore = newList.length === this.pageSize
          if (this.hasMore) {
            this.page++
          }
        }
      } catch (error) {
        this.$toast('获取历史记录失败')
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },

    // 加载更多
    loadMore() {
      if (!this.loadingMore && this.hasMore) {
        this.getHistoryList(true)
      }
    },

    // 前往绑定页面
    goToBind() {
      this.$router.push('/salesman/my-salesman')
    },

    // 获取操作类型文本
    getOperationTypeText(type) {
      const typeMap = {
        1: '新增绑定',
        2: '更换业务员',
        3: '解除绑定',
        4: '系统解绑'
      }
      return typeMap[type] || '未知操作'
    },

    // 获取操作类型标签类型
    getOperationTagType(type) {
      const typeMap = {
        1: 'success',
        2: 'warning',
        3: 'danger',
        4: 'default'
      }
      return typeMap[type] || 'default'
    },

    // 获取时间线圆点样式
    getTimelineDotClass(type) {
      const classMap = {
        1: 'success',
        2: 'warning',
        3: 'danger',
        4: 'default'
      }
      return classMap[type] || 'default'
    },

    // 获取操作人类型文本
    getOperatorTypeText(type) {
      const typeMap = {
        1: '本人操作',
        2: '业务员操作',
        3: '管理员操作',
        4: '系统操作'
      }
      return typeMap[type] || '未知'
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) {
        return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      } else if (days === 1) {
        return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      }
    }
  }
}
</script>

<style scoped>
.binding-history-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 16px;
}

.current-status {
  margin-bottom: 20px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.status-header .van-icon {
  margin-right: 8px;
  color: #1989fa;
}

.current-binding {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.salesman-name {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.salesman-code {
  font-size: 14px;
  color: #969799;
}

.no-binding {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #969799;
}

.history-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.section-title .van-icon {
  margin-right: 8px;
  color: #1989fa;
}

.history-list {
  position: relative;
}

.history-item {
  position: relative;
  padding-left: 32px;
  margin-bottom: 24px;
}

.history-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ebedf0;
}

.timeline-dot.success {
  background-color: #07c160;
}

.timeline-dot.warning {
  background-color: #ff976a;
}

.timeline-dot.danger {
  background-color: #ee0a24;
}

.timeline-line {
  position: absolute;
  left: 5px;
  top: 20px;
  bottom: -24px;
  width: 2px;
  background-color: #ebedf0;
}

.history-content {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.operation-time {
  font-size: 12px;
  color: #969799;
}

.detail-content p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #646566;
}

.detail-content p:last-child {
  margin-bottom: 0;
}

.operator-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
  font-size: 12px;
}

.operator-label {
  color: #969799;
}

.operator-text {
  color: #646566;
}

.no-history {
  text-align: center;
  padding: 40px 20px;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.load-more {
  margin-top: 20px;
}
</style>
