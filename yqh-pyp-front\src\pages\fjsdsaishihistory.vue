<template>
    <div>
        <div style="position:fixed;top: 0;z-index: 999;width: 100%;">
            <van-search v-model="dataForm.name" placeholder="请输入您要搜索的历史赛事活动名称" show-action shape="round" @search="onLoad">
                <div slot="action" @click="onSearch" class="search-text">搜索</div>
            </van-search>
            <div class="nav-title">
                <div class="color"></div>
                <div class="text">历史赛事活动列表</div>
            </div>
        </div>
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" style="margin-top: 88px">
            <van-card style="background: white" v-for="item in dataList" :key="item.id" @click="turnUrl(item)"
                :thumb="!item.mobileBanner ? 'van-icon' : item.mobileBanner.split(',')[0]">
                <div slot="title" class="title">{{ item.name }}</div>
                <div slot="desc" style="padding-top: 3px;font-size: 14px;color:grey" v-if="item.startTime == item.endTime">
                    {{ item.startTime.substring(0, 10) }}</div>
                <div slot="desc" style="padding-top: 3px;font-size: 14px;color:grey" v-else>
                    {{ item.startTime.substring(0, 10) }} 至 {{ item.endTime.substring(5, 10) }}</div>
                <div slot="price" style="font-size: 14px">{{ item.address }}</div>
            </van-card>
        </van-list>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dataForm: {
                name: '',
                configActivityTypeId: ''
            },
            loading: false,
            finished: false,
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
        }
    },
    mounted() {
        document.title = "历史赛事活动列表";
        this.dataForm.configActivityTypeId = this.$route.query.configActivityTypeId || '';
        this.$wxShare(this.$cookie.get("accountName"), this.$cookie.get("logo"), this.$cookie.get("slog"));
        //加载微信分享
        // this.getActivityList();
    },
    methods: {
        onSearch() {
            this.pageIndex = 1;
            this.dataList = [];
            this.getActivityList();
        },
        onLoad() {
            this.getActivityList();
        },
        getActivityList() {
            this.$fly.get('/pyp/web/activity/activity/historyList', {
                'page': this.pageIndex,
                'limit': this.pageSize,
                'configActivityTypeId': this.dataForm.configActivityTypeId,
                'name': this.dataForm.name
            }).then(res => {
                this.loading = false;
                if (res.code == 200) {
                    if (res.page.list && res.page.list.length > 0) {
                        res.page.list.forEach(e => {
                            this.dataList.push(e);
                        })
                        this.totalPage = res.page.totalPage
                        this.pageIndex++;
                        this.loading = false
                        if (this.totalPage < this.pageIndex) {
                            this.finished = true
                        } else {
                            this.finished = false
                        }
                    } else {
                        this.finished = true
                    }
                } else {
                    // vant.Toast(res.msg);
                    this.dataList = []
                    this.totalPage = 0
                    this.finished = true
                }
            });
        },
        turnUrl(item) {
            if (item.turnurl) {
                location.href = item.turnurl
            } else {
                if (this.$cookie.get("appid") != item.appid) {
                    if (item.appid == 'wx0f8d389094ac6910') {
                        location.href = 'http://ztmeeting.com/mp/#/cms/index?id=' + item.id;
                    }
                    if (item.appid == 'wx0770d56458b33c67') {
                        location.href = 'http://fjmeeting.com/mp_fjsd/#/cms/index?id=' + item.id;
                    }
                } else {
                    this.$router.push({ name: 'cmsIndex', query: { id: item.id } })
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>.van-card__thumb {
    width: 156px;
}

.title {
  font-size: 18px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>