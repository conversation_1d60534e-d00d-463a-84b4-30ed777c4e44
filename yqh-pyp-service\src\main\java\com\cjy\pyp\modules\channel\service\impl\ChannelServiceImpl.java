package com.cjy.pyp.modules.channel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.modules.channel.dao.ChannelDao;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 渠道服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@Service("channelService")
public class ChannelServiceImpl extends ServiceImpl<ChannelDao, ChannelEntity> implements ChannelService {

    @Autowired
    private SalesmanService salesmanService;

    @Override
    public List<ChannelEntity> findByAppid(String appid) {
        return this.list(new QueryWrapper<ChannelEntity>()
            .eq("appid", appid)
            .eq("status", 1)
            .orderByDesc("create_on"));
    }

    @Override
    public ChannelEntity findByCode(String code, String appid) {
        return this.getOne(new QueryWrapper<ChannelEntity>()
            .eq("code", code)
            .eq("appid", appid));
    }

    @Override
    public boolean existsByCode(String code, String appid, Long excludeId) {
        QueryWrapper<ChannelEntity> wrapper = new QueryWrapper<ChannelEntity>()
            .eq("code", code)
            .eq("appid", appid);
        
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    @Override
    public List<ChannelEntity> queryPageWithStats(Map<String, Object> params) {
        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page, limit);
        
        // 使用自定义查询方法，关联统计信息
        List<ChannelEntity> list = this.baseMapper.selectPageWithStats(params);
        return list;
    }

    @Override
    public Integer calculateLevel(Long parentId) {
        if (parentId == null) {
            return 1;
        }

        ChannelEntity channel = this.getById(parentId);
        if (channel == null || channel.getParentId() == null) {
            return 1;
        }

        // 递归计算层级
        return calculateLevel(channel.getParentId()) + 1;
    }

    @Override
    public void updateLevel(Long channelId) {
        if (channelId == null) {
            return;
        }

        Integer level = calculateLevel(channelId);
        ChannelEntity channel = new ChannelEntity();
        channel.setId(channelId);
        channel.setLevel(level);
        this.updateById(channel);

        // 更新所有下级的层级
        List<ChannelEntity> children = this.list(
            new QueryWrapper<ChannelEntity>().eq("parent_id", channelId)
        );
        for (ChannelEntity child : children) {
            updateLevel(child.getId());
        }
    }

    @Override
    public List<Long> getAllChildChannelIds(Long channelId) {
        if (channelId == null) {
            return new ArrayList<>();
        }

        List<Long> allChildIds = new ArrayList<>();
        collectChildChannelIds(channelId, allChildIds);
        return allChildIds;
    }

    /**
     * 递归收集所有子渠道ID
     * @param channelId 当前渠道ID
     * @param allChildIds 收集结果列表
     */
    private void collectChildChannelIds(Long channelId, List<Long> allChildIds) {
        // 查询直接子渠道
        List<Long> directChildIds = this.baseMapper.selectAllChildChannelIds(channelId);

        for (Long childId : directChildIds) {
            if (!allChildIds.contains(childId)) {
                allChildIds.add(childId);
                // 递归查询子渠道的子渠道
                collectChildChannelIds(childId, allChildIds);
            }
        }
    }

    @Override
    public Map<String, Object> getStatsByChannelId(Long channelId, String appid) {
        return this.baseMapper.selectStatsByChannelId(channelId, appid);
    }

    @Override
    public Map<String, Object> getStatsByChannelIds( List<Long> channelIds, String appid) {
        return this.baseMapper.selectStatsByChannelId(channelIds, appid);
    }

    @Override
    public Map<String, Object> getOverallStatsByAppid(String appid) {
        return this.baseMapper.selectOverallStatsByAppid(appid);
    }

    @Override
    public boolean hasAccessToSalesman(Long channelId, Long salesmanId) {
        if (channelId == null || salesmanId == null) {
            return false;
        }

        // 查询业务员信息
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null) {
            return false;
        }

        // 检查业务员是否属于该渠道或其子渠道
        if (channelId.equals(salesman.getChannelId())) {
            return true;
        }

        // 检查是否属于子渠道
        List<Long> childChannelIds = getAllChildChannelIds(channelId);
        return childChannelIds.contains(salesman.getChannelId());
    }

    @Override
    public boolean hasAccessToActivity(Long channelId, Long activityId) {
        if (channelId == null || activityId == null) {
            return false;
        }

        // 获取渠道及其子渠道的所有业务员
        List<Long> channelIds = new ArrayList<>();
        channelIds.add(channelId);
        channelIds.addAll(getAllChildChannelIds(channelId));

        // 查询这些渠道下是否有业务员参与了该活动
        List<SalesmanEntity> salesmen = salesmanService.list(
            new QueryWrapper<SalesmanEntity>().in("channel_id", channelIds)
        );

        if (CollectionUtils.isEmpty(salesmen)) {
            return false;
        }

        // 这里可以进一步检查业务员是否参与了该活动
        // 具体实现需要根据业务逻辑来定义
        return true;
    }

    @Override
    public boolean hasAccessToOrder(Long channelId, Long orderId) {
        if (channelId == null || orderId == null) {
            return false;
        }

        // 获取渠道及其子渠道的所有业务员ID
        List<Long> channelIds = new ArrayList<>();
        channelIds.add(channelId);
        channelIds.addAll(getAllChildChannelIds(channelId));

        List<SalesmanEntity> salesmen = salesmanService.list(
            new QueryWrapper<SalesmanEntity>().in("channel_id", channelIds)
        );

        if (CollectionUtils.isEmpty(salesmen)) {
            return false;
        }

        List<Long> salesmanIds = salesmen.stream()
            .map(SalesmanEntity::getId)
            .collect(java.util.stream.Collectors.toList());

        // 检查订单是否属于这些业务员
        // 这里需要注入 ActivityRechargeRecordService 来查询
        // 为了避免循环依赖，我们使用 DAO 层直接查询
        return baseMapper.checkOrderBelongsToSalesmen(orderId, salesmanIds);
    }

    @Override
    public boolean hasAccessToCustomer(Long channelId, Long wxUserId) {
        if (channelId == null || wxUserId == null) {
            return false;
        }

        // 获取渠道及其子渠道的所有业务员ID
        List<Long> channelIds = new ArrayList<>();
        channelIds.add(channelId);
        channelIds.addAll(getAllChildChannelIds(channelId));

        List<SalesmanEntity> salesmen = salesmanService.list(
            new QueryWrapper<SalesmanEntity>().in("channel_id", channelIds)
        );

        if (CollectionUtils.isEmpty(salesmen)) {
            return false;
        }

        List<Long> salesmanIds = salesmen.stream()
            .map(SalesmanEntity::getId)
            .collect(java.util.stream.Collectors.toList());

        // 检查客户是否绑定了这些业务员
        return baseMapper.checkCustomerBelongsToSalesmen(wxUserId, salesmanIds);
    }

    @Override
    public List<WxUser>  queryCustomerPage(Map<String, Object> params) {
        // 使用自定义SQL查询渠道客户列表
        int pageNum = Integer.parseInt(params.getOrDefault("page", "1").toString());
        int pageSize = Integer.parseInt(params.getOrDefault("limit", "10").toString());

        PageHelper.startPage(pageNum, pageSize);
        List<WxUser> list = baseMapper.selectCustomerPage(params);

        return list;
    }

    @Override
    public Map<String, Object> getCustomerStats(Map<String, Object> params) {
        return baseMapper.selectCustomerStats(params);
    }

    @Override
    public void batchUpdateCustomerChannel(String appid) {
        // 调用工具类进行批量更新
        com.cjy.pyp.modules.channel.utils.ChannelDataUtils channelDataUtils =
            com.cjy.pyp.common.utils.SpringContextUtils.getBean("channelDataUtils", com.cjy.pyp.modules.channel.utils.ChannelDataUtils.class);
        channelDataUtils.batchUpdateWxUserChannelId(appid);
    }
}
