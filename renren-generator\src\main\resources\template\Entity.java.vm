package ${package}.${moduleName}.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Data
@TableName("${tableName}")
@Accessors(chain = true)
public class ${className}Entity implements Serializable {
	private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
	/**
	 * $column.comments
	 */
	#if($column.columnName == $pk.columnName)
@TableId
	#end
	#if($column.attrname == 'createOn' || $column.attrname == 'createBy')
@TableField(fill = FieldFill.INSERT)
	#end
	#if($column.attrname == 'updateOn' || $column.attrname == 'updateBy')
@TableField(fill = FieldFill.UPDATE)
	#end
private $column.attrType $column.attrname;
#end

	@TableField(exist = false)
	private String repeatToken;
}
