<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-select v-model="dataForm.placeId" placeholder="全部场地" filterable>
          <el-option label="全部场地" value=""></el-option>
          <el-option v-for="item in placeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="主题名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('place:placeactivitytopic:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('place:placeactivitytopic:delete')" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="会场名称">
      </el-table-column>
      <el-table-column prop="placeName" header-align="center" align="center" label="场地名称">
      </el-table-column>
      <el-table-column prop="activitySpeakers" header-align="center" align="center" label="主席">
        <div slot-scope="scope" @click="showTopicSpeaker(scope.row.id)">
          <el-tag type="primary" :class="'tag-color tag-color-' + item.confirmStatus" v-for="(item, index) in scope.row.activitySpeakers" :key="index">{{ item.name }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="activityGuests" header-align="center" align="center" label="主持人">
        <div slot-scope="scope" @click="showTopicGuest(scope.row.id)">
          <el-tag type="primary" :class="'tag-color tag-color-' + item.confirmStatus" v-for="(item, index) in scope.row.activityGuests" :key="index">{{ item.name }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="activityDiscuss" header-align="center" align="center" label="讨论">
        <div slot-scope="scope" @click="showTopicDiscuss(scope.row.id)">
          <el-tag type="primary" :class="'tag-color tag-color-' + item.confirmStatus" v-for="(item, index) in scope.row.activityDiscuss" :key="index">{{ item.name }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="startTime" header-align="center" align="center" label="开始时间">
      </el-table-column>
      <el-table-column prop="endTime" header-align="center" align="center" label="结束时间">
      </el-table-column>
      <el-table-column prop="orderBy" header-align="center" align="center" label="排序">
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="placeactivitytopicschedule(scope.row.id)">日程管理</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 嘉宾修改 -->
    <topicguest v-if="topicguestVisible" ref="topicguest" @refreshDataList="getDataList"></topicguest>
    <!-- 嘉宾修改 -->
    <topicspeaker v-if="topicspeakerVisible" ref="topicspeaker" @refreshDataList="getDataList"></topicspeaker>
    <topicdiscuss v-if="topicdiscussVisible" ref="topicdiscuss" @refreshDataList="getDataList"></topicdiscuss>
  </div>
</template>

<script>
import AddOrUpdate from "./placeactivitytopic-add-or-update";
import topicguest from "./placeactivitytopic-guest";
import topicspeaker from "./placeactivitytopic-speaker";
import topicdiscuss from "./placeactivitytopic-discuss";
export default {
  data() {
    return {
      dataForm: {
        name: "",
        placeId: "",
        activityId: undefined,
      },
      dataList: [],
      placeList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      topicguestVisible: false,
      topicspeakerVisible: false,
      topicdiscussVisible: false,
    };
  },
  components: {
    AddOrUpdate,
    topicguest,
    topicspeaker,
    topicdiscuss,
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.dataForm.placeId = this.$route.query.placeId || undefined;
    this.getDataList();
    this.getPlace();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl("/place/placeactivitytopic/list"),
        method: "get",
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.dataForm.activityId,
          placeId: this.dataForm.placeId,
          name: this.dataForm.name,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list;
          this.totalPage = data.page.totalCount;
        } else {
          this.dataList = [];
          this.totalPage = 0;
        }
        this.dataListLoading = false;
      });
    },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id);
      });
    },
    // 嘉宾管理
    showTopicGuest(id) {
      this.topicguestVisible = true;
      this.$nextTick(() => {
        this.$refs.topicguest.init(this.dataForm.activityId, id);
      });
    },
    showTopicDiscuss(id) {
      this.topicdiscussVisible = true;
      this.$nextTick(() => {
        this.$refs.topicdiscuss.init(this.dataForm.activityId, id);
      });
    },
    // 主席管理
    showTopicSpeaker(id) {
      this.topicspeakerVisible = true;
      this.$nextTick(() => {
        this.$refs.topicspeaker.init(this.dataForm.activityId, id);
      });
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.id;
        });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl("/place/placeactivitytopic/delete"),
          method: "post",
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    placeactivitytopicschedule(id) {
      if (this.dataForm.placeId) {

        this.$router.push({
          name: "placeactivitytopicschedule",
          query: {
            activityId: this.dataForm.activityId,
            placeId: this.dataForm.placeId,
            topicId: id,
          },
        });
      } else {

        this.$router.push({
          name: "placeactivitytopicschedule",
          query: {
            activityId: this.dataForm.activityId,
            topicId: id,
          },
        });
      }
    },
  },
};
</script>
