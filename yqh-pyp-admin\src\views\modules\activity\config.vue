<template>
  <div>
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div></div>
      <div style="display: flex; justify-content: center; align-items: center;">
        <div style="
        text-align: center;
        padding: 20px;
        font-weight: bold;
        font-size: 28px;
      ">
          {{ activityInfo.name }}
        </div>
        <el-badge  :value="noReadCount" :max="99" class="item">
          <el-button style="float: right" type="success" @click="goToActivityNotify">站内通知</el-button>
        </el-badge>
      </div>
      <el-button  type="success" @click="$router.go(-1)">返回</el-button>
    </div>

    <!-- 用户账户余额显示区域 -->
    <div class="account-balance-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="balance-card">
            <div class="balance-item">
              <div class="balance-label">总次数</div>
              <div class="balance-value total">{{ activityInfo.allCount || 0 }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="balance-card">
            <div class="balance-item">
              <div class="balance-label">已使用</div>
              <div class="balance-value used">{{ activityInfo.useCount || 0 }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="balance-card">
            <div class="balance-item">
              <div class="balance-label">剩余次数</div>
              <div class="balance-value remaining">{{ (activityInfo.allCount || 0) - (activityInfo.useCount || 0) }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="balance-card action-card">
            <div class="balance-item">
              <el-button type="primary" size="medium" @click="openRechargeDialog">
                <i class="el-icon-plus"></i> 立即充值
              </el-button>
              <el-button type="info" size="medium" @click="openUsageRecordsDialog" style="margin-left: 10px;">
                <i class="el-icon-document"></i> 使用记录
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-row class="row" :gutter="24">
      <el-col :span="6" @click.native="editActivity">
        <el-card shadow="hover" class="card"> 修改活动 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityVideo">
        <el-card shadow="hover" class="card"> 视频素材 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityFinishedVideo">
        <el-card shadow="hover" class="card"> 成品视频 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityImage">
        <el-card shadow="hover" class="card"> 图片素材 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityText">
        <el-card shadow="hover" class="card"> 文案管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="groupBuyingCoupon">
        <el-card shadow="hover" class="card"> 团购券管理 </el-card>
      </el-col>
    </el-row>
    <el-row v-show="false" class="row" :gutter="24">
      <el-col :span="6" @click.native="editActivity">
        <el-card shadow="hover" class="card"> 编辑会议信息 </el-card>
      </el-col>
      <el-col :span="6" @click.native="cmsConfig">
        <el-card shadow="hover" class="card"> 网站配置 </el-card>
      </el-col>
      <el-col :span="6" @click.native="bottomConfig">
        <el-card shadow="hover" class="card"> 九宫格底部配置 </el-card>
      </el-col>
      <el-col :span="6" @click.native="applyConfig">
        <el-card shadow="hover" class="card"> 报名配置 </el-card>
      </el-col>
      <el-col :span="6" @click.native="applyOrder">
        <el-card shadow="hover" class="card"> 报名订单 </el-card>
      </el-col>
      <el-col v-if="isAuth('activity:activityrole:list')" :span="6" @click.native="activityRole">
        <el-card shadow="hover" class="card"> 工作人员管理 </el-card>
      </el-col>
      <el-col v-if="isAuth('activity:activityviewlog:list')" :span="6" @click.native="activityviewlog">
        <el-card shadow="hover" class="card"> pv&uv记录 </el-card>
      </el-col>
      <el-col :span="6" @click.native="createFastQrCode" v-if="!wxQrCode || wxQrCode == null">
        <el-card shadow="hover" class="card"> 生成公众号会议二维码 </el-card>
      </el-col>
      <el-col :span="6" @click.native="downFastQrCode" v-else>
        <el-card shadow="hover" class="card"> 查看公众号会议二维码 </el-card>
      </el-col>
    </el-row>
    <el-row v-show="false" class="row" :gutter="24">
      <el-col :span="6" @click.native="placeactivity">
        <el-card shadow="hover" class="card"> 会场管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="placeactivitytopic">
        <el-card shadow="hover" class="card"> 主题管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="placeactivitytopicschedule">
        <el-card shadow="hover" class="card"> 日程管理 </el-card>
      </el-col>
      <el-col :span="6" @click.native="activityguest">
        <el-card shadow="hover" class="card"> 嘉宾管理 </el-card>
      </el-col>
      <el-col :span="6" v-if="isAuth('activity:activityguest:servicefee')" @click.native="activityguestservicefee">
        <el-card shadow="hover" class="card"> 劳务费管理 </el-card>
      </el-col>
      <el-col v-if="isAuth('place:placelivetabconfig:list')" :span="6" @click.native="placelivetabconfig">
        <el-card shadow="hover" class="card"> 直播标签 </el-card>
      </el-col>
      <el-col v-if="isAuth('place:placeactivityhourlog:list')" :span="6" @click.native="placeactivityhourlog">
        <el-card shadow="hover" class="card"> 直播观看记录 </el-card>
      </el-col>
    </el-row>
    <el-row v-show="false" class="row" :gutter="24" v-if="isAuth('merchant:merchant:list')">
      <el-col v-if="isAuth('merchant:merchant:list')" :span="6" @click.native="merchant">
        <el-card shadow="hover" class="card"> 展商管理 </el-card>
      </el-col>
    </el-row>
    <el-row v-show="false" class="row" :gutter="24" v-if="isAuth('exam:exam:list')">
      <el-col v-if="isAuth('exam:exam:list')" :span="6" @click.native="exam">
        <el-card shadow="hover" class="card"> 考卷管理 </el-card>
      </el-col>
    </el-row>
    <el-row v-show="false" class="row" :gutter="24">
      <el-col :span="6" @click.native="hotel">
        <el-card shadow="hover" class="card"> 会议酒店 </el-card>
      </el-col>
      <el-col :span="6" @click.native="hotelOrder">
        <el-card shadow="hover" class="card"> 酒店订单 </el-card>
      </el-col>
      <el-col :span="6" @click.native="hotelAssign">
        <el-card shadow="hover" class="card"> 分房管理 </el-card>
      </el-col>
      <el-col :span="6" v-if="isAuth('activity:activitynav:list')" @click.native="activitynav">
        <el-card shadow="hover" class="card"> 导航管理 </el-card>
      </el-col>
    </el-row>
    <!-- <el-row v-if="haveRole" class="row" :gutter="24">
      <el-col :span="6" @click.native="settle">
        <el-card shadow="hover" class="card"> 项目结算表 </el-card>
      </el-col>
    </el-row> -->
    <configupdateactivityconfig v-if="configupdateactivityconfigVisible" ref="configupdateactivityconfig"
      @refreshDataList="getActivityConfig"></configupdateactivityconfig>
    <!-- 充值弹窗 -->
    <gift-dialog v-if="giftVisible" ref="giftDialog" @refreshDataList="getActivity"></gift-dialog>
    <!-- 使用记录弹窗 -->
    <usage-records-dialog v-if="usageRecordsVisible" ref="usageRecordsDialog" :activity-id="activityId" @close="usageRecordsVisible = false"></usage-records-dialog>
  </div>
</template>

<script>
import configupdateactivityconfig from "./config-updateactivityconfig.vue";
import GiftDialog from "./activityrechargerecord-gift.vue";
import UsageRecordsDialog from "./usage-records-dialog.vue";
export default {
  data() {
    return {
      noReadCount: 0,
      haveRole: false,
      activityInfo: {},
      wxQrCode: {},
      activityConfig: {},
      activityId: undefined,
      configupdateactivityconfigVisible: false,
      accountInfo: {
        allCount: 0,
        useCount: 0
      },
      giftVisible: false,
      usageRecordsVisible: false
    };
  },
  components: {
    configupdateactivityconfig,
    GiftDialog,
    UsageRecordsDialog
  },
  activated() {
    this.activityId = this.$route.query.activityId;
    this.getActivity();
    this.getActivityRole();
    this.getFastQrCode();
    this.getActivityConfig();
    this.getNoReadCount();
  },
  methods: {
    groupBuyingCoupon() {
      this.$router.push({
        name: "groupbuying-coupon",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityText() {
      this.$router.push({
        name: "activitytext",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityImage() {
      this.$router.push({
        name: "activityimage",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityVideo() {
      this.$router.push({
        name: "activityvideo",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityFinishedVideo() {
      this.$router.push({
        name: "activityfinishedvideo",
        query: {
          activityId: this.activityId,
        },
      });
    },
    editActivityConfig() {
      this.configupdateactivityconfigVisible = true
      this.$nextTick(() => {
        this.$refs.configupdateactivityconfig.init(this.activityConfig.id)
      })

    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
        }
      });
    },
    getNoReadCount() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activitynotify/noReadCount`),
        method: "get",
        params: this.$http.adornParams({
          activityId: this.activityId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.noReadCount = data.result;
        }
      });
    },
    openRechargeDialog() {
      this.giftVisible = true
      this.$nextTick(() => {
        this.$refs.giftDialog.init()
        // 设置当前活动ID
        this.$refs.giftDialog.dataForm.activityId = this.activityId
      })
    },
    openUsageRecordsDialog() {
      this.usageRecordsVisible = true
      this.$nextTick(() => {
        this.$refs.usageRecordsDialog.init()
      })
    },
    getActivityConfig() {
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityconfig/findByActivityId/${this.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityConfig = data.result;
        }
      })
    },
    getActivityRole() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activityrole/haveRole`),
        method: "get",
        params: this.$http.adornParams({
          activityId: this.activityId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.haveRole = data.result;
        }
      });
    },
    getFastQrCode() {
      this.$http({
        url: this.$http.adornUrl(`/manage/wxQrCode/findByActivityId/${this.activityId}`),
        method: 'get',
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxQrCode = data.wxQrCode
        } else {
          this.wxQrCode = {}
        }
      })
    },
    downFastQrCode() {
      window.open("https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + this.wxQrCode.ticket);
    },
    createFastQrCode() {
      this.$http({
        url: this.$http.adornUrl(`/manage/wxQrCode/fastCreateTicket`),
        method: 'get',
        params: this.$http.adornParams({
          activityId: this.activityId
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxQrCode = data.wxQrCode
        } else {
          this.wxQrCode = {}
        }
      })
    },
    editActivity() {
      this.$router.push({
        name: "activityAddOrUpdate",
        query: {
          id: this.activityId,
        },
      });
    },
    goToActivityNotify() {
      this.$router.push({
        name: "activitynotify",
        query: {
          activityId: this.activityId,
        },
      });
    },
    cmsConfig() {
      this.$router.push({
        name: "cms",
        query: {
          activityId: this.activityId,
        },
      });
    },
    bottomConfig() {
      this.$router.push({
        name: "activitybottom",
        query: {
          activityId: this.activityId,
        },
      });
    },
    applyConfig() {
      this.$router.push({
        name: "applyActivityChannelConfig",
        query: {
          activityId: this.activityId,
        },
      });
    },
    applyOrder() {
      this.$router.push({
        name: "activityuserapplyorder",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityRole() {
      this.$router.push({
        name: "activityrole",
        query: {
          activityId: this.activityId,
        },
      });
    },
    hotel() {
      this.$router.push({
        name: "hotelactivity",
        query: {
          activityId: this.activityId,
        },
      });
    },
    hotelOrder() {
      this.$router.push({
        name: "hotelorder",
        query: {
          activityId: this.activityId,
        },
      });
    },
    hotelAssign() {
      this.$router.push({
        name: "hotelactivityroomassign",
        query: {
          activityId: this.activityId,
        },
      });
    },
    settle() {
      this.$router.push({
        name: "activitysettle",
        query: {
          activityId: this.activityId,
        },
      });
    },
    adTypeConfig() {
      this.$router.push({
        name: "adtypeconfig",
        query: {
          activityId: this.activityId,
        },
      });
    },
    placeactivity() {
      this.$router.push({
        name: "placeactivity",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityguest() {
      this.$router.push({
        name: "activityguest",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityguestservicefee() {
      this.$router.push({
        name: "activityguestservicefee",
        query: {
          activityId: this.activityId,
        },
      });
    },
    placeactivitytopic() {
      this.$router.push({
        name: "placeactivitytopic",
        query: {
          activityId: this.activityId,
        },
      });
    },
    placeactivitytopicschedule() {
      this.$router.push({
        name: "placeactivitytopicschedule",
        query: {
          activityId: this.activityId,
        },
      });
    },
    placelivetabconfig() {
      this.$router.push({
        name: "placelivetabconfig",
        query: {
          activityId: this.activityId,
        },
      });
    },
    placeactivityhourlog() {
      this.$router.push({
        name: "placeactivityhourlog",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityviewlog() {
      this.$router.push({
        name: "activityviewlog",
        query: {
          activityId: this.activityId,
        },
      });
    },
    exam() {
      this.$router.push({
        name: "exam",
        query: {
          activityId: this.activityId,
        },
      });
    },
    merchant() {
      this.$router.push({
        name: "merchant",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activitynav() {
      this.$router.push({
        name: "activitynav",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityRechargePackage() {
      this.$router.push({
        name: "activityrechargepackage",
        query: {
          activityId: this.activityId,
        },
      });
    },
    activityRechargeRecord() {
      this.$router.push({
        name: "activityrechargerecord",
        query: {
          activityId: this.activityId,
        },
      });
    },
  },
};
</script>

<style scoped>
.row {

  border: 1px solid #EBEEF5;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border-radius: 10px;
  margin: 20px 0;
}

.card {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
}

/* 账户余额显示区域样式 */
.account-balance-section {
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.balance-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.balance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.balance-item {
  text-align: center;
  padding: 20px 10px;
}

.balance-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.balance-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.balance-value.total {
  color: #409EFF;
}

.balance-value.used {
  color: #F56C6C;
}

.balance-value.remaining {
  color: #67C23A;
}

.action-card .balance-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.action-card .el-button {
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}
</style>