<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.sendService" placeholder="劳务费签字" filterable>
          <el-option label="全部(劳务费签字)" value=""></el-option>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.sendServiceInfo" placeholder="劳务费信息" filterable>
          <el-option label="全部(劳务费信息)" value=""></el-option>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.isServiceSign" placeholder="专家确认签字" filterable>
          <el-option label="全部(专家确认签字)" value=""></el-option>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('activity:activityguest:save')" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <!-- <el-button v-if="isAuth('activity:activityguest:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
        <el-button @click="exportPdfBatch()" type="success" :disabled="dataListSelections.length <= 0">批量导出确认函</el-button>
        <el-button @click="exportHandle()" type="success">导出</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="专家姓名">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="专家联系方式">
      </el-table-column>
      <el-table-column prop="serviceFee" header-align="center" align="center" label="劳务费">
        <div slot-scope="scope" @click="activityguestservicefeeupdate(scope.row.id)">
          {{ scope.row.serviceFee }}
        </div>
      </el-table-column>
      <el-table-column prop="sendServiceInfo" header-align="center" align="center" label="劳务费信息收集短信">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendServiceInfo == 1 ? 'success' : 'danger'">{{ scope.row.sendServiceInfo == 1 ? '已发送' : '未发送'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendServiceInfoTime" header-align="center" align="center" label="发送时间">
      </el-table-column>
      <el-table-column prop="sendService" header-align="center" align="center" label="劳务费签字短信">
        <div slot-scope="scope">
          <el-tag :type="scope.row.sendService == 1 ? 'success' : 'danger'">{{ scope.row.sendService == 1 ? '已发送' : '未发送'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="sendServiceTime" header-align="center" align="center" label="发送时间">
      </el-table-column>
      <el-table-column prop="isServiceSign" header-align="center" align="center" label="专家是否确认">
        <div slot-scope="scope">
          <el-tag :type="scope.row.isServiceSign == 1 ? 'success' : 'danger'">{{ scope.row.isServiceSign == 1 ? '已确认' : '未确认'
            }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="serviceSign" header-align="center" align="center" label="专家签字">
        <div slot-scope="scope">
          <img class="image-sm" style="height: 80px" :src="scope.row.serviceSign" />
        </div>
      </el-table-column>
      <el-table-column prop="serviceSignTime" header-align="center" align="center" label="专家确认时间">
      </el-table-column>
      <el-table-column prop="areaName" header-align="center" align="center" label="区域">
      </el-table-column>
      <el-table-column prop="unit" header-align="center" align="center" label="工作单位">
      </el-table-column>
      <!-- <el-table-column prop="duties" header-align="center" align="center" label="职称">
      </el-table-column> -->
      <el-table-column prop="idCardType" header-align="center" align="center" label="身份证类型">
      </el-table-column>
      <el-table-column prop="idCard" header-align="center" align="center" label="身份证">
      </el-table-column>
      <el-table-column prop="avatar" header-align="center" align="center" label="头像">
        <div slot-scope="scope">
          <img class="image-sm" style="height: 80px" :src="scope.row.avatar" />
        </div>
      </el-table-column>
      <el-table-column prop="bank" header-align="center" align="center" label="银行卡号">
      </el-table-column>
      <el-table-column prop="kaihuhang" header-align="center" align="center" label="开户行">
      </el-table-column>
      <!-- <el-table-column prop="idCardZheng" header-align="center" align="center" label="身份证正面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardZheng)" :src="scope.row.idCardZheng" />
          <a :href="scope.row.idCardZheng" target="_blank" v-else>{{ scope.row.idCardZheng }}</a>
        </div>
      </el-table-column>
      <el-table-column prop="idCardFan" header-align="center" align="center" label="身份证反面">
        <div slot-scope="scope">
          <img class="image-sm" v-if="isImageUrl(scope.row.idCardFan)" :src="scope.row.idCardFan" />
          <a :href="scope.row.idCardFan" target="_blank" v-else>{{ scope.row.idCardFan }}</a>
        </div>
      </el-table-column> -->
      <el-table-column prop="orderBy" header-align="center" align="center" label="排序">
      </el-table-column>
      <!-- <el-table-column prop="wxUserId" header-align="center" align="center" label="关联用户ID">
      </el-table-column> -->
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="updateOn" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="280" label="操作">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="small" v-if="scope.row.isServiceSign" @click="exportPdf(scope.row.id)">导出确认函</el-button> -->
          <el-button type="text" size="small" @click="exportPdf(scope.row.id)">导出确认函</el-button>
          <el-button type="text" size="small" @click="rebuildHandle(scope.row.id)">重新计算劳务费</el-button>
          <el-button type="text" size="small" @click="sendInfoTaskHandle(scope.row.id)">劳务费信息短信</el-button>
          <el-button type="text" size="small" @click="sendTaskHandle(scope.row.id)">劳务费签字短信</el-button>
          <el-button type="text" size="small" @click="showTaskHandle(scope.row.id)">学术任务</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <showtask v-if="showtaskVisible" ref="showtask" @refreshDataList="getDataList"></showtask>
    <activityguestservicefeeupdate v-if="activityguestservicefeeupdateVisible" ref="activityguestservicefeeupdate"
      @refreshDataList="getDataList"></activityguestservicefeeupdate>
  </div>
</template>

<script>
import AddOrUpdate from './activityguest-add-or-update'
import showtask from './activityguest-showtask'
import activityguestservicefeeupdate from './activityguestservicefee-update'
import {yesOrNo} from "@/data/common"
export default {
  data() {
    return {
      yesOrNo,
      dataForm: {
        name: '',
        mobile: '',
        sendService: '',
        sendServiceInfo: '',
        isServiceSign: '',
        isFirstChar: 0,
        activityId: undefined
      },
      activityInfo: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      showtaskVisible: false,
      activityguestservicefeeupdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    activityguestservicefeeupdate,
    showtask,
  },
  activated() {
    this.dataForm.activityId = this.$route.query.activityId;
    this.getActivity();
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/activityguest/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'mobile': this.dataForm.mobile,
          'activityId': this.dataForm.activityId,
          'sendService': this.dataForm.sendService,
          'sendServiceInfo': this.dataForm.sendServiceInfo,
          'isServiceSign': this.dataForm.isServiceSign,
          'isFirstChar': this.activityInfo.isFirstChar,
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.dataForm.activityId, id)
      })
    },
    activityguestservicefeeupdate(id) {
      this.activityguestservicefeeupdateVisible = true
      this.$nextTick(() => {
        this.$refs.activityguestservicefeeupdate.init(this.dataForm.activityId, id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendTaskHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送学术通知短信' : '批量发送学术通知短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendServiceFeeTask'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendInfoTaskHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定[${id ? '发送劳务费信息收集短信' : '批量发送劳务费信息收集短信'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/activityguest/sendServiceFeeInfoTask'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '发送操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 重新计算劳务费
    rebuildHandle(id) {
      // var ids = id ? [id] : this.dataListSelections.map(item => {
      //   return item.id
      // })
      this.$confirm(`确定[${id ? '重新计算劳务费' : '批量重新计算劳务费'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        this.$http({
          url: this.$http.adornUrl(
            `/activity/activityguest/rebuild/${id}`
          ),
          method: "get",
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '重新计算成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        });
      })
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
          this.getDataList()
        }
      });
    },
    updateIsFirstChar(v) {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/updateIsFirstChar`),
        method: "post",
        data: this.$http.adornData({
          id: this.dataForm.activityId,
          isFirstChar: v,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message.success("操作成功");
          this.getActivity();
          this.pageIndex = 1;
          this.getDataList()
        } else {
          this.$message.error(data.msg)

        }
      });
    },
    exportPdfBatch() {
      // console.log(this.dataListSelections)
      const contractPriceCertIds = this.dataListSelections
      .map(item => item.id)
      .join(",");
      this.exportPdf(contractPriceCertIds);
    },
    exportPdf(v) {
      this.$http({
        url: this.$http.adornOnlyUrl(`/activity/activityguest/exportPdf`),
        // url: this.$http.adornUrl(`/activity/activityguest/exportPdf`),
        method: "get",
        params: this.$http.adornParams({
          activityGuestId: v,
        }),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              window.open(data.result);
            }
          })
        } else {
          this.$message.error(data.msg)

        }
      });
    },
    showTaskHandle(v) {
      this.showtaskVisible = true
      this.$nextTick(() => {
        this.$refs.showtask.init(v)
      })
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/activityguest/exportServiceFee?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "name=" + this.dataForm.name,
        "mobile=" + this.dataForm.mobile,
        "activityId=" + this.dataForm.activityId,
        "sendService=" + this.dataForm.sendService,
        "sendServiceInfo=" + this.dataForm.sendServiceInfo,
        "isServiceSign=" + this.dataForm.isServiceSign,
        "isFirstChar=" + this.activityInfo.isFirstChar
      ].join('&'));
      window.open(url);
    },
    exportWord(v) {
      var url = this.$http.adornUrl("/activity/activityguest/exportWord?" + [
        "token=" + this.$cookie.get('token'),
        "activityGuestId=" + v,
      ].join('&'));
      window.open(url);
    },
  }
}
</script>
