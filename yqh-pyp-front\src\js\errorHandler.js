/**
 * 全局错误处理工具
 */

// 路由导航错误处理
export function handleRouterError(error) {
    console.log('处理路由错误:', error);
    
    // 忽略的错误类型
    const ignoredErrors = [
        'NavigationCancelled',
        'NavigationDuplicated',
        'NavigationAborted'
    ];
    
    if (ignoredErrors.includes(error.name)) {
        console.log(`路由${error.name}，这是正常的行为`);
        return true; // 表示错误已被处理
    }
    
    // 其他路由错误
    console.error('未处理的路由错误:', error);
    return false; // 表示错误未被处理
}

// Token失效处理
export function handleTokenExpired() {
    console.log('Token失效，清理本地数据');
    
    // 清除本地存储的用户信息
    Vue.cookie.delete('token');
    localStorage.removeItem('userInfo');
    sessionStorage.removeItem('userInfo');
    
    // 清除store中的用户信息（如果有的话）
    if (window.store && window.store.commit) {
        try {
            window.store.commit('user/logout');
        } catch (e) {
            console.log('清除store用户信息失败:', e);
        }
    }
}

// 安全的路由跳转
export function safeRouterPush(router, location) {
    return new Promise((resolve, reject) => {
        router.push(location).then(resolve).catch(error => {
            if (handleRouterError(error)) {
                resolve(); // 错误已被处理，视为成功
            } else {
                reject(error);
            }
        });
    });
}

// 安全的路由替换
export function safeRouterReplace(router, location) {
    return new Promise((resolve, reject) => {
        router.replace(location).then(resolve).catch(error => {
            if (handleRouterError(error)) {
                resolve(); // 错误已被处理，视为成功
            } else {
                reject(error);
            }
        });
    });
}

// 安全的页面跳转（用于token失效时）
export function safeNavigateToLogin(router, returnUrl) {
    console.log('安全跳转到登录页面');

    try {
        const cleanReturnUrl = returnUrl || window.location.href;

        console.log('=== errorHandler 跳转登录开始 ===');
        console.log('传入的returnUrl:', returnUrl);
        console.log('cleanReturnUrl:', cleanReturnUrl);

        // 如果当前已经在登录页面，不要再次跳转
        if (cleanReturnUrl.includes('/mobileLogin') || cleanReturnUrl.includes('/wxLogin')) {
            console.log('已在登录页面，避免重复跳转');
            return;
        }

        // 检查URL是否已经被编码过（简单检测）
        const isAlreadyEncoded = cleanReturnUrl.includes('%') && cleanReturnUrl.includes('http');
        console.log('URL是否已编码:', isAlreadyEncoded);

        const finalReturnUrl = isAlreadyEncoded ? cleanReturnUrl : encodeURIComponent(cleanReturnUrl);
        console.log('最终编码的returnUrl:', finalReturnUrl);

        const loginRoute = {
            name: 'mobileLogin',
            query: {
                returnUrl: finalReturnUrl
            }
        };
        console.log('=== errorHandler 跳转登录结束 ===');

        // 先尝试使用路由跳转
        safeRouterReplace(router, loginRoute).catch(() => {
            // 如果路由跳转失败，使用window.location
            console.log('路由跳转失败，使用window.location跳转');
            const loginUrl = router.resolve(loginRoute).href;
            setTimeout(() => {
                window.location.replace(loginUrl);
            }, 100);
        });
    } catch (error) {
        console.error('跳转到登录页面失败:', error);
        // 最后的备用方案
        setTimeout(() => {
            window.location.href = '/mobileLogin';
        }, 100);
    }
}

// 网络错误处理
export function handleNetworkError(error) {
    console.error('网络错误:', error);
    
    // 检查是否是网络连接问题
    if (!navigator.onLine) {
        vant.Toast('网络连接已断开，请检查网络设置');
        return;
    }
    
    // 检查是否是超时
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        vant.Toast('请求超时，请重试');
        return;
    }
    
    // 其他网络错误
    vant.Toast('网络错误，请稍后重试');
}

// 全局错误处理器
export function setupGlobalErrorHandler() {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', event => {
        console.error('未处理的Promise错误:', event.reason);
        
        // 如果是路由错误，尝试处理
        if (event.reason && typeof event.reason === 'object' && event.reason.name) {
            if (handleRouterError(event.reason)) {
                event.preventDefault(); // 阻止默认的错误处理
            }
        }
    });
    
    // 捕获JavaScript错误
    window.addEventListener('error', event => {
        console.error('JavaScript错误:', event.error);
    });
    
    console.log('全局错误处理器已设置');
}

export default {
    handleRouterError,
    handleTokenExpired,
    safeRouterPush,
    safeRouterReplace,
    safeNavigateToLogin,
    handleNetworkError,
    setupGlobalErrorHandler
};
