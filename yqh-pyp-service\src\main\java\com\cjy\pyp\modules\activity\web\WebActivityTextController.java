package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 活动文案素材Web接口
 */
@RestController
@RequestMapping("web/activity/activitytext")
public class WebActivityTextController extends AbstractController {
    
    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private ActivityService activityService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params) {
        try {
            PageUtils page = activityTextService.queryPage(params);
            return R.ok().put("page", page);
        } catch (Exception e) {
            return R.error("获取文案列表失败: " + e.getMessage());
        }
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        try {
            ActivityTextEntity activityText = activityTextService.getById(id);
            return R.ok().put("activityText", activityText);
        } catch (Exception e) {
            return R.error("获取文案信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ActivityTextEntity activityText) {
        try {
            // 设置创建用户
            activityText.setCreateBy(getUserId());
            activityTextService.save(activityText);
            return R.ok();
        } catch (Exception e) {
            return R.error("保存文案失败: " + e.getMessage());
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody ActivityTextEntity activityText) {
        try {
            // 设置更新用户
            activityText.setUpdateBy(getUserId());
            activityTextService.updateById(activityText);
            return R.ok();
        } catch (Exception e) {
            return R.error("更新文案失败: " + e.getMessage());
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        try {
            activityTextService.removeByIds(Arrays.asList(ids));
            return R.ok();
        } catch (Exception e) {
            return R.error("删除文案失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI文案
     */
    @RequestMapping("/generate")
    public R generateText(@RequestBody ActivityTextEntity activityText) {
        try {
            return activityTextService.generateText(activityText, getUserId());
        } catch (Exception e) {
            return R.error("生成文案失败: " + e.getMessage());
        }
    }

    /**
     * 根据AI标签获取文案（换一篇功能）
     */
    @RequestMapping("/getByTag")
    public R getByTag(@RequestParam("activityId") Long activityId,
                      @RequestParam("adType") String adType,
                      @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            // 验证活动是否存在
            ActivityEntity activity = activityService.getById(activityId);
            if (activity == null) {
                return R.error("活动不存在");
            }

            // 如果没有传入AI标签，使用活动的默认AI标签
            if (StringUtils.isBlank(aiTag)) {
                aiTag = activity.getAiTag();
            }

            // 根据活动ID、广告类型和AI标签获取文案
            ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeAndAiTag(activityId, adType, aiTag);

            if (textEntity == null) {
                return R.error("未找到对应的文案");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", textEntity.getId());
            result.put("title", textEntity.getName());
            result.put("content", textEntity.getResult());
            result.put("adType", textEntity.getAdType());
            result.put("aiTag", textEntity.getAiTag());
            result.put("promptKeyword", textEntity.getQuery());

            return R.ok().put("text", result);

        } catch (Exception e) {
            return R.error("获取文案失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动的AI标签列表
     */
    @RequestMapping("/getAiTags")
    public R getAiTags(@RequestParam("activityId") Long activityId) {
        try {
            ActivityEntity activity = activityService.getById(activityId);
            if (activity == null) {
                return R.error("活动不存在");
            }

            String aiTag = activity.getAiTag();
            if (StringUtils.isBlank(aiTag)) {
                return R.ok().put("tags", new String[0]);
            }

            // 按逗号分割AI标签
            String[] tags = aiTag.split(",");
            for (int i = 0; i < tags.length; i++) {
                tags[i] = tags[i].trim();
            }

            return R.ok().put("tags", tags);

        } catch (Exception e) {
            return R.error("获取AI标签失败: " + e.getMessage());
        }
    }

}
