export default {
    namespaced: true,
    state: {
        userInfo: {
            appid: null,
            createTime: null,
            createUserId: null,
            email: null,
            mobile: "",
            openid: "",
            password: null,
            roleIdList: null,
            salt: null,
            status: 1,
            userId: "",
            username: "",
        },
        showPcLogin: false
    },
    mutations: {
        update(state, user) {
            state.userInfo = user
        },
        changePcLogin(state, val) {
            state.showPcLogin = val
        },
        logout(state) {
            // 重置用户信息为初始状态
            state.userInfo = {
                appid: null,
                createTime: null,
                createUserId: null,
                email: null,
                mobile: "",
                openid: "",
                password: null,
                roleIdList: null,
                salt: null,
                status: 1,
                userId: "",
                username: "",
            };
            state.showPcLogin = false;
        }
    }
}