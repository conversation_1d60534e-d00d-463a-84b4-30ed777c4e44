package com.cjy.pyp.modules.activity.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.service.ActivityExpirationService;
import com.cjy.pyp.modules.activity.vo.ActivityExpirationStatusVo;
import com.cjy.pyp.modules.activity.vo.RenewalOrderVo;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 活动过期管理控制器（管理端）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/activity/expiration")
public class ActivityExpirationController extends AbstractController {

    @Autowired
    private ActivityExpirationService activityExpirationService;

    /**
     * 获取活动过期状态
     */
    @RequestMapping("/status/{activityId}")
    @RequiresPermissions("activity:expiration:info")
    public R getExpirationStatus(@PathVariable("activityId") Long activityId) {
        ActivityExpirationStatusVo status = activityExpirationService.getActivityExpirationStatus(activityId);
        if (status == null) {
            return R.error("活动不存在");
        }
        return R.ok().put("status", status);
    }

    /**
     * 批量获取活动过期状态
     */
    @RequestMapping("/status/batch")
    @RequiresPermissions("activity:expiration:list")
    public R getBatchExpirationStatus(@RequestParam("activityIds") List<Long> activityIds) {
        // TODO: 实现批量获取逻辑
        return R.ok();
    }

    /**
     * 设置活动过期时间
     */
    @SysLog("设置活动过期时间")
    @RequestMapping("/setExpirationTime")
    @RequiresPermissions("activity:expiration:update")
    public R setExpirationTime(@RequestBody Map<String, Object> params) {
        Long activityId = Long.valueOf(params.get("activityId").toString());
        Date expirationTime = null;
        if (params.get("expirationTime") != null) {
            expirationTime = new Date(Long.valueOf(params.get("expirationTime").toString()));
        }
        
        return activityExpirationService.setActivityExpirationTime(activityId, expirationTime);
    }

    /**
     * 延长活动有效期
     */
    @SysLog("延长活动有效期")
    @RequestMapping("/extend")
    @RequiresPermissions("activity:expiration:update")
    public R extendExpiration(@RequestBody Map<String, Object> params) {
        Long activityId = Long.valueOf(params.get("activityId").toString());
        Integer days = Integer.valueOf(params.get("days").toString());
        
        return activityExpirationService.extendActivityExpiration(activityId, days);
    }

    /**
     * 手动更新活动过期状态
     */
    @SysLog("更新活动过期状态")
    @RequestMapping("/updateStatus/{activityId}")
    @RequiresPermissions("activity:expiration:update")
    public R updateExpirationStatus(@PathVariable("activityId") Long activityId) {
        activityExpirationService.updateActivityExpirationStatus(activityId);
        return R.ok();
    }

    /**
     * 批量更新所有活动过期状态
     */
    @SysLog("批量更新活动过期状态")
    @RequestMapping("/batchUpdate")
    @RequiresPermissions("activity:expiration:update")
    public R batchUpdateExpirationStatus() {
        activityExpirationService.batchUpdateExpirationStatus();
        return R.ok();
    }

    /**
     * 获取即将过期的活动列表
     */
    @RequestMapping("/expiringSoon")
    @RequiresPermissions("activity:expiration:list")
    public R getActivitiesExpiringSoon(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        return R.ok().put("activities", activityExpirationService.getActivitiesExpiringSoon(days));
    }

    /**
     * 获取已过期的活动列表
     */
    @RequestMapping("/expired")
    @RequiresPermissions("activity:expiration:list")
    public R getExpiredActivities() {
        return R.ok().put("activities", activityExpirationService.getExpiredActivities());
    }

    /**
     * 发送过期提醒
     */
    @SysLog("发送过期提醒")
    @RequestMapping("/sendReminder/{activityId}")
    public R sendExpirationReminder(@PathVariable("activityId") Long activityId, 
                                   @RequestParam(value = "days", defaultValue = "7") Integer days) {
        return activityExpirationService.sendExpirationReminder(activityId, days);
    }

    /**
     * 发送过期通知
     */
    @SysLog("发送过期通知")
    @RequestMapping("/sendNotification/{activityId}")
    public R sendExpirationNotification(@PathVariable("activityId") Long activityId) {
        return activityExpirationService.sendExpirationNotification(activityId);
    }

    /**
     * 管理端创建续费订单
     */
    @SysLog("管理端创建续费订单")
    @RequestMapping("/createRenewalOrder")
    @RequiresPermissions("activity:expiration:renew")
    public R createRenewalOrder(@Valid @RequestBody RenewalOrderVo renewalOrderVo) {
        return activityExpirationService.createRenewalOrder(renewalOrderVo, getUserId());
    }
}
