<template>
  <div
    :class="isMobilePhone ? '' : 'pc-container'"
    
  >
    <pcheader v-if="!isMobilePhone" />
    <van-search
      v-model="dataForm.name"
      placeholder="请输入您要搜索的考试&问卷关键词"
      show-action
      shape="round"
    >
      <div slot="action" @click="onSearch" class="search-text">搜索</div>
    </van-search>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >

  <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">考试&问卷列表</div>
        </div>
      <van-card
        style="background: white"
        v-for="item in dataList"
        :key="item.id"
      >
        <div slot="title" style="font-size: 18px">{{ item.name }}</div>
        <div slot="price" style="padding-top: 5px; font-size: 14px">
          <van-tag
            style="margin-right: 10px"
            size="medium"
            plain
            type="danger"
            v-if="item.type == 0"
            >考试</van-tag
          >
          <van-tag
            style="margin-right: 10px"
            size="medium"
            plain
            type="primary"
            v-else
            >问卷</van-tag
          >
          <van-tag size="medium" plain type="danger" v-if="item.examType == 0"
            >统一时间答题</van-tag
          >
          <van-tag size="medium" plain type="primary" v-else>随时答题</van-tag>
        </div>
        <div slot="desc" style="padding-top: 5px; font-size: 14px; color: grey">
          <div v-if="item.examType == 0">
            {{ item.examStartTime }} ~ {{ item.examEndTime }}
          </div>
          <div v-else>答题时长：{{ item.examTime }}分钟</div>
        </div>
        <template #num>
          <van-button
            v-if="(!item.examStatus && item.examStatus != 0)"
            size="small"
            round
            type="primary"
            @click="startExam(item.id)"
            >开始答题</van-button
          >
          <van-button style="margin-left: 10px"
            v-else-if="item.examStatus == 0"
            size="small"
            round
            type="info"
            @click="startExam(item.id)"
            >继续答题</van-button
          >
          <van-button style="margin-left: 10px" v-else size="small" round type="info">{{
            examActivityUserStatus[item.examStatus].value + (item.type == 0 ? '(' + item.points + '分)': '')
          }}</van-button>
        </template>
      </van-card>
    </van-list>
  </div>
</template>

<script>
import { examActivityUserStatus } from "@/data/exam";
import { isMobilePhone } from "@/js/validate";
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: {
    pcheader,
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      examActivityUserStatus: examActivityUserStatus,
      openid: undefined,
      activityId: undefined,
      dataForm: {
        name: "",
      },
      loading: false,
      finished: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
    };
  },
  mounted() {
          document.title ="考试&问卷列表";
    this.activityId = this.$route.query.id;
    this.openid = this.$cookie.get("openid");
    this.$wxShare(); //加载微信分享
  },
  methods: {
    onSearch() {
      this.pageIndex = 1;
      this.dataList = [];
      this.getActivityList();
    },
    onLoad() {
      this.checkApply();
      this.getActivityList();
    },
    checkApply() {
      this.$fly
        .get("/pyp/web/activity/activityuserapplyorder/checkApply", {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$store.commit("apply/update", res.isPay);
            if (res.isPay == 1 && res.verifyStatus == 1) {
            } else {
              this.$router.push({
                name: "applyIndex",
                query: {
                  id: this.activityId,
                },
              });
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityList() {
      this.$fly
        .get("/pyp/web/exam/list", {
          page: this.pageIndex,
          limit: this.pageSize,
          activityId: this.activityId,
          name: this.dataForm.name,
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.page.list && res.page.list.length > 0) {
              res.page.list.forEach((e) => {
                this.dataList.push(e);
              });
              this.totalPage = res.page.totalPage;
              this.pageIndex++;
              if (this.totalPage < this.pageIndex) {
                this.finished = true;
              } else {
                this.finished = false;
              }
            } else {
              this.finished = true;
            }
          } else {
            vant.Toast(res.msg);
            this.dataList = [];
            this.totalPage = 0;
            this.finished = true;
          }
        });
    },
    startExam(v) {
      this.$fly
        .get("/pyp/web/exam/startExam", {
          examId: v,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$router.push({
              name: "examDetail",
              query: {
                examActivityUserId: res.examActivityUserId,
                id: this.activityId,
                examId: v,
                token: res.token,
              },
            });
          } else {
            vant.Toast(res.msg);
          }
        });
    },
  },
};
</script>