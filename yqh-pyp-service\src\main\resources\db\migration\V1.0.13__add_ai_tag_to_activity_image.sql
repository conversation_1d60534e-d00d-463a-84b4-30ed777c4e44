-- 为activity_image表添加AI标签字段，支持图片与AI标签的匹配
-- 这样可以根据用户选择的AI标签来筛选对应的图片，提升文案和图片的匹配度

-- 为activity_image表添加AI标签字段
ALTER TABLE `activity_image` ADD COLUMN `ai_tag` varchar(100) DEFAULT NULL COMMENT 'AI标签，用于图片分类匹配（如：男,女,儿童等），多个标签用逗号分隔，为空表示通用图片';

-- 为activity_image表添加索引，提高根据AI标签查询的性能
ALTER TABLE `activity_image` ADD INDEX `idx_activity_ai_tag` (`activity_id`, `ai_tag`);

-- 添加复合索引，优化常用查询场景
ALTER TABLE `activity_image` ADD INDEX `idx_activity_type_tag` (`activity_id`, `type`, `ai_tag`);
