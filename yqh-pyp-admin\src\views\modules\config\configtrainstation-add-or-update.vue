<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
    <el-form-item label="机场名称" prop="stationName">
              <el-input v-model="dataForm.stationName" placeholder="机场名称"></el-input>
          </el-form-item>
    <el-form-item label="机场简称" prop="stationShortName">
              <el-input v-model="dataForm.stationShortName" placeholder="机场简称"></el-input>
          </el-form-item>
    <el-form-item label="机场三字码" prop="stationCode">
              <el-input v-model="dataForm.stationCode" placeholder="机场三字码"></el-input>
          </el-form-item>
    <el-form-item label="城市拼音" prop="stationPinYin">
              <el-input v-model="dataForm.stationPinYin" placeholder="城市拼音"></el-input>
          </el-form-item>
    <el-form-item label="城市名称" prop="cityName">
              <el-input v-model="dataForm.cityName" placeholder="城市名称"></el-input>
          </el-form-item>
    <el-form-item label="是否热门" prop="isHot">
              <el-input v-model="dataForm.isHot" placeholder="是否热门"></el-input>
          </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
                    
          stationName: '',
    
          stationShortName: '',
    
          stationCode: '',
    
          stationPinYin: '',
    
          cityName: '',
    
          isHot: ''
        },
        dataRule: {
          stationName: [
            { required: true, message: '机场名称不能为空', trigger: 'blur' }
          ],
          stationShortName: [
            { required: true, message: '机场简称不能为空', trigger: 'blur' }
          ],
          stationCode: [
            { required: true, message: '机场三字码不能为空', trigger: 'blur' }
          ],
          stationPinYin: [
            { required: true, message: '城市拼音不能为空', trigger: 'blur' }
          ],
          cityName: [
            { required: true, message: '城市名称不能为空', trigger: 'blur' }
          ],
          isHot: [
            { required: true, message: '是否热门不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.getToken();
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/config/configtrainstation/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.stationName = data.configTrainStation.stationName
                this.dataForm.stationShortName = data.configTrainStation.stationShortName
                this.dataForm.stationCode = data.configTrainStation.stationCode
                this.dataForm.stationPinYin = data.configTrainStation.stationPinYin
                this.dataForm.cityName = data.configTrainStation.cityName
                this.dataForm.isHot = data.configTrainStation.isHot
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/config/configtrainstation/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                                                                                                                            'stationName': this.dataForm.stationName,
                            'stationShortName': this.dataForm.stationShortName,
                            'stationCode': this.dataForm.stationCode,
                            'stationPinYin': this.dataForm.stationPinYin,
                            'cityName': this.dataForm.cityName,
                            'isHot': this.dataForm.isHot,
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
                if(data.msg != '不能重复提交') {
                      this.getToken();
                  }
              }
            })
          }
        })
      }
    }
  }
</script>
