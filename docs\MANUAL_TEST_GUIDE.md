# 点评功能手动测试指南

## 测试环境准备

1. 启动后端服务：`yqh-pyp-service`
2. 启动前端服务：`yqh-pyp-front`
3. 准备测试活动ID和用户账号
4. 确保测试账号有足够的转发次数

## 测试步骤

### 1. 抖音点评功能测试

#### 1.1 预览功能测试（不消耗次数）

**步骤**：
1. 打开活动页面
2. 点击"抖音点评"按钮
3. 观察弹窗内容

**预期结果**：
- 弹窗显示"抖音点评内容预览"
- 显示文案内容和配图
- 确认按钮显示"确认跳转（消耗次数）"
- 此时转发次数未减少

#### 1.2 取消操作测试

**步骤**：
1. 在预览弹窗中点击"关闭"按钮
2. 检查转发次数

**预期结果**：
- 弹窗关闭
- 转发次数未减少

#### 1.3 确认跳转测试（消耗次数）

**步骤**：
1. 再次点击"抖音点评"按钮
2. 在预览弹窗中点击"确认跳转（消耗次数）"
3. 观察跳转和次数变化

**预期结果**：
- 显示"正在确认跳转..."加载提示
- 转发次数减少1
- 弹窗关闭并跳转到抖音应用

### 2. 大众点评功能测试

重复上述步骤，测试大众点评功能：
- 点击"大众点评"按钮
- 验证预览、取消、确认流程

### 3. 美团点评功能测试

重复上述步骤，测试美团点评功能：
- 点击"美团点评"按钮
- 验证预览、取消、确认流程

### 4. 携程点评功能测试

重复上述步骤，测试携程点评功能：
- 点击"携程点评"按钮
- 验证预览、取消、确认流程

### 5. 次数不足测试

**步骤**：
1. 将账号转发次数消耗至0
2. 点击任意点评按钮
3. 在预览弹窗中点击"确认跳转"

**预期结果**：
- 显示次数不足的错误提示
- 不执行跳转操作
- 弹窗保持打开状态

### 6. 网络异常测试

**步骤**：
1. 断开网络连接
2. 点击点评按钮并确认跳转

**预期结果**：
- 显示"网络请求失败"错误提示
- 不执行跳转操作

## API测试

### 使用Postman或curl测试

#### 1. 预览API测试

```bash
# 抖音点评预览
GET /pyp/web/activity/review/douyin/preview?activityId=123456

# 大众点评预览
GET /pyp/web/activity/review/dianping/preview?activityId=123456

# 美团点评预览
GET /pyp/web/activity/review/meituan/preview?activityId=123456

# 携程点评预览
GET /pyp/web/activity/review/ctrip/preview?activityId=123456&type=review
```

#### 2. 确认API测试

```bash
# 抖音点评确认
POST /pyp/web/activity/review/douyin/confirm
Content-Type: application/x-www-form-urlencoded
activityId=123456

# 大众点评确认
POST /pyp/web/activity/review/dianping/confirm
Content-Type: application/x-www-form-urlencoded
activityId=123456

# 美团点评确认
POST /pyp/web/activity/review/meituan/confirm
Content-Type: application/x-www-form-urlencoded
activityId=123456

# 携程点评确认
POST /pyp/web/activity/review/ctrip/confirm
Content-Type: application/x-www-form-urlencoded
activityId=123456&type=review
```

## 验证要点

### 功能验证
- [ ] 预览API不消耗次数
- [ ] 确认API消耗次数
- [ ] 取消操作不消耗次数
- [ ] 次数不足时显示错误
- [ ] 网络异常时显示错误

### UI验证
- [ ] 弹窗标题正确显示
- [ ] 确认按钮文本包含"消耗次数"提示
- [ ] 加载状态正确显示
- [ ] 错误提示正确显示

### 兼容性验证
- [ ] 旧版本API仍可正常使用
- [ ] 新旧版本功能一致

## 常见问题

### Q: 预览时显示"暂无内容"
A: 检查活动是否配置了对应平台的文案和图片

### Q: 确认跳转时提示次数不足
A: 检查账号的转发次数余额

### Q: 跳转失败
A: 检查设备是否安装了对应的应用（抖音、大众点评等）

### Q: 网络请求失败
A: 检查网络连接和服务器状态
