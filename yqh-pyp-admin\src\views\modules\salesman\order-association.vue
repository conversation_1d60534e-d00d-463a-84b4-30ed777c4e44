<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.orderSn" placeholder="订单号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.wxUserName" placeholder="微信用户" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.hasAssociation" placeholder="关联状态" clearable>
          <el-option label="已关联" :value="1"></el-option>
          <el-option label="未关联" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-date-picker v-model="dataForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="batchAssociate()" :disabled="dataListSelections.length <= 0">批量关联</el-button>
        <el-button type="warning" @click="batchDisassociate()"
          :disabled="dataListSelections.length <= 0">批量取消关联</el-button>
        <el-button type="success" @click="batchAssociateHistorical()">关联历史订单</el-button>
        <!-- <el-button type="info" @click="validateConsistency()">验证一致性</el-button> -->
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <div class="stats-overview" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.totalOrders || 0 }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.associatedOrders || 0 }}</div>
              <div class="stats-label">已关联订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.unassociatedOrders || 0 }}</div>
              <div class="stats-label">未关联订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.associationRate || '0%' }}</div>
              <div class="stats-label">关联率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <!-- <el-table-column prop="id" header-align="center" align="center" width="80" label="订单ID">
      </el-table-column> -->
      <el-table-column prop="orderSn" header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="userName" header-align="center" align="center" label="微信用户">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.userName }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.mobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="amount" header-align="center" align="center" label="订单金额">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="关联业务员">
        <template slot-scope="scope">
          <div v-if="scope.row.salesmanId">
            <div>{{ scope.row.salesmanName }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.salesmanCode }}</div>
          </div>
          <el-tag v-else type="danger" size="mini">未关联</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createOn" header-align="center" align="center" width="150" label="下单时间">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="订单状态">
        <template slot-scope="scope">
          <el-tag :type="getOrderStatusTagType(scope.row.status)">
            {{ getOrderStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.salesmanId" type="text" size="small"
            @click="associateHandle(scope.row)">关联业务员</el-button>
          <el-button v-else type="text" size="small" @click="changeAssociationHandle(scope.row)">更换业务员</el-button>
          <el-button v-if="scope.row.salesmanId" type="text" size="small"
            @click="disassociateHandle(scope.row)">取消关联</el-button>
          <!-- <el-button type="text" size="small" @click="validateOrderHandle(scope.row)">验证</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 关联业务员弹窗 -->
    <associate-salesman v-if="associateVisible" ref="associateSalesman"
      @refreshDataList="getDataList"></associate-salesman>
  </div>
</template>

<script>
import AssociateSalesman from './order-associate-salesman'

export default {
  data() {
    return {
      dataForm: {
        wxUserId: '',
        salesmanId: '',
        orderSn: '',
        wxUserName: '',
        salesmanName: '',
        hasAssociation: '',
        dateRange: []
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      associateVisible: false,
      overallStats: {}
    }
  },
  components: {
    AssociateSalesman
  },
  activated() {
    this.dataForm.wxUserId = this.$route.query.wxUserId
    this.dataForm.salesmanId = this.$route.query.salesmanId
    this.getDataList()
    this.getOverallStats()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/orderassociation/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'wxUserId': this.dataForm.wxUserId,
          'salesmanId': this.dataForm.salesmanId,
          'orderSn': this.dataForm.orderSn,
          'wxUserName': this.dataForm.wxUserName,
          'salesmanName': this.dataForm.salesmanName,
          'hasAssociation': this.dataForm.hasAssociation,
          'startDate': this.dataForm.dateRange && this.dataForm.dateRange[0],
          'endDate': this.dataForm.dateRange && this.dataForm.dateRange[1]
        })
      }).then(({ data }) => {
        if (data && data.code ===200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },

    // 获取统计数据
    getOverallStats() {
      this.$http({
        url: this.$http.adornUrl('/salesman/orderassociation/stats'),
        params: this.$http.adornParams({
          wxUserId: this.dataForm.wxUserId,
          salesmanId: this.dataForm.salesmanId,
          startDate: this.dataForm.dateRange && this.dataForm.dateRange[0],
          endDate: this.dataForm.dateRange && this.dataForm.dateRange[1]
        }),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code ===200) {
          this.overallStats = data.stats || {}
        }
      })
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },

    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },

    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },

    // 关联业务员
    associateHandle(row) {
      this.associateVisible = true
      this.$nextTick(() => {
        this.$refs.associateSalesman.init(row, 'associate')
      })
    },

    // 更换业务员
    changeAssociationHandle(row) {
      this.associateVisible = true
      this.$nextTick(() => {
        this.$refs.associateSalesman.init(row, 'change')
      })
    },

    // 取消关联
    disassociateHandle(row) {
      this.$confirm('确定要取消此订单的业务员关联吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/orderassociation/disassociate'),
          method: 'post',
          params: this.$http.adornParams({
            rechargeRecordId: row.id
          })
        }).then(({ data }) => {
          if (data && data.code ===200) {
            this.$message({
              message: '取消关联成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 批量关联
    batchAssociate() {
      this.$message.info('批量关联功能开发中...')
    },

    // 批量取消关联
    batchDisassociate() {
      this.$message.info('批量取消关联功能开发中...')
    },

    // 关联历史订单
    batchAssociateHistorical() {
      this.$confirm('此操作将为所有未关联业务员的历史订单自动关联业务员，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/orderassociation/batchAssociateHistorical'),
          method: 'post'
        }).then(({ data }) => {
          if (data && data.code ===200) {
            this.$message({
              message: data.message || '关联完成',
              type: 'success',
              duration: 2000,
              onClose: () => {
                this.getDataList()
                this.getOverallStats()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    // 验证一致性
    validateConsistency() {
      this.$message.info('验证一致性功能开发中...')
    },

    // 验证单个订单
    validateOrderHandle(row) {
      this.$http({
        url: this.$http.adornUrl('/salesman/orderassociation/validate'),
        method: 'get',
        params: this.$http.adornParams({
          rechargeRecordId: row.id
        })
      }).then(({ data }) => {
        if (data && data.code ===200) {
          if (data.valid) {
            this.$message.success(data.message)
          } else {
            this.$message.warning(data.message)
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已取消',
        3: '已退款'
      }
      return statusMap[status] || '未知'
    },

    // 获取订单状态标签类型
    getOrderStatusTagType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'info',
        3: 'danger'
      }
      return typeMap[status] || ''
    }
  }
}
</script>

<style>
.stats-overview .stats-card {
  text-align: center;
  padding: 20px;
}

.stats-overview .stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stats-overview .stats-label {
  font-size: 14px;
  color: #666;
}
</style>
