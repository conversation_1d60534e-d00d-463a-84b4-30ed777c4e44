<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" @closed="closeDialog"
    :visible.sync="visible">
    <el-table size="mini" :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column show-overflow-tooltip prop="orderSn" header-align="center" align="center" label="订单号">
      </el-table-column>
      <el-table-column prop="contact" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="联系方式">
      </el-table-column>
      <el-table-column prop="roomType" header-align="center" align="center" label="房间类型">
        <div slot-scope="scope">
          <el-tag type="primary"
            :class="'tag-color-mini tag-color-' + (scope.row.roomType)">{{ 
              roomType[scope.row.roomType].value}}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="inDate" width="85px" header-align="center" align="center" label="入住日期">
      </el-table-column>
      <el-table-column prop="outDate" width="85px" header-align="center" align="center" label="退房日期">
      </el-table-column>
      <el-table-column width="75px" prop="dayNumber" header-align="center" align="center" label="总天数">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="入住状态">
        <div slot-scope="scope">
          <el-tag type="primary"
            :class="'tag-color-mini tag-color-' + (scope.row.status)">{{ roomAssignStatus[scope.row.status].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="80" label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.status == 1" size="small"
            @click="select(scope.row.id)">取消入住</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭页面</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { orderStatus } from '@/data/common'
import { roomAssignStatus, roomType, roomTypeFjsd } from "@/data/room.js";
export default {
  data() {
    return {
      appid: '',
      roomType,
      roomTypeFjsd,
      roomAssignStatus,
      orderStatus,
      visible: false,
      dataForm: {
        contact: '',
        mobile: '',
        roomId: '',
        numberId: '',
      },
      dataList: [],
      dataListLoading: false,
    }
  },
  methods: {
    init(numberId) {
      this.appid = this.$cookie.get("appid");
      this.dataForm.numberId = numberId
      this.visible = true
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      this.$http({
        url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/findByNumberId/${this.dataForm.numberId}`),
        method: 'get',
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.dataList = data.result
        }
      })
    },
    // 选择酒店
    select(v) {
      this.$confirm(`确认取消入住?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/hotel/hotelactivityroomassign/cancel`),
          method: 'get',
          params: this.$http.adornParams({
            'id': v,
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    closeDialog() {
      this.$emit('refreshDataList')
    },
    isImageUrl(url) {
      return url && /.*\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)/.test(url)
    }
  }
}
</script>
