<template>
  <div class="container">
    <div style="position: relative">
      <div style="
          background-color: rgba(32, 33, 36, 0.5);
          border-radius: 30px;
          padding: 0.2rem 0.5rem;
          font-size: 0.12rem;
          display: flex;
          position: absolute;
          z-index: 999;
          align-items: center;
          justify-content: center;
          font-size: 0.15rem;
          right: 10px;
          top: 10px;
        ">
        <van-icon size="20" color="white" name="eye-o" />
        <span style="color: white; margin-left: 15px; font-size: 14px">{{
          livesDetail.virtualPvCount
        }}</span>
      </div>
      <div @click="refresh" style="
          background-color: rgba(32, 33, 36, 0.5);
          border-radius: 30px;
          padding: 0.2rem 0.5rem;
          font-size: 0.12rem;
          display: flex;
          position: absolute;
          z-index: 999;
          align-items: center;
          justify-content: center;
          font-size: 0.15rem;
          right: 100px;
          top: 10px;
        ">
        <img style="width: 20px; height: 20px" src="@/assets/refresh.png" alt="" />
      </div>
      <div v-if="activityInfo.liveNeedApply" style="
          background-color: rgba(32, 33, 36, 0.5);
          border-radius: 30px;
          padding: 0.2rem 0.5rem;
          font-size: 0.12rem;
          display: flex;
          position: absolute;
          z-index: 999;
          align-items: center;
          justify-content: center;
          font-size: 0.15rem;
          left: 20px;
          top: 10px;
        ">
        <span style="color: white; margin-left: 5px; font-size: 14px">累计观看：{{ userInfo.hours | toUserLookTime }}</span>
      </div>
      <!-- v-show="$refs.VueAliplayerV2.getStatus() == 'play' || $refs.VueAliplayerV2.getStatus() == 'pause' || $refs.VueAliplayerV2.getStatus() == 'playing'" -->
      <img @click="endedHandle" v-if="livesDetail.playStatus == 2" style="
          position: absolute;
          z-index: 999;
          left: 45px;
          bottom: 8px;
          width: 24px;
          height: 24px;
        " src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20221006/bb0e2c6f4b38470db8c54edf3e7b63fd.png" alt="" />
      <!-- <div v-if="yugao" class="nostart">直播还未开始</div> -->
      <vue-aliplayer-v2 class="aliplayer" :source="source" ref="VueAliplayerV2" :options="options"></vue-aliplayer-v2>
      <div @click="play" style="
          z-index: 99999;
          position: absolute;
          left: 30px;
          bottom: 50px;
          display: block;
          background: url('https://g.alicdn.com/de/prismplayer/2.9.7/skins/default/img/bigplay.png')
            no-repeat;
          background-size: contain;
          width: 64px;
          height: 64px;
        " v-show="livesDetail.playStatus != 0 &&
          $refs.VueAliplayerV2 &&
          $refs.VueAliplayerV2.getStatus() == 'pause'
          "></div>
    </div>
    <!-- 多会场 -->
    <div v-if="liveRoomList.length > 1" class="mult">
      <div @click="selectRoom(item)" v-for="item in liveRoomList" :key="item.id" class="mult-item" :style="{
        backgroundImage:
          'url(' + (item.cover ? item.cover : options.cover) + ')',
        backgroundSize: 'cover',
        border: placeId == item.id ? '2px solid green' : 'none',
      }">
        <div style="
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, transparent, #000);
          "></div>
        <div class="mult-item-text">
          <img v-if="placeId == item.id"
            src="http://conferencehuizhan.oss-cn-beijing.aliyuncs.com/20211115/6139242f737f439f900217357b1715fb.gif"
            style="height: 35px; width: 34px" alt="" />
          <div v-else src="" style="height: 35px; width: 34px" alt="" />
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <!-- 倒计时-模板1 -->
    <van-cell-group style="margin-bottom: 3px" v-if="livesDetail.isCountdown && dateCompare > 0">
      <van-cell class="coutdown" title="直播倒计时">
        <van-count-down :time="dateCompare" format="DD 天 HH 时 mm 分 ss 秒" slot="default" />
      </van-cell>
    </van-cell-group>
    <van-notice-bar v-if="livesDetail.advice" left-icon="volume-o" :text="livesDetail.advice" />
    <div id="tabs" class="tabs">
      <van-tabs animated @change="change" v-if="tabConfigs.length > 0">
        <van-tab v-for="item in tabConfigs" :key="item.id" :title="item.name">
          <!-- 自定义内容 -->
          <div class="block1" v-if="item.type == 0 && item.content" v-html="item.content" @click="showImg($event)">
          </div>
          <!-- 会议议程 -->
          <div class="block" v-if="item.type == 2">
            <!-- <schedules :placeId="placeId" :activityId="activityId" /> -->
            <iframe class="frame" :src="baseUrl + '#/schedules/simpleIndex?id=' + activityId" frameborder="0"></iframe>
          </div>
          <!-- 会议嘉宾 -->
          <div class="block" v-if="item.type == 3">
            <!-- <experts :activityId="activityId" /> -->
            <iframe class="frame" :src="baseUrl + '#/schedules/experts?id=' + activityId" frameborder="0"></iframe>
          </div>
          <!-- 考试&问卷 -->
          <div class="block" v-if="item.type == 5">
            <!-- <exam :activityId="activityId" /> -->
            <iframe class="frame" :src="baseUrl + '#/exam/index?id=' + activityId" frameborder="0"></iframe>
          </div>
          <!-- 展商 -->
          <div class="block" v-if="item.type == 6">
            <!-- <exam :activityId="activityId" /> -->
            <iframe class="frame" :src="baseUrl + '#/merchant/index?id=' + activityId" frameborder="0"></iframe>
          </div>
          <!-- 录播回看 -->
          <div class="block" v-if="item.type == 7">
            <videolist v-model="videoIndex" :items="placeActivityVideoEntities" @item-selected="handleItemSelected" />
          </div>
          <!-- 聊天室 -->
          <div class="block" v-if="item.type == 4">
            <chat ref="chat" @showImage="showActionSheet = true" :pushKey="livesDetail.pushKey"
              :activityId="activityId" />
          </div>
          <!-- 自定义链接 -->
          <iframe :src="item.content" class="frame" frameborder="0" v-if="item.type == 1"></iframe>
        </van-tab>
      </van-tabs>
    </div>
    <van-dialog theme="round-button" v-model="show" title="温馨提示">
      <div slot="default">
        <div class="content" v-html="livesDetail.notify"></div>
      </div>
    </van-dialog>
    <img class="back" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    <div class="ball" v-if="channelInfo && channelInfo.hour > 0" @click="hourShow = true">
      <div class="wave"></div>
      <div class="wave-mask" ref="progress"></div>
      <div class="percent" :style="{ color: progress > 50 ? 'white' : '' }">
        {{ progress }}%
      </div>
    </div>
    <van-dialog theme="round-button" v-model="hourShow" title="观看时长统计" @confirm="hourShow = false">
      <div class="text-center padding">
        <div>需要观看时长：{{ channelInfo.hour }}分钟</div>
        <div>已观看时长：{{ parseInt(userInfo.hours / 60) }}分钟</div>
      </div>
    </van-dialog>
    <van-action-sheet v-model="showActionSheet" :actions="actions" cancel-text="取消" close-on-click-action
      @select="select" />
  </div>
</template>

<script>
import videolist from "./component/video.vue";
import chat from "./chat.vue";
// import schedules from "./component/schedules.vue";
// import experts from "./component/experts.vue";
// import exam from "./component/exam.vue";
import date from "@/js/date.js";
import playStatus from "@/data/playStatus.json";
import VueAliplayerV2 from "vue-aliplayer-v2";
import deviceVersion from "@/js/deviceVersion.js";
export default {
  data() {
    return {
      showActionSheet: false,
      actions: [{ name: "撤回" }, { name: "预览图片" }],
      progress: 0,
      videoIndex: 0,
      hourShow: false,
      show: false,
      baseUrl: "",
      startTime: null,
      openid: undefined,
      activityId: undefined,
      placeId: undefined,
      playStatus: playStatus,
      yugao: false,
      flag: false,
      livesDetail: {},
      activityInfo: {},
      userInfo: {},
      options: {
        source: "",
        isLive: true, //切换为直播流的时候必填
        format: "m3u8", //切换为直播流的时候必填
        height: "230px",
        width: "100%",
        cover: "",
        liveStartTime: "",
        autoplay: true,
      },
      dateCompare: 0,
      tabConfigs: [],
      placeActivityVideoEntities: [],
      countTimer: "",
      adviceTimer: "",
      channelInfo: {},
      liveRoomList: [],
    };
  },
  components: {
    chat,
    videolist,
    VueAliplayerV2,
    // schedules,
    // experts,
    // exam,
  },
  filters: {
    playStatusFilter(v) {
      let data = playStatus.filter((item) => item.key === v);
      if (data.length >= 1) {
        return data[0].value;
      }
    },
    toUserLookTime(v) {
      return date.toUserLook(v);
    },
  },
  mounted() {
    this.baseUrl = window.location.origin + window.location.pathname;
    localStorage.setItem("logFlag", 0);
    let startTime = new Date().getTime();
    localStorage.setItem("startTime", startTime);
    this.activityId = this.$route.query.id;
    this.placeId = this.$route.query.detailId;
    this.openid = this.$cookie.get("openid");
    this.getActivityInfo();
    document.getElementById("tabs").style.height =
      document.getElementsByClassName("container")[0].clientHeight - 280 + "px";
    this.countTimer = setInterval(() => {
      console.log(
        "---------------------统计学时定时器执行---------------------"
      );
      this.unloadHandler();
    }, 300 * 1000);
    this.adviceTimer = setInterval(() => {
      console.log(
        "--------------------直播间通知定时器执行---------------------"
      );
      this.getAdvice();
    }, 300 * 1000);
    // 判断是否是微信浏览器
    let ua = window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);
    ua = ua !== null ? ua[0] : null;
    // if (ua === "micromessenger") {
    window.addEventListener("unload", (e) => this.unloadHandler(e));
    // } else {
    // 监听普通页面离开事件
  },
  destroyed() {
    this.unloadHandler();
    this.countTimer = clearInterval(this.countTimer);
    this.adviceTimer = clearInterval(this.adviceTimer);
    // 销毁监听的关闭事件
    window.removeEventListener("unload", (e) => this.unloadHandler(e));
  },
  methods: {
    handleItemSelected(index) {
      console.log("Selected index:", index);
      this.getVideoPlayInfo(this.placeActivityVideoEntities[index].fileId);
    },
    checkApply() {
      this.$fly
        .get("/pyp/web/activity/activityuserapplyorder/checkApply", {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.isPay = res.isPay;
            this.$store.commit("apply/update", this.isPay);

            if (res.isPay == 1 && res.verifyStatus == 1) {
            } else {
              this.$router.push({
                name: "applyIndex",
                query: {
                  id: this.activityId,
                },
              });
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.activityInfo = res.activity;
            if (this.activityInfo.liveNeedApply) {
              this.checkApply();
            }
            this.getUserActivityInfo();
            document.title = this.activityInfo.name;
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/20220614/9c9298f1d3474660977a02c8a84aafa8.png";
            this.getLivesInfo();
            this.getLiveRoomList();
            let startTime = date.formatDate.format(
              new Date(this.activityInfo.startTime),
              "yyyy年MM月dd日"
            );
            let endTime = date.formatDate.format(
              new Date(this.activityInfo.endTime),
              "MM月dd日"
            );
            if (this.activityId == 1791808208781807618) {
              let desc = "易企化财务软件视频演示";
              this.$wxShare(
                "" + this.activityInfo.name,
                this.activityInfo.shareUrl
                  ? this.activityInfo.shareUrl
                  : this.activityInfo.mobileBanner.split(",")[0],
                desc
              ); //加载微信分享
            } else {
              if (startTime.includes(endTime)) {
                let desc =
                  "时间:" + startTime + "\n地址:" + this.activityInfo.address;
                this.$wxShare(
                  "正在直播-" + this.activityInfo.name,
                  this.activityInfo.shareUrl
                    ? this.activityInfo.shareUrl
                    : this.activityInfo.mobileBanner.split(",")[0],
                  desc
                ); //加载微信分享
              } else {
                let desc =
                  "时间:" +
                  startTime +
                  "-" +
                  endTime +
                  "\n地址:" +
                  this.activityInfo.address;
                this.$wxShare(
                  "正在直播-" + this.activityInfo.name,
                  this.activityInfo.shareUrl
                    ? this.activityInfo.shareUrl
                    : this.activityInfo.mobileBanner.split(",")[0],
                  desc
                ); //加载微信分享
              }
            }
          } else {
            vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    getLiveRoomList() {
      this.$fly
        .get(
          `/pyp/web/place/placeactivity/findByActivityIdLive/${this.activityId}`
        )
        .then((res) => {
          if (res.code == 200) {
            this.liveRoomList = res.result;
          } else {
            vant.Toast(res.msg);
            this.liveRoomList = [];
          }
        });
    },
    getLivesInfo() {
      this.$fly
        .get(`/pyp/web/place/placeactivity/info/${this.placeId}`)
        .then((res) => {
          if (res.code == 200) {
            this.livesDetail = res.result;
            // 如果有直播间通知，显示直播间通知
            if (this.livesDetail.notify) {
              this.show = true;
              if (this.livesDetail.notifyTime) {
                let t1 = this.livesDetail.notifyTime;
                let dateEnd = new Date(t1.replace(/-/g, "/"));
                let dateBegin = new Date(); //当前时间数据
                let dateCompare = dateEnd.getTime() - dateBegin.getTime();
                console.log(dateCompare);
                this.adviceTimer = setTimeout(() => {
                  this.show = true;
                }, dateCompare);
              }
            }
            this.options.liveStartTime = this.livesDetail.liveTime;
            this.options.cover = this.livesDetail.cover
              ? this.livesDetail.cover
              : this.activityInfo.shareUrl
                ? this.activityInfo.shareUrl
                : this.activityInfo.mobileBanner.split(",")[0];
            // 处理倒计时
            let t1 = this.livesDetail.liveTime;
            if (t1) {
              let dateEnd = new Date(t1.replace(/-/g, "/"));
              let dateBegin = new Date(); //当前时间数据
              let dateCompare = dateEnd.getTime() - dateBegin.getTime();
              this.dateCompare = dateCompare > 0 ? dateCompare : 0;
            }
            if (this.livesDetail.playStatus == 1) {
              // 如果是直播
              this.options.source = this.livesDetail.playUrl;
              // this.options.source = this.livesDetail.playRtmpUrl;
              this.options.isLive = true;
              this.options.preload = true;
              this.options.autoplay = true;
              this.options.skinLayout = [
                {
                  name: "bigPlayButton",
                  align: "blabs",
                  x: 30,
                  y: 50,
                },
                {
                  name: "errorDisplay",
                  align: "tlabs",
                  x: 0,
                  y: 0,
                },
                {
                  name: "infoDisplay",
                  align: "cc",
                },
                {
                  name: "controlBar",
                  align: "blabs",
                  x: 0,
                  y: 0,
                  children: [
                    {
                      name: "liveDisplay",
                      align: "tlabs",
                      x: 15,
                      y: 6,
                    },
                    {
                      name: "fullScreenButton",
                      align: "tr",
                      x: 10,
                      y: 10,
                    },
                    {
                      name: "subtitle",
                      align: "tr",
                      x: 15,
                      y: 12,
                    },
                    {
                      name: "setting",
                      align: "tr",
                      x: 15,
                      y: 12,
                    },
                    {
                      name: "volume",
                      align: "tr",
                      x: 5,
                      y: 10,
                    },
                  ],
                },
              ];
            } else if (this.livesDetail.playStatus == 2) {
              // 如果是录播
              this.getVideo();
              this.options.isLive = false;
              this.options.autoplay = true;
              this.options.skinLayout = [
                {
                  name: "bigPlayButton",
                  align: "blabs",
                  x: 30,
                  y: 50,
                },
                {
                  name: "H5Loading",
                  align: "cc",
                },
                {
                  name: "errorDisplay",
                  align: "tlabs",
                  x: 0,
                  y: 0,
                },
                {
                  name: "infoDisplay",
                },
                {
                  name: "tooltip",
                  align: "blabs",
                  x: 0,
                  y: 56,
                },
                {
                  name: "thumbnail",
                },
                {
                  name: "controlBar",
                  align: "blabs",
                  x: 0,
                  y: 0,
                  children: [
                    {
                      name: "progress",
                      align: "blabs",
                      x: 0,
                      y: 44,
                    },
                    {
                      name: "playButton",
                      align: "tl",
                      x: 15,
                      y: 12,
                    },
                    {
                      name: "timeDisplay",
                      align: "tl",
                      x: 40,
                      y: 7,
                    },
                    {
                      name: "nextButton",
                      align: "tl",
                      x: 10,
                      y: 26,
                    },
                    {
                      name: "fullScreenButton",
                      align: "tr",
                      x: 10,
                      y: 12,
                    },
                    {
                      name: "setting",
                      align: "tr",
                      x: 15,
                      y: 12,
                    },
                    {
                      name: "volume",
                      align: "tr",
                      x: 5,
                      y: 10,
                    },
                  ],
                },
              ];
            } else {
              // 如果是预告
              this.options.source = "";
              this.yugao = true;
            }
            this.getTabConfig();
            this.countPlace();
          } else {
            vant.Toast(res.msg);
            this.livesDetail = {};
          }
        });
    },
    getAdvice() {
      this.$fly
        .get(`/pyp/web/place/placeactivity/advice/${this.placeId}`)
        .then((res) => {
          if (res.code == 200) {
            this.livesDetail.advice = res.result;
          }
        });
    },
    countPlace() {
      this.$fly
        .post(`/pyp/web/place/placeactivity/count`, {
          activityId: this.activityId,
          placeId: this.placeId,
          mulity: this.livesDetail.mulity ? this.livesDetail.mulity : 1,
          device: deviceVersion.getVersion(),
        })
        .then((res) => { });
    },
    getTabConfig() {
      this.$fly
        .get(`/pyp/web/place/placelivetabconfig/findByActivityIdAndPlaceId`, {
          activityId: this.activityId,
          placeId: this.placeId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.tabConfigs = res.result;
            // 判断是否有聊天室
            if (this.tabConfigs.filter((q) => q.type == 2) != null) {
              // this.pageIndex = 0;
            }
          } else {
            vant.Toast(res.msg);
            this.tabConfigs = [];
          }
        });
    },
    getVideo() {
      this.$fly
        .get(`/pyp/web/place/placeactivityvideo/findByPlaceId/${this.placeId}`)
        .then((res) => {
          if (res.code == 200) {
            this.placeActivityVideoEntities = res.result;
            if (this.placeActivityVideoEntities.length > 0) {
              this.videoIndex = 0;
              this.getVideoPlayInfo(this.placeActivityVideoEntities[0].fileId);
            }
          } else {
            vant.Toast(res.msg);
            this.placeActivityVideoEntities = [];
          }
        });
    },
    getUserActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activityuser/meMine`, {
          activityId: this.activityId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.userInfo = res.result;
            if (this.userInfo.applyActivityChannelConfigId) {
              this.getApplyActivityChannelConfig(
                this.userInfo.applyActivityChannelConfigId
              );
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    getApplyActivityChannelConfig(v) {
      this.$fly
        .get(`/pyp/web/apply/applyactivitychannelconfig/info/${v}`)
        .then((res) => {
          if (res.code == 200) {
            this.channelInfo = res.applyActivityChannelConfig;
            this.countHour();
          }
        });
    },
    getVideoPlayInfo(vId) {
      this.$fly
        .get(`/pyp/web/place/placeactivityvideo/getVideoPlayInfo/${vId}`)
        .then((res) => {
          if (res.code == 200) {
            this.options.source = res.result;
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    endedHandle() {
      this.videoIndex = this.videoIndex + 1;
      if (this.videoIndex < this.placeActivityVideoEntities.length) {
        // 如果当前节点小于所有视频，自动下一个
        this.getVideoPlayInfo(
          this.placeActivityVideoEntities[this.videoIndex].fileId
        );
      } else {
        vant.Toast("视频已全部播放完毕");
      }
    },
    // next() {
    //   this.$refs.VueAliplayerV2.play();
    // },
    play() {
      this.$refs.VueAliplayerV2.play();
    },
    pause() {
      this.$refs.VueAliplayerV2.pause();
    },
    replay() {
      this.$refs.VueAliplayerV2.replay();
    },
    getStatus() {
      const status = this.$refs.VueAliplayerV2.getStatus();
      console.log(`getStatus:`, status);
      alert(`getStatus:${status}`);
    },
    getCurrentTime() {
      let time = this.$refs.VueAliplayerV2.getCurrentTime();
      console.log(time);
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
    cmsTurnBack() {
      if (this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({
          name: "cmsIndex",
          query: { id: this.activityInfo.id },
        });
      }
    },
    unloadHandler(e) {
      if (this.livesDetail.playStatus != 0 && this.activityInfo.liveNeedApply) {
        // 这里调用时间函数确保记录时间的准确性
        let flag = localStorage.getItem("logFlag");
        let startTime = localStorage.getItem("startTime");
        let endTime = new Date().getTime();
        let count = (endTime - startTime) / 1000;
        if (flag == 0 && count > 5) {
          localStorage.setItem("logFlag", 1);
          // const blob = new Blob([JSON.stringify({
          //   activityId: this.activityId,
          //   placeId: this.placeId,
          //   device: deviceVersion.getVersion(),
          //   count: count
          // })], {
          //   type: 'application/json; charset=UTF-8',
          // });
          // navigator.sendBeacon('/pyp/web/place/placeactivityhourlog/count', blob);
          this.$fly
            .post(`/pyp/web/place/placeactivityhourlog/count`, {
              activityId: this.activityId,
              placeId: this.placeId,
              device: deviceVersion.getVersion(),
              count: count,
            })
            .then((res) => {
              if (res.code == 200 && res.hours) {
                this.userInfo.hours = res.hours;
                this.countHour();
              }
            });
          localStorage.setItem("logFlag", 0);
          localStorage.setItem("startTime", endTime);
        }
      }
    },
    countHour() {
      if (this.channelInfo.hour > 0) {
        if (this.userInfo.hours < this.channelInfo.hour * 60) {
          this.progress = Math.min(
            parseInt(
              (this.userInfo.hours / (this.channelInfo.hour * 60)) * 100
            ),
            100
          );
        } else {
          this.progress = 100;
        }
        this.$nextTick(() => {
          this.$refs.progress.style.top = 20 - this.progress / 2 + "px";
        });
      }
    },

    select(v, index) {
      this.$refs.chat[0].select(index);
    },
    refresh() {
      location.reload();
    },

    async selectRoom(v) {
      if (this.placeId != v.id) {
        this.$router.push({
          name: "livesDetail",
          query: { detailId: v.id, id: this.activityId },
        });
        this.unloadHandler();
        this.countTimer = clearInterval(this.countTimer);
        this.adviceTimer = clearInterval(this.adviceTimer);
        // 销毁监听的关闭事件
        window.removeEventListener("unload", (e) => this.unloadHandler(e));
        this.refresh();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  height: 100%;
}

@media screen and (min-width: 760px) {
  .container {
    width: 400px;
    margin: auto auto;
  }
}

.nostart {
  position: absolute;
  top: 70px;
  z-index: 999;
  text-align: center;
  width: 100%;
  font-weight: bold;
  color: red;
  font-size: 24px;
}

.coutdown {
  .van-count-down {
    line-height: 24px;
  }

  .van-cell__title,
  .van-cell__value {
    text-align: center;
  }
}

.aliplayer /deep/.prism-info-display {
  padding: 0px;
}

.prism-player {
  background-color: white;
}

// .block {
//   background: white;
//   box-sizing: border-box;
//   overflow: auto;
//   word-break: break-word;
//   /deep/ img,
//   /deep/ video {
//     width: 100%;
//     height: auto;
//   }
// }
.block1 {
  padding: 0 5px;
  background: white;
  box-sizing: border-box;
  overflow: auto;
  word-break: break-word;

  /deep/ img,
  /deep/ video {
    width: 100%;
    height: auto;
  }
}

.block {
  height: 100%;
  position: relative;
}

iframe {
  width: 100%;
  height: 100%;
}

.tabs {
  flex: 1;
  overflow: hidden;

  .van-tabs {
    margin-top: 0;
    height: 100%;
  }

  /deep/ .van-tabs__content {
    /*padding: 17px 13px;*/
    // height: 100%;
    height: ~"calc(100% - 44px)";
  }

  /deep/ .van-tab__pane-wrapper--inactive {
    overflow: hidden;
  }

  /deep/ .van-tab__pane,
  .van-tab__pane-wrapper {
    overflow-y: auto;
    height: 100%;
  }

  /deep/ .van-tab__pane-wrapper:nth-of-type {
    padding: 17px 13px;
  }
}

.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  height: 350px;
  overflow: auto;

  /deep/ p {
    width: 100%;
  }

  /deep/ img {
    width: 100%;
    height: auto;
  }
}

.ball {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ffffff;
  position: absolute;
  left: 0;
  bottom: 100px;
  overflow: hidden;
  box-sizing: border-box;

  .wave {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(130deg,
        rgba(30, 141, 244, 1) 0%,
        rgba(69, 91, 242, 1) 100%);
    border-radius: 50%;
  }

  .percent {
    position: absolute;
    text-align: center;
    font-size: 12px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: rgba(69, 91, 242, 1);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .wave-mask {
    position: absolute;
    width: 100px;
    height: 100px;
    top: 20px;
    left: 50%;
    border-radius: 40%;
    background: white;
    transform: translate(-50%, -70%) rotate(0);
    animation: toRotate 10s linear -5s infinite;
  }

  @keyframes toRotate {
    50% {
      transform: translate(-50%, -70%) rotate(180deg);
    }

    100% {
      transform: translate(-50%, -70%) rotate(360deg);
    }
  }
}

.container /deep/ .prism-cover {
  background-size: auto;
  background-repeat: round;
  background-position: inherit;
}

.mult {
  height: 70px;
  width: 100%;
  display: -webkit-box;
  overflow: hidden;
  overflow-x: scroll;
  white-space: nowrap;
  background-color: black;
  position: relative;
}

.mult-item {
  height: 64px;
  width: 120px;
  margin: 3px;
  background-color: gray;
  overflow: hidden;
  border-radius: 5px;
}

.mult-item-text {
  position: absolute;
  bottom: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mult-item-text span {
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 75px;
}
</style>