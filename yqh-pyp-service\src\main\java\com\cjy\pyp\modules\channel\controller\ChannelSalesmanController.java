package com.cjy.pyp.modules.channel.controller;

import com.cjy.pyp.common.annotation.SysLog;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.validator.ValidatorUtils;
import com.cjy.pyp.modules.channel.utils.ChannelPermissionUtils;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 渠道业务员管理控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/channel/salesman")
@Api(tags = "渠道业务员管理")
public class ChannelSalesmanController extends AbstractController {
    
    @Autowired
    private SalesmanService salesmanService;
    
    @Autowired
    private ChannelPermissionUtils channelPermissionUtils;

    /**
     * 列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("channel:salesman:list")
    @ApiOperation(value = "渠道业务员列表", notes = "")
    public R list(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        params.put("appid", appid);
        
        // 渠道管理员只能查看自己渠道的业务员
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        List<SalesmanEntity> salesmanList = salesmanService.queryPageWithStats(params);
        return R.okList(salesmanList);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    // @RequiresPermissions("channel:salesman:info")
    @ApiOperation(value = "业务员信息", notes = "")
    public R info(@PathVariable("id") Long id) {
        // 检查权限
        if (!channelPermissionUtils.canAccessSalesman(getUser(), id)) {
            return R.error("无权限访问该业务员信息");
        }
        
        SalesmanEntity salesman = salesmanService.getById(id);
        return R.ok().put("salesman", salesman);
    }

    /**
     * 检查业务员编号是否存在
     */
    @RequestMapping("/checkCode")
    @RequiresPermissions("channel:salesman:list")
    @ApiOperation(value = "检查业务员编号", notes = "")
    public R checkCode(@RequestParam String code,
                       @RequestParam(required = false) Long excludeId,
                       @CookieValue String appid) {
        boolean exists = salesmanService.existsByCode(code, appid, excludeId);
        return R.ok().put("exists", exists);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    // @RequiresPermissions("channel:salesman:save")
    @SysLog("渠道管理员保存业务员")
    @ApiOperation(value = "保存业务员", notes = "")
    public R save(@RequestBody SalesmanEntity salesman, @CookieValue String appid) {
        ValidatorUtils.validateEntity(salesman);
        
        // 设置应用ID
        salesman.setAppid(appid);
        
        // 渠道管理员只能在自己的渠道下创建业务员
        if (channelPermissionUtils.isChannelAdmin(getUserId())) {
            if (getUser().getChannelId() == null) {
                return R.error("渠道管理员必须关联渠道");
            }
            
            // 如果没有指定渠道，默认为当前管理员的渠道
            if (salesman.getChannelId() == null) {
                salesman.setChannelId(getUser().getChannelId());
            } else {
                // 检查是否有权限在指定渠道创建业务员
                if (!channelPermissionUtils.canManageChannel(getUser(), salesman.getChannelId())) {
                    return R.error("无权限在该渠道创建业务员");
                }
            }
        }
        
        // 检查业务员编号是否重复
        if (salesmanService.existsByCode(salesman.getCode(), appid, null)) {
            return R.error("业务员编号已存在");
        }
        
        // 检查手机号是否重复
        if (salesmanService.existsByMobile(salesman.getMobile(), appid, null)) {
            return R.error("手机号已存在");
        }

        // 计算层级
        salesman.setLevel(salesmanService.calculateLevel(salesman.getParentId()));

        salesmanService.save(salesman);

        // 如果有上级，更新上级的层级信息
        if (salesman.getParentId() != null) {
            salesmanService.updateLevel(salesman.getParentId());
        }

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    // @RequiresPermissions("channel:salesman:update")
    @SysLog("渠道管理员修改业务员")
    @ApiOperation(value = "修改业务员", notes = "")
    public R update(@RequestBody SalesmanEntity salesman, @CookieValue String appid) {
        ValidatorUtils.validateEntity(salesman);
        
        // 检查权限
        if (!channelPermissionUtils.canAccessSalesman(getUser(), salesman.getId())) {
            return R.error("无权限修改该业务员信息");
        }
        
        // 渠道管理员不能将业务员转移到其他渠道
        if (channelPermissionUtils.isChannelAdmin(getUserId())) {
            SalesmanEntity existingSalesman = salesmanService.getById(salesman.getId());
            if (existingSalesman != null && !existingSalesman.getChannelId().equals(salesman.getChannelId())) {
                if (!channelPermissionUtils.canManageChannel(getUser(), salesman.getChannelId())) {
                    return R.error("无权限将业务员转移到该渠道");
                }
            }
        }
        
        // 检查业务员编号是否重复
        if (salesmanService.existsByCode(salesman.getCode(), appid, salesman.getId())) {
            return R.error("业务员编号已存在");
        }
        
        // 检查手机号是否重复
        if (salesmanService.existsByMobile(salesman.getMobile(), appid, salesman.getId())) {
            return R.error("手机号已存在");
        }
        
        // 重新计算层级
        salesman.setLevel(salesmanService.calculateLevel(salesman.getParentId()));
        
        salesmanService.updateById(salesman);
        
        // 更新层级信息
        salesmanService.updateLevel(salesman.getId());
        if (salesman.getParentId() != null) {
            salesmanService.updateLevel(salesman.getParentId());
        }
        
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    // @RequiresPermissions("channel:salesman:delete")
    @SysLog("渠道管理员删除业务员")
    @ApiOperation(value = "删除业务员", notes = "")
    public R delete(@RequestBody Long[] ids) {
        // 检查权限
        for (Long id : ids) {
            if (!channelPermissionUtils.canAccessSalesman(getUser(), id)) {
                return R.error("无权限删除业务员ID: " + id);
            }
        }
        
        salesmanService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 获取渠道业务员统计
     */
    @RequestMapping("/stats")
    // @RequiresPermissions("channel:salesman:list")
    @ApiOperation(value = "渠道业务员统计", notes = "")
    public R stats(@CookieValue String appid) {
        // 渠道管理员只能查看自己渠道的统计
        List<Long> accessibleChannelIds = channelPermissionUtils.getAccessibleChannelIds(getUser());
        
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("appid", appid);
        if (accessibleChannelIds != null) {
            params.put("channelIds", accessibleChannelIds);
        }
        
        // 这里可以调用统计服务获取数据
        // Map<String, Object> stats = salesmanService.getStatsByCondition(params);
        
        return R.ok().put("stats", new java.util.HashMap<>());
    }
}
