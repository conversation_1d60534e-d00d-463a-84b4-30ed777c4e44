<template>
  <div :class="isMobilePhone ? 'page' : 'page pc-container'">
    <pcheader v-if="!isMobilePhone" />
    <div v-if="activityInfo.showApplyNumber"
      style="background-color: rgba(32, 33, 36, 0.5);border-radius: 30px;padding: 0.2rem 0.5rem;font-size: 0.12rem;display: flex;position: absolute;z-index: 999;align-items: center;justify-content: center;    font-size: .15rem;right: 10px;top: 10px;">

      <span style="color:white;margin-left: 5px;font-size: 14px;">报名人数：{{ applyNumber }}人</span>
    </div>
    <!-- <div v-show="channelVisible"> -->
    <div>
      <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">报名通道选择</div>
      </div>
      <van-radio-group v-model="channelId">
        <van-cell-group inset>
          <van-cell v-for="(item, index) in channelList" :key="item.id" :title="item.name" clickable
            @click="chooseChannel(item.id, index)">
            <template v-if="!item.children" #right-icon>
              <van-radio :name="item.id" />
            </template>
            <div slot="label">
              <div v-if="item.children">
                <van-radio-group v-model="channelId">
                  <van-cell-group >
                    <van-cell v-for="(item1, index1) in item.children" :key="item1.id" :title="item1.name" clickable
                      @click.stop="chooseChannelChild(item1.id, index, index1)">
                      <template #right-icon>
                        <van-radio :name="item1.id" />
                      </template>
                      <div slot="label">
                        <div v-if="item1.description">{{ item1.description }}</div>
                        <div v-if="item1.price > 0">￥{{ item1.price }}元</div>
                      </div>
                    </van-cell>
                  </van-cell-group>
                </van-radio-group>
              </div>
              <div v-else>
                <div v-if="item.description">{{ item.description }}</div>
                <div v-if="item.price > 0">￥{{ item.price }}元</div>
              </div>
            </div>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
    <div style="margin-top: 8px" class="nav-title">
      <div class="color"></div>
      <div class="text">手机验证</div>
    </div>
    <van-cell-group inset>
      <van-field v-model="userInfo.contact" name="姓名" label="姓名" required placeholder="姓名"
        :rules="[{ required: true, message: '请填写姓名' }]" />
      <van-field v-model="userInfo.mobile" :disabled="!isMobilePhone" name="手机号" label="手机号" required placeholder="手机号"
        :rules="[{ required: true, message: '请填写手机号' }]" />
      <!-- @input="verifyApplyByMobile"  -->
      <van-field v-model="userInfo.code" v-show="isSms" center clearable maxlength="6" label="短信验证码" required
        placeholder="请输入短信验证码">
        <template #button>
          <van-button size="small" type="primary" :disabled="waiting" @click="doSendSmsCode()">
            <span v-if="waiting">{{ waitingTime }}秒后重发</span>
            <span v-else style="font-size: 13px">获取验证码</span>
          </van-button></template>
      </van-field>
    </van-cell-group>

    <van-form @submit="onSubmit">
      <div v-show="applyContentVisible">
        <div style="margin-top: 8px" class="nav-title">
          <div class="color"></div>
          <div class="text">报名信息填写</div>
        </div>
        <van-cell-group inset>
          <!-- 如果不是扩展字段 -->
          <div class="dyn-item" v-for="item in applyActivityConfigList" :key="item.id">
            <!-- 如果是填空 -->
            <div v-if="item.type == 0">
              <van-field v-model="userInfo[item.applyConfigFieldName]" :name="item.finalName" :label="item.finalName"
                :required="item.required == 1 ? true : false" :placeholder="item.placeholder || item.finalName" :rules="[
                  {
                    required: item.required == 1 ? true : false,
                    message: '请填写' + item.finalName,
                  },
                ]" />
            </div>
            <!-- 如果是单选 -->
            <div v-else-if="item.type == 1">
              <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
                :title="item.finalName" is-link @click="showRadio(item)" />
            </div>
            <!-- 如果是多选 -->
            <div v-else-if="item.type == 2">
              <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
                :title="item.finalName" is-link @click.native="showCheckBox(item)" />
            </div>
            <!-- 如果是日期 -->
            <div v-else-if="item.type == 5">
              <van-cell v-model="userInfo[item.applyConfigFieldName]" :required="item.required == 1 ? true : false"
                :title="item.finalName" is-link @click.native="showDate(item)" />
            </div>
            <!-- 如果是特殊字段 -->
            <div v-else-if="item.type == 4">
              <van-cell v-if="item.applyConfigFieldName == 'area'" v-model="userInfo.areaName"
                :required="item.required == 1 ? true : false" :title="item.finalName" is-link @click="showArea(item)" />
              <van-cell v-if="item.applyConfigFieldName == 'isHotel'" v-model="userInfo[item.applyConfigFieldName]"
                :required="item.required == 1 ? true : false" :title="item.finalName" is-link
                @click="showYesOrNo(item)" />
            </div>
          </div>
          <!-- 如果是扩展字段 -->
          <div class="dyn-item" v-for="(extraItem, index) in extraResult" :key="index">
            <!-- 如果是填空(扩展字段) -->
            <div v-if="extraItem.type == 0">
              <van-field v-model="extraItem.value" :name="extraItem.finalName" :label="extraItem.finalName"
                :required="extraItem.required == 1 ? true : false"
                :placeholder="extraItem.placeholder || extraItem.finalName" :rules="[
                  {
                    required: extraItem.required == 1 ? true : false,
                    message: '请填写' + extraItem.finalName,
                  },
                ]" />
            </div>
            <!-- 如果是单选(扩展字段) -->
            <div v-else-if="extraItem.type == 1">
              <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
                :title="extraItem.finalName" is-link @click="showExtraRadio(extraItem)" />
            </div>
            <!-- 如果是多选(扩展字段) -->
            <div v-else-if="extraItem.type == 2">
              <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
                :title="extraItem.finalName" is-link @click.native="showExtraCheckBox(extraItem)" />
            </div>
            <div v-else-if="extraItem.type == 5">
              <van-cell v-model="extraItem.value" :required="extraItem.required == 1 ? true : false"
                :title="extraItem.finalName" is-link @click.native="showExtraDate(extraItem)" />
            </div>
          </div>
          <div v-if="indexChannel.isNeedInvite > 0">
            <van-field v-model="userInfo.inviteCode" name="inviteCode" label="邀请码" :required="true" placeholder="请输入邀请码"
              :rules="[
                {
                  required: true,
                  message: '请填写邀请码',
                },
              ]" />
          </div>
          <div v-if="indexChannel.isVerify">
            <van-cell
              title="审核材料"
              required
              :rules="[{ required: true, message: '请上传审核材料' }]"
            >
              <van-uploader
                :after-read="afterRead"
                name="credit"
                :before-read="beforeRead"
                accept="image/*"
              >
                <van-icon
                  v-if="!userInfo.credit"
                  slot="default"
                  name="add-o"
                  size="50px"
                  style="margin-left: 5px"
                ></van-icon>
                <van-image v-else height="50px" :src="userInfo.credit" fit="contain" />
              </van-uploader>
            </van-cell>
          </div>
        </van-cell-group>
      </div>
      <div v-if="indexChannel.isInvoice">
        <div style="margin-top: 8px" class="nav-title">
          <div class="color"></div>
          <div class="text">发票信息填写</div>
        </div>
        <van-cell-group inset>
          <van-field required v-model="userInfo.invoiceName" label="发票抬头" placeholder="请输入发票抬头" @clear="cancel" clearable
            @input="autoCompleteList" :rules="[{ required: true, message: '请填写发票抬头' }]" />
          <van-cell title="" v-show="autoCompleteListView" style="height:62px">
            <template>
              <div v-for="item in invoicelist" :key="item.id" :title="item.invoiceName" clearable
                @click="invoiceChoose(item)">
                <div style="flex:3"></div>
                <div style="flex:6;height: 23px;overflow: hidden;line-height: 22px;">{{ item.invoiceName }}</div>
                <div style="font-size: 12px;color: #949494;line-height: 17px;">{{ item.invoiceCode }}</div>
              </div>
            </template>
          </van-cell>
          <van-field v-model="userInfo.invoiceCode" label="纳税人识别号" clearable required placeholder="请输入纳税人识别号"
            :rules="[{ required: true, message: '请输入纳税人识别号' }]" />
          <van-field v-model="userInfo.invoiceAddress" label="注册地址(专票)" clearable placeholder="请输入注册地址(专票)" />
          <van-field v-model="userInfo.invoiceMobile" label="注册电话(专票)" clearable placeholder="请输入注册电话(专票)" />
          <van-field v-model="userInfo.invoiceBank" label="开户银行(专票)" clearable placeholder="请输入开户银行(专票)" />
          <van-field v-model="userInfo.invoiceAccount" label="银行账户(专票)" clearable placeholder="请输入银行账户(专票)" />
          <van-field v-model="userInfo.email" label="邮箱" clearable placeholder="请输入邮箱" />
        </van-cell-group>
      </div>
      <div style="margin: 16px">
        <van-button :disabled="submitDisabled" round block type="info" native-type="submit" :loading="loading"
          loading-text="提交中">提交</van-button>
      </div>
    </van-form>
    <!-- 单选弹窗 -->
    <van-popup v-model="radioVisible" closeable position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ chooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-radio-group v-model="userInfo[chooseResult.applyConfigFieldName]">
          <van-cell-group>
            <van-cell v-for="item in chooseResult.selectData" :key="item" :title="item" clickable @click="choose(item)">
              <template #right-icon>
                <van-radio :name="item" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
    <!-- 多选弹窗 -->
    <van-popup v-model="checkBoxVisible" closeable @click-close-icon="chooseConfirm"
      close-icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png"
      position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ chooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-checkbox-group v-model="checkBoxResult" change="onChange">
          <van-cell-group>
            <van-cell v-for="(item, index) in chooseResult.selectData" :key="item" :title="item" clickable
              :data-index="index" @click="toggle">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxes"></van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
    <!-- 日期选择 -->
    <van-calendar v-model="dateVisible" @confirm="dateSelect"  :min-date="minDate" :max-date="maxDate"/>
    <van-calendar v-model="extraDateVisible" @confirm="dateExtraSelect"  :min-date="minDate" :max-date="maxDate"/>
    <!-- 单选弹窗（扩展字段） -->
    <van-popup v-model="extraRadioVisible" closeable position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ extraChooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-radio-group v-model="extraChooseResult.value">
          <van-cell-group>
            <van-cell v-for="item in extraChooseResult.selectData" :key="item" :title="item" clickable
              @click="chooseExtra(item)">
              <template #right-icon>
                <van-radio :name="item" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
    <!-- 多选弹窗（扩展字段） -->
    <van-popup v-model="extraCheckBoxVisible" closeable @click-close-icon="chooseExtraConfirm"
      close-icon="http://mpjoy.oss-cn-beijing.aliyuncs.com/20211108/186452e1a5de47759e81e0c439343da3.png"
      position="bottom" :style="{ height: '45%' }">
      <div class="popup-title">{{ extraChooseResult.finalName }}</div>
      <div style="padding-top: 5px">
        <van-checkbox-group v-model="extraCheckBoxResult" change="onChangeExtra">
          <van-cell-group>
            <van-cell v-for="(item, index) in extraChooseResult.selectData" :key="item" :title="item" clickable
              :data-index="index" @click="toggleExtra">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxesExtra"></van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
    <!-- 省市区弹窗 -->
    <van-popup v-model="areaVisible" position="bottom" :style="{ height: '45%' }">
      <van-area @cancel="areaVisible = false" @confirm="areaSelect" title="省市区选择" :area-list="areaList"
        :value="areaCode" />
    </van-popup>
    <!-- 返回按钮 -->
    <img class="back" v-if="activityId != 1651597072446533634" @click="cmsTurnBack" :src="activityInfo.backImg" alt="" />
    <!-- 九宫格底部-自定义 -->
    <div class="bottomdiy" v-if="activityInfo.applyYhy">
      <a class="item" href="https://zhaoshengniuren.com/mp_yqh/#/yunhuiyi">技术支持：云会易</a>
    </div>
    <!-- 支付弹窗 -->
    <van-popup v-model="payVisible" closeable position="bottom" :style="{ height: '35%' }" @close="turnSuccess">
      <div class="popup-title">选择支付方式</div>
      <div style="padding-top: 5px">
        <van-radio-group v-model="payModel">
          <van-cell-group>
            <van-cell v-if="indexChannel.isWechatPay" title="微信支付" clickable @click="weixin(orderId)">
              <template #right-icon>
                <van-radio name="微信支付" />
              </template>
            </van-cell>
            <van-cell v-if="indexChannel.isAliPay" title="支付宝支付" clickable @click="ali(orderId)">
              <template #right-icon>
                <van-radio name="支付宝支付" />
              </template>
            </van-cell>
            <van-cell v-if="indexChannel.isBankTransfer" title="银行转账" clickable @click="turnSuccess()">
              <template #right-icon>
                <van-radio name="银行转账" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { yesOrNoText } from "@/data/common";
import { isMobile, isMobilePhone } from "@/js/validate";
import { areaList } from "@vant/area-data";
import pcheader from "@/pages/cms/components/pcheader.vue";
import date from "@/js/date.js";
export default {
  components: { pcheader },
  data() {
    return {
      minDate: new Date(2023, 0, 1),
      maxDate: new Date(2028, 0, 31),
      dateVisible: false,
      extraDateVisible: false,
      orderId: '',
      payModel: 0,
      paySize: 0,
      payVisible: false,
      autoCompleteListView: false,
      invoicelist: [],
      submitDisabled: true,
      activityInfo: {},
      yesOrNoText,
      isMobilePhone: isMobilePhone(),
      // todo 代报名不开启手机验证
      isSms: false,
      openid: undefined,
      waiting: false,
      isSend: false,
      areaVisible: false,
      areaCode: '110101',
      waitingTime: 60,
      activityId: undefined,
      channelId: undefined,
      inChannelId: undefined,
      loading: false,
      isPay: 0,
      userInfo: {
        invoiceName: '',
        invoiceCode: '',
        invoiceBank: '',
        invoiceAddress: '',
        invoiceMobile: '',
        invoiceAccount: '',
        email: '',
        credit: '',
      },
      indexChannel: {},
      channelList: [],
      applyActivityConfigList: [],
      extraResult: [],
      channelVisible: true,
      applyContentVisible: true,
      radioVisible: false,
      checkBoxVisible: false,
      chooseResult: {},
      checkBoxResult: [],
      extraRadioVisible: false,
      extraCheckBoxVisible: false,
      extraChooseResult: {},
      extraCheckBoxResult: [],
      areaList,
      applyNumber: 0,
    };
  },
  mounted() {
    document.title = "代报名信息填写";
    this.activityId = this.$route.query.id;
    this.channelId = this.$route.query.channelId || '';
    this.inChannelId = this.$route.query.channelId || '';
    this.openid = this.$cookie.get("openid")
    this.rebuildUrl();
    this.getToken();
    this.getActivityInfo();
    this.getApplyActivityChannelConfig();
  },
  methods: {
    showDate(v) {
      this.chooseResult = v;
      this.dateVisible = true;
    },
    // 展示日期（扩展字段）
    showExtraDate(v) {
      this.extraChooseResult = v;
      this.extraDateVisible = true;
    },
    // 日期选择
    dateSelect(v) {
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, date.formatDate.format(
              new Date(v),
              "yyyy/MM/dd"
            ));
      this.dateVisible = false;
    },
    dateExtraSelect(v) {
      console.log(v)
      this.extraResult.forEach((item) => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = date.formatDate.format(
              new Date(v),
              "yyyy/MM/dd"
            );
        }
      });
      this.extraDateVisible = false;
    },
    autoCompleteList() {
      if (this.userInfo.invoiceName) {
        this.$fly
          .get(`/pyp/web/invoice/findByInvoiceName/`, {
            invoiceName: this.userInfo.invoiceName
          })
          .then((res) => {
            if (res.code == 200) {
              console.log(res.result.length)
              if (res.result.length != 0) {
                this.invoicelist = res.result
                this.autoCompleteListView = true
              } else {
                this.invoicelist = []
              }
            }
          });
      }
    },
    invoiceChoose(item) {
      this.userInfo.invoiceName = item.invoiceName
      this.userInfo.invoiceCode = item.invoiceCode
      this.userInfo.invoiceAccount = item.invoiceAccount
      this.userInfo.invoiceBank = item.invoiceBank
      this.userInfo.invoiceAddress = item.invoiceAddress
      this.userInfo.invoiceMobile = item.invoiceMobile
      this.autoCompleteListView = false
    },
    cancel() {
      this.autoCompleteListView = false
      this.invoicelist = []
    },
    getToken() {
      this.$fly
        .get(`/pyp/activity/activityuser/getToken`, {
        })
        .then((res) => {
          if (res.code == 200) {
            this.$set(this.userInfo, "orderToken", res.result);
          }
        });
    },
    getActivityInfo() {
      this.$fly
        .get(`/pyp/activity/activity/info/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.activityInfo = res.activity
            this.activityInfo.backImg =
              this.activityInfo.backImg ||
              "http://mpjoy.oss-cn-beijing.aliyuncs.com/********/9c9298f1d3474660977a02c8a84aafa8.png";
            if (res.activity.applyNotify) {
              vant.Dialog.alert({
                title: "报名须知",
                message: res.activity.applyNotify,
              }).then(() => {
                // on close
              });
            }
            if (res.activity.showApplyNumber) {
              this.getApplyCount();
            }
          } else {

          }
        });
    },
    getApplyCount() {
      this.$fly
        .get(`/pyp/web/activity/activityuserapplyorder/count/${this.activityId}`)
        .then((res) => {
          if (res.code == 200) {
            this.applyNumber = res.result;
          } else {

          }
        });
    },
    getApplyActivityChannelConfig() {
      this.$fly
        .get(
          `/pyp/web/apply/applyactivitychannelconfig/findByActivityId/${this.activityId}`,
          {channelId: this.inChannelId}
        )
        .then((res) => {
          if (res.code == 200) {
            this.channelList = res.result;
            if (this.channelList.length == 1) {
              if( !this.inChannelId) {
              this.channelVisible = false;
              this.chooseChannel(this.channelList[0].id, 0);
              }
            } else if (this.channelList.length == 0) {
              this.channelVisible = false;
              this.applyContentVisible = false;
            }
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    //选择报名通道
    chooseChannel(v, index) {
      this.channelId = v;
      this.indexChannel = this.channelList[index];
      if (isMobilePhone() == true) {
        // todo 代报名不开启手机验证
        this.isSms = false;
      }
      this.paySize = 0;
      if (this.indexChannel.price > 0 && this.indexChannel.isWechatPay) {
        this.paySize += 1;
      }
      if (this.indexChannel.price > 0 && this.indexChannel.isAliPay) {
        this.paySize += 1;
      }
      if (this.indexChannel.price > 0 && this.indexChannel.isBankTransfer) {
        this.paySize += 1;
      }
      this.applyActivityConfigList = [];
      this.extraResult = [];
      this.getApplyActivityConfig();
    },
    // 单选选择
    choose(v) {
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, v);
      this.radioVisible = false;
    },
    // 展示单选
    showRadio(v) {
      this.chooseResult = v;
      this.radioVisible = true;
    },
    // 展示是否预定酒店
    showYesOrNo(v) {
      this.chooseResult = v;
      this.$set(this.chooseResult, "selectData", this.yesOrNoText);
      this.radioVisible = true;
    },
    // 多选框改变事件
    onChange(event) {
      this.setData({
        checkBoxResult: event.detail,
      });
    },
    // 多选选择
    toggle(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxes[index].toggle();
    },
    // 多选选择确定
    chooseConfirm() {
      var searchResult = "";
      if (this.checkBoxResult.length > 1) {
        for (let i = 0; i < this.checkBoxResult.length; i++) {
          if (i === this.checkBoxResult.length - 1) {
            searchResult = searchResult + this.checkBoxResult[i];
          } else {
            searchResult = searchResult + this.checkBoxResult[i] + ",";
          }
        }
      } else {
        searchResult = this.checkBoxResult[0];
      }
      this.$set(
        this.userInfo,
        this.chooseResult.applyConfigFieldName,
        searchResult
      );
      this.checkBoxVisible = false;
    },
    // 展示多选
    showCheckBox(v) {
      this.chooseResult = v;
      var defaultResult = this.userInfo[this.chooseResult.applyConfigFieldName];
      this.checkBoxResult = !defaultResult ? [] : defaultResult.split(",");
      this.checkBoxVisible = true;
    },
    // 展示特殊配置===省市区选择====
    showArea(v) {
      this.chooseResult = v;
      this.areaCode = this.userInfo.area ? this.userInfo.area.split(",")[2] : '110101'
      this.areaVisible = true;
    },
    areaSelect(v) {
      console.log(v)
      let areaCode = v[0].code + ',' + v[1].code + ',' + v[2].code;
      let areaName = v[0].name + ',' + v[1].name + ',' + v[2].name;
      this.areaCode = v[2].code;
      this.$set(this.userInfo, this.chooseResult.applyConfigFieldName, areaCode);
      this.$set(this.userInfo, 'areaName', areaName);
      this.areaVisible = false;
    },
    // 单选选择（扩展字段）
    chooseExtra(v) {
      this.extraResult.forEach((item) => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = v;
        }
      });
      this.extraRadioVisible = false;
    },
    // 展示单选（扩展字段）
    showExtraRadio(v) {
      this.extraChooseResult = v;
      this.extraRadioVisible = true;
    },
    // 多选框改变事件（扩展字段）
    onChangeExtra(event) {
      this.setData({
        extraCheckBoxResult: event.detail,
      });
    },
    // 多选选择（扩展字段）
    toggleExtra(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxesExtra[index].toggle();
    },
    // 多选选择确定（扩展字段）
    chooseExtraConfirm() {
      var searchResult = "";
      if (this.extraCheckBoxResult.length > 1) {
        for (let i = 0; i < this.extraCheckBoxResult.length; i++) {
          if (i === this.extraCheckBoxResult.length - 1) {
            searchResult = searchResult + this.extraCheckBoxResult[i];
          } else {
            searchResult = searchResult + this.extraCheckBoxResult[i] + ",";
          }
        }
      } else {
        searchResult = this.extraCheckBoxResult[0];
      }
      this.extraResult.forEach((item) => {
        if (item.finalName == this.extraChooseResult.finalName) {
          item.value = searchResult;
        }
      });
      this.extraCheckBoxVisible = false;
    },
    // 展示多选（扩展字段）
    showExtraCheckBox(v) {
      this.extraChooseResult = v;
      var defaultResult = v.value;
      this.extraCheckBoxResult = !defaultResult ? [] : defaultResult.split(",");
      this.extraCheckBoxVisible = true;
    },
    getApplyActivityConfig() {
      this.submitDisabled = true;
      this.$fly
        .get(`/pyp/apply/applyactivityconfig/findByChannelId/${this.channelId}`)
        .then((res) => {
          if (res.code == 200) {
            this.submitDisabled = false;
            this.applyActivityConfigList = res.result;
            this.applyActivityConfigList.forEach((item) => {
              if (item.type == 1) {
                // 单选
                item.selectData = item.selectData
                  .replace(/，/gi, ",")
                  .split(",");
              } else if (item.type == 2) {
                // 多选
                item.selectData = item.selectData
                  .replace(/，/gi, ",")
                  .split(",");
              } else if (item.type == 3) {
                if (item.extra) {
                  this.extraResult = JSON.parse(item.extra);
                  this.extraResult.forEach((extraItem) => {
                    if (extraItem.type == 1) {
                      // 单选
                      extraItem.selectData = extraItem.selectData
                        .replace(/，/gi, ",")
                        .split(",");
                    } else if (extraItem.type == 2) {
                      // 多选
                      extraItem.selectData = extraItem.selectData
                        .replace(/，/gi, ",")
                        .split(",");
                    }
                  });
                }
              }
            });
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    onSubmit() {
      if (!this.userInfo.contact) {
        vant.Toast("请输入姓名");
        return false;
      }
      if (!this.userInfo.mobile) {
        vant.Toast("请输入手机号");
        return false;
      }
      if (!isMobile(this.userInfo.mobile)) {
        vant.Toast("手机号格式错误");
        return false;
      }
      if (!this.userInfo.code && this.isSms) {
        vant.Toast("请输入验证码");
        return false;
      }
      if (!/^\d{6}$/.test(this.userInfo.code) && this.isSms) {
        vant.Toast("验证码错误");
        return false;
      }
      // 如果有报名通道，是否选择了报名通道
      if (this.channelList.length > 0 && !this.channelId) {
        vant.Toast("请选择报名通道");
        return false;
      }
      // 如果报名通道需要填写邀请码
      if (this.indexChannel.isNeedInvite > 0 && !this.userInfo.inviteCode) {
        vant.Toast("请输入邀请码");
        return false;
      }
      // 基础信息校验
      for (var i = 0; i < this.applyActivityConfigList.length; i++) {
        if (
          this.applyActivityConfigList[i].type != 3 &&
          this.applyActivityConfigList[i].required &&
          !this.userInfo[this.applyActivityConfigList[i].applyConfigFieldName]
        ) {
          vant.Toast("请输入" + this.applyActivityConfigList[i].finalName);
          return false;
        }
      }
      // 扩展字段校验
      for (var i = 0; i < this.extraResult.length; i++) {
        if (
          this.extraResult[i].type != 3 &&
          this.extraResult[i].required &&
          !this.extraResult[i].value
        ) {
          vant.Toast("请输入" + this.extraResult[i].finalName);
          return false;
        }
      }
      // 扩展字段恢复
      for (var i = 0; i < this.extraResult.length; i++) {
        this.extraResult[i].selectData = !this.extraResult[i].selectData
          ? null
          : this.extraResult[i].selectData.toString();
      }
      // 全部校验通过，开始传送数据
      this.userInfo.activityId = this.activityId;
      this.userInfo.applyActivityChannelConfigId = this.channelId;
      this.userInfo.applyExtraVos = this.extraResult;
      this.userInfo.isMobilePhone = this.isSms;
      this.loading = true;
      var that = this;
      // 保存
      this.$fly
        .post(
          "/pyp/web/activity/activityuserapplyorder/createOrderProxy",
          this.userInfo
        )
        .then((res) => {
          if (res && res.code === 200) {
            // if (res.token) {
            //   this.$cookie.set("token", res.token);
            // }
            if (this.activityId == 26 && !res.isNeedPay) {
              // 妇幼会议跳转其他地方
              location.href = 'https://wx.vzan.com/live/tvchat-775359187?v=637903014806319913'
              return false;
            }
            if (this.activityId == 78 && !res.isNeedPay) {
              // 海峡会议，跳转微赞
              location.href = 'https://wx.vzan.com/live/mk/aggspread/618781199/f9dfa6f6-5cbf-11ed-93c5-043f72d45e40?v=1667621522612'
              return false;
            }
            this.orderId = res.result;
            if (res.isNeedPay && this.paySize == 0) {
              // 没有设置支付方式,直接跳转到支付成功页面
              this.$router.push({
                name: "applyProxySuccess",
                query: {
                  orderId: res.result,
                  id: this.activityId
                },
              });
            } else if (res.isNeedPay && this.paySize == 1) {
              // 设置了一个支付方式
              // 如果设置了银行转账，跳转到支付详情
              if (this.indexChannel.isBankTransfer) {
                this.$router.push({
                  name: "applyProxySuccess",
                  query: {
                    orderId: res.result,
                    id: this.activityId
                  },
                });
              }
              if (this.indexChannel.isWechatPay) {
                // 如果需要支付，调用支付接口，并且设置了微信支付，没有设置银行支付的前提下
                this.weixin(res.result);
              }
              if (this.indexChannel.isAliPay) {
                // 如果设置了支付宝支付
                this.ali(res.result);
              }
            } else if (res.isNeedPay && this.paySize > 1) {
              // 设置了超过一个支付方式
              // 显示支付弹窗
              this.payVisible = true;
            } else {
              // 如果不需要支付，直接判断师傅需要跳转酒店
              if (this.userInfo.isHotel && this.userInfo.isHotel == '是') {
                // 如果需要跳转酒店
                vant.Toast('报名成功，即将跳转酒店预定页面');
                setTimeout(() => {
                  this.$router.push({
                    name: "hotelIndex",
                    query: {
                      id: this.activityId
                    },
                  });
                  return false;
                }, 1000)

              } else {
                vant.Toast("报名成功");
                this.$router.push({
                  name: "applyProxySuccess",
                  query: {
                    orderId: res.result,
                    id: this.activityId
                  },
                });
              }
            }
            // 绑定手机
          } else {
            vant.Toast(res.msg);
          }
          this.loading = false;
        });
    },
    turnSuccess() {
      this.$router.push({
              name: 'applyProxySuccess',
              query: {
                id: this.activityId,
                orderId: this.orderId
              }
            })
    },
    ali(orderId) {
      this.$fly
        .get(
          "/pyp/web/activity/activityuserapplyorder/payAliProxy",
          { orderId: orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            this.$router.push({
              name: 'commonAlipay',
              query: {
                form: encodeURIComponent(res1.result)
              }
            })
          } else {
            vant.Toast(res1.msg);
          }
        })
    },
    weixin(orderId) {
      var that = this;
      this.$fly
        .get(
          "/pyp/web/activity/activityuserapplyorder/payProxy",
          { orderId: orderId }
        )
        .then((res1) => {
          if (res1 && res1.code === 200) {
            WeixinJSBridge.invoke(
              'getBrandWCPayRequest', {
              "appId": res1.result.appId,
              "timeStamp": res1.result.timeStamp,
              "nonceStr": res1.result.nonceStr,
              "package": res1.result.packageValue,
              "signType": res1.result.signType,
              "paySign": res1.result.paySign
            },
              function (res2) {
                console.log("开始支付")
                if (res2.err_msg == "get_brand_wcpay_request:ok") {
                  var baseUrl = window.location.href.split("#")[0];
                  location.href = baseUrl + '#/apply/proxySuccess?id=' + that.activityId + "&orderId=" + res.result; //支付成功跳转到详情页
                } else if (res2.err_msg == "get_brand_wcpay_request:cancel") {
                  var baseUrl = window.location.href.split("#")[0];
                  location.href = baseUrl + '#/apply/proxySuccess?id=' + that.activityId + "&orderId=" + res.result;
                } else {
                  var baseUrl = window.location.href.split("#")[0];
                  location.href = baseUrl + '#/apply/proxySuccess?id=' + that.activityId + "&orderId=" + res.result;
                }
              });
          } else {
            vant.Toast(res1.msg);

          }
        })

    },
    doSendSmsCode() {
      if (!this.userInfo.mobile) {
        vant.Toast("请输入手机号");
        return false;
      }
      if (!isMobile(this.userInfo.mobile)) {
        vant.Toast("手机号格式错误");
        return false;
      }
      this.$fly
        .post("/pyp/sms/sms/send", {
          mobile: this.userInfo.mobile,
          activityId: this.activityId,
        })
        .then((res) => {
          if (res && res.code === 200) {
            this.isSend = true;
            this.countdown();
            vant.Toast("发送验证码成功");
          } else {
            vant.Toast(res.msg);
          }
        });
    },
    countdown() {
      this.waiting = true
      let clock = window.setInterval(() => {
        this.waitingTime--
        if (this.waitingTime < 0) {
          window.clearInterval(clock)
          this.waitingTime = 60
          this.waiting = false
        }
      }, 1000)
    },
    verifyApplyByMobile(v) {
      if (v.length == 6 && this.isSend == true) {
        // 如果是这样子，再去校验是否有报名数据
        this.$fly
          .get("/pyp/web/activity/activityuserapplyorder/checkApplyByMobile", {
            activityId: this.activityId,
            mobile: this.userInfo.mobile,
            code: v
          })
          .then((res) => {
            if (res.code == 200) {
              if (res.isPay != 0) {
                vant.Dialog.alert({
                  title: "提示",
                  message: "您已存在报名订单，请点击“确定”关联",
                }).then(() => {
                  this.$fly
                    .get("/pyp/web/activity/activityuserapplyorder/connectApply", {
                      activityId: this.activityId,
                      mobile: this.userInfo.mobile,
                      code: v
                    })
                    .then((res) => {
                      if (res.code == 200) {
                        location.reload();
                      } else {
                        vant.Toast(res.msg);
                      }
                    });
                });
              }
            } else {
            }
          });
      }
    },
    cmsTurnBack() {
      if(this.activityInfo.backUrl) {
        window.open(this.activityInfo.backUrl);
      } else {
        this.$router.replace({ name: 'cmsIndex', query: { id: this.activityInfo.id } })
      }
    },
    rebuildUrl() {
      let {
        href,
        protocol,
        host,
        pathname,
        search,
        hash
      } = window.location
      console.log(window.location)
      search = search || '?'
      let newHref = `${protocol}//${host}${pathname}${search}${hash}`
      console.log(newHref)
      if (newHref !== href) {
        window.location.replace(newHref)
      }
    },
    afterRead(e, name) {
      let filedName = name.name;
      e.status = "uploading";
      e.message = "上传中...";
      let formData = new FormData();
      // formData.append("pushKey", this.pushKey);
      // formData.append("activityId", this.activityId);
      formData.append("file", e.file);

      this.$fly.post("/pyp/web/upload", formData).then((res) => {
        if (res && res.code === 200) {
          this.$set(this.userInfo, filedName, res.result);
        }
      });
    },
    beforeRead(file) {
      // if (file.type !== 'image/jpeg') {
      //   Toast('请上传 jpg 格式图片');
      //   return false;
      // }
      return true;
    },
  },
};
</script>

<style lang="less" scoped>
@popup-close-icon-size: 40px;
@popup-close-icon-color: black;

.page {
  background-color: #f6f6f6;
}

.dyn-item {
  margin-top: 5px;
}

.popup-title {
  font-weight: bold;
  font-size: 15px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  padding: 0 50px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.dyn-item {
  .van-cell {
    border-bottom: 1px solid #f8f8f8;
  }

  .van-cell__title {
    flex: none;
    width: 8.2em;
    margin-right: 12px;
  }

  .van-cell__value {
    flex: 1;
    text-align: left;
  }
}

.bottomdiy {
  margin-top: 30px;
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  background-color: black;
  opacity: 0.3;

  .item {
    color: white;
  }
}
</style>