<template>
  <div id="printResult" style="text-align: center">
    <div style="width: 100%">
      <div style="font-size: 50px;margin-top: 50px;font-weight:800">{{ activityInfo.name }}</div>
      <vue-qrcode :options="{ width: 800 }"
        :value="wxAccount.baseUrl + 'apply/sign?id=' + activityId"></vue-qrcode>
      <div style="font-size: 40px;font-weight:800">扫描此二维码，进行签到</div>
    </div>
  </div>
</template>
<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
export default {
  data() {
    return {
      wxAccount: {},
      activityInfo: {},
      appid: '',
      activityId: '',
      baseUrl: '',
      visible: false,
    };
  },
  components: {
    VueQrcode
  },
  created() {
    this.appid = this.$cookie.get("appid");
    this.activityId = this.$route.query.activityId;
    this.getActivity();
    this.getAccountInfo();
  },
  methods: {

    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.activityId}`),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity;
        }
      });
    },
    getAccountInfo() {
      this.$http({
        url: this.$http.adornUrl(
          `/manage/wxAccount/info/${this.appid}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.wxAccount = data.wxAccount;
        }
      });
    },
  }
};
</script>
<style>
.el-form-item {
  margin-bottom: 10px;
}

.el-form--label-top .el-form-item__label {
  float: none;
  display: inline-block;
  text-align: left;
  padding: 0 0 10px;
}

.el-form-item__label {
  text-align: right;
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 20px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media print {
  @page {
    margin: 0;
  }

  body {
    margin: 15px;
  }
}</style>
