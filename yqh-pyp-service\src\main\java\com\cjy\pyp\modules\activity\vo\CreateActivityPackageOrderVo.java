package com.cjy.pyp.modules.activity.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 创建活动套餐订单VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Data
public class CreateActivityPackageOrderVo {

    /**
     * 防重令牌
     */
    private String repeatToken;

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    private Long packageId;

    /**
     * 活动模板ID（可选，如果不指定则使用默认模板）
     */
    private Long activityTemplateId;

    /**
     * 活动名称
     */
    @NotNull(message = "活动名称不能为空")
    private String activityName;

    /**
     * 备注
     */
    private String remarks;
    private String appid;
}
