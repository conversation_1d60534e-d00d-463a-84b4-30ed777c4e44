package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.modules.salesman.dao.WxUserSalesmanBindingLogDao;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingLogEntity;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingLogService;
import com.github.pagehelper.PageHelper;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 微信用户业务员绑定变更记录服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Service("wxUserSalesmanBindingLogService")
public class WxUserSalesmanBindingLogServiceImpl extends ServiceImpl<WxUserSalesmanBindingLogDao, WxUserSalesmanBindingLogEntity> 
        implements WxUserSalesmanBindingLogService {

    @Override
    public List<WxUserSalesmanBindingLogEntity> queryPage(Map<String, Object> params) {
        int page = Integer.parseInt((String) params.get("page"));
        int limit = Integer.parseInt((String) params.get("limit"));
        PageHelper.startPage(page,limit);
        List<WxUserSalesmanBindingLogEntity> list = baseMapper.queryPage(params);
        return list;
    }

    @Override
    public List<WxUserSalesmanBindingLogEntity> getLogsByWxUser(Long wxUserId, String appid) {
        QueryWrapper<WxUserSalesmanBindingLogEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("wx_user_id", wxUserId)
               .eq("appid", appid)
               .orderByDesc("create_on");
        return this.list(wrapper);
    }

    @Override
    public List<WxUserSalesmanBindingLogEntity> getLogsBySalesman(Long salesmanId, String appid) {
        QueryWrapper<WxUserSalesmanBindingLogEntity> wrapper = new QueryWrapper<>();
        wrapper.and(w -> w.eq("old_salesman_id", salesmanId).or().eq("new_salesman_id", salesmanId))
               .eq("appid", appid)
               .orderByDesc("create_on");
        return this.list(wrapper);
    }

    @Override
    public void recordLog(Long wxUserId, Long oldSalesmanId, Long newSalesmanId, Integer operationType,
                         String reason, String bindingSource, Long operatorId, Integer operatorType, String appid) {
        WxUserSalesmanBindingLogEntity log = new WxUserSalesmanBindingLogEntity();
        log.setWxUserId(wxUserId);
        log.setOldSalesmanId(oldSalesmanId);
        log.setNewSalesmanId(newSalesmanId);
        log.setOperationType(operationType);
        log.setOperationReason(reason);
        log.setBindingSource(bindingSource);
        log.setOperatorId(operatorId);
        log.setOperatorType(operatorType);
        log.setAppid(appid);
        
        this.save(log);
    }
}
