<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <el-form-item label="主题名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="主题名称"></el-input>
      </el-form-item>
      <el-form-item label="会议场地" prop="placeId">
        <el-select v-model="dataForm.placeId" placeholder="会议场地" filterable>
          <el-option v-for="item in placeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会议日期" prop="startTime">
        <!-- <el-date-picker v-model="dataForm.startTime" style="windth: 100%" type="date" value-format="yyyy/MM/dd" placeholder="主题日期"  :picker-options="pickerOptions"></el-date-picker> -->
        <el-date-picker :picker-options="pickerOptions" v-model="dataForm.times" style="windth: 100%"
          @change="dateChange" :default-value="activityInfo.startTime" type="datetimerange"
          value-format="yyyy/MM/dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="主持人" prop="topicGuestIds">
        <el-select v-model="dataForm.topicGuestIds" multiple placeholder="主持人" filterable>
          <el-option v-for="item in guestList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主席" prop="topicSpeakerIds">
        <el-select v-model="dataForm.topicSpeakerIds" multiple placeholder="主持人" filterable>
          <el-option v-for="item in guestList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="讨论" prop="topicDiscussIds">
        <el-select v-model="dataForm.topicDiscussIds" multiple placeholder="讨论" filterable>
          <el-option v-for="item in guestList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序，数值越小越靠前" prop="orderBy">
        <el-input v-model="dataForm.orderBy" placeholder="排序，数值越小越靠前"></el-input>
      </el-form-item>
      <el-form-item label="主持别名" prop="aliasGuestName">
        <el-input v-model="dataForm.aliasGuestName" placeholder="主持别名"></el-input>
      </el-form-item>
      <el-form-item label="主席别名" prop="aliasSpeakerName">
        <el-input v-model="dataForm.aliasSpeakerName" placeholder="主席别名"></el-input>
      </el-form-item>
      <el-form-item label="讨论别名" prop="aliasDiscussName">
        <el-input v-model="dataForm.aliasDiscussName" placeholder="讨论别名"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      placeList: [],
      guestList: [],
      activityInfo: {},
      dataForm: {
        id: 0,
        activityId: "",
        name: "",
        placeId: "",
        startTime: "",
        endTime: "",
        aliasGuestName: "",
        aliasSpeakerName: "",
        aliasDiscussName: "",
        topicGuestIds: [],
        topicSpeakerIds: [],
        topicDiscussIds: [],
        times: [],
        orderBy: 0,
      },
      dataRule: {
        activityId: [
          { required: true, message: "会议id不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "会场名称不能为空", trigger: "blur" },
        ],
        times: [{
          required: true,
          message: '会议时间不能为空',
          trigger: 'blur'
        }],
        startTime: [{
          required: true,
          message: '会议时间不能为空',
          trigger: 'blur'
        }],
        endTime: [
          { required: true, message: '结束时间不能为空', trigger: 'blur' }
        ],
        placeId: [{ required: true, message: "场地不能为空", trigger: "blur" }],
        orderBy: [
          {
            required: true,
            message: "排序，数值越小越靠前不能为空",
            trigger: "blur",
          },
        ],
      },
      pickerOptions: {
        disabledDate: (time) => {
          // 如果函数里处理的数据比较麻烦,也可以单独放在一个函数里,避免data数据太臃肿
          return this.dealDisabledDate(time);
        }
      },
    };
  },
  methods: {
    init(activityId, id) {
      this.dataForm.activityId = activityId;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placeactivitytopic/info/${this.dataForm.id}`
            ),
            method: "get",
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$set(this.dataForm, "times", [data.placeActivityTopic.startTime, data.placeActivityTopic.endTime])
              this.dataForm.activityId = data.placeActivityTopic.activityId;
              this.dataForm.createOn = data.placeActivityTopic.createOn;
              this.dataForm.createBy = data.placeActivityTopic.createBy;
              this.dataForm.updateOn = data.placeActivityTopic.updateOn;
              this.dataForm.updateBy = data.placeActivityTopic.updateBy;
              this.dataForm.name = data.placeActivityTopic.name;
              this.dataForm.placeId = data.placeActivityTopic.placeId;
              this.dataForm.topicGuestIds = data.placeActivityTopic.topicGuestIds;
              this.dataForm.topicSpeakerIds = data.placeActivityTopic.topicSpeakerIds;
              this.dataForm.topicDiscussIds = data.placeActivityTopic.topicDiscussIds;
              this.dataForm.orderBy = data.placeActivityTopic.orderBy;
              this.dataForm.startTime = data.placeActivityTopic.startTime
              this.dataForm.endTime = data.placeActivityTopic.endTime
              this.dataForm.aliasGuestName = data.placeActivityTopic.aliasGuestName
              this.dataForm.aliasSpeakerName = data.placeActivityTopic.aliasSpeakerName
              this.dataForm.aliasDiscussName = data.placeActivityTopic.aliasDiscussName
            }
          });
        }
      });
      this.getPlace();
      this.getGuest();
      this.getActivity();
    },
    getPlace() {
      this.$http({
        url: this.$http.adornUrl(
          `/place/placeactivity/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.placeList = data.result;
        }
      });
    },
    getGuest() {
      this.$http({
        url: this.$http.adornUrl(
          `/activity/activityguest/findByActivityId/${this.dataForm.activityId}`
        ),
        method: "get",
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.guestList = data.result;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/place/placeactivitytopic/${!this.dataForm.id ? "save" : "update"
              }`
            ),
            method: "post",
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              activityId: this.dataForm.activityId,
              createOn: this.dataForm.createOn,
              createBy: this.dataForm.createBy,
              updateOn: this.dataForm.updateOn,
              updateBy: this.dataForm.updateBy,
              name: this.dataForm.name,
              placeId: this.dataForm.placeId,
              topicGuestIds: this.dataForm.topicGuestIds,
              topicSpeakerIds: this.dataForm.topicSpeakerIds,
              topicDiscussIds: this.dataForm.topicDiscussIds,
              orderBy: this.dataForm.orderBy,
              'startTime': this.dataForm.startTime,
              'endTime': this.dataForm.endTime,
              'aliasGuestName': this.dataForm.aliasGuestName,
              'aliasSpeakerName': this.dataForm.aliasSpeakerName,
              'aliasDiscussName': this.dataForm.aliasDiscussName,
            }),
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    getActivity() {
      this.$http({
        url: this.$http.adornUrl(`/activity/activity/info/${this.dataForm.activityId}`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
        data
      }) => {
        if (data && data.code === 200) {
          this.activityInfo = data.activity
        }
      })
    },
    dealDisabledDate(time) {
      // time 是一个new Date数据
      if (this.activityInfo.endTime != null && this.activityInfo.startTime != null) {
        return (time.getTime() + 24 * 60 * 60 * 1000) < (new Date(this.activityInfo.startTime).getTime()) || time.getTime() >= (new Date(this.activityInfo.endTime).getTime());//时间范围必须是时间戳
      }
    },
    dateChange(v) {
      this.dataForm.startTime = v[0];
      this.dataForm.endTime = v[1];
      console.log(v)
    }
  },
};
</script>
