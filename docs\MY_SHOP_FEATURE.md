# 我的小店功能实现文档

## 功能概述

为 tb_activity 表添加"我的小店"功能，支持跳转到商户自己的网页或小程序。

## 数据库变更

### 新增字段

在 `tb_activity` 表中新增以下字段：

- `shop_type` (tinyint): 我的小店类型，0-网页，1-小程序
- `shop_url` (varchar): 我的小店网页URL（当shop_type=0时使用）
- `shop_appid` (varchar): 我的小店小程序AppID（当shop_type=1时使用）
- `shop_page_path` (varchar): 我的小店小程序页面路径（当shop_type=1时使用）
- `show_my_shop` (tinyint): 是否显示我的小店，0-不显示，1-显示

### 数据库迁移

执行迁移文件：`V1.0.6__add_my_shop_to_activity.sql`

## 后端实现

### 1. 实体类更新

在 `ActivityEntity.java` 中添加了新字段的属性定义。

### 2. 管理后台表单

在 `activity-add-or-update.vue` 中添加了我的小店配置表单：
- 是否显示我的小店开关
- 小店类型选择（网页/小程序）
- 网页URL输入框（当选择网页时显示）
- 小程序AppID和页面路径输入框（当选择小程序时显示）

### 3. 小程序URL Scheme生成

在 `WxMiniProgramController.java` 中新增 `generateShopScheme` API，用于生成小店小程序的URL Scheme。

## 前端实现

### 1. 九宫格页面

在 `cms/Index.vue` 中：
- 添加了"我的小店"按钮显示逻辑
- 实现了 `goMyShop()` 方法处理跳转逻辑

### 2. 移动端编辑页面

在 `activity/edit.vue` 中：
- 添加了我的小店配置表单
- 支持开关控制、类型选择和相关参数配置

### 3. 跳转逻辑

`goMyShop()` 方法实现：
- 网页类型：直接使用 `window.open()` 打开网页
- 小程序类型：
  - 微信内：使用 `wx.navigateToMiniProgram()` 跳转
  - 非微信环境：调用后端API生成URL Scheme跳转

## 图标资源

需要准备小店图标文件：
- 路径：`https://pyp.yqihua.com/shop/static/icons/myshop.png`
- 建议尺寸：64x64像素
- 建议样式：购物袋、商店等相关图标

## 使用说明

### 管理员配置

1. 在活动管理后台编辑活动
2. 找到"我的小店配置"部分
3. 开启"是否显示我的小店"开关
4. 选择小店类型：
   - **网页**：输入完整的网页URL
   - **小程序**：输入小程序AppID和页面路径
5. 保存配置

### 用户使用

1. 用户访问活动页面
2. 在九宫格中看到"我的小店"按钮（如果管理员已开启）
3. 点击按钮：
   - 网页类型：在新窗口打开商户网页
   - 小程序类型：跳转到指定小程序页面

## 注意事项

### 小程序配置

1. **AppSecret配置**：当前小店小程序的AppSecret需要在 `WxMiniProgramServiceImpl.java` 中配置
2. **权限要求**：小程序需要开通URL Scheme生成权限
3. **页面路径**：确保输入的页面路径在小程序中存在

### 安全考虑

1. **URL验证**：建议对输入的网页URL进行格式验证
2. **域名白名单**：可考虑限制允许跳转的域名范围
3. **小程序验证**：可验证输入的AppID是否有效

## 携程点评功能优化

### 功能改进

将 `goCtrip()` 方法改为与 `goDazhongdianping()` 相同的实现模式：

1. **统一交互模式**：使用 `showReviewContent()` 方法显示点评内容
2. **内容预览**：用户可以预览生成的携程点评文案和图片
3. **一键复制**：支持复制点评内容到剪贴板
4. **智能跳转**：支持多种跳转方式
   - JSON配置：`{"url": "https://...", "id": "店铺ID"}`
   - 直接URL：以http开头的链接
   - 携程APP：使用 `ctrip://wireless/hotel/店铺ID` 格式

### 后端支持

1. **新增API**：`/pyp/web/activity/review/ctrip`
2. **广告类型配置**：添加 `ctrip` 类型的文案生成配置
3. **内容生成**：支持携程点评风格的文案和话题生成

## 扩展功能

### 可能的增强

1. **统计功能**：记录小店访问次数
2. **多小店支持**：支持配置多个小店链接
3. **条件显示**：根据用户类型或时间条件显示不同小店
4. **样式自定义**：允许自定义小店按钮的图标和文字

### 技术优化

1. **缓存优化**：URL Scheme生成结果已实现Redis缓存
2. **错误处理**：完善各种异常情况的处理
3. **日志记录**：添加详细的操作日志
