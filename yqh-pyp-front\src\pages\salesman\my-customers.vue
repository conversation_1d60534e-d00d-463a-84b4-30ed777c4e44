<template>
  <div class="my-customers-page">
    <!-- 导航栏 -->
    <van-nav-bar title="我的客户" left-text="返回" left-arrow @click-left="$router.go(-1)"
                 class="custom-nav-bar" />

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search v-model="searchKeyword" placeholder="搜索客户昵称或手机号"
                  @search="onSearch" @clear="onClear" shape="round" />
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <van-dropdown-menu active-color="#1989fa">
        <van-dropdown-item v-model="filterStatus" :options="statusOptions" @change="onFilterChange" />
        <van-dropdown-item v-model="filterType" :options="typeOptions" @change="onFilterChange" />
        <van-dropdown-item v-model="sortType" :options="sortOptions" @change="onFilterChange" />
      </van-dropdown-menu>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-icon customers">
            <van-icon name="friends-o" size="20" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ customerStats.totalCustomers || 0 }}</div>
            <div class="stat-label">总客户数</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon active">
            <van-icon name="fire-o" size="20" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ customerStats.activeCustomers || 0 }}</div>
            <div class="stat-label">活跃客户</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon new">
            <van-icon name="add-o" size="20" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ customerStats.newCustomers || 0 }}</div>
            <div class="stat-label">本月新增</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon amount">
            <van-icon name="gold-coin-o" size="20" />
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ customerStats.totalAmount || 0 }}</div>
            <div class="stat-label">总消费额</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户列表 -->
    <div class="customer-list">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了"
                @load="onLoad" loading-text="加载中..." error-text="请求失败，点击重新加载">
        <div v-for="customer in customerList" :key="customer.id" class="customer-item"
          @click="viewCustomerDetail(customer)">
          <div class="customer-avatar">
            <van-image v-if="customer.avatar" :src="customer.avatar" round width="50" height="50"
                       fit="cover" :show-loading="false" />
            <div v-else class="default-avatar">
              {{ customer.nickname ? customer.nickname.charAt(0) : '客' }}
            </div>
            <div class="status-indicator" :class="getStatusClass(customer.status)"></div>
          </div>

          <div class="customer-info">
            <div class="customer-basic">
              <div class="customer-header">
                <div class="customer-name">{{ customer.nickname || '未知客户' }}</div>
                <div class="customer-status">
                  <van-tag :type="getStatusTagType(customer.status)" size="small" round>
                    {{ getStatusText(customer.status) }}
                  </van-tag>
                </div>
              </div>

              <div class="customer-details">
                <div class="detail-item">
                  <van-icon name="phone-o" size="14" color="#969799" />
                  <span>{{ customer.mobile || '未绑定手机' }}</span>
                </div>
                <div class="detail-item">
                  <van-icon name="clock-o" size="14" color="#969799" />
                  <span>{{ formatTime(customer.bindingTime) }}</span>
                </div>
              </div>
            </div>

            <div class="customer-stats-section">
              <div class="customer-stats">
                <div class="stat-item">
                  <van-icon name="orders-o" size="14" color="#1989fa" />
                  <span class="stat-text">
                    <span class="stat-label">订单</span>
                    <span class="stat-value">{{ customer.orderCount || 0 }}</span>
                  </span>
                </div>
                <div class="stat-item">
                  <van-icon name="gold-coin-o" size="14" color="#ff976a" />
                  <span class="stat-text">
                    <span class="stat-label">消费</span>
                    <span class="stat-value amount">¥{{ customer.totalAmount || 0 }}</span>
                  </span>
                </div>
                <div class="stat-item">
                  <van-icon name="gem-o" size="14" color="#07c160" />
                  <span class="stat-text">
                    <span class="stat-label">佣金</span>
                    <span class="stat-value commission">¥{{ customer.commission || 0 }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="customer-actions">
            <van-icon name="arrow" size="16" color="#c8c9cc" />
          </div>
        </div>
      </van-list>

      <van-empty v-if="customerList.length === 0 && !loading"
                 description="暂无客户数据"
                 image="https://img.yzcdn.cn/vant/custom-empty-image.png" />
    </div>

    <!-- 客户详情弹窗 -->
    <van-popup v-model="detailVisible" position="bottom" :style="{ height: '85%' }"
               round closeable close-icon-position="top-right">
      <div class="detail-popup" v-if="selectedCustomer">
        <div class="popup-header">
          <div class="customer-profile">
            <div class="profile-avatar">
              <van-image v-if="selectedCustomer.avatar" :src="selectedCustomer.avatar"
                         round width="60" height="60" fit="cover" />
              <div v-else class="default-avatar large">
                {{ selectedCustomer.nickname ? selectedCustomer.nickname.charAt(0) : '客' }}
              </div>
              <div class="status-indicator" :class="getStatusClass(selectedCustomer.status)"></div>
            </div>
            <div class="profile-info">
              <div class="profile-name">{{ selectedCustomer.nickname || '未知客户' }}</div>
              <div class="profile-mobile">{{ selectedCustomer.mobile || '未绑定手机' }}</div>
              <van-tag :type="getStatusTagType(selectedCustomer.status)" size="small" round>
                {{ getStatusText(selectedCustomer.status) }}
              </van-tag>
            </div>
          </div>
        </div>

        <div class="detail-content">
          <!-- 客户基本信息 -->
          <div class="detail-section">
            <div class="section-title">
              <van-icon name="contact" size="16" color="#1989fa" />
              基本信息
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">
                  <van-icon name="user-o" size="16" color="#1989fa" />
                </div>
                <div class="info-content">
                  <div class="info-label">昵称</div>
                  <div class="info-value">{{ selectedCustomer.nickname || '未知' }}</div>
                </div>
              </div>
              <div class="info-item">
                <div class="info-icon">
                  <van-icon name="phone-o" size="16" color="#07c160" />
                </div>
                <div class="info-content">
                  <div class="info-label">手机号</div>
                  <div class="info-value">{{ selectedCustomer.mobile || '未绑定' }}</div>
                </div>
              </div>
              <div class="info-item">
                <div class="info-icon">
                  <van-icon name="clock-o" size="16" color="#ff976a" />
                </div>
                <div class="info-content">
                  <div class="info-label">绑定时间</div>
                  <div class="info-value">{{ formatTime(selectedCustomer.bindingTime) }}</div>
                </div>
              </div>
              <div class="info-item">
                <div class="info-icon">
                  <van-icon name="link-o" size="16" color="#9c26b0" />
                </div>
                <div class="info-content">
                  <div class="info-label">绑定方式</div>
                  <div class="info-value">{{ getBindingTypeText(selectedCustomer.bindingType) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 消费统计 -->
          <div class="detail-section">
            <div class="section-title">
              <van-icon name="chart-trending-o" size="16" color="#1989fa" />
              消费统计
            </div>
            <div class="stats-grid">
              <div class="stat-card orders">
                <div class="stat-icon">
                  <van-icon name="orders-o" size="20" />
                </div>
                <div class="stat-value">{{ selectedCustomer.orderCount || 0 }}</div>
                <div class="stat-label">订单数量</div>
              </div>
              <div class="stat-card amount">
                <div class="stat-icon">
                  <van-icon name="gold-coin-o" size="20" />
                </div>
                <div class="stat-value">¥{{ selectedCustomer.totalAmount || 0 }}</div>
                <div class="stat-label">消费总额</div>
              </div>
              <div class="stat-card commission">
                <div class="stat-icon">
                  <van-icon name="gem-o" size="20" />
                </div>
                <div class="stat-value">¥{{ selectedCustomer.commission || 0 }}</div>
                <div class="stat-label">获得佣金</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="detail-actions">
            <van-button type="primary" block round @click="viewCustomerOrders"
                        :loading="ordersLoading" icon="orders-o">
              查看订单
            </van-button>
            <van-button block round @click="contactCustomer"
                        style="margin-top: 12px;" icon="phone-o">
              联系客户
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 客户订单弹窗 -->
    <van-popup v-model="ordersVisible" position="bottom" :style="{ height: '90%' }"
               round closeable close-icon-position="top-right">
      <div class="orders-popup">
        <div class="popup-header">
          <div class="popup-title">
            <van-icon name="orders-o" size="18" color="#1989fa" />
            客户订单
          </div>
          <div class="customer-name">{{ selectedCustomer ? selectedCustomer.nickname : '' }}</div>
        </div>

        <div class="orders-content">
          <!-- 订单筛选 -->
          <div class="orders-filter">
            <van-tabs v-model="orderStatusFilter" @change="onOrderFilterChange"
                      color="#1989fa" title-active-color="#1989fa">
              <van-tab title="全部" name="all"></van-tab>
              <van-tab title="待支付" name="pending"></van-tab>
              <van-tab title="已支付" name="paid"></van-tab>
              <van-tab title="已取消" name="cancelled"></van-tab>
            </van-tabs>
          </div>

          <!-- 订单列表 -->
          <div class="orders-list">
            <van-list v-model="ordersLoading" :finished="ordersFinished"
                      finished-text="没有更多订单了" @load="loadCustomerOrders">
              <div v-for="order in ordersList" :key="order.id" class="order-item">
                <div class="order-header">
                  <div class="order-info">
                    <div class="order-number">订单号：{{ order.orderSn }}</div>
                    <div class="order-time">{{ formatTime(order.createTime) }}</div>
                  </div>
                  <div class="order-status">
                    <van-tag :type="getOrderStatusTagType(order.status)" size="small" round>
                      {{ getOrderStatusText(order.status) }}
                    </van-tag>
                  </div>
                </div>

                <div class="order-content">
                  <div class="order-detail">
                    <div class="detail-row">
                      <span class="label">订单类型：</span>
                      <span class="value">{{ getOrderTypeText(order.rechargeType) }}</span>
                    </div>
                    <div class="detail-row" v-if="order.countValue">
                      <span class="label">充值次数：</span>
                      <span class="value">{{ order.countValue }}次</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">订单金额：</span>
                      <span class="value amount">¥{{ order.amount || 0 }}</span>
                    </div>
                    <div class="detail-row" v-if="order.payAmount && order.status === 1">
                      <span class="label">实付金额：</span>
                      <span class="value paid">¥{{ order.payAmount || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </van-list>

            <van-empty v-if="ordersList.length === 0 && !ordersLoading"
                       description="暂无订单数据"
                       image="https://img.yzcdn.cn/vant/custom-empty-image.png" />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'MyCustomers',
  data() {
    return {
      activityId: null,
      searchKeyword: '',
      filterStatus: 0,
      filterType: 0,
      sortType: 0,
      statusOptions: [
        { text: '全部状态', value: 0 },
        { text: '有效绑定', value: 1 },
        { text: '已失效', value: 2 }
      ],
      typeOptions: [
        { text: '全部类型', value: 0 },
        { text: '二维码绑定', value: 1 },
        { text: '邀请链接', value: 2 },
        { text: '邀请码', value: 3 }
      ],
      sortOptions: [
        { text: '绑定时间', value: 0 },
        { text: '消费金额', value: 1 },
        { text: '订单数量', value: 2 }
      ],
      customerList: [],
      customerStats: {},
      loading: false,
      finished: false,
      page: 1,
      detailVisible: false,
      selectedCustomer: null,
      // 订单相关
      ordersVisible: false,
      ordersLoading: false,
      ordersFinished: false,
      ordersList: [],
      ordersPage: 1,
      orderStatusFilter: 'all'
    }
  },
  created() {
    this.activityId = this.$route.query.activityId
    this.loadCustomerStats()
    this.onLoad()
  },
  methods: {
    // 加载客户列表
    async onLoad() {
      if (this.loading) return

      this.loading = true

      try {
        const res = await this.$fly.get('/pyp/web/salesman/getCustomerBindings', {
          page: this.page,
          limit: 10,
          keyword: this.searchKeyword,
          status: this.filterStatus,
          bindingType: this.filterType,
          sortType: this.sortType
        })

        if (res.code === 200) {
          const newCustomers = (res.result || []).map(item => ({
            id: item.id,
            wxUserId: item.wxUserId,
            nickname: item.wxUserName || '未知客户',
            mobile: item.wxUserMobile || '未绑定手机',
            avatar: item.wxUserAvatar || '',
            status: item.status, // 1-有效, 0-已失效, 2-已解绑
            bindingTime: item.bindingTime,
            bindingType: item.bindingType, // 1-二维码, 2-邀请链接, 3-邀请码
            orderCount: item.orderCount || 0,
            totalAmount: item.totalAmount || '0.00',
            commission: item.commission || '0.00'
          }))

          if (this.page === 1) {
            this.customerList = newCustomers
          } else {
            this.customerList.push(...newCustomers)
          }

          this.page++
          this.finished = newCustomers.length < 10
        } else {
          this.$toast(res.msg || '加载失败')
          this.finished = true
        }
      } catch (error) {
        console.error('加载客户列表失败:', error)
        // 使用模拟数据作为fallback

      } finally {
        this.loading = false
      }
    },


    // 搜索
    onSearch() {
      this.resetList()
      this.onLoad()
    },

    // 清除搜索
    onClear() {
      this.searchKeyword = ''
      this.resetList()
      this.onLoad()
    },

    // 筛选变化
    onFilterChange() {
      this.resetList()
      this.onLoad()
    },

    // 重置列表
    resetList() {
      this.customerList = []
      this.page = 1
      this.finished = false
    },

    // 查看客户详情
    viewCustomerDetail(customer) {
      this.selectedCustomer = customer
      this.detailVisible = true
    },

    // 查看客户订单
    viewCustomerOrders() {
      this.ordersVisible = true
      this.ordersList = []
      this.ordersPage = 1
      this.ordersFinished = false
      this.loadCustomerOrders()
    },

    // 加载客户订单
    async loadCustomerOrders() {
      if (this.ordersLoading || !this.selectedCustomer) return

      this.ordersLoading = true

      try {
        const res = await this.$fly.get('/pyp/web/salesman/getCustomerOrders', {
          wxUserId: this.selectedCustomer.wxUserId,
          page: this.ordersPage,
          limit: 10,
          status: this.orderStatusFilter === 'all' ? '' : this.getOrderStatusValue(this.orderStatusFilter)
        })

        if (res.code === 200) {
          const newOrders = (res.result || []).map(item => ({
            id: item.id,
            orderSn: item.orderSn,
            rechargeType: item.rechargeType,
            countValue: item.countValue,
            amount: item.amount,
            payAmount: item.payAmount,
            status: item.status,
            createTime: item.createTime || item.createOn,
            payTime: item.payTime
          }))

          if (this.ordersPage === 1) {
            this.ordersList = newOrders
          } else {
            this.ordersList.push(...newOrders)
          }

          this.ordersPage++
          this.ordersFinished = newOrders.length < 10
        } else {
          this.$toast(res.msg || '加载订单失败')
          this.ordersFinished = true
        }
      } catch (error) {
        console.error('加载客户订单失败:', error)
        // 使用模拟数据作为fallback
        if (this.ordersPage === 1) {
          this.ordersList = [
            {
              id: 1,
              orderSn: 'RC20250118001',
              rechargeType: 1,
              countValue: 100,
              amount: '99.00',
              payAmount: '99.00',
              status: 1,
              createTime: '2025-01-18 10:30:00',
              payTime: '2025-01-18 10:31:00'
            },
            {
              id: 2,
              orderSn: 'RC20250117002',
              rechargeType: 2,
              countValue: 50,
              amount: '49.00',
              payAmount: '49.00',
              status: 1,
              createTime: '2025-01-17 15:20:00',
              payTime: '2025-01-17 15:21:00'
            }
          ]
          this.ordersFinished = true
        }
      } finally {
        this.ordersLoading = false
      }
    },

    // 订单筛选变化
    onOrderFilterChange() {
      this.ordersList = []
      this.ordersPage = 1
      this.ordersFinished = false
      this.loadCustomerOrders()
    },

    // 联系客户
    contactCustomer() {
      if (this.selectedCustomer.mobile && this.selectedCustomer.mobile !== '未绑定手机') {
        window.location.href = `tel:${this.selectedCustomer.mobile}`
      } else {
        this.$toast('客户未绑定手机号')
      }
    },

    // 加载客户统计
    async loadCustomerStats() {
      try {
        const res = await this.$fly.get('/pyp/salesman/wxuserbinding/customerStats')

        if (res.code === 200) {
          const stats = res.stats || {}
          this.customerStats = {
            totalCustomers: stats.totalCustomers || 0,
            activeCustomers: stats.activeCustomers || 0,
            newCustomers: stats.todayBindings || 0,
            totalAmount: stats.totalAmount || '0.00'
          }
        } else {
          console.log('获取客户统计失败:', res.msg)
        }
      } catch (error) {
        console.error('获取客户统计失败:', error)
        // 使用模拟数据作为fallback
        this.customerStats = {
          totalCustomers: 156,
          activeCustomers: 142,
          newCustomers: 23,
          totalAmount: '45680.50'
        }
        console.log('使用模拟统计数据')
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '有效',
        2: '已失效'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        1: 'success',
        2: 'danger'
      }
      return typeMap[status] || 'default'
    },

    // 获取绑定方式文本
    getBindingTypeText(type) {
      const typeMap = {
        1: '二维码绑定',
        2: '邀请链接',
        3: '邀请码绑定'
      }
      return typeMap[type] || '未知'
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        1: 'active',
        2: 'inactive'
      }
      return classMap[status] || 'inactive'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '未知时间'

      try {
        const date = new Date(time)
        const now = new Date()
        const diff = now - date
        const days = Math.floor(diff / (1000 * 60 * 60 * 24))

        if (days === 0) {
          return '今天'
        } else if (days === 1) {
          return '昨天'
        } else if (days < 7) {
          return `${days}天前`
        } else {
          return date.toLocaleDateString()
        }
      } catch (error) {
        return time
      }
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已取消',
        3: '已退款'
      }
      return statusMap[status] || '未知'
    },

    // 获取订单状态标签类型
    getOrderStatusTagType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger',
        3: 'default'
      }
      return typeMap[status] || 'default'
    },

    // 获取订单类型文本
    getOrderTypeText(type) {
      const typeMap = {
        1: '套餐充值',
        2: '自定义充值',
        3: '系统赠送',
        4: '创建活动套餐'
      }
      return typeMap[type] || '未知类型'
    },

    // 获取订单状态值
    getOrderStatusValue(filterValue) {
      const valueMap = {
        'pending': 0,
        'paid': 1,
        'cancelled': 2
      }
      return valueMap[filterValue]
    }
  }
}
</script>

<style scoped>
.my-customers-page {
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  min-height: 100vh;
}

.custom-nav-bar {
  /* background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%); */
}

.search-section {
  padding: 16px;
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.filter-section {
  background: white;
  border-bottom: 1px solid #ebedf0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stats-section {
  padding: 16px;
}

.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 16px;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  box-shadow: 0 4px 20px rgba(25, 137, 250, 0.1);
  border: 1px solid rgba(25, 137, 250, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(25, 137, 250, 0.05);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.customers {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #ff976a 0%, #ff6b35 100%);
}

.stat-icon.new {
  background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.customer-list {
  padding: 0 16px 20px;
}

.customer-item {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.customer-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1989fa, #07c160, #ff976a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.customer-item:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.customer-item:active::before {
  opacity: 1;
}

.customer-avatar {
  margin-right: 16px;
  position: relative;
}

.default-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
}

.default-avatar.large {
  width: 60px;
  height: 60px;
  font-size: 20px;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-indicator.active {
  background: #07c160;
}

.status-indicator.inactive {
  background: #ee0a24;
}

.customer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.customer-basic {
  flex: 1;
}

.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.customer-name {
  font-size: 17px;
  font-weight: 600;
  color: #323233;
  line-height: 1.2;
}

.customer-details {
  display: flex;
  gap: 20px;
  margin-bottom: 0;
}

.customer-stats-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #646566;
  background: #f7f8fa;
  padding: 4px 8px;
  border-radius: 8px;
}

.customer-stats {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.customer-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 0;
}

.customer-stats .stat-text {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.customer-stats .stat-label {
  font-size: 11px;
  color: #969799;
  line-height: 1.2;
}

.customer-stats .stat-value {
  font-size: 13px;
  color: #323233;
  font-weight: 600;
  line-height: 1.2;
  margin-top: 1px;
}

.customer-stats .stat-value.amount {
  color: #ff976a;
}

.customer-stats .stat-value.commission {
  color: #07c160;
}

.customer-actions {
  margin-left: 16px;
  color: #c8c9cc;
  display: flex;
  align-items: center;
}

/* 客户详情弹窗样式 */
.detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 20px;
  border-bottom: 1px solid #ebedf0;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.customer-profile {
  display: flex;
  align-items: center;
  gap: 16px;
}

.profile-avatar {
  position: relative;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.profile-mobile {
  font-size: 14px;
  color: #646566;
  margin-bottom: 8px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 28px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(25, 137, 250, 0.1);
}

.info-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(25, 137, 250, 0.1);
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(25, 137, 250, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(25, 137, 250, 0.15);
}

.stat-card .stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
}

.stat-card.amount .stat-icon {
  background: linear-gradient(135deg, #ff976a 0%, #ff6b35 100%);
}

.stat-card.commission .stat-icon {
  background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
}

.stat-card .stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.stat-card .stat-label {
  font-size: 12px;
  color: #969799;
}

.detail-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebedf0;
}

/* 订单弹窗样式 */
.orders-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.orders-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.orders-filter {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.orders-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #ebedf0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
  color: #969799;
}

.order-content {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  font-size: 13px;
  color: #646566;
}

.detail-row .value {
  font-size: 13px;
  color: #323233;
  font-weight: 500;
}

.detail-row .value.amount {
  color: #ff976a;
}

.detail-row .value.paid {
  color: #07c160;
}
</style>
