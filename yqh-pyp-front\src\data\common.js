
// 预算周期
export const yesOrNo = [
    {
        "key": 0,
        "value": "否"
    },
    {
        "key": 1,
        "value": "是"
    },
]
export const yesOrNoText = ["否","是"]
export const idCardType = [{"name": "身份证"},{"name": "军官证"},{"name": "台胞证"},{"name": "护照"},{"name": "其他"}]

export const gradient = [
    "linear-gradient(to right, #7C4DFF, #9F7AEA)",
    "linear-gradient(to right, #E91E63, #F06292)",
    "linear-gradient(to right, #673AB7, #9C27B0)",
    "linear-gradient(to right, #3F51B5, #5C6BC0)",
    "linear-gradient(to right, #009688, #4DB6AC)",
    "linear-gradient(to right, #4CAF50, #81CDBE)",
    "linear-gradient(to right, #FFEB3B, #F57F17)",
    "linear-gradient(to right, #FF9800, #F44336)",
    "linear-gradient(to right, #2196F3, #64B5F6)",
    "linear-gradient(to right, #03A9F4, #4FC3E9)",
    "linear-gradient(to right, #0097A7, #00BFA5)",
    "linear-gradient(to right, #4E8C9A, #5FB7B5)",
    "linear-gradient(to right, #F44336, #E91E63)",
    "linear-gradient(to right, #9C27B0, #7E57C2)",
    "linear-gradient(to right, #3F51B5, #4D4D4D)",
    "linear-gradient(to right, #F57F17, #FBC312)",
    "linear-gradient(to right, #4DB6AC, #80DEEA)",
    "linear-gradient(to right, #5C6BC0, #6C71C4)",
    "linear-gradient(to right, #81CDBE, #B2DFDB)",
    "linear-gradient(to right, #F06292, #C2185B)",
    ]

// export const topicColor = [
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
//     '124, 77, 255',
//     '233, 30, 99',
//     '63, 81, 181',
//     '0, 150, 136',
//     '33, 150, 243',
// ]
export const topicColor = [
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
    '68,133,255',
]
export const guestGoType =     [
    {
        "key": 0,
        "value": "飞机",
        "name": "飞机",
    },
    {
        "key": 1,
        "value": "火车",
        "name": "火车",
    },
    {
        "key": 2,
        "value": "其他",
        "name": "其他",
    }
]
export const tripType =     [
    {
        "key": 0,
        "value": "会务组出票",
        "name": "会务组出票",
    },
    {
        "key": 1,
        "value": "自行出票",
        "name": "自行出票",
    },
]
export const isBuy =     [
    {
        "key": 0,
        "value": "未出票",
        "name": "未出票",
    },
    {
        "key": 1,
        "value": "已出票",
        "name": "已出票",
    },
]
export const doType =     [
    {
        "key": 0,
        "show": true,
        "value": "图片",
        "name": "图片",
    },
    {
        "key": 1,
        "show": true,
        "value": "手填",
        "name": "手填",
    },
    {
        "key": 2,
        "show": false,
        "value": "查询",
        "name": "查询",
    },
]


export const tripPlaneStatus = [
    {
        key: 0,
        value: "已提交",
    },
    {
        key: 1,
        value: "已预订",
    },
    {
        key: 2,
        value: "已支付,待出票",
    },
    {
        key: 4,
        value: "已出票",
    },
    {
        key: 5,
        value: "不能出票待退款",
    },
    {
        key: 7,
        value: "不能出票已退款",
    },
    {
        key: 13,
        value: "(退票)审核不通过交易结束",
    },
    {
        key: 15,
        value: "(退票)申请中",
    },
    {
        key: 16,
        value: "(退票)已退款交易结束",
    },
    {
        key: 22,
        value: "(改签)审核不通过交易结束",
    },
    {
        key: 23,
        value: "(改签)需补款待支付",
    },
    {
        key: 26,
        value: "(改签)无法改签已退款交易结束",
    },
    {
        key: 27,
        value: "(改签)改签成功交易结束",
    },
    {
        key: 28,
        value: "(改签)改签已取消交易结束",
    },
    {
        key: 29,
        value: "(改签)改签处理中",
    },
    {
        key: 99,
        value: "已取消",
    },
];
export const tripTrainStatus = [
    {
        key: 0,
        value: "已提交",
    },
    {
        key: 1,
        value: "已预订",
    },
    {
        key: 2,
        value: "占位成功",
    },
    {
        key: 4,
        value: "占位失败",
    },
    {
        key: 5,
        value: "已取消",
    },
    {
        key: 8,
        value: "出票成功",
    },
    {
        key: 9,
        value: "出票失败",
    },
    {
        key: 31,
        value: "改签占位成功",
    },
    {
        key: 32,
        value: "改签占位失败",
    },
    {
        key: 34,
        value: "改签确认出票成功",
    },
    {
        key: 35,
        value: "改签确认出票失败",
    },
    {
        key: 37,
        value: "改签已取消",
    },
    // =21,=, = 
    {
        key: 21,
        value: "退票成功",
    },
    {
        key: 22,
        value: "退票失败",
    },
    {
        key: 23,
        value: "已退票未退款",
    },
];

export const ticketType = [
    {
        key: 0,
        value: "大会出票",
    },
    {
        key: 1,
        value: "自行购买，凭票大会报销",
    },
];