package com.cjy.pyp.modules.salesman.service;

import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 业务员佣金计算服务
 */
public interface SalesmanCommissionService {

    /**
     * 处理订单支付成功后的佣金计算
     * @param rechargeRecord 充值记录
     */
    void processOrderCommission(ActivityRechargeRecordEntity rechargeRecord);

    /**
     * 处理转发操作的佣金计算
     * @param wxUserId 微信用户ID
     * @param activityId 活动ID
     * @param appid 应用ID
     */
    void processForwardCommission(Long wxUserId, Long activityId, String appid);

    /**
     * 计算佣金金额
     * @param orderAmount 订单金额
     * @param commissionType 佣金类型
     * @param calculationType 计算方式
     * @param commissionRate 佣金比例
     * @param commissionAmount 固定佣金金额
     * @param minAmount 最小金额限制
     * @param maxAmount 最大金额限制
     * @return 计算后的佣金金额
     */
    BigDecimal calculateCommissionAmount(BigDecimal orderAmount, Integer commissionType, 
                                       Integer calculationType, BigDecimal commissionRate, 
                                       BigDecimal commissionAmount, BigDecimal minAmount, 
                                       BigDecimal maxAmount);

    /**
     * 创建佣金记录
     * @param salesmanId 业务员ID
     * @param wxUserId 微信用户ID
     * @param orderId 订单ID
     * @param orderType 订单类型
     * @param commissionConfigId 佣金配置ID
     * @param commissionType 佣金类型
     * @param calculationType 计算方式
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @param commissionAmount 佣金金额
     * @param actualAmount 实际佣金金额
     * @param description 描述
     * @param appid 应用ID
     * @return 佣金记录
     */
    SalesmanCommissionRecordEntity createCommissionRecord(Long salesmanId, Long wxUserId, 
                                                         Long orderId, Integer orderType,
                                                         Long commissionConfigId, Integer commissionType,
                                                         Integer calculationType, BigDecimal orderAmount,
                                                         BigDecimal commissionRate, BigDecimal commissionAmount,
                                                         BigDecimal actualAmount, String description, String appid);

    /**
     * 检查是否已存在佣金记录
     * @param orderId 订单ID
     * @param orderType 订单类型
     * @return 是否存在
     */
    boolean existsCommissionRecord(Long orderId, Integer orderType);

    /**
     * 获取业务员佣金统计
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getCommissionStats(Long salesmanId, String appid);

    /**
     * 获取业务员佣金记录列表
     * @param salesmanId 业务员ID
     * @param appid 应用ID
     * @param page 页码
     * @param limit 每页数量
     * @return 佣金记录列表
     */
    List<SalesmanCommissionRecordEntity> getCommissionRecords(Long salesmanId, String appid, Integer page, Integer limit);
}
