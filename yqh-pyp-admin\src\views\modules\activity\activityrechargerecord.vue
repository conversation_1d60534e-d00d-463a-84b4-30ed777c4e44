<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.orderSn" placeholder="订单号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.activityId" placeholder="选择活动" clearable filterable>
          <el-option
            v-for="item in activityList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.rechargeType" placeholder="充值类型" clearable>
          <el-option label="套餐充值" :value="1"></el-option>
          <el-option label="自定义充值" :value="2"></el-option>
          <el-option label="系统赠送" :value="3"></el-option>
          <el-option label="创建活动套餐" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.status" placeholder="订单状态" clearable>
          <el-option label="待支付" :value="0"></el-option>
          <el-option label="已支付" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
          <el-option label="已退款" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="timeArray"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="giftHandle()">系统赠送</el-button>
        <el-button type="success" @click="exportHandle()">导出</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="orderSn" header-align="center" align="center" label="订单号" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="activityId" header-align="center" align="center" label="活动ID">
      </el-table-column>
      <el-table-column prop="createdActivityId" header-align="center" align="center" label="创建的活动ID" v-if="showCreatedActivityColumn">
        <template slot-scope="scope">
          <span v-if="scope.row.createdActivityId">{{ scope.row.createdActivityId }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="activityName" header-align="center" align="center" label="活动名称" v-if="showCreatedActivityColumn" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.activityName">{{ scope.row.activityName }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="rechargeType" header-align="center" align="center" label="充值类型">
        <template slot-scope="scope">
          <el-tag :type="getRechargeTypeColor(scope.row.rechargeType)">{{ getRechargeTypeText(scope.row.rechargeType) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="packageName" header-align="center" align="center" label="套餐名称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="countValue" header-align="center" align="center" label="充值次数">
      </el-table-column>
      <el-table-column prop="amount" header-align="center" align="center" label="金额(元)">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusColor(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="payMethod" header-align="center" align="center" label="支付方式">
        <template slot-scope="scope">
          <span v-if="scope.row.payMethod">{{ scope.row.payMethod }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="expireTime" header-align="center" align="center" label="有效期至">
      </el-table-column>
      <el-table-column prop="remarks" header-align="center" align="center" label="备注" show-overflow-tooltip>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="createOn" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="payTime" header-align="center" align="center" label="支付时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="180" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.status === 0" type="text" size="small" @click="cancelOrder(scope.row.id)">取消</el-button>
          <el-button v-if="scope.row.status === 4" type="text" size="small" @click="approveRefund(scope.row)" style="color: #67c23a;">通过退款</el-button>
          <el-button v-if="scope.row.status === 4" type="text" size="small" @click="rejectRefund(scope.row)" style="color: #f56c6c;">拒绝退款</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 系统赠送弹窗 -->
    <gift-dialog v-if="giftVisible" ref="giftDialog" @refreshDataList="getDataList"></gift-dialog>
  </div>
</template>

<script>
import GiftDialog from './activityrechargerecord-gift'
export default {
  data() {
    return {
      timeArray: [],
      activityList: [],
      dataForm: {
        orderSn: '',
        activityId: '',
        rechargeType: '',
        status: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      giftVisible: false
    }
  },
  components: {
    GiftDialog
  },
  computed: {
    // 是否显示创建活动相关列
    showCreatedActivityColumn() {
      return this.dataForm.rechargeType === 4 || this.dataList.some(item => item.rechargeType === 4)
    }
  },
  activated() {
    this.getActivityList()
    this.getDataList()
  },
  methods: {
    // 获取活动列表
    getActivityList() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activityList = data.page.list
        } else {
          this.activityList = []
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/activity/rechargerecord/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'orderSn': this.dataForm.orderSn,
          'activityId': this.dataForm.activityId,
          'rechargeType': this.dataForm.rechargeType,
          'status': this.dataForm.status,
          'startTime': (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : '',
          'endTime': (this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : ''
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 系统赠送
    giftHandle() {
      this.giftVisible = true
      this.$nextTick(() => {
        this.$refs.giftDialog.init()
      })
    },

    // 通过退款
    approveRefund(row) {
      this.$confirm(`确定通过订单 ${row.orderSn} 的退款申请吗？\n退款金额：¥${row.refundAmount || row.amount}`, '确认退款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/rechargerecord/approveRefund'),
          method: 'post',
          data: this.$http.adornData({
            id: row.id,
            orderSn: row.orderSn
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '退款审核通过，正在处理退款...',
              type: 'success',
              duration: 2000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
        // 用户取消
      })
    },

    // 拒绝退款
    rejectRefund(row) {
      this.$prompt('请输入拒绝退款的原因', '拒绝退款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '请输入拒绝原因'
      }).then(({ value }) => {
        this.$http({
          url: this.$http.adornUrl('/activity/rechargerecord/rejectRefund'),
          method: 'post',
          data: this.$http.adornData({
            id: row.id,
            orderSn: row.orderSn,
            rejectReason: value
          })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '已拒绝退款申请',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
        // 用户取消
      })
    },
    // 查看详情
    viewDetail(row) {
      let detailHtml = `
        <div style="text-align: left;">
          <p><strong>订单号：</strong>${row.orderSn}</p>
          <p><strong>活动ID：</strong>${row.activityId}</p>
          <p><strong>充值类型：</strong>${this.getRechargeTypeText(row.rechargeType)}</p>
          <p><strong>套餐名称：</strong>${row.packageName || '-'}</p>
          <p><strong>充值次数：</strong>${row.countValue}</p>
          <p><strong>金额：</strong>¥${row.amount}</p>
          <p><strong>状态：</strong>${this.getStatusText(row.status)}</p>`

      // 如果是创建活动套餐，显示相关信息
      if (row.rechargeType === 4) {
        detailHtml += `
          <p><strong>创建的活动ID：</strong>${row.createdActivityId || '-'}</p>
          <p><strong>活动名称：</strong>${row.activityName || '-'}</p>
          <p><strong>活动模板ID：</strong>${row.activityTemplateId || '-'}</p>`
      }

      detailHtml += `
          <p><strong>支付方式：</strong>${row.payMethod || '-'}</p>
          <p><strong>有效期至：</strong>${row.expireTime || '-'}</p>
          <p><strong>备注：</strong>${row.remarks || '-'}</p>
          <p><strong>创建时间：</strong>${row.createOn}</p>
          <p><strong>支付时间：</strong>${row.payTime || '-'}</p>
        </div>`

      this.$alert(detailHtml, '充值记录详情', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      })
    },
    // 取消订单
    cancelOrder(id) {
      this.$confirm('确定要取消该订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activity/rechargerecord/cancel'),
          method: 'post',
          data: this.$http.adornData({ id: id })
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 导出
    exportHandle() {
      var url = this.$http.adornUrl("/activity/rechargerecord/export?" + [
        "token=" + this.$cookie.get('token'),
        "page=" + 1,
        "limit=65535",
        "orderSn=" + this.dataForm.orderSn,
        "activityId=" + this.dataForm.activityId,
        "rechargeType=" + this.dataForm.rechargeType,
        "status=" + this.dataForm.status,
        "startTime=" + ((this.timeArray && this.timeArray.length > 0) ? (this.timeArray[0] + " 00:00:00") : ''),
        "endTime=" + ((this.timeArray && this.timeArray.length > 0) ? (this.timeArray[1] + " 23:59:59") : '')
      ].join('&'));
      window.open(url);
    },
    // 获取充值类型文本
    getRechargeTypeText(type) {
      const typeMap = {
        1: '套餐充值',
        2: '自定义充值',
        3: '系统赠送',
        4: '创建活动套餐'
      }
      return typeMap[type] || '未知'
    },
    // 获取充值类型颜色
    getRechargeTypeColor(type) {
      const colorMap = {
        1: 'primary',
        2: 'warning',
        3: 'success',
        4: 'danger'
      }
      return colorMap[type] || 'info'
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已取消',
        3: '已退款',
        4: '退款中'
      }
      return statusMap[status] || '未知'
    },
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        0: 'warning',
        1: 'success',
        2: 'info',
        3: 'danger',
        4: 'warning'
      }
      return colorMap[status] || 'info'
    }
  }
}
</script>
