<template>
  <div>
    <div v-if="merchantInfo.paramValue"
      class="content"
      v-html="merchantInfo.paramValue"
      @click="showImg($event)"
    ></div>
    <van-empty v-else  description="暂无信息" />
  </div>
</template>

<script>

export default {
  components: {},
  data() {
    return {
      openid: undefined,
      merchantInfo: {},
    };
  },
  mounted() {
    document.title = "系统说明";
    this.$wxShare(
      this.$cookie.get("accountName"),
      this.$cookie.get("logo"),
      this.$cookie.get("slog")
    );
    this.getCmsInfo();
  },
  methods: {
    getCmsInfo() {
      this.$fly.get(`/pyp/web/config/findParamKey`, {
        paramKey: 'help'
      }).then((res) => {
        if (res.code == 200) {
          this.merchantInfo = res.result;
          
        } else {
          vant.Toast(res.msg);
          this.merchantInfo = {};
        }
      });
    },
    // 图片点击放大
    showImg(e) {
      if (e.target.tagName == "IMG" && e.target.src) {
        vant.ImagePreview({
          images: [e.target.src], // 图片集合
          closeable: true, // 关闭按钮
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 10px;
  word-break: break-all;
  word-wrap: break-word;
  margin-top: 10px;
  background-color: white;
  /deep/ p {
    width: 100%;
  }
  /deep/ img {
    width: 100%;
    height: auto;
  }
}
.van-tabs /deep/ .van-tabs__wrap {
  height: 48px;
}
.van-card__thumb /deep/ img {
  object-fit: contain !important;
}
</style>