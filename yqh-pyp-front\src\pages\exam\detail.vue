<template>
  <div :class="isMobilePhone ? '' : 'pc-container'"
    
  >
  <pcheader v-if="!isMobilePhone"/>

  <div style="margin-top: 8px" class="nav-title">
        <div class="color"></div>
        <div class="text">开始您的答题</div>
        </div>
    <div class="list">
      <div
        class="box"
        v-for="(item, index) in exam.examQuestionEntities"
        :key="item.id"
      >
        <div style="padding: 5px 10px 0px 10px" v-html="item.name"></div>
        <van-radio-group
          v-if="item.type == 0"
          v-model="exam.examQuestionEntities[index].selectId"
        >
          <van-cell-group>
            <van-cell
              v-for="item1 in item.examQuestionOptionEntities"
              :key="item1.id"
              :title="item1.optionId + '、' + item1.name"
              clickable
              @click="choose(item1.id, index)"
            >
              <template #right-icon>
                <van-radio :name="item1.id" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
        <van-checkbox-group
          v-if="item.type == 1"
          v-model="exam.examQuestionEntities[index].selectId"
          @change="onChange"
        >
          <van-cell-group>
            <van-cell
              v-for="item1 in item.examQuestionOptionEntities"
              :key="item1.id"
              clickable
              :data-index="item1.id"
              :title="item1.optionId + '、' + item1.name"
              @click="toggle"
            >
              <template #right-icon>
                <van-checkbox :name="item1.id" ref="checkboxes" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
        <van-cell-group v-else-if="item.type == 2">
          <van-field
            placeholder="请输入答题内容"
            style="border-radius: unset"
            v-model="exam.examQuestionEntities[index].selectId"
          />
        </van-cell-group>
      </div>
      <div class="bottom">
        <van-button
          type="primary"
          style="width: 45%"
          round
          block
          @click="saveExam"
          >暂存</van-button
        >
        <van-button
          type="info"
          style="width: 45%"
          round
          block
          @click="submitExam"
          >提交</van-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { isMobilePhone } from '@/js/validate'
import pcheader from "@/pages/cms/components/pcheader.vue";
export default {
  components: {
    pcheader
  },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      openid: undefined,
      token: undefined, // 此token为防重另外
      examActivityUserId: undefined,
      examId: undefined,
      activityId: undefined,
      examActivityUserEntity: {},
      exam: {},
    };
  },
  mounted() {
          document.title ="开始答题";
    this.activityId = this.$route.query.id;
    this.examActivityUserId = this.$route.query.examActivityUserId;
    this.examId = this.$route.query.examId;
    this.token = this.$route.query.token;
    this.openid = this.$cookie.get("openid");
    this.$wxShare(); //加载微信分享
    this.getActivityInfo();
  },
  methods: {
    getActivityInfo() {
      this.$fly
        .get(`/pyp/web/exam/detail`, {
          examId: this.examId,
          examActivityUserId: this.examActivityUserId,
        })
        .then((res) => {
          if (res.code == 200) {
            this.exam = res.exam;
            this.exam.examQuestionEntities.forEach((e) => {
              if (e.type == 1) {
                e.selectId = e.selectId ? e.selectId.split(",") : [];
              }
            });
            this.examActivityUserEntity = res.examActivityUserEntity;
          } else {
          vant.Toast(res.msg);
            this.activityInfo = {};
          }
        });
    },
    // 单选选择
    choose(v, index) {
      this.exam.examQuestionEntities[index].selectId = v;
      console.log(this.exam.examQuestionEntities[index].selectId);
    },
    // 多选选择
    toggle(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxes.filter((item) => item.name == index)[0].toggle();
    },
    // 提交试卷
    submitExam() {
        this.exam.token = this.token;
        this.exam.examActivityUserId = this.examActivityUserId;
        this.exam.examQuestionEntities.forEach((e) => {
            if (e.type == 1) {
                e.selectId = e.selectId ? e.selectId.toString() : '';
            }
        });
      this.$fly.post("/pyp/web/exam/submitExam", this.exam).then((res) => {
        if (res.code == 200) {
          let msg = '';
          if(res.type == 0) {
            // 如果是考试
            msg = '考试提交成功,您的成绩是：' + res.result.points + '分';
          } else {
            // 如果是问卷
            msg = '问卷提交成功';
          }
          vant.Dialog.alert({
            title: "提交成功",
            message: msg,
          }).then(() => {
            // on close
            this.$router.replace({
              name: "examIndex",
              query: { id: this.exam.activityId },
            });
          });
        } else {
          vant.Toast(res.msg);
            this.exam.examQuestionEntities.forEach((e) => {
              if (e.type == 1) {
                e.selectId = e.selectId ? e.selectId.split(",") : [];
              }
            });
        }
      });
    },
    // 提交试卷
    saveExam() {
        this.exam.token = this.token;
        this.exam.examActivityUserId = this.examActivityUserId;
        this.exam.examQuestionEntities.forEach((e) => {
            if (e.type == 1) {
                e.selectId = e.selectId ? e.selectId.toString() : '';
            }
        });
      this.$fly.post("/pyp/web/exam/saveExam", this.exam).then((res) => {
        if (res.code == 200) {
          vant.Dialog.alert({
            title: "暂存成功",
            message: "暂存考试成功",
          }).then(() => {
            // on close
            this.$router.replace({
              name: "examIndex",
              query: { id: this.exam.activityId },
            });
          });
        } else {
          vant.Toast(res.msg);
            this.exam.examQuestionEntities.forEach((e) => {
              if (e.type == 1) {
                e.selectId = e.selectId ? e.selectId.split(",") : [];
              }
            });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  width: 94%;
  margin-left: 3%;
  background: white;
  margin-top: 20px;
}
.bottom {
  display: flex;
  padding: 20px;
  justify-content: space-around;
}
</style>