<template>
    <div>

        <div style="margin: 8px 0" class="nav-title">
            <div class="color"></div>
            <div class="text">我的拼团(拼团进度：{{ activityUserInvite.length + 1 }}/3)</div>
        </div>
        <van-card style="background: white;width: 94%;margin-left: 3%;border-radius: 8px 8px 0 0 ;"
            :thumb="!activityUser.avatar ? 'van-icon' : activityUser.avatar">
            <div slot="title"
                >
                <div style="font-size: 18px">{{ activityUser.contact }}</div>
                <div style="font-size: 16px;color: grey">{{ activityUser.mobile }}</div>
            </div>
            <div slot="desc" style="padding-top: 10px; font-size: 14px; color: grey">
                {{ activityUser.createOn }}
            </div>
        </van-card>
        <div style="width: 94%;margin-left: 3%;background: white;border-radius: 0 0  8px 8px;">
            <div style="display: flex;align-items: center;justify-content: space-between;padding: 5px 10px;" v-for="item in activityUserInvite" :key="item.id" >
                <div style="display: flex;align-items: center;">
                    <img style="width: 40px;height: 40px;border-radius: 40px;" :src="item.avatar" alt="">
                    <div style="margin-left: 10px;font-size: 16px;">{{ item.contact }}</div>
                </div>
                <div style="font-size: 14px; color: grey">{{ item.createOn }}</div>
            </div>
        </div>
        <div style="width: 94%;color: red;margin: 10px 3%;">注：点击右上角，转发给好友，邀请参与拼团。</div>
        <div @click="showImgV = true" class="bottom">邀请好友参团</div>
        <img style="width: 100%;position: fixed;top: 0;z-index: 9999" v-if="showImgV" @click="showImgV = false" src="http://mpjoy.oss-cn-beijing.aliyuncs.com/20230718/5247d62375784bed86a201159a94f372.png" alt="">
    </div>
</template>
  
<script>
import orderStatus from '@/data/orderStatus.json'
export default {
    data() {
        return {
            showImgV: false,
            activityId: '',
            loading: false,
            finished: false,
            flag: false,
            orderStatus: orderStatus,
            activityUser: {},
            activityUserInvite: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
        };
    },
    filters: {
        statusFilter(v) {
            let data = orderStatus.filter(item => item.key === v)
            if (data.length >= 1) {
                return data[0].value;
            }
        },
        statusTypeFilter(v) {
            let data = orderStatus.filter(item => item.key === v)
            if (data.length >= 1) {
                return data[0].type;
            }
        },
    },
    mounted() {
        document.title = "我的拼团";
        this.activityId = this.$route.query.id;
        this.getActivityList();
    },
    methods: {
        showImg() {
            this.showImgV = !this.showImgV;
        },
        getActivityList() {
            this.$fly
                .get("/pyp/web/activityUser/findPintuan", {
                    activityId: this.activityId
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.activityUser = res.activityUser ? res.activityUser : {};
                        this.activityUserInvite = res.activityUserInvite ? res.activityUserInvite : [];
                        if(this.activityUserInvite.length + 1 >= 3) {

                            vant.Dialog.alert({
                                title: "拼团成功",
                                message: "恭喜您，3人成团已完成，福利待领取，48小时内客服将与您确认。",
                            }).then(() => {
                                // on close
                            });
                        }
            this.$wxShare(
            "12个月免费代理记账",
            "http://mpjoy.oss-cn-beijing.aliyuncs.com/20230717/518f97cd97304a60b7d37397fe2fe047.png",
            "快来参与12个月免费代理记账，仅限500家",
            "https://zhaoshengniuren.com/mp_yqh/#/apply/index?id=1674612614545080321&diy=" + encodeURIComponent(this.activityUser.contact)
            );
                    } else if(res.msg == '请先报名') {
                        vant.Toast(res.msg);
                        this.$router.push({
                            name: 'applyIndex',
                            query: {
                                id: this.activityId
                            }
                        })
                    } else {
                        vant.Toast(res.msg);
                        this.activityUser = {};
                        this.activityUserInvite = [];
                    }
                });
        },
    },
};
</script>
<style lang="less" scoped>
/deep/ .van-card__num {
    display: flex;
}
.bottom {
    width: 94%;
    position: fixed;
    left: 3%;
    bottom: 10px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 50px;
    background-color: rgb(202, 67, 67);
    color: white;
}
</style>