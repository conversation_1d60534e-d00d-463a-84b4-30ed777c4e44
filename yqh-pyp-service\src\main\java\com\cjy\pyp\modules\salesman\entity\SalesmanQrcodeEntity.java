package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务员二维码记录实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@Data
@TableName("salesman_qrcode")
@Accessors(chain = true)
public class SalesmanQrcodeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 活动ID（可选，为空表示通用二维码）
     */
    private Long activityId;

    /**
     * 二维码内容
     */
    private String qrcodeContent;

    /**
     * 扫码次数
     */
    private Integer scanCount;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    /**
     * 业务员姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 活动名称（非数据库字段）
     */
    @TableField(exist = false)
    private String activityName;
}
