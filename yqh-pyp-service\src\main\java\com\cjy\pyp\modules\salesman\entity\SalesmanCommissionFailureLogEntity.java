package com.cjy.pyp.modules.salesman.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务员佣金计算失败记录实体类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-17
 */
@Data
@TableName("salesman_commission_failure_log")
@Accessors(chain = true)
public class SalesmanCommissionFailureLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 佣金类型
     */
    private Integer commissionType;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 错误详情
     */
    private String errorDetail;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    private Date nextRetryTime;

    /**
     * 失败状态：0-待重试，1-重试成功，2-重试失败，3-已忽略
     */
    private Integer status;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateOn;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    // 关联查询字段
    /**
     * 业务员姓名
     */
    @TableField(exist = false)
    private String salesmanName;

    /**
     * 业务员编号
     */
    @TableField(exist = false)
    private String salesmanCode;

    /**
     * 佣金类型描述
     */
    @TableField(exist = false)
    private String commissionTypeDesc;

    /**
     * 状态描述
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 是否可重试
     */
    @TableField(exist = false)
    private Boolean canRetry;

    /**
     * 失败时长（分钟）
     */
    @TableField(exist = false)
    private Long failureDuration;
}
