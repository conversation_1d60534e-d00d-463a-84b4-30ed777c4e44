package com.cjy.pyp.modules.activity.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 管理端充值VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-24
 */
@Data
public class AdminRechargeVo {

    /**
     * 防重令牌
     */
    private String repeatToken;
    private String appid;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 充值方式：1-套餐充值，3-系统赠送
     */
    @NotNull(message = "充值方式不能为空")
    private Integer rechargeType;

    /**
     * 套餐ID（套餐充值时必填）
     */
    private Long packageId;

    /**
     * 充值次数
     */
    private Integer count;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 有效天数
     */
    private Integer validDays;

    /**
     * 备注
     */
    private String remarks;
}
