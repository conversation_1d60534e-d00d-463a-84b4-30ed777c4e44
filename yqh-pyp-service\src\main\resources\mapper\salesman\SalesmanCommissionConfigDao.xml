<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cjy.pyp.modules.salesman.dao.SalesmanCommissionConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity" id="salesmanCommissionConfigMap">
        <result property="id" column="id"/>
        <result property="salesmanId" column="salesman_id"/>
        <result property="commissionType" column="commission_type"/>
        <result property="calculationType" column="calculation_type"/>
        <result property="commissionValue" column="commission_value"/>
        <result property="minAmount" column="min_amount"/>
        <result property="maxAmount" column="max_amount"/>
        <result property="status" column="status"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="expiryDate" column="expiry_date"/>
        <result property="remarks" column="remarks"/>
        <result property="appid" column="appid"/>
        <result property="createOn" column="create_on"/>
        <result property="createBy" column="create_by"/>
        <result property="updateOn" column="update_on"/>
        <result property="updateBy" column="update_by"/>
        <result property="salesmanName" column="salesman_name"/>
        <result property="salesmanCode" column="salesman_code"/>
    </resultMap>

    <select id="queryPage" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity">
        SELECT 
            c.*,
            s.name as salesmanName,
            s.code as salesmanCode,
            CASE c.commission_type
                WHEN 1 THEN '创建活动佣金'
                WHEN 2 THEN '充值次数佣金'
                WHEN 3 THEN '用户转发佣金'
                ELSE '未知'
            END as commissionTypeDesc,
            CASE c.calculation_type
                WHEN 1 THEN '固定金额'
                WHEN 2 THEN '百分比'
                ELSE '未知'
            END as calculationTypeDesc
        FROM salesman_commission_config c
        LEFT JOIN salesman s ON c.salesman_id = s.id
        WHERE 1=1
        <if test="salesmanId != null and salesmanId != ''">
            AND c.salesman_id = #{salesmanId}
        </if>
        <if test="commissionType != null and commissionType != ''">
            AND c.commission_type = #{commissionType}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        <if test="salesmanName != null and salesmanName != ''">
            AND s.name LIKE CONCAT('%', #{salesmanName}, '%')
        </if>
        <if test="appid != null and appid != ''">
            AND c.appid = #{appid}
        </if>
        ORDER BY c.create_on DESC
    </select>

    <select id="getByTypeAndSalesman" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity">
        SELECT 
            c.*,
            s.name as salesmanName,
            s.code as salesmanCode,
            CASE c.commission_type
                WHEN 1 THEN '创建活动佣金'
                WHEN 2 THEN '充值次数佣金'
                WHEN 3 THEN '用户转发佣金'
                ELSE '未知'
            END as commissionTypeDesc,
            CASE c.calculation_type
                WHEN 1 THEN '固定金额'
                WHEN 2 THEN '百分比'
                ELSE '未知'
            END as calculationTypeDesc
        FROM salesman_commission_config c
        LEFT JOIN salesman s ON c.salesman_id = s.id
        WHERE c.salesman_id = #{salesmanId} 
        AND c.commission_type = #{commissionType}
        AND c.appid = #{appid}
        AND c.status = 1
        AND (c.effective_date IS NULL OR c.effective_date &lt;= NOW())
        AND (c.expiry_date IS NULL OR c.expiry_date &gt; NOW())
        LIMIT 1
    </select>

    <select id="getEffectiveConfigsBySalesman" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity">
        SELECT 
            c.*,
            s.name as salesmanName,
            s.code as salesmanCode,
            CASE c.commission_type
                WHEN 1 THEN '创建活动佣金'
                WHEN 2 THEN '充值次数佣金'
                WHEN 3 THEN '用户转发佣金'
                ELSE '未知'
            END as commissionTypeDesc,
            CASE c.calculation_type
                WHEN 1 THEN '固定金额'
                WHEN 2 THEN '百分比'
                ELSE '未知'
            END as calculationTypeDesc
        FROM salesman_commission_config c
        LEFT JOIN salesman s ON c.salesman_id = s.id
        WHERE c.salesman_id = #{salesmanId}
        AND c.appid = #{appid}
        AND c.status = 1
        AND (c.effective_date IS NULL OR c.effective_date &lt;= NOW())
        AND (c.expiry_date IS NULL OR c.expiry_date &gt; NOW())
        ORDER BY c.commission_type
    </select>

    <select id="getBatchConfigsBySalesmen" resultType="com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity">
        SELECT 
            c.*,
            s.name as salesmanName,
            s.code as salesmanCode,
            CASE c.commission_type
                WHEN 1 THEN '创建活动佣金'
                WHEN 2 THEN '充值次数佣金'
                WHEN 3 THEN '用户转发佣金'
                ELSE '未知'
            END as commissionTypeDesc,
            CASE c.calculation_type
                WHEN 1 THEN '固定金额'
                WHEN 2 THEN '百分比'
                ELSE '未知'
            END as calculationTypeDesc
        FROM salesman_commission_config c
        LEFT JOIN salesman s ON c.salesman_id = s.id
        WHERE c.salesman_id IN
        <foreach collection="salesmanIds" item="salesmanId" open="(" separator="," close=")">
            #{salesmanId}
        </foreach>
        AND c.appid = #{appid}
        AND c.status = 1
        AND (c.effective_date IS NULL OR c.effective_date &lt;= NOW())
        AND (c.expiry_date IS NULL OR c.expiry_date &gt; NOW())
        ORDER BY c.salesman_id, c.commission_type
    </select>

</mapper>
