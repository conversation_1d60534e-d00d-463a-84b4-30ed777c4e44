<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="发票抬头" prop="invoiceName">
      <el-input v-model="dataForm.invoiceName" placeholder="发票抬头"></el-input>
    </el-form-item>
    <el-form-item label="纳税人识别码" prop="invoiceCode">
      <el-input v-model="dataForm.invoiceCode" placeholder="纳税人识别码"></el-input>
    </el-form-item>
    <el-form-item label="银行" prop="invoiceBank">
      <el-input v-model="dataForm.invoiceBank" placeholder="银行"></el-input>
    </el-form-item>
    <el-form-item label="开户行" prop="invoiceAccount">
      <el-input v-model="dataForm.invoiceAccount" placeholder="开户行"></el-input>
    </el-form-item>
    <el-form-item label="联系方式" prop="invoiceMobile">
      <el-input v-model="dataForm.invoiceMobile" placeholder="联系方式"></el-input>
    </el-form-item>
    <el-form-item label="注册地址" prop="invoiceAddress">
      <el-input v-model="dataForm.invoiceAddress" placeholder="注册地址"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
            repeatToken: '',
          id: 0,
    
          invoiceName: '',
    
          invoiceCode: '',
    
          invoiceBank: '',
    
          invoiceAccount: '',
    
          invoiceMobile: '',
    
          invoiceAddress: ''
        },
        dataRule: {
          invoiceName: [
            { required: true, message: '发票抬头不能为空', trigger: 'blur' }
          ],
          invoiceCode: [
            { required: true, message: '纳税人识别码不能为空', trigger: 'blur' }
          ],
          invoiceBank: [
            { required: true, message: '银行不能为空', trigger: 'blur' }
          ],
          invoiceAccount: [
            { required: true, message: '开户行不能为空', trigger: 'blur' }
          ],
          invoiceMobile: [
            { required: true, message: '注册地址不能为空', trigger: 'blur' }
          ],
          invoiceAddress: [
            { required: true, message: '联系方式不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/invoice/invoice/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.dataForm.invoiceName = data.invoice.invoiceName
                this.dataForm.invoiceCode = data.invoice.invoiceCode
                this.dataForm.invoiceBank = data.invoice.invoiceBank
                this.dataForm.invoiceAccount = data.invoice.invoiceAccount
                this.dataForm.invoiceMobile = data.invoice.invoiceMobile
                this.dataForm.invoiceAddress = data.invoice.invoiceAddress
              }
            })
          }
        })
      },
        getToken() {
            this.$http({
                url: this.$http.adornUrl("/common/createToken"),
                method: "get",
                params: this.$http.adornParams(),
            })
                    .then(({ data }) => {
                        if (data && data.code === 200) {
                            this.dataForm.repeatToken = data.result;
                        }
                    })
        },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/invoice/invoice/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                    'repeatToken': this.dataForm.repeatToken,
                'id': this.dataForm.id || undefined,
                            'invoiceName': this.dataForm.invoiceName,
                            'invoiceCode': this.dataForm.invoiceCode,
                            'invoiceBank': this.dataForm.invoiceBank,
                            'invoiceAccount': this.dataForm.invoiceAccount,
                            'invoiceMobile': this.dataForm.invoiceMobile,
                            'invoiceAddress': this.dataForm.invoiceAddress
              })
            }).then(({data}) => {
              if (data && data.code === 200) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
