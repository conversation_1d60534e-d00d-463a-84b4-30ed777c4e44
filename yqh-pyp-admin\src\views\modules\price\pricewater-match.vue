<template>
  <div>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="匹配金额" prop="price">
        <el-input v-model="dataForm.price" placeholder="匹配金额"></el-input>
      </el-form-item>
      <el-form-item label="摘要" prop="name">
        <el-input v-model="dataForm.name" placeholder="摘要"></el-input>
      </el-form-item>
      <el-form-item :label="'所属会议'" prop="activityId">
        <el-select v-model="dataForm.activityId" :placeholder="'所属会议'" filterable @change="activityChange">
          <el-option v-for="item in activity" :key="item.id" :label="item.code + '-' + item.name"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配类型" prop="matchType">
        <el-radio v-model="dataForm.matchType" :label="0">生成结算单</el-radio>
        <el-radio v-model="dataForm.matchType" :label="1">匹配结算单</el-radio>
      </el-form-item>
            <el-form-item label="供应商" prop="supplierId" v-if="dataForm.type == 0">
              <div style="display: flex">
                <el-select v-model="dataForm.supplierId" filterable>
                  <el-option v-for="item in suppliers" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-button type="text" @click="supplierAddHandle">快速新增</el-button>
              </div>
            </el-form-item>
            <el-form-item label="客户" prop="clientId" v-else>
              <div style="display: flex">
                <el-select v-model="dataForm.clientId" filterable>
                  <el-option v-for="item in client" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-button type="text" @click="clientAddHandle">快速新增</el-button>
              </div>
            </el-form-item>
      <!-- <el-form-item label="科目" v-if="dataForm.matchType == 0" prop="priceConfigId">
        <el-select v-model="dataForm.priceConfigId" placeholder="科目" filterable>
          <el-option v-for="item in priceConfig" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item :label="'关联结算单'" v-if="dataForm.matchType == 1" prop="activitySettleId">
        <el-select v-model="dataForm.activitySettleId" :placeholder="'关联结算单'" filterable>
          <el-option v-for="item in activitySettle" :key="item.id" :label="item.name + '-' + item.price" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
    <supplieradd v-if="supplieraddVisible" ref="supplieradd" @refreshDataList="findSupplier"></supplieradd>
    <clientadd v-if="clientaddVisible" ref="clientadd" @refreshDataList="findClient"></clientadd>
</div>
</template>

<script>
import supplieradd from '@/views/modules/supplier/supplier-add-or-update'
import clientadd from '@/views/modules/client/client-add-or-update'
export default {
  components: {
    supplieradd,
    clientadd,
  },
  data() {
    return {
      supplieraddVisible: false,
      clientaddVisible: false,
      client: [],
      suppliers: [],
      activity: [],
      activitySettle: [],
      priceConfig: [],
      visible: false,
      dataForm: {
        repeatToken: '',
        id: 0,
        type: '',
        name: '',
        price: '',
        priceConfigId: '',
        activityId: '',
        activitySettleId: '',
        matchType: 0,
        clientId: "",
        supplierId: "",
      },
      dataRule: {
        name: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '到款金额不能为空', trigger: 'blur' }
        ],
        priceConfigId: [
          { required: true, message: '科目不能为空', trigger: 'blur' }
        ],
        activityId: [
          { required: true, message: '会议ID不能为空', trigger: 'blur' }
        ],
        activitySettleId: [
          { required: true, message: '所属会议结算单不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    supplierAddHandle() {
      this.supplieraddVisible = true
      this.$nextTick(() => {
        this.$refs.supplieradd.init()
      })
    },
    clientAddHandle() {
      this.clientaddVisible = true
      this.$nextTick(() => {
        this.$refs.clientadd.init()
      })
    },
    init(id) {
      this.getToken();
      this.getPriceConfig();
      this.findActivity();
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.$http({
          url: this.$http.adornUrl(`/price/pricewater/info/${this.dataForm.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.name = data.priceWater.name
            this.dataForm.type = data.priceWater.type
            this.dataForm.price = data.priceWater.price - data.priceWater.matchPrice
            this.dataForm.priceConfigId = data.priceWater.priceConfigId
            this.dataForm.activityId = data.priceWater.activityId
            this.dataForm.activitySettleId = data.priceWater.activitySettleId
          }
        })
      })
      this.findClient();
      this.findSupplier();
    },
    findClient(v) {
      this.$http({
        url: this.$http.adornUrl("/client/client/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.client = data.result;
            if (v) {
              this.dataForm.clientId = v;
              this.getToken();
            }
          }
        })
    },
    findSupplier(v) {
      this.$http({
        url: this.$http.adornUrl(`/supplier/supplier/findAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.suppliers = data.result;
          if (v) {
            this.dataForm.supplierId = v;
            this.getToken();
          }
        }
      })
    },
    findActivity() {
      this.$http({
        url: this.$http.adornUrl('/activity/activity/findByAppid'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activity = data.result
        }
      })
    },
    activityChange(v) {
      this.$http({
        url: this.$http.adornUrl('/activity/activitysettle/findByActivityIdNoPay'),
        method: 'get',
        params: this.$http.adornParams({
          'activityId': v
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.activitySettle = data.result
        }
      })
    },
    getToken() {
      this.$http({
        url: this.$http.adornUrl("/common/createToken"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.dataForm.repeatToken = data.result;
          }
        })
    },
    getPriceConfig() {
      this.$http({
        url: this.$http.adornUrl("/price/priceconfig/findAll"),
        method: "get",
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          if (data && data.code === 200) {
            this.priceConfig = data.result;
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/price/pricewater/match`),
            method: 'post',
            data: this.$http.adornData({
              'repeatToken': this.dataForm.repeatToken,
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'price': this.dataForm.price,
              'priceConfigId': this.dataForm.priceConfigId,
              'appid': this.$cookie.get('appid'),
              'activityId': this.dataForm.activityId,
              'matchType': this.dataForm.matchType,
              'activitySettleId': this.dataForm.activitySettleId,
              'clientId': this.dataForm.clientId,
              'supplierId': this.dataForm.supplierId,
            })
          }).then(({ data }) => {
            if (data && data.code === 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
              if (data.msg != '不能重复提交') {
                this.getToken();
              }
            }
          })
        }
      })
    }
  }
}
</script>
