package com.cjy.pyp.modules.salesman.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;

import com.cjy.pyp.modules.salesman.dao.SalesmanQrcodeDao;
import com.cjy.pyp.modules.salesman.entity.SalesmanQrcodeEntity;
import com.cjy.pyp.modules.salesman.service.SalesmanQrcodeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 业务员二维码记录服务实现类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@Service("salesmanQrcodeService")
public class SalesmanQrcodeServiceImpl extends ServiceImpl<SalesmanQrcodeDao, SalesmanQrcodeEntity> implements SalesmanQrcodeService {

    @Value("${file.upload.path:/upload/}")
    private String uploadPath;

    @Value("${file.access.url:https://yqihua.com}")
    private String accessUrl;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String salesmanName = (String) params.get("salesmanName");
        String activityName = (String) params.get("activityName");
        String appid = (String) params.get("appid");
        Long salesmanId = params.get("salesmanId") != null ? Long.valueOf(params.get("salesmanId").toString()) : null;
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : null;

        IPage<SalesmanQrcodeEntity> page = this.page(
            new Query<SalesmanQrcodeEntity>().getPage(params),
            new QueryWrapper<SalesmanQrcodeEntity>()
                .eq(salesmanId != null, "salesman_id", salesmanId)
                .eq(StringUtils.isNotBlank(appid), "appid", appid)
                .eq(status != null, "status", status)
                .orderByDesc("create_on")
        );

        return new PageUtils(page);
    }

    @Override
    public SalesmanQrcodeEntity generateQrcode(Long salesmanId, Long activityId, String appid) {
        // 构建二维码内容
        String qrcodeContent = buildQrcodeContent(salesmanId, activityId, appid);

        // 检查是否已存在相同的二维码
        SalesmanQrcodeEntity existingQrcode = this.getOne(new QueryWrapper<SalesmanQrcodeEntity>()
            .eq("salesman_id", salesmanId)
            .eq(activityId != null, "activity_id", activityId)
            .eq("appid", appid)
            .eq("status", 1));

        if (existingQrcode != null) {
            return existingQrcode;
        }

        // 创建二维码记录（不生成图片，由前端生成）
        SalesmanQrcodeEntity qrcodeEntity = new SalesmanQrcodeEntity();
        qrcodeEntity.setSalesmanId(salesmanId);
        qrcodeEntity.setActivityId(activityId);
        qrcodeEntity.setQrcodeContent(qrcodeContent);
        qrcodeEntity.setScanCount(0);
        qrcodeEntity.setOrderCount(0);
        qrcodeEntity.setTotalAmount(BigDecimal.ZERO);
        qrcodeEntity.setStatus(1);
        qrcodeEntity.setAppid(appid);

        this.save(qrcodeEntity);
        return qrcodeEntity;
    }

    @Override
    public List<SalesmanQrcodeEntity> findBySalesmanId(Long salesmanId) {
        return this.list(new QueryWrapper<SalesmanQrcodeEntity>()
            .eq("salesman_id", salesmanId)
            .eq("status", 1)
            .orderByDesc("create_on"));
    }

    @Override
    public SalesmanQrcodeEntity findByQrcodeContent(String qrcodeContent) {
        return this.getOne(new QueryWrapper<SalesmanQrcodeEntity>()
            .eq("qrcode_content", qrcodeContent));
    }

    @Override
    public void increaseScanCount(Long qrcodeId) {
        SalesmanQrcodeEntity qrcode = this.getById(qrcodeId);
        if (qrcode != null) {
            qrcode.setScanCount(qrcode.getScanCount() + 1);
            this.updateById(qrcode);
        }
    }

    @Override
    public void increaseOrderStats(Long qrcodeId, BigDecimal orderAmount) {
        SalesmanQrcodeEntity qrcode = this.getById(qrcodeId);
        if (qrcode != null) {
            qrcode.setOrderCount(qrcode.getOrderCount() + 1);
            qrcode.setTotalAmount(qrcode.getTotalAmount().add(orderAmount));
            this.updateById(qrcode);
        }
    }

    @Override
    public List<SalesmanQrcodeEntity> findByAppid(String appid) {
        return this.list(new QueryWrapper<SalesmanQrcodeEntity>()
            .eq("appid", appid)
            .eq("status", 1)
            .orderByDesc("create_on"));
    }

    /**
     * 构建二维码内容
     */
    private String buildQrcodeContent(Long salesmanId, Long activityId, String appid) {
        StringBuilder content = new StringBuilder();
        content.append("https://yqihua.com/p_front/#/salesman/scan");
        content.append("?salesmanId=").append(salesmanId);
        
        if (activityId != null) {
            content.append("&activityId=").append(activityId);
        }
        
        // 添加时间戳确保唯一性
        content.append("&t=").append(System.currentTimeMillis());
        
        return content.toString();
    }
}
