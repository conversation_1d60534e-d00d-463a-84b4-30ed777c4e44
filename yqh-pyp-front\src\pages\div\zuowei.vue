<template>
    <div :class="isMobilePhone ? 'page' : 'page pc-container'">
        <pcheader v-if="!isMobilePhone" />
        <div style="margin-top: 8px" class="nav-title">
            <div class="color"></div>
            <div class="text">座位查询</div>
        </div>
        <van-cell-group inset>
            <van-field v-model="mobile" name="手机号" label="手机号" required placeholder="手机号"
                :rules="[{ required: true, message: '请填写手机号' }]" />
        </van-cell-group>
        <div style="margin: 16px">
            <van-button round block type="info" @click="onSubmit" loading-text="提交中">提交</van-button>
        </div>
    </div>
</template>
  
<script>
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { isMobile, isMobilePhone } from "@/js/validate";
export default {
    components: {
        VueQrcode
    },
    data() {
        return {
            mobile: '',
            activityId: undefined,
            userInfo: {},
            activityInfo: {},
            isMobilePhone: isMobilePhone(),
            isSms: isMobilePhone(),
        };
    },
    mounted() {
        document.title = "座位查询";
        this.activityId = this.$route.query.id;
        this.getActivityInfo();
        this.getUserInfo();
    },
    methods: {
        onSubmit() {
            if (!this.mobile) {
                vant.Toast("请输入手机号");
                return false;
            }
            if (!isMobile(this.mobile)) {
                vant.Toast("手机号格式错误");
                return false;
            }
            this.$fly
                .get(`/pyp/web/activityUser/findByMobileAndActivityId`, {
                    mobile: this.mobile,
                    activityId: this.activityId,
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.userInfo = res.result;
                        if (this.userInfo.status != 1) {
                            vant.Dialog.alert({
                                title: "提示",
                                message: '查询不到座位信息',
                            }).then(() => {
                                // on close
                            });
                        } else {

                            vant.Dialog.alert({
                                title: "查询成功",
                                message: '您的座位是：' + this.userInfo.remarks,
                            }).then(() => {
                                // on close
                            });
                        }
                    } else {
                        vant.Dialog.alert({
                            title: "报名须知",
                            message: '查询不到座位信息',
                        }).then(() => {
                            // on close
                        });
                    }
                });
        },
    getUserInfo() {
      this.$fly.get("/pyp/wxUser/getUserInfo").then((res) => {
        if (res.code == 200) {
        //   this.userInfo = res.data;
        }
      });
    },
        getActivityInfo() {
            this.$fly.get(`/pyp/activity/activity/info/${this.activityId}`).then(res => {
                if (res.code == 200) {
                    this.activityInfo = res.activity
                } else {
                    this.activityInfo = {}
                }
            });
        },
    },
};
</script>
  
<style lang="less" scoped>
.page {
    background-color: #f6f6f6;
}

.activity-info {
    text-align: center;
    padding: 10px 0;
    width: 90%;
    margin-left: 5%;
    background: white;
    border-radius: 20px;
    position: fixed;
    top: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
    height: 70px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
}

.sign-info {
    text-align: center;
    padding: 10px 0;
    width: 90%;
    margin-left: 5%;
    background: white;
    border-radius: 20px;
    position: fixed;
    top: 120px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
    height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
}

.apply-info {
    padding: 10px 0;
    width: 90%;
    margin-left: 5%;
    background: white;
    border-radius: 20px;
    position: fixed;
    top: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.16);
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.bottom-button {
    width: 90%;
}
</style>